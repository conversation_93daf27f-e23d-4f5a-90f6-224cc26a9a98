package com.voghion.product.client;

import com.voghion.merchant.api.service.MerchantsRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MerchantsClientFactory {


    @Reference
    private MerchantsRemoteService merchantsRemoteService;

    /**
     * 查询商家协议
     * @return
     */
    public Boolean checkAggrement(Long shopId,Integer code){
        log.info("调用merchantsRemoteService.checkAggrement 开始，shopId is :{},code is: {}", shopId,code);
        try{
            return merchantsRemoteService.checkAggrement(shopId.toString(), code);
        }catch (Exception e){
            log.error("调用merchantsRemoteService.checkAggrement 失败，shopId is :{}", shopId);
            return false;
        }
    }
}
