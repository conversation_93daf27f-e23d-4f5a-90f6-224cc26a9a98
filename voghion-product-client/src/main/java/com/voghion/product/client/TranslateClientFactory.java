package com.voghion.product.client;

import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.model.Result;
import com.colorlight.base.utils.StringUtil;
import com.colorlight.base.utils.TransferUtils;
import com.colorlight.translate.api.TranslateRemoteService;
import com.colorlight.translate.dto.BatchTranslateDto;
import com.colorlight.translate.dto.WordContentDto;
import com.colorlight.translate.enums.AwsLang;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * 类目工厂方法用于远程调用
 */
@Component
@Slf4j
public class TranslateClientFactory {

    @Value("${voghion.dev}")
    private String profile;

    @Reference(timeout = 10 * 1000)
    private TranslateRemoteService translateRemoteService;


    public String translate(String content, AwsLang source, AwsLang target) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        if (StringUtils.isNotBlank(profile) && profile.equals("test")) {
            return content;
        }
        if (!isContainChinese(content)) {
            return content;
        }
        content = content.replaceAll("#", "&archer0603&");
        content = content.replaceAll("@", "&archer0602&");
        WordContentDto wordContentDto = new WordContentDto();
        wordContentDto.setSource(source);
        wordContentDto.setTarget(target);
        wordContentDto.setOrginalContent(content);
        log.info("翻译前内容:{}", JSONObject.toJSONString(wordContentDto));
        Result<String> translateResult = translateRemoteService.translate(wordContentDto);
        log.info("翻译后内容:{}", JSONObject.toJSONString(translateResult));
        if (translateResult.isSuccess() && StringUtil.isNotBlank(translateResult.getData())) {
            String result = translateResult.getData();

            result = result.replaceAll("&archer0603&", "#");
            result = result.replaceAll("&archer0603 &", "#");

            result = result.replaceAll("&archer0602&", "@");
            result = result.replaceAll("&archer0602 &", "@");
            return result;
        }

        return null;
    }


    public void batchTranslate(List<String> contentList){
        BatchTranslateDto batchTranslateDto = new BatchTranslateDto();
        batchTranslateDto.setContentList(contentList);
        batchTranslateDto.setSource(AwsLang.EN);
        batchTranslateDto.setTarget(AwsLang.DE);
        Result<HashMap<String, String>> hashMapResult = translateRemoteService.batchTranslate(batchTranslateDto);

    }


    public HashMap<String, String>  batchTranslate(List<String> contentList,AwsLang source, AwsLang target){
        if (CollectionUtils.isEmpty(contentList)) {
            return Maps.newHashMap();
        }

        HashMap<String, String> resultMap = Maps.newHashMap();
        contentList.stream().filter(s -> !isContainChinese(s)).forEach(s -> resultMap.put(s, s));

        List<String> needTranslateList = contentList.stream().filter(TranslateClientFactory::isContainChinese).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needTranslateList)) {
            BatchTranslateDto batchTranslateDto = new BatchTranslateDto();
            batchTranslateDto.setContentList(contentList);
            batchTranslateDto.setSource(source);
            batchTranslateDto.setTarget(target);
            Result<HashMap<String, String>> hashMapResult = translateRemoteService.batchTranslate(batchTranslateDto);
            if(hashMapResult.isSuccess() && hashMapResult.getData()!=null){
                log.info("批量翻译结果{}", hashMapResult.getData());
                resultMap.putAll(hashMapResult.getData());
            }
        }
        return resultMap;
    }

    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\u4e00-\u9fa5]");
    protected static boolean isContainChinese(String str) {
        if(StringUtils.isBlank(str)){
            return false;
        }
        Matcher m = CHINESE_PATTERN.matcher(str);
        return m.find();
    }

}
