package com.voghion.product.client;

import com.alibaba.fastjson.JSON;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.google.common.collect.Maps;
import com.voghion.comment.api.output.GoodsCommentPageDTO;
import com.voghion.comment.api.output.GoodsCommentQueryDto;
import com.voghion.comment.api.output.ShopGoodsCommentDTO;
import com.voghion.comment.api.service.UserGoodsCommentRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GoodsCommentFactory {
    @Reference(check = false)
    private UserGoodsCommentRemoteService userGoodsCommentRemoteService;

    public Map<Long, Integer> countGoodsComment(List<Long> goodsIds){
        String goodsIdStr = JSON.toJSONString(goodsIds);
        log.info("call GoodsCommentFactory countGoodsComment goodsIds:{}", goodsIdStr);
        try{
            Result<Map<Long, Integer>> mapResult = userGoodsCommentRemoteService.countGoodsComment(goodsIds);
            log.info("call GoodsCommentFactory countGoodsComment mapResult data:{}", JSON.toJSONString(mapResult.getData()));
            return mapResult.getData();
        }catch (Exception e) {
            log.info("call GoodsCommentFactory countGoodsComment fail, goodsIds = {}, message = {}, e = {}", goodsIdStr, e.getMessage(), e);
            return null;
        }
    }

    public Map<Long, ShopGoodsCommentDTO> queryCountAndAverageByGoodsIds(List<Long> goodsIds) {
        log.info("开始调用【userGoodsCommentRemoteService.queryCountAndAverageByGoodsIds】goodsIds:{}", JSON.toJSONString(goodsIds));
        Result<List<ShopGoodsCommentDTO>> result = userGoodsCommentRemoteService.queryCountAndAverageByGoodsIds(goodsIds);
        log.info("调用【userGoodsCommentRemoteService.queryCountAndAverageByGoodsIds】返回:{}", JSON.toJSONString(result));

        if (!result.isSuccess() || result.getData() == null) {
            return Maps.newHashMap();
        }

        return result.getData().stream().collect(Collectors.toMap(ShopGoodsCommentDTO::getGoodsId, Function.identity(), (v1, v2) -> v1));
    }


    public Map<Long, ShopGoodsCommentDTO> queryCountAndAverageByShopIds(List<Long> shopIds) {
        log.info("开始调用【userGoodsCommentRemoteService.queryCountAndAverageByShopIds】shopIds:{}", JSON.toJSONString(shopIds));
        Result<List<ShopGoodsCommentDTO>> result = userGoodsCommentRemoteService.queryCountAndAverageByShopIds(shopIds);
        log.info("调用【userGoodsCommentRemoteService.queryCountAndAverageByShopIds】返回:{}", JSON.toJSONString(result));

        if (!result.isSuccess() || result.getData() == null) {
            return Maps.newHashMap();
        }

        return result.getData().stream().collect(Collectors.toMap(ShopGoodsCommentDTO::getShopId, Function.identity(), (v1, v2) -> v1));
    }

    public  ShopGoodsCommentDTO queryTotalCountAndAverageByGoodsIds(List<Long> goodsIds) {
        log.info("开始调用【userGoodsCommentRemoteService.queryTotalCountAndAverageByGoodsIds】goodsIds:{}", JSON.toJSONString(goodsIds));
        GoodsCommentQueryDto queryDto = new GoodsCommentQueryDto();
        queryDto.setGoodsIds(goodsIds);
        ShopGoodsCommentDTO shopGoodsCommentDTO = userGoodsCommentRemoteService.queryTotalCountAndAverageByGoodsIds(queryDto);
        log.info("调用【userGoodsCommentRemoteService.queryTotalCountAndAverageByGoodsIds】返回:{}", JSON.toJSONString(shopGoodsCommentDTO));
        return shopGoodsCommentDTO;
    }

    public  PageView<GoodsCommentPageDTO> pageCommentsByGoodsIds(GoodsCommentQueryDto queryDto) {
        log.info("开始调用【userGoodsCommentRemoteService.pageCommentsByGoodsIds】goodsIds:{}", JSON.toJSONString(queryDto));
        PageView<GoodsCommentPageDTO> pageView = userGoodsCommentRemoteService.pageCommentsByGoodsIds(queryDto);
        log.info("调用【userGoodsCommentRemoteService.pageCommentsByGoodsIds】返回:{}", JSON.toJSONString(pageView));
        return pageView;
    }
}
