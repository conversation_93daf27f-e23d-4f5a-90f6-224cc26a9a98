CREATE TABLE IF NOT EXISTS `ads_goods_catsum_ranking_w` (
                                                            `id` int(11) NOT NULL AUTO_INCREMENT,
    `week`          DATE            NOT NULL COMMENT '周（周一日期）',
    `category1`     VARCHAR(64)     NOT NULL COMMENT '一级类目',
    `category1_name` VARCHAR(128)   NOT NULL COMMENT '一级类目名',
    `category2`     VARCHAR(64)     NOT NULL COMMENT '二级类目',
    `category2_name` VARCHAR(128)   NOT NULL COMMENT '二级类目名',
    `goods_id`      INT UNSIGNED    NOT NULL COMMENT '商品id',
    `list_type`     VARCHAR(32)     NOT NULL COMMENT '榜单类型',
    `hot_value`     FLOAT           DEFAULT NULL COMMENT '热度值',
    `rank`          INT             DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (`id`),
    index idx_week_goods_id_type (`week`, `goods_id`, `list_type`)
    ) ENGINE=InnoDB
    DEFAULT CHARSET=utf8mb4
    COMMENT='周榜数据表临时模拟表';

-- 2. 月榜数据表
CREATE TABLE IF NOT EXISTS `ads_goods_catsum_ranking_m` (
                                                            `id` int(11) NOT NULL AUTO_INCREMENT,
    `month`         DATE            NOT NULL COMMENT '月（当月第一天日期）',
    `category1`     VARCHAR(64)     NOT NULL COMMENT '一级类目',
    `category1_name` VARCHAR(128)   NOT NULL COMMENT '一级类目名',
    `category2`     VARCHAR(64)     NOT NULL COMMENT '二级类目',
    `category2_name` VARCHAR(128)   NOT NULL COMMENT '二级类目名',
    `goods_id`      INT UNSIGNED    NOT NULL COMMENT '商品id',
    `list_type`     VARCHAR(32)     NOT NULL COMMENT '榜单类型',
    `hot_value`     FLOAT           DEFAULT NULL COMMENT '热度值',
    `rank`          INT             DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (id),
    index idx_month_goods_id (`month`, `goods_id`, `list_type`)
    ) ENGINE=InnoDB
    DEFAULT CHARSET=utf8mb4
    COMMENT='月榜数据表临时模拟表';

-- 3. 季度榜数据表
CREATE TABLE IF NOT EXISTS `ads_goods_catsum_ranking_q` (
                                                            `id` int(11) NOT NULL AUTO_INCREMENT,
    `quarter`       DATE            NOT NULL COMMENT '季度（当季第一天日期）',
    `category1`     VARCHAR(64)     NOT NULL COMMENT '一级类目',
    `category1_name` VARCHAR(128)   NOT NULL COMMENT '一级类目名',
    `category2`     VARCHAR(64)     NOT NULL COMMENT '二级类目',
    `category2_name` VARCHAR(128)   NOT NULL COMMENT '二级类目名',
    `goods_id`      INT UNSIGNED    NOT NULL COMMENT '商品id',
    `list_type`     VARCHAR(32)     NOT NULL COMMENT '榜单类型',
    `hot_value`     FLOAT           DEFAULT NULL COMMENT '热度值',
    `rank`          INT             DEFAULT NULL COMMENT '排序',
    PRIMARY KEY (id),
    index idx_quarter_goods_id (`quarter`, `goods_id`, `list_type`)
    ) ENGINE=InnoDB
    DEFAULT CHARSET=utf8mb4
    COMMENT='季度榜数据表临时模拟表';