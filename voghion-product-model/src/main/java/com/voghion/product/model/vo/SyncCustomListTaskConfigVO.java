package com.voghion.product.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.voghion.product.model.conveter.LocalDateTimeConverter;
import com.voghion.product.model.conveter.LocalDateTimeDeSerializerConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel
public class SyncCustomListTaskConfigVO extends PageParam{

        private static final long serialVersionUID = 1L;

        @TableId(value = "id", type = IdType.AUTO)
        private Long id;

        /**
         * 任务名称
         */
        @ApiModelProperty("任务名称")
        private String taskName;

        /**
         * 1 - 生效 2 - 暂停 3 - 删除
         */
        @ApiModelProperty("1 - 生效 2 - 暂停 3 - 删除")
        private Integer taskStatus;

        /**
         * 类目id
         */
        @ApiModelProperty("类目id")
        private Long categoryId;

        /**
         * 商品创建开始时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
        @ApiModelProperty("商品创建开始时间")
        @JsonSerialize(using = LocalDateTimeConverter.class)
        private LocalDateTime goodsCreateStartTime;

        /**
         * 商品创建结束时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
        @JsonSerialize(using = LocalDateTimeConverter.class)
        @ApiModelProperty("商品创建结束时间")
        private LocalDateTime goodsCreateEndTime;

        /**
         * 活动id
         */
        @ApiModelProperty("活动id")
        private String activityId;

        /**
         * 活动最低折扣率
         */
        @ApiModelProperty("活动最低折扣率")
        private BigDecimal activityMaxCost;

        /**
         * 商品最低价
         */
        @ApiModelProperty("商品最低价")
        private BigDecimal minPriceStart;

        /**
         * 商品最低价
         */
        @ApiModelProperty("商品最低价")
        private BigDecimal minPriceEnd;

        /**
         * 商品最高价
         */
        @ApiModelProperty("商品最高价")
        private BigDecimal maxPriceStart;
        /**
         * 商品最高价
         */
        @ApiModelProperty("商品最高价")
        private BigDecimal maxPriceEnd;

        /**
         * 商家id 换行符分割
         */
        @ApiModelProperty("商家id 换行符分割")
        private String shopIdStr;

        /**
         * 所有国家或者部分国家
         */
        @ApiModelProperty("国家列表")
        private List<String> countryList;

        /**
         * 标签id
         */
        @ApiModelProperty("标签id")
        private List<Long> tagIds;

        /**
         * 自定义标签ID
         */
        @ApiModelProperty("自定义标签ID")
        private List<Long> customTags;

        /**
         * 是否负向商品
         */
        @ApiModelProperty("是否负向商品 1 - 是 0 - 否")
        private Integer isNegative;

        /**
         * 几天内
         */
        @ApiModelProperty("几天内")
        private Integer runDays;

        @ApiModelProperty("是否listing 1 - 是 0 - 否")
        private Integer isListing;

        @ApiModelProperty("商品类型")
        private Integer type;

        /**
         * 成交单数
         */
        @ApiModelProperty("成交单数")
        private Integer dealCnt;

        /**
         * 成交uv
         */
        @ApiModelProperty("成交uv")
        private Integer dealUv;

        /**
         * gmv
         */
        @ApiModelProperty("gmv")
        private BigDecimal dealPayMoney;

        /**
         * uv点击率
         */
        @ApiModelProperty("uv点击率")
        private BigDecimal clickRateUv;

        /**
         * uv架构率
         */
        @ApiModelProperty("uv加购率")
        private BigDecimal addRateUv;

        /**
         * uv支付率
         */
        @ApiModelProperty("uv支付率")
        private BigDecimal dealRateUv;

        /**
         * 千次转化
         */
        @ApiModelProperty("千次转化")
        private Integer ecpm;

        @ApiModelProperty("虚拟列表id")
        private Long customId;

        @ApiModelProperty("执行类型 1 - 全量替换  2 - 只添加")
        private Integer executeType;

        @ApiModelProperty("排序字段")
        private String orderItem;

        @ApiModelProperty("总数")
        private Long totalMin;


        private String executeTime;

        /**
         * 操作人
         */
        private String operator;

        /**
         * 创建时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
        @JsonSerialize(using = LocalDateTimeConverter.class)
        private LocalDateTime createTime;

        /**
         * 更新时间
         */
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
        @JsonSerialize(using = LocalDateTimeConverter.class)
        private LocalDateTime updateTime;


        /**
         * 0 - 固定时间 1 - 动态时间
         */
        @ApiModelProperty("0 - 固定时间 1 - 动态时间")
        private Integer goodsCreateType;
        /**
         * 商品创建动态时间7 15 30
         */
        @ApiModelProperty("商品创建动态时间7 15 30 60 90")
        private Integer goodsCreateDaysType;
        /**
         * 商品状态
         */
        @ApiModelProperty("商品状态")
        private Integer isShow;

        /**
         * 商品状态
         */
        @ApiModelProperty("商品状态")
        private List<Integer> isShowList;
        /**
         * 曝光uv
         */
        @ApiModelProperty("曝光uv开始")
        private Integer showUvStart;
        @ApiModelProperty("曝光uv结束")
        private Integer showUvEnd;
        /**
         * 0 - 单个类目价格限制 1 - 多个价格类目限制
         */
        @ApiModelProperty("0 - 单个类目价格限制 1 - 文件上传")
        private Integer categoryType;

        /**
         * 多个类目价格限制ID
         */
        @ApiModelProperty("文件上传的ID")
        private String categoryPriceId;

        @ApiModelProperty("合规审核 1待审核 2通过 3驳回")
        private Integer complianceAuditStatus;


        /**
         * 点击uv
         */
        @ApiModelProperty("点击uv")
        private BigDecimal clickUv;

        @ApiModelProperty("加购")
        private BigDecimal addUv;

        @ApiModelProperty("类目")
        private List<Long> categoryIds;

        @ApiModelProperty("商品id")
        private String goodsIdStr;

        @ApiModelProperty("是否查看置顶商品 1是0否")
        private Integer topGoodsStatus = 0;


        @ApiModelProperty("查询类型 1运营 2商家")
        private Integer queryType = 1;

        @ApiModelProperty("商品名称")
        private String name;

        @ApiModelProperty("添加类型 1覆盖 2追加")
        private Integer addType;

        @ApiModelProperty("商家标签")
        private List<Long> shopTagIds;

        private Integer listingId;
}
