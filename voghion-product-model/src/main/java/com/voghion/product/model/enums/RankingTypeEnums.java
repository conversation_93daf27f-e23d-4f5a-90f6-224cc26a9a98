package com.voghion.product.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.YearMonth;

/**
 * 榜单类型枚举
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Getter
@AllArgsConstructor
public enum RankingTypeEnums {

    /**
     * 周榜 - 最近7天平台热门商品
     */
    WEEKLY(8, "周榜", "最近7天平台热门商品"),

    /**
     * 月榜 - 最近30天平台热门商品
     */
    MONTHLY(9, "月榜", "最近30天平台热门商品"),

    /**
     * 当前季度热销榜
     */
    CURRENT_QUARTER(10, "当前季度", "当前季度热销商品"),

    /**
     * 上1季度热销榜
     */
    LAST_QUARTER_1(11, "上1季度", "上1季度热销商品"),

    /**
     * 上2季度热销榜
     */
    LAST_QUARTER_2(12, "上2季度", "上2季度热销商品"),

    /**
     * 上3季度热销榜
     */
    LAST_QUARTER_3(13, "上3季度", "上3季度热销商品");

    private final Integer code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static RankingTypeEnums getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RankingTypeEnums type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    private static String recentQuarters(int delta) {
        LocalDate today = LocalDate.now();          // 当前日期
        YearMonth ym  = YearMonth.from(today);      // 当前年月

        int year  = ym.getYear();
        int month = ym.getMonthValue();

        // 本季度（1~3 月 -> 1 季度，4~6 -> 2 季度 …）
        int quarter = (month - 1) / 3 + 1;

        int q = quarter - delta;
        int y = year;
        if (q <= 0) {          // 跨年时处理
            q += 4;
            y -= 1;
        }
        return String.format("%dQ%d", y, q);
    }

    public String getYearName(){
        if (this.code < CURRENT_QUARTER.getCode()){
            return this.name;
        }

        return recentQuarters(this.code - CURRENT_QUARTER.getCode());
    }

    public static String getYearName(Integer code){
        if (code == null){
            return "";
        }
        for (RankingTypeEnums type : values()) {
            if (type.getCode().equals(code)) {
                return type.getYearName();
            }
        }
        return "";
    }

    /**
     * 判断是否为季度榜单
     */
    public boolean isQuarterlyRanking() {
        return this == CURRENT_QUARTER || this == LAST_QUARTER_1 || 
               this == LAST_QUARTER_2 || this == LAST_QUARTER_3;
    }

    /**
     * 判断是否为周榜
     */
    public boolean isWeeklyRanking() {
        return this == WEEKLY;
    }

    /**
     * 判断是否为月榜
     */
    public boolean isMonthlyRanking() {
        return this == MONTHLY;
    }
}
