package com.voghion.product.model.vo.condition;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @date: 2022/12/16 上午11:44
 * @author: jashley
 */
@Data
@ApiModel
public class ChanceGoodsTemplateQueryCondition implements Serializable {
    private static final long serialVersionUID = -1561814747725053318L;

    @ApiModelProperty("模板类型 0-普通模板 1-简易模板")
    private Integer type = 0;

    @ApiModelProperty("来源类型 1直接创建，2克隆，3速卖通爬虫，4shein爬虫 5temu美站  6temu德站")
    private Integer sourceType;

    @ApiModelProperty("模板id")
    private Long id;

    @ApiModelProperty("模板id集合，换行分隔")
    private String ids;

    @ApiModelProperty("状态 0草稿 1开放 2自动关闭 3人工关闭")
    private Integer status;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("类目id")
    private Long categoryId;

    @ApiModelProperty("最小创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startCreateTime;

    @ApiModelProperty("最大创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCreateTime;

    @ApiModelProperty("最小更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startUpdateTime;

    @ApiModelProperty("最大更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endUpdateTime;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("最后更新人")
    private String updateUser;

    @ApiModelProperty("最低销量")
    private Integer minSales;

    @ApiModelProperty("最高销量")
    private Integer maxSales;

    @ApiModelProperty("最低热度")
    private Integer minHot;

    @ApiModelProperty("最高热度")
    private Integer maxHot;

    private Integer pageNow = 1;
    private Integer pageSize = 20;
}
