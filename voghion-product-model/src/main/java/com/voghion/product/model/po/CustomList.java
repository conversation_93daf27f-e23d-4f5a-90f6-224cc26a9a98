package com.voghion.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 * 自定义列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-24
 */
@Data
//@TableName("`skr-backend`.custom_list ")
@TableName("custom_list ")
public class CustomList extends Model<CustomList> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 标题
     */
    private String title;

    /**
     * 子标题
     */
    private String subTitle;

    /**
     * 图片地址
     */
    private String imgUrl;

    private LocalDateTime createTime;

    /**
     * 是否上架 1-上架 0-下架
     */
    private String isShow;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDel;

    /**
     * 排序从大到小
     */
    private Integer sort;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 轮播图地址
     */
    private String cycleImg;

    /**
     * offers图链接
     */
    private String detailImg;

    /**
     * 国家 code
     */
    private String countryCode;

    /**
     * 标签id
     */
    private String tagId;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后更新人
     */
    private String updateUser;

    private int insertQuantity;

    @ApiModelProperty(value = "部门 0 运营 1 网红 2 投放")
    private int departmentType;

    @ApiModelProperty(value = "置顶类型 0不置顶  1 插位置顶  3 全部置顶")
    private int placementType;

    @ApiModelProperty(value = "是否开启算法 0 不开启 1 开启")
    private int useAlgorithm;

    private Long insertCustomId;

    public Long getInsertCustomId() {
        return insertCustomId;
    }

    public void setInsertCustomId(Long insertCustomId) {
        this.insertCustomId = insertCustomId;
    }

    public int getInsertQuantity() {
        return insertQuantity;
    }

    public void setInsertQuantity(int insertQuantity) {
        this.insertQuantity = insertQuantity;
    }

    public int getDepartmentType() {
        return departmentType;
    }

    public void setDepartmentType(int departmentType) {
        this.departmentType = departmentType;
    }

    public int getPlacementType() {
        return placementType;
    }

    public void setPlacementType(int placementType) {
        this.placementType = placementType;
    }

    public int getUseAlgorithm() {
        return useAlgorithm;
    }

    public void setUseAlgorithm(int useAlgorithm) {
        this.useAlgorithm = useAlgorithm;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getTagId() {
        return tagId;
    }

    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    public String getDetailImg() {
        return detailImg;
    }

    public void setDetailImg(String detailImg) {
        this.detailImg = detailImg;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCycleImg(){return cycleImg;}

    public void setCycleImg(String cycleImg){this.cycleImg = cycleImg;}

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }
    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getIsShow() {
        return isShow;
    }

    public void setIsShow(String isShow) {
        this.isShow = isShow;
    }
    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "CustomList{" +
                "id=" + id +
                ", type=" + type +
                ", title='" + title + '\'' +
                ", subTitle='" + subTitle + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                ", createTime=" + createTime +
                ", isShow='" + isShow + '\'' +
                ", isDel=" + isDel +
                ", sort=" + sort +
                ", channel=" + channel +
                ", cycleImg='" + cycleImg + '\'' +
                ", detailImg='" + detailImg + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", tagId='" + tagId + '\'' +
                '}';
    }
}
