package com.voghion.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 商品标签
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GoodsTagStyle extends Model<GoodsTagStyle> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标签id
     */
    private Long tagId;

    /**
     * 样式类型 1列表 2商详 3购物车 4 checkout 5订单
     */
    private Integer styleType;

    /**
     * 标签展示类型1图片 2文字
     */
    private Integer tagType;

    /**
     * 是否显示 0否 1是
     */
    private Integer isShow;

    /**
     * 标签图片
     */
    private String img;

    /**
     * 文字
     */
    private String title;

    /**
     * 位置
     */
    private Integer position;

    /**
     *  跳转类型  0不跳转(默认) 1跳批发 2跳趋势(待定
     */
    private Integer jumpType;

    /**
     * 前面文字
     */
    private String preTitle;
    /**
     * 前面文字颜色
     */
    private String preColor;
    /**
     * 文字颜色
     */
    private String textColor;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
