package com.voghion.product.model.po;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class CountryFreight extends Model<CountryFreight> {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 国家，用二字码
     */
    private String receiverCountry;

    /**
     * 商品类型，0-普货，1-特货 默认普货
     */
    private Integer goodsType =0;

    /**
     * 阈值类型，0-标准，1-轻小件
     */
    private Integer thresholdType=0;

    /**
     * 最小重量
     */
    private BigDecimal minWeight;

    /**
     * 最大重量
     */
    private BigDecimal maxWeight;

    /**
     * item费
     */
    private BigDecimal baseAmount;

    /**
     * 每KG的费用
     */
    private BigDecimal weightParam;

    /**
     * 生效时间
     */
    private LocalDateTime validTime;

    /**
     * 生效时间
     */
    private LocalDateTime failureTime;

    /**
     * 是否删除，0未删除，1已删除
     */
    private Integer isDelete = 0;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 物流属性
     */
    private Integer logisticsProperty;

    /**
     * 物流场景 1通用 2尾货
     */
    private Integer logisticsScene;

    /**
     * 版本
     */
    private Integer version;


    public CountryFreight(Integer goodsType, BigDecimal minWeight, BigDecimal maxWeight, BigDecimal baseAmount, BigDecimal weightParam) {
        this.goodsType = goodsType;
        this.minWeight = minWeight;
        this.maxWeight = maxWeight;
        this.baseAmount = baseAmount;
        this.weightParam = weightParam;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
