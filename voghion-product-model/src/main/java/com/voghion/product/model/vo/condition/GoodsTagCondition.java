package com.voghion.product.model.vo.condition;

import com.voghion.product.model.vo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * GoodsTagCondition
 *
 * <AUTHOR>
 * @date 2022/12/09
 */
@ApiModel
@Data
public class GoodsTagCondition extends PageParam implements Serializable {

    @ApiModelProperty("标签id")
    private String tagIdListStr;

    /**
     * 标签名称
     */
    @ApiModelProperty("标签名称")
    private String tagName;

    /**
     * 是否显示
     */
    @ApiModelProperty("是否显示")
    private Integer isShow;

    /**
     * 标签展示类型
     */
    @ApiModelProperty("标签类型")
    private Integer type;

    /**
     * 展示国家集合
     */
    @ApiModelProperty("展示国家")
    private List<String> countryList;
    /**
     * 最后更新人
     */
    @ApiModelProperty("最后更新人")
    private String updateUser;

    private List<Integer> typeList;
}
