package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/14
 */
@Data
@ApiModel
public class ListingInfoConfigAddVO implements Serializable {
    private static final long serialVersionUID = 8938638949138407083L;

    @ApiModelProperty(value = "listing id")
    private String listingId;

    @ApiModelProperty(value = "标签,146、507、517")
    private List<Long> tagIds;

    @ApiModelProperty(value = "listing类型，0为非负向，1为负向")
    private Integer listingType;

    @ApiModelProperty(value = "转换几率, 0.00 - 1.00")
    private float replaceRate;

    @ApiModelProperty(value = "是否开启转单 0不开启 1开启")
    private Integer isReplace;

    @ApiModelProperty(value = "经营类型,0-代发商家 1-直发商家 2-香港代发 3-自营店铺 4-自营店铺scm")
    private List<Integer> deliveryTypes;
}
