package com.voghion.product.model.po;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FaMerchantsApply extends Model<FaMerchantsApply> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 验证电话
     */
    private String phone;

    /**
     * 区号
     */
    private String code;

    /**
     * 电邮
     */
    private String email;

    /**
     * 店铺名称
     */
    private String shopname;

    /**
     * 企业名称
     */
    private String legalPerson;

    /**
     * 企业组织机构代码
     */
    private String organCode;

    /**
     * 企业营业执照
     */
    private String licenseImg;

    /**
     * 法人姓名
     */
    private String legalname;

    /**
     * 法人身份证正面
     */
    private String cardPositiveImg;

    /**
     * 法人代表身份证背面
     */
    private String cardReverseImg;

    /**
     * 联系人
     */
    private String name;

    /**
     * 联系人电话
     */
    private String mobile;

    /**
     * 联系人电话区号
     */
    private String numcode;

    /**
     * 商品信息
     */
    private String goodmsg;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDel;

    /**
     * 创建事件
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态：0 待四要素验证1已开通2已取消,3 四要素通过;4四要素驳回
     */
    private Integer status;

    /**
     * 是否停用0停用 1正常
     */
    private Integer isDisable;

    /**
     * 商户webhook_url
     */
    private String webhookUrl;

    /**
     * 招商id
     */
    private Long invitationId;

    /**
     * 招商名称
     */
    @TableField("Invitation_name")
    private String invitationName;

    /**
     * 注册邀请码
     */
    private String inviteCode;

    /**
     * 实际邀请码
     */
    private String initCode;

    /**
     * 取消原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private LocalDateTime optTime;

    /**
     * 头像
     */
    private String shopIcon;

    /**
     * 发货类型 0.代发商家 1.直发商家 2.香港代发 3.自营店铺 4.scm自营店铺
     * @see com.voghion.product.model.enums.DeliveryTypeEnum
     */
    private Integer deliveryType;

    /**
     * 销售区域0无；1欧美；2东南亚
     */
    private Integer salesArea;

    /**
     * 是否直播0否1是
     */
    private Integer isLive;

    /**
     * 申请入驻码
     */
    private String applyCode;

    /**
     * 复审状态0未发起1复审中2复审通过3复审驳回
     */
    private Integer isRecheck;

    /**
     * 复审时间
     */
    private LocalDateTime recheckTime;

    /**
     * 四要素信息
     */
    private String fourElements;

    /**
     * 实际商品数量
     */
    private Integer realGoodsCount;

    /**
     * 商品上新上线数量
     */
    private Integer limitGoodsCount;

    /**
     * 是否精选店铺 0否1是
     */
    private Integer isGold;

    /**
     * 店铺销售状态 1正常 2休假 3禁售
     */
    private Integer isSale;

    /**
     * 绑定小二
     */
    private String principal;

    /**
     * 主营类目id
     */
    private Long categoryMainId;

    /**
     * 保证金状态:0无需缴纳10待缴纳20部分缴纳30已缴纳
     */
    private Integer depositStatus;


    /**
     * 经营类型1一般贸易商2品牌授权商3普通工厂4世界500强代工厂
     */
    private Integer managementType;

    /**
     * 同盾白名单：0-否 1-是
     * */
    private Integer tongDunWhiteList;

    /**
     * 微信openId
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String wxId;

    /**
     * 店铺类型:0普通 1供应商 2全托管 3全托管隐藏店铺 4JIT，5JIT隐藏店铺
     * (voghion:0,3,5     scm:1,2,4)
     */
    private Integer shopType;

    /**
     * 限流比例 0:不限流
     */
    private Integer limitingGear;

//    /**
//     * 店铺标签
//     */
//    private String shopTags;
//    /**
//     * 商家服务公众号微信id
//     */
//    private String swxId;
    /**
     * 隐藏店铺
     */
    private Long hiddenShopId;
    /**
     * 负责买手
     */
    private String buyer;

    private Integer returnMethod;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
