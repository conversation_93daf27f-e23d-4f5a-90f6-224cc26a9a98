package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class FilterLabelCategoryDetailVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "目录名")
    private String name;

    @ApiModelProperty(value = "父类id")
    private Long parentId;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "子类目录")
    private List<FilterLabelCategoryDetailVo> children;
}
