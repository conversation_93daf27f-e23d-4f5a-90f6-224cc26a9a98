package com.voghion.product.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class InvalidPropertyNameExportVo implements Serializable {
    private static final long serialVersionUID = -4462304296014565346L;

    @ExcelProperty(value="商品id",index = 0)
    private Long goodsId;

    @ExcelProperty(value = "店铺id", index = 1)
    private Long shopId;

    @ExcelProperty(value = "是否删除", index = 2)
    private Integer isDel;


}
