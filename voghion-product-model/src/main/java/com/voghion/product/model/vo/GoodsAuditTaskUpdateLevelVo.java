package com.voghion.product.model.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class GoodsAuditTaskUpdateLevelVo implements Serializable {
    private static final long serialVersionUID = 337776341213170909L;

    @ApiModelProperty(value = "商品id")
    private Long goodsId;

    @ApiModelProperty(value = "商品id集合")
    private List<Long> goodsIds;

    @ApiModelProperty(value = "业务类型 1合规打标 2投放自动化 3商家申诉", required = true)
    private Integer type;

    @ApiModelProperty(value = "原始任务审核等级(0-3，越小越高)", required = true)
    private Integer originLevel;

    @ApiModelProperty(value = "修改后审核等级(0-3，越小越高)", required = true)
    private Integer newLevel;

}
