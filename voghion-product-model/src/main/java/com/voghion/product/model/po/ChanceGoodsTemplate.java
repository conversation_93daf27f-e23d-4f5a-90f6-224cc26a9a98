package com.voghion.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 机会商品
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-24
 */
@Data
public class ChanceGoodsTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 来源类型 1直接创建，2克隆，3速卖通爬虫，4shein爬虫 5 temu美站  6 temu德站
     */
    private Integer sourceType;

    /**
     * 来源详情
     */
    private String sourceDetail;

    /**
     * 模板商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 最低价
     */
    private BigDecimal minPrice;

    /**
     * 最高价
     */
    private BigDecimal maxPrice;

    /**
     * 热度
     */
    private Integer hot;

    /**
     * 近七日销量
     */
    private Integer sevenSales;

    /**
     * 状态 0草稿 1开放 2自动关闭 3人工关闭
     */
    private Integer status;

    /**
     * 生成商品数
     */
    private Integer generateGoodsCount;

    private Integer isDel;

    private Date createTime;

    private Date updateTime;

    private String createUser;

    private String updateUser;

    private String sourceProId;

}
