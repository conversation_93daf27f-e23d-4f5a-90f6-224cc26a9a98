package com.voghion.product.model.vo.condition;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @date: 2022/12/16 上午11:44
 * @author: jashley
 */
@Data
@ApiModel
public class ChanceGoodsTemplateQueryForSignUpCondition implements Serializable {
    private static final long serialVersionUID = -1561814747725053318L;

    @ApiModelProperty("模板id")
    private Long templateId;

    @ApiModelProperty("类目id")
    private Long categoryId;

    @ApiModelProperty("建议价格最低价")
    private BigDecimal minPrice;

    @ApiModelProperty("建议价格最高价")
    private BigDecimal maxPrice;

    @ApiModelProperty("来源类型 99 其他（1直接创建或者2克隆），3速卖通爬虫，4shein爬虫 5 temu美站  6 temu德站")
    private Integer sourceType;

    @ApiModelProperty("最低销量")
    private Integer minSales;

    @ApiModelProperty("最高销量")
    private Integer maxSales;

    @ApiModelProperty("最低热度")
    private Integer minHot;

    @ApiModelProperty("最高热度")
    private Integer maxHot;

    @ApiModelProperty("开始创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startCreateTime;

    @ApiModelProperty("结束创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endCreateTime;

    @ApiModelProperty("排序规则 1、7日销量降序 2、跟卖热度降序 3、创建时间降序")
    private Integer sortType;

    private Integer pageNow = 1;
    private Integer pageSize = 20;
}
