package com.voghion.product.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.voghion.product.model.conveter.DateConverter;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel
public class FilterWordsRecordExportVo implements Serializable {
    private static final long serialVersionUID = -184919510312176693L;

    @ExcelProperty("流水号")
    private Long serialNo;

    @ExcelProperty("商品id")
    private Long goodsId;

    @ExcelProperty("商品名称")
    private String goodsName;

    @ExcelProperty("主图")
    private String mainImage;

    @ExcelProperty("店铺id")
    private Long shopId;

    @ExcelProperty("店铺名称")
    private String shopName;

    @ExcelProperty("绑定小二")
    private String principal;

    @ExcelProperty("类目完整路径")
    private String categoryTreeStr;

    @ExcelProperty("拦截词id")
    private Long filterId;

    @ExcelProperty("主词名称")
    private String mainFilterName;

    @ExcelProperty("副词名称")
    private String minorFilterName;

    @ExcelProperty("一级标签名称")
    private String firstFilterLabelName;

    @ExcelProperty("二级标签名称")
    private String secondFilterLabelName;

    @ExcelProperty("风险等级")
    private String riskLevel;

    @ExcelProperty("商品当前状态")
    private String isShow;

    @ExcelProperty("匹配后标签tag配置")
    private String tagIds;

    @ExcelProperty("匹配命中后效果")
    private String matchResult;

    @ExcelProperty(value = "命中记录时间", converter = DateConverter.class)
    private Date checkTime;

    @ExcelProperty("审核人")
    private String checkUser;

    @ExcelProperty("复审结果")
    private String recheckResult;

    @ExcelProperty("复审关联流水号")
    private Long recheckRelNo;

    @ExcelProperty(value = "复审时间", converter = DateConverter.class)
    private Date recheckTime;

    @ExcelProperty("复审人")
    private String recheckUser;

}
