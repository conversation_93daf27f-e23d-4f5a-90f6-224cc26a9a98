package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @date: 2022/12/16 上午11:51
 * @author: jashley
 */
@Data
@ApiModel
public class ChanceGoodsTemplateVo implements Serializable {
    private static final long serialVersionUID = -2682878501963900468L;

    @ApiModelProperty("模板id")
    private Long id;

    @ApiModelProperty("来源类型 1直接创建，2克隆，3速卖通爬虫，4shein爬虫")
    private Integer sourceType;

    @ApiModelProperty("来源详情")
    private String sourceDetail;

    @ApiModelProperty("商品主图")
    private String mainImage;

    @ApiModelProperty("商品id")
    private Long goodsId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("类目id")
    private Long categoryId;

    @ApiModelProperty("类目路径")
    private String categoryPath;

    @ApiModelProperty("热度")
    private Integer hot;

    @ApiModelProperty("销量")
    private Integer sales;

    @ApiModelProperty("最低价格")
    private BigDecimal minPrice;

    @ApiModelProperty("最高价格")
    private BigDecimal maxPrice;

    @ApiModelProperty("状态 0草稿 1开放 2自动关闭 3人工关闭")
    private Integer status;

    @ApiModelProperty("生成商品数")
    private Integer generateGoodsCount;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("最后更新人")
    private String updateUser;

    @ApiModelProperty("近7日销量")
    private Integer sevenSales;
}
