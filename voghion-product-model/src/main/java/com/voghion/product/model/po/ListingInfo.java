package com.voghion.product.model.po;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * listing模板信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ListingInfo extends Model<ListingInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * list模板商品id
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 最低价
     */
    private BigDecimal minPrice;

    /**
     * 最高价
     */
    private BigDecimal maxPrice;

    /**
     * 算法状态
     */
    private Integer algorithmStatus;

    /**
     * 发布状态 0草稿中 1已发布 -1关闭中
     */
    private Integer status;

    /**
     * 跟卖热度
     */
    private Integer hot;

    /**
     * 近7日销量
     */
    private Integer sevenSale;

    /**
     * 源头商品id
     */
    private Long sourceGoodsId;

    /**
     * 跟卖商品数量
     */
    private Integer followCount;

//    private String version;

    /**
     * 删除状态 1已删除 0未删除
     */
    private Integer isDel;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 最后更新人
     */
    private String updateUser;

    /**
     * 基准价
     */
    private BigDecimal basePrice;

    /**
     * 店铺标签 146, 507, 514
     */
    private Integer shopTag;

    /**
     * 可报名商家 0全部商家 1部分商家
     */
    private Integer shopWhiteListType;

    /**
     * 店铺经营类型 0.代发商家 1.直发商家 2.香港代发 3.自营店铺 4.scm自营店铺
     */
    private Integer deliveryType;

    /**
     * listing 类型 0 为非负向 1为负向
     */
    private Integer listingType;

    /**
     * 转换几率
     */
    private float replaceRate;

    /**
     * 是否开启转单 0 不开启 1开启
     */
    private Integer isReplace;

    /**
     * 基准vat费率(需除以100)
     */
    private BigDecimal baseVatRate;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
