package com.voghion.product.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.voghion.product.model.conveter.DateConverter;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description:
 * @date: 2022/12/16 上午11:51
 * @author: jashley
 */
@Data
@ApiModel
public class ChanceGoodsTemplateExportVo implements Serializable {
    private static final long serialVersionUID = -2682878501963900468L;

    @ExcelProperty(value = "模板id", index = 0)
    private Long id;

    private Integer sourceType;

    @ExcelProperty(value = "来源类型", index = 1)
    private String sourceTypeStr;

    @ExcelProperty(value = "来源详情", index = 2)
    private String sourceDetail;

    @ExcelProperty(value = "图片", index = 3)
    private String mainImage;

    private Long goodsId;

    @ExcelProperty(value = "商品名称", index = 4)
    private String goodsName;

    private Long categoryId;

    @ExcelProperty(value = "类目", index = 5)
    private String categoryPath;

    @ExcelProperty(value = "热度", index = 6)
    private Integer hot;

    private Integer status;

    @ExcelProperty(value = "状态", index = 7)
    private String statusStr;

    @ExcelProperty(value = "生成商品数", index = 8)
    private Integer generateGoodsCount;

    @ExcelProperty(value = "创建时间", index = 9, converter = DateConverter.class)
    private Date createTime;

    @ExcelProperty(value = "创建人", index = 10)
    private String createUser;

    @ExcelProperty(value = "更新时间", index = 11, converter = DateConverter.class)
    private Date updateTime;

    @ExcelProperty(value = "最后更新人", index = 12)
    private String updateUser;
}
