package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel
public class FilterWordsConfigHistoryVo implements Serializable {
    private static final long serialVersionUID = -1849195101215276693L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("修改内容类型")
    private Integer updateType;

    @ApiModelProperty("修改内容")
    private String updateText;

    @ApiModelProperty("修改前")
    private String updateBefore;

    @ApiModelProperty("修改后")
    private String updateAfter;

    @ApiModelProperty("修改原因")
    private String updateReason;

    @ApiModelProperty("修改人")
    private String createUser;

    @ApiModelProperty("更新时间")
    private Date createTime;

}
