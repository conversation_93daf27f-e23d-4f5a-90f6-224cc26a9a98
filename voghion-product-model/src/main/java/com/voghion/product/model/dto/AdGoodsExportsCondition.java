package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AdGoodsExportsCondition implements Serializable {


    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "国家列表")
    private List<String> countryCodes;

    @ApiModelProperty(value = "商品标签")
    private Long goodsTag;

}
