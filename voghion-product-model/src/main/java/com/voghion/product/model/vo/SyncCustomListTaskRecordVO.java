package com.voghion.product.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.voghion.product.model.conveter.LocalDateTimeDeSerializerConverter;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
@Data
public class SyncCustomListTaskRecordVO extends PageParam{

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1 - 自动  2 - 手动
     */
    private Integer triggerType;

    /**
     * 虚拟列表id
     */
    private Long customId;

    /**
     * 1 - 全量替换  2 - 只添加
     */
    private Integer executeType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    private LocalDateTime createStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    private LocalDateTime createEndTime;

    /**
     * 任务配置id
     */
    private Long taskId;


    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    private LocalDateTime executeStartTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    private LocalDateTime executeEndTime;

    /**
     * 执行状态  1 - 成功  0 - 失败  2 - 执行中
     */
    private Integer executeStatus;

    /**
     * 执行结果描述
     */
    private String executeDesc;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeSerializerConverter.class)
    private LocalDateTime createTime;

}
