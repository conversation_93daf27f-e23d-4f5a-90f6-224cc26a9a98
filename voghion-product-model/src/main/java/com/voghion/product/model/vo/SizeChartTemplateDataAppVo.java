package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SizeChartTemplateDataAppVo implements Serializable {
    private static final long serialVersionUID = -287648608642135020L;

    @ApiModelProperty(value = "区域集合")
    private List<SizeTemplateVO> countryData;

    @ApiModelProperty(value = "尺码集合")
    private List<SizeTemplateVO> propData;

    @Data
    public class SizeTemplateVO {
        private String key;

        private List<String> value;
    }
}
