package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-08-26)
 */
@Data
public class CustomListQueryVO extends PageParam implements Serializable {
    private static final long serialVersionUID = 2729168002755101668L;
    /*
     * 自定义列表ID
     */
    @ApiModelProperty(value = "虚拟商品列表id")
    private Long customId;

    @ApiModelProperty(value = "虚拟商品列表id")
    private String customIdStr;

    @ApiModelProperty(value = "类目id")
    private Long categoryId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")

    private String createUser;

    /**
     * 最后更新人
     */
    @ApiModelProperty(value = "最后更新人")
    private String updateUser;

    @ApiModelProperty(value = "商品id字符串")
    private String goodsIdListStr;

    @ApiModelProperty(value = "商品id列表")
    private List<Long> goodsIds;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "类型 1-人工配置 2-自动配置 3 -offer 配置 4-bestSeling 配置")
    private Integer type;

    @ApiModelProperty(value = "头图")
    private String imgUrl;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "状态  0-下架 1-上架")
    private String isShow;

    @ApiModelProperty(value = "状态  0-下架 1-上架")
    private List<String> isShowList;

    @ApiModelProperty(value = "是否负向")
    private Integer isNegative;

    @ApiModelProperty(value = "自定义商品列表ID集合")
    private List<Long> customListIds;

    @ApiModelProperty(value = "轮播图地址")
    private String cycleImg;

    @ApiModelProperty(value = "offers图链接")
    private String detailImg;

    @ApiModelProperty(value = "标签id")
    private String tagId;

    @ApiModelProperty(value = "国家")
    private String countryCode;

    @ApiModelProperty(value = "国家列表")
    private List<String> countryCodeList;

//    @ApiModelProperty(value = "部门 0 运营 1 网红 2 投放 投放改为 2 活动  ")
    @ApiModelProperty(value = "0活动运营 1 网红 2 投放  3类目运营")
    private Integer departmentType;

    @ApiModelProperty(value = "置顶类型 0不置顶  1 插位置顶  2 全部置顶 3置顶别的虚拟列表")
    private Integer placementType;

    @ApiModelProperty(value = "被置顶的虚拟列表id")
    private Long insertCustomId;

    @ApiModelProperty(value = "是否开启算法 0 不开启 1 开启")
    private Integer useAlgorithm;

    private  Integer insertQuantity;

    private  Integer listingId;

    @ApiModelProperty(value = "部门列表")
    private List<Integer> departmentTypeList;

}
