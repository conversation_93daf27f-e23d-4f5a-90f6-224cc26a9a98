package com.voghion.product.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @date: 2022/12/16 上午11:51
 * @author: jashley
 */
@Data
@ApiModel
public class ChanceGoodsForAuditExportVo implements Serializable {
    private static final long serialVersionUID = -2682878501963900468L;

    private Long id;

    @ExcelProperty(value = "机会商品模板id", index = 0)
    private Long templateId;

    @ExcelProperty(value = "模板商品名称", index = 1)
    private String templateGoodsName;

    @ExcelProperty(value = "模板商品主图", index = 2)
    private String templateMainImage;

    private Long shopId;

    @ExcelProperty(value = "商家名称", index = 3)
    private String shopName;

    @ExcelProperty(value = "商品id", index = 4)
    private Long goodsId;

    @ExcelProperty(value = "图片", index = 5)
    private String mainImage;

    @ExcelProperty(value = "商品名称", index = 6)
    private String name;

    private Long categoryId;

    @ExcelProperty(value = "类目", index = 7)
    private String categoryPath;

    private BigDecimal minPrice;

    private BigDecimal maxPrice;

    @ExcelProperty(value = "价格", index = 8)
    private String price;

//    private List<GoodsFreightResult> goodsFreightList;

    @ExcelProperty(value = "国家运费", index = 9)
    private String existGoodsFreight;

    @ExcelProperty(value = "均价涨幅", index = 10)
    private String avgIncreaseRate;

    @ExcelProperty(value = "30天销量", index = 11)
    private Integer thirtySales;

    @ExcelProperty(value = "上下架", index = 12)
    private Integer isShow;

    private Integer auditStatus;

    @ExcelProperty(value = "审核状态", index = 13)
    private String auditStatusStr;

    @ExcelProperty(value = "审核说明", index = 14)
    private String auditRemark;

    @ExcelProperty(value = "最新报名时间", index = 15)
    private Date createTime;

    @ExcelProperty(value = "最新审核时间", index = 16)
    private Date auditTime;

    @ExcelProperty(value = "最新审核人", index = 17)
    private String auditUser;

}
