package com.voghion.product.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商户品牌库
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MerchantsBrandStore extends Model<MerchantsBrandStore> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 类型：1国际品牌，2国内品牌
     */
    private Integer type;

    /**
     * 子品牌
     */
    private String childBrand;

    /**
     * 品牌logo图片url
     */
    private String logoImgUrl;

    /**
     * 描述
     */
    private String description;

    /**
     * 所属行业
     */
    private String trade;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 是否删除：0否，1是
     */
    private Integer isDel;

    /**
     * 状态：1有效，2无效
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 父级品牌id
     */
    private Long parentId;

    /**
     * 标签id
     */
    private Long tagId;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
