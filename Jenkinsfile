// 应用名称
def application='voghion-product'

// 主模块
def mainModule='voghion-product-admin'

// 各环境JVM参数
def jvmParamTest="-Xms256m -Xmx256m -Xmn128m -Xss256k"
def jvmParamGray="-Xms1024m -Xmx1024m -Xmn512m -Xss256k -XX:CMSInitiatingOccupancyFraction=75 -XX:+UseCMSInitiatingOccupancyOnly -XX:-OmitStackTraceInFastThrow -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/logs/voghion-product/ -XX:+UseGCLogFileRotation -XX:+PrintGCDetails -XX:+PrintGCDateStamps -verbose:gc -Xloggc:/data/logs/voghion-product/gc/gc-%t.log -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=50M"
def jvmParamProd="-Xms2048m -Xmx2048m -Xmn512m -Xss512k"
def jvmParamTask="-Xms2048m -Xmx2048m -Xmn512m -Xss512k -XX:CMSInitiatingOccupancyFraction=75 -XX:+UseCMSInitiatingOccupancyOnly -XX:-OmitStackTraceInFastThrow -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/logs/voghion-product/ -XX:+UseGCLogFileRotation -XX:+PrintGCDetails -XX:+PrintGCDateStamps -verbose:gc -Xloggc:/data/logs/voghion-product/gc/gc-%t.log -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=50M"
def jvmParamMarketing="-Xms3072m -Xmx3072m -Xmn1024m -Xss256k -XX:CMSInitiatingOccupancyFraction=75 -XX:+UseCMSInitiatingOccupancyOnly -XX:-OmitStackTraceInFastThrow -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/data/logs/voghion-product/ -XX:+UseGCLogFileRotation -XX:+PrintGCDetails -XX:+PrintGCDateStamps -verbose:gc -Xloggc:/data/logs/voghion-product/gc/gc-%t.log -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=50M"

// gitlab url
def gitUrl = 'http://dev-git.voghion.com/voghion-parent/voghion-product.git'

// 各个环境的IP地址
def ipsTest = ['voghion-test-01::gcp-test-ecs-pub.chatgptvips.com']
def ipsGray = ['voghion-gray::*********']
def ipsProd = ['voghion-01::*********','voghion-02::*********','voghion-105::**********','voghion-10::**********']
def ipsTask = ['task-02::*********','task-03::*********']
def ipsMarketing = ['marketing-02::*********','voghion-07::*********']

// 健康检测地址
def healthUrl = 'http://localhost:8481/health/check'

pipeline {
  agent any
  parameters {
    choice(name: 'env', choices: ['test', 'gray', 'prod', 'task','marketing'], description: 'Environment')
    choice(name: 'operation', choices: ['deploy', 'restart'], description: 'Operation')
    string(name: 'customIps', defaultValue: '', description: 'Custom Ips(eg name1::ip1,name2::ip2)')
    string(name: 'version', defaultValue: '', description: 'Restart Version')
  }
  environment {
    notifyUrl="https://message.voghion.com/jenkins/message"
    notifyHeader="Content-Type: application/json"
    deployRootDir="/data/deploy_service"
    buildUser = ""
    datetime = ""
    isProdMode = false
  }
  stages {
    stage('Checkout') {
      steps {
        echo "start Checkout"
        echo "restart ips: ${params.customIps}"
        script {
          echo "environment: ${params.env}"
          sh "printenv"
          wrap([$class: 'BuildUser']) {
          buildUser = env.BUILD_USER
          }
          echo "build user: ${buildUser}"

          isProdMode = "${params.env}"!="test" && "${params.env}"!="gray"

          if ("${params.operation}"=="deploy") {
            datetime = sh (script: 'date +%Y%m%d%H%M%S', returnStdout: true).trim()
            echo "deploy ${datetime}"

            if (isProdMode) {
              if (!"${BRANCH_NAME}".startsWith("release/") && "${BRANCH_NAME}" != "master") {
                error "prod not with release or master branch: ${BRANCH_NAME}"
              }
            }
          } else if ("${params.operation}"=="restart") {
            if ("${params.version}" == '') {
              error "restart need historical version number."
            }

            datetime = "${params.version}"
            echo "restart ${datetime}"
          }

          def url = "https://jenkins.voghion.com/blue/organizations/jenkins/" + "${application}" + "/detail/"+"${JOB_BASE_NAME}"+"/"+"${BUILD_ID}"+"/pipeline/"
          def body = "{\"application\":\"${application}\",\"env\":\"${params.env}\",\"status\":\"start\",\"buildUser\":\"${buildUser}\",\"branch\":\"${BRANCH_NAME}\",\"datetime\":\"[${datetime}](${url})\",\"operation\":\"${params.operation}\"}"
          sh "curl --location '${notifyUrl}' --header '${notifyHeader}' --data '${body}'"

          if ("${params.operation}"=="deploy") {
            checkout([$class: 'GitSCM', branches: [[name: "${BRANCH_NAME}"]], extensions: [], userRemoteConfigs: [[credentialsId: '0c2b20d2-36e4-420f-b6a9-1208de294be2', url: "${gitUrl}"]]])
          }
          echo "datetime: ${datetime}"
        }
      }
    }

    stage('Build') {
      steps {
        echo "start Build"
        script {
          // 默认构建, 成功退出
          if ("${buildUser}" == 'Branch Indexing') {
            currentBuild.result = 'SUCCESS'
            return
          }

          if ("${params.operation}"=="deploy") {
            sh "mvn clean package -U -Dmaven.test.skip=true -e"
          } else if ("${params.operation}"=="restart") {
            echo "restart xxx"
          }
        }
      }
    }

    stage('Deploy') {
      steps {
        echo "start Deploy"
        script {
          // 默认构建, 成功退出
          if ("${buildUser}" == 'Branch Indexing') {
            currentBuild.result = 'SUCCESS'
            return
          }
          def ips = ipsTest
          def jvmParam = jvmParamTest
          if ("${params.env}" == 'gray') {
            ips = ipsGray
            jvmParam = jvmParamGray
          } else if ("${params.env}" == 'prod') {
            ips = ipsProd
            jvmParam = jvmParamProd
          } else if ("${params.env}" == 'task') {
            ips = ipsTask
            jvmParam = jvmParamTask
          } else if ("${params.env}" == 'marketing') {
            ips = ipsMarketing
            jvmParam = jvmParamMarketing
          }

          if ("${params.customIps}" != '') {
            ips = "${params.customIps}".split(",")
          }

          for (ip in ips) {
            def split = "${ip}".split("::")
            def configName = "${split[0]}"

            def startJvmParam = jvmParam + " " + "-Dvoghion.startup.ip=" + split[1]
            def startCommand = "sh ${deployRootDir}/app.sh restart ${deployRootDir} ${mainModule} '${deployRootDir}/target/${mainModule}/target/${mainModule}-${datetime}.jar' '${startJvmParam}' '${params.env}'"
            echo startCommand

            if ("${params.operation}"=="deploy") {
              stage ("Deploy: ${configName}") {
                if (isProdMode) {
                  input(message: 'continue to deploy?', id: 'continue')
                }

                echo "start deploy ${configName}"
                sshPublisher(publishers: [sshPublisherDesc(configName: "${configName}", transfers: [sshTransfer(cleanRemote: false, excludes: '', execCommand: """
                mv ${deployRootDir}/target/${mainModule}/target/${mainModule}.jar ${deployRootDir}/target/${mainModule}/target/${mainModule}-${datetime}.jar
                sh ${deployRootDir}/app.sh restart ${deployRootDir} ${mainModule} '${deployRootDir}/target/${mainModule}/target/${mainModule}-${datetime}.jar' '${startJvmParam}' '${params.env}' > ${deployRootDir}/logs/deploy-${application}.log
                echo "${startCommand}" > ${deployRootDir}/shell/${application}-restart.sh
                """, execTimeout: 120000, flatten: false, makeEmptyDirs: false, noDefaultExcludes: false, patternSeparator: '[, ]+', remoteDirectory: 'target', remoteDirectorySDF: false, removePrefix: '', sourceFiles: "${mainModule}/target/${mainModule}.jar")], usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: false)])
              }
            } else if ("${params.operation}"=="restart") {
              stage ("Restart: ${configName}") {
                input(message: 'continue to restart?', id: 'continue')
                echo "start restart ${configName}"
                sshPublisher(publishers: [sshPublisherDesc(configName: "${configName}", transfers: [sshTransfer(cleanRemote: false, excludes: '', execCommand: """
                sh ${deployRootDir}/app.sh restart ${deployRootDir} ${mainModule} '${deployRootDir}/target/${mainModule}/target/${mainModule}-${datetime}.jar' '${startJvmParam}' '${params.env}' > ${deployRootDir}/logs/deploy-${application}.log
                """, execTimeout: 120000, flatten: false, makeEmptyDirs: false, noDefaultExcludes: false, patternSeparator: '[, ]+', remoteDirectory: 'target', remoteDirectorySDF: false, removePrefix: '', sourceFiles: "${mainModule}/target/xxx.jar")], usePromotionTimestamp: false, useWorkspaceInPromotion: false, verbose: false)])
              }
            }

            stage ("Health check: ${configName}") {
              echo "start health check"
              sleep 60
              script {
                def host = "${split[1]}"
                for (i=1;i<=8;i++) {
                  echo "health check ${ip}, time ${i}"
                  try {
                    def url =  "$healthUrl".replaceAll("localhost", "${host}")
                    def result = """${sh(
                      script: "curl $url",
                      returnStdout: true
                    )}"""
                    echo "health check result: ${result}"
                    if (result == "success") {
                      echo "health check ${ip} success"
                      break
                    }
                  }catch (e) {
                    echo "health check ${ip} exception: " + e.getMessage()
                  }
                  if ("${i}" == '8') {
                    error "health check ${ip} failed"
                  }
                  sleep 20
                }
              }
            }
          }
        }
      }
    }
  }

  post {
    aborted {
      echo 'pipeline aborted'
      script {
        def url = "https://jenkins.voghion.com/blue/organizations/jenkins/" + "${application}" + "/detail/"+"${JOB_BASE_NAME}"+"/"+"${BUILD_ID}"+"/pipeline/"
        def body = "{\"application\":\"${application}\",\"env\":\"${params.env}\",\"status\":\"aborted\",\"buildUser\":\"${buildUser}\",\"branch\":\"${BRANCH_NAME}\",\"datetime\":\"[${datetime}](${url})\",\"operation\":\"${params.operation}\"}"
        sh "curl --location '${notifyUrl}' --header '${notifyHeader}' --data '${body}'"
      }
    }
    success {
      echo 'pipeline success'
      script {
        def url = "https://jenkins.voghion.com/blue/organizations/jenkins/" + "${application}" + "/detail/"+"${JOB_BASE_NAME}"+"/"+"${BUILD_ID}"+"/pipeline/"
        def body = "{\"application\":\"${application}\",\"env\":\"${params.env}\",\"status\":\"success\",\"buildUser\":\"${buildUser}\",\"branch\":\"${BRANCH_NAME}\",\"datetime\":\"[${datetime}](${url})\",\"operation\":\"${params.operation}\"}"
        sh "curl --location '${notifyUrl}' --header '${notifyHeader}' --data '${body}'"
      }
    }
    failure {
      echo 'pipeline failure'
      script {
        def url = "https://jenkins.voghion.com/blue/organizations/jenkins/" + "${application}" + "/detail/"+"${JOB_BASE_NAME}"+"/"+"${BUILD_ID}"+"/pipeline/"
        def body = "{\"application\":\"${application}\",\"env\":\"${params.env}\",\"status\":\"failed\",\"buildUser\":\"${buildUser}\",\"branch\":\"${BRANCH_NAME}\",\"datetime\":\"[${datetime}](${url})\",\"operation\":\"${params.operation}\"}"
        sh "curl --location '${notifyUrl}' --header '${notifyHeader}' --data '${body}'"
      }
    }
  }
}