package com.voghion.product.enums;

import org.apache.commons.lang.StringUtils;

public enum CountryManualRelationEnums {

    EN("en", 1), FR("fr", 2), DE("de", 3), IT("it", 4), ES("es", 5),
    ;
    private String countryKey;
    private int code;

    CountryManualRelationEnums(String countryKey, int code) {
        this.countryKey = countryKey;
        this.code = code;
    }

    public String getCountryKey() {
        return countryKey;
    }

    public void setCountryKey(String countryKey) {
        this.countryKey = countryKey;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public static boolean contains(String test) {
        for (CountryManualRelationEnums c : CountryManualRelationEnums.values()) {
            if (c.getCountryKey().equals(test)) {
                return true;
            }
        }

        return false;
    }

    public static Integer get(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        for (CountryManualRelationEnums c : CountryManualRelationEnums.values()) {
            if (c.getCountryKey().equals(key)) {
                return c.code;
            }
        }
        return null;
    }

}
