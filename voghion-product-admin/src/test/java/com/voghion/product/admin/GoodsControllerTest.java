package com.voghion.product.admin;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.colorlight.base.model.enums.CountryEnums;
import com.colorlight.base.utils.DateUtil;
import com.voghion.product.admin.controller.GoodsController;
import com.voghion.product.api.dto.GoodsTagInfoDTO;
import com.voghion.product.api.dto.QueryGoodsVO;
import com.voghion.product.bq.OutDbGoodsEveryDayEsService;
import com.voghion.product.bq.vo.OutDbGoodsEveryDayQueryParam;
import com.voghion.product.bq.vo.OutDbGoodsEveryDayVO;
import com.voghion.product.core.*;
import com.voghion.product.api.service.GoodsEsRemoteService;
import com.voghion.product.core.impl.GoodsCoreServiceImpl;
import com.voghion.product.core.impl.SensitiveWordsCoreServiceImpl;
import com.voghion.product.model.dto.GoodsDTO;
import com.voghion.product.model.dto.GoodsInfoDTO;
import com.voghion.product.model.dto.GoodsItemDTO;
import com.voghion.product.model.dto.PackageSizeDTO;
import com.voghion.product.model.enums.EsEnums;
import com.voghion.product.model.po.GoodsItem;
import com.voghion.product.model.vo.*;
import com.voghion.product.remote.impl.GoodsEsRemoteServiceImpl;
import com.voghion.product.service.GoodsFreightService;
import com.voghion.product.service.GoodsItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.core.CountRequest;
import org.opensearch.client.core.CountResponse;
import org.opensearch.index.query.InnerHitBuilder;
import org.opensearch.index.query.NestedQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.index.query.TermQueryBuilder;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class GoodsControllerTest {

    @Resource
    GoodsController goodsController;
    @Resource
    GoodsItemService goodsItemService;
    @Resource
    GoodsFreightService goodsFreightService;
    @Resource
    GoodsFreightCoreService goodsFreightCoreService;

    @Autowired
    private OutDbGoodsEveryDayEsService everyDayEsService;
    @Resource
    NewAddGoodsCoreService newAddGoodsCoreService;
//    @Resource(name = "esClient")
//    private TransportClient esClient;

    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;

    @Test
    public void testCountByEs() throws IOException {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        TermQueryBuilder builder = QueryBuilders.termQuery("shopId", 12);
        sourceBuilder.query(builder);
        sourceBuilder.from(10);
        sourceBuilder.size(20);
        CountRequest countRequest = new CountRequest();
        countRequest.indices(EsEnums.GOODS_ES.getIndex());
        countRequest.source(sourceBuilder);
        CountResponse count = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
        System.out.println("count:" + count.getCount());

    }

    @Test
    public void saveGoodItem() {
        List<GoodsItem> goodsItems = com.google.common.collect.Lists.newArrayList();
        for (int i = 0; i < 100; i++) {
            GoodsItem goodsItem = new GoodsItem();
            goodsItem.setGoodsId(Long.valueOf(i + 1));
            goodsItem.setSkuId(Long.valueOf(i + 1));
            goodsItem.setPrice(new BigDecimal(8.34));
            goodsItem.setCreateTime(LocalDateTime.now());
            goodsItems.add(goodsItem);
        }
        goodsItemService.saveBatch(goodsItems);
    }


    @Test
    public void saveFreight() {
        ProductInfoInput productInfoInput = new ProductInfoInput();
        productInfoInput.setId(1l);
        Random rm = new Random();
        GoodsFreightVO goodsFreight = new GoodsFreightVO();
        goodsFreight.setGoodsId(1l);
        goodsFreight.setPrice(new BigDecimal(32));
        goodsFreight.setCurrentFreight(new BigDecimal(rm.nextInt(10) + 1));
//        goodsFreight.setCode(CountryEnums.US.getCode());
        //productInfoInput.setFreightList(Collections.singletonList(goodsFreight));
        goodsFreightCoreService.saveOrUpdateFreight(productInfoInput);
    }

    @Test
    public void queryDetailBySkuId() {

        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("itemList.skuId", "10730");
        InnerHitBuilder innerHitBuilder = new InnerHitBuilder();
        innerHitBuilder.setName("inner_hits");
        NestedQueryBuilder queryBuilder = new NestedQueryBuilder("itemList", termQueryBuilder, ScoreMode.None);
        queryBuilder.innerHit(innerHitBuilder);
//        SearchResponse searchResponse = esClient.prepareSearch(EsEnums.GOODS_ES.getIndex()).
//                setTypes(EsEnums.GOODS_ES.getType()).setQuery(queryBuilder).get();
//        SearchHit hit = searchResponse.getHits().getAt(0).getInnerHits().get("inner_hits").getAt(0);
//        String jsonStr = JSON.toJSONString(hit.getSourceAsMap());
//        JSONArray jsonArray = JSON.parseObject(jsonStr).getJSONArray("countryDelivery");
//        System.out.println(jsonArray);
    }

    @Test
    public void goodsByPageList() {
        QueryGoodsVO queryGoodsVO = new QueryGoodsVO();

        queryGoodsVO.setName("test");
        Result<PageView<GoodsInfoVO>> pageViewResult = goodsController.goodsByPageList(queryGoodsVO);
        System.out.println(pageViewResult.getData().getRecords());
    }

    @Test
    public void shopGoodsByPageList() {
        QueryGoodsVO queryGoodsVO = new QueryGoodsVO();

//        queryGoodsVO.setName("test");
        queryGoodsVO.setShopId(10L);
        Result<PageView<ShopGoodsInfoVO>> pageViewResult = goodsController.shopGoodsByPageList(queryGoodsVO);

        log.info(JSON.toJSONString(pageViewResult));
    }

    @Test
    public void addGoodsInfo() {
        //参数未全面暂时不可用
        ProductInfoInput productInfoInput = new ProductInfoInput();
        Result<GoodsDetailInfoVO> goodsDetailInfoVOResult = goodsController.queryGoodsById(1L, null);
        BeanUtils.copyProperties(goodsDetailInfoVOResult.getData(), productInfoInput);
        List<GoodsItem> goodsItems = goodsItemService.queryGoodsItemByGoodsId(1l);
        List<GoodsItemDTO> skuInfo = new ArrayList<>(goodsItems.size());
        long i = 1;
        for (GoodsItem goodsItem : goodsItems) {
            GoodsItemDTO goodsItemDTO = new GoodsItemDTO();
            BeanUtils.copyProperties(goodsItem, goodsItemDTO);
            goodsItemDTO.setName("haha");
            PackageSizeDTO packageSizeDTO = new PackageSizeDTO();
            packageSizeDTO.setHeight("2");
            packageSizeDTO.setLength("3");
            packageSizeDTO.setWidth("4");
            goodsItemDTO.setWeight("0.23");
            goodsItem.setSkuId(++i);
            goodsItem.setGoodsId(1l);
            goodsItem.setId(++i);
            skuInfo.add(goodsItemDTO);
        }
        productInfoInput.setSkuInfo(skuInfo);
        Result<Long> longResult = goodsController.addGoodsInfo(productInfoInput);
    }

    @Test
    public void updateGoods() {
        ProductInfoInput productInfoInput = new ProductInfoInput();

        Result<Boolean> booleanResult = goodsController.updateGoods(productInfoInput);
    }

    @Test
    public void queryGoodsById() {
        Result<GoodsDetailInfoVO> goodsDetailInfoVOResult = goodsController.queryGoodsById(1L, null);
        log.info("goodsDetailInfoVOResult {}", goodsDetailInfoVOResult);
    }

    @Test
    public void comp() {
        List<Integer> a = Lists.newArrayList(1, 2, 3, 4, 5);
        a = a.subList(0, 3);
        String is = "#：1234523123126";
        if (StringUtils.isNotEmpty(is) && ("#:".equals(is.substring(0, 2)) ||
                "#：".equals(is.substring(0, 2))) && StringUtils.isNumeric(is.substring(2))) {
            System.out.println(StringUtils.isNumeric(is.substring(2)));

        }


    }

    @Resource
    private GoodsEsRemoteServiceImpl goodsEsRemoteService;
    @Resource
    private GoodsCoreServiceImpl goodsCoreService;

    @Test
    public void testQueryGoodsListByEs() {

        GoodsDTO dto = new GoodsDTO();
        dto.setPageNow(1);
        dto.setPageSize(20);
        dto.setIsDel(0);
        dto.setGoodsIds(Arrays.asList(4528628L, 4528631L, 615045L));
        dto.setStartTime("2022-04-29 00:00:00");
        dto.setEndTime("2022-04-29 23:59:59");
//        dto.setOriginalStartPrice(BigDecimal.valueOf(3));
//        dto.setOriginalEndPrice(BigDecimal.valueOf(5));
//        dto.setName("ceshi");
//        dto.setIsShow("0");
//        dto.setCategoryIds(Collections.singletonList(13L));
        PageView<GoodsInfoDTO> pageView = goodsEsRemoteService.queryPageBackgroundByOption(dto);
        log.info("{}", JSON.toJSONString(pageView));
        log.info("*********************************************************");
    }


    @Test
    public void testDeleteTag() {
        String str = "[{\"activityId\":36,\"goodsId\":4629131},{\"activityId\":36,\"goodsId\":4629130}]";
        List<GoodsTagInfoDTO> goodsTagInfoDTOList = JSON.parseArray(str, GoodsTagInfoDTO.class);
        Boolean res = goodsCoreService.deleteTag(goodsTagInfoDTOList);
        System.out.println(res);
    }

    @Resource
    private SensitiveWordsCoreServiceImpl sensitiveWordsCoreService;

    @Test
    public void testSensitiveFilter() {
        Set<String> set1 = sensitiveWordsCoreService.check("我靠");
        System.out.println("set1 = " + set1);
    }

    @Resource
    private CustomItemCoreService customItemCoreService;

    @Test
    public void eliminateJob() {
        try {
            customItemCoreService.eliminateJob();
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    @Test
    public void testShopCount() {
        Long count = goodsEsRemoteService.countGoodsByShopId(2L);
        System.out.println(count);
    }

    @Test
    public void getVirtualDiscount() {
        List<Long> longs = Lists.newArrayList(123L, 456L);
            BigDecimal virtualDiscount = newAddGoodsCoreService.getVirtualDiscount(longs, 123L);
            System.out.println(virtualDiscount);
    }

    @Test
    public void getOutDbGoodsEveryDayCount() throws IOException {
        Date today = new Date();
        Date date = DateUtil.addDays(today, -10);
        ZoneId defaultZoneId = ZoneId.systemDefault();
        LocalDateTime dateTime = LocalDateTime.ofInstant(date.toInstant(), defaultZoneId);

        OutDbGoodsEveryDayQueryParam queryParam = new OutDbGoodsEveryDayQueryParam();
        queryParam.setRunDays(7);
        queryParam.setCreateDayStartTime(dateTime);
        queryParam.setDealPayMoney(new BigDecimal(0.01).setScale(2, BigDecimal.ROUND_HALF_UP));
        queryParam.setGoodsIdList(Lists.newArrayList(23357021L,6880935L,6880797L));

        List<OutDbGoodsEveryDayVO> vos = everyDayEsService.listOutDbGoodsEveryDayV1(queryParam, OutDbGoodsEveryDayVO.class);
        System.out.println(JSONArray.toJSONString(vos));
    }

}
