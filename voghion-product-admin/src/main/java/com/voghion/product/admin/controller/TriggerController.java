package com.voghion.product.admin.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.lang.exception.CustomException;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.colorlight.base.model.constants.NoLogin;
import com.colorlight.base.utils.CheckUtils;
import com.google.cloud.bigquery.TableResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.onlest.GoodsSyncModel;
import com.voghion.es.dto.GoodsTestQueryInput;
import com.voghion.es.dto.VatCondition;
import com.voghion.es.dto.VatDto;
import com.voghion.es.model.GoodsExtConfigModel;
import com.voghion.es.service.GoodsEsService;
import com.voghion.product.api.dto.BackendTopicShopChangeDto;
import com.voghion.product.api.dto.BindTagDTO;
import com.voghion.product.api.dto.GoodsOperationLogDto;
import com.voghion.product.api.dto.RefreshGoodsFreightEventDto;
import com.voghion.product.api.enums.GoodsEditTypeEnums;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.api.enums.SaleableCountryEnum;
import com.voghion.product.bigquery.dto.HotGoodsMonitorDto;
import com.voghion.product.client.ActivityRemoteClientFactory;
import com.voghion.product.core.*;
import com.voghion.product.enums.CustomizedGoodsSyncEnums;
import com.voghion.product.external.VO.DataVO;
import com.voghion.product.external.VO.For1688.For1688VO;
import com.voghion.product.external.assmebler.For1688Assembler;
import com.voghion.product.external.checker.Of1688Checker;
import com.voghion.product.external.decoder.For1688Decoder;
import com.voghion.product.external.handler.For1688Handler;
import com.voghion.product.external.handler.MessageHandler;
import com.voghion.product.listener.ExcelNewListener;
import com.voghion.product.listener.GoodsImportVO;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.enums.*;
import com.voghion.product.model.po.*;
import com.voghion.product.model.vo.GoodsTestSetV2DTO;
import com.voghion.product.model.vo.InvalidPropertyNameExportVo;
import com.voghion.product.model.vo.ProductInfoInput;
import com.voghion.product.model.vo.PropertyGoodsInfoVO;
import com.voghion.product.model.vo.condition.*;
import com.voghion.product.mq.MqDelayLevel;
import com.voghion.product.mq.MqSender;
import com.voghion.product.mq.model.CustomizedGoodsSyncModel;
import com.voghion.product.remote.impl.GoodsEsRemoteServiceImpl;
import com.voghion.product.service.*;
import com.voghion.product.support.ElasticsearchHandler;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.util.BigQueryUtil;
import com.voghion.product.util.LogUtils;
import com.voghion.product.utils.CommonConstants;
import com.voghion.product.utils.DateUtil;
import com.voghion.product.utils.LocalDateTimeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.ibatis.annotations.Param;
import org.opensearch.action.ActionListener;
import org.opensearch.action.bulk.BulkItemResponse;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.action.search.SearchScrollRequest;
import org.opensearch.action.search.SearchType;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.common.unit.TimeValue;
import org.opensearch.index.query.*;
import org.opensearch.index.reindex.BulkByScrollResponse;
import org.opensearch.index.reindex.UpdateByQueryRequest;
import org.opensearch.script.Script;
import org.opensearch.search.Scroll;
import org.opensearch.search.SearchHit;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.SortOrder;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.zookeeper.ZooDefs.OpCode.error;

/**
 * @description: 手动触发事件入口
 * @date: 2022/4/25 下午12:19
 * @author: jashley
 */
@Slf4j
@RestController
@RequestMapping("trigger")
@Api(tags = "手动触发数据处理")
public class TriggerController {
    @Resource
    private TaxVatCoreService taxVatCoreService;
    @Resource
    private GoodsFreightCoreService goodsFreightCoreService;
    @Resource
    private GoodsFreightService goodsFreightService;
    @Resource
    private GoodsService goodsService;
    @Resource
    private GoodsItemService goodsItemService;
    @Resource
    private GoodsCoreService goodsCoreService;
    @Resource
    private WarehouseOverseasConfigCoreService warehouseOverseasConfigCoreService;
    @Resource
    private FaMerchantsApplyService faMerchantsApplyService;
    @Resource
    private ActivityRemoteClientFactory activityRemoteClientFactory;
    @Resource
    private GoodsMonitorChannelService goodsMonitorChannelService;
    @Resource
    private GoodsLockInfoService goodsLockInfoService;
    @Resource
    private GoodsLockApplyService goodsLockApplyService;
    @Resource
    private GoodsTestSetCoreService goodsTestSetCoreService;
    @Resource
    private ElasticsearchHandler elasticsearchHandler;
    @Resource
    private RedisApi redisApi;
    @Resource(name = "goodsInfoPool")
    private Executor execute;
    @Resource
    private MqSender mqSender;
    @Resource
    private GoodsEsRemoteServiceImpl goodsEsRemoteService;
    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private GoodsPriceReductionCoreService goodsPriceReductionCoreService;

    @Resource
    private FileSyncRecordService fileSyncRecordService;

    @Resource
    private FileSyncRecordDataService fileSyncRecordDataService;

    @Resource
    private GoodsTdTagService goodsTdTagService;

    @Resource
    private GoodsEsService goodsEsService;

    @Resource
    private UpdateGoodsInfoCoreService updateGoodsInfoCoreService;

    @Resource
    private GoodsEditInfoService goodsEditInfoService;

    @Resource
    private GoodsEditInfoDetailService goodsEditInfoDetailService;

    @Resource
    private GoodsEditInfoCoreService goodsEditInfoCoreService;

    @Resource
    private GoodsDetailService goodsDetailService;
    @Resource
    private ListingInfoService listingInfoService;
    @Resource
    private ListingFollowGoodsService listingFollowGoodsService;
    @Resource
    private ChanceGoodsTemplateService chanceGoodsTemplateService;
    @Resource
    private ProductService productService;
    @Resource
    private ProductSkuService productSkuService;
    @Resource
    private GoodsExtDetailService goodsExtDetailService;
    @Resource
    private GoodsExtDetailImgService goodsExtDetailImgService;
    @Resource
    private GoodsImageService goodsImageService;
    @Resource
    private GoodsExtConfigService goodsExtConfigService;
    @Resource
    private PropertyInfoService propertyInfoService;
    @Resource
    private PropertyGoodsInfoCoreService propertyGoodsInfoCoreService;
    @Resource
    private PropertyImgDetailService propertyImgDetailService;
    @Resource
    private WarehouseStockGoodsService warehouseStockGoodsService;
    @Resource
    private GoodsStockBackUpService goodsStockBackUpService;
    @Resource
    private GoodsPriceReductionService goodsPriceReductionService;
    @Resource
    private SkuReductionPriceService skuReductionPriceService;
    @Resource
    private PropertyValueRecordService propertyValueRecordService;
    @Resource
    private GoodsLogisticsService goodsLogisticsService;
    @Resource
    private GoodsLogisticsValueService goodsLogisticsValueService;
    @Resource
    private LogisticsPropertyConfigService logisticsPropertyConfigService;
    @Resource
    private LogisticsPropertyConfigCoreService logisticsPropertyConfigCoreService;
    @Resource
    private GoodsVatConfigService goodsVatConfigService;

    @Resource
    private GoodsMoveService goodsMoveService;

    @Resource
    private HotGoodsMonitorService hotGoodsMonitorService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;
    @Resource
    private BigQueryUtil bigQueryUtil;

    @Resource
    private For1688Handler for1688Handler;
    @Resource
    private Of1688Checker of1688Checker;
    @Resource
    private For1688Decoder for1688Decoder;
    @Resource
    private For1688Assembler for1688Assembler;
    @Resource
    private NewAddGoodsCoreService newAddGoodsCoreService;
    @Resource
    private AliGoodsCategoryPriceConfigService aliGoodsCategoryPriceConfigService;
    @Resource
    private ActivityOriginalPriceService activityOriginalPriceService;
    @Resource
    private GoodsLockCoreService goodsLockCoreService;
    @Resource
    private DobaGoodsService dobaGoodsService;

//    @NoLogin
//    @GetMapping("refreshVatCache")
//    @ApiOperation(value = "刷新vat缓存", tags = "刷新vat缓存")
//    public Result<Void> refreshVatCache() {
//        log.info("refreshVatCache starting!!!");
//        taxVatCoreService.refreshCache();
//        return Result.success(null);
//    }

//    @NoLogin
//    @GetMapping("queryVatByCountry")
//    @ApiOperation(value = "根据国家查vat税率", tags = "根据国家查vat税率")
//    public Result<BigDecimal> queryVatByCountry(Long goodsId, String country, Long shopId, Long categoryId) {
//        log.info("queryVatByCountry goodsId:{}, country:{}, shopId:{}, categoryId:{}", goodsId, country, shopId, categoryId);
//        BigDecimal vatRate = taxVatCoreService.getVatRate(goodsId, country, shopId, categoryId, null);
//        return Result.success(vatRate);
//    }

    @NoLogin
    @GetMapping("initFreightForGoodsLevel")
    public String initFreightForGoodsLevel() {
        log.info("开始处理国家运费数据 ===> 商品维度");

        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id");
        queryWrapper.eq("is_del", "0");
        int pageNow = 1;
        long pageEnd = 100000;
        while (pageNow <= pageEnd) {
            Page<Goods> page = new Page<>(pageNow, 200);
            IPage<Goods> pageResult = goodsService.page(page, queryWrapper);
            pageEnd = pageResult.getPages();
            log.info("pageNow:{}, pageEnd:{}", pageNow, pageEnd);
            execute.execute(() -> {
                for (Goods goods : pageResult.getRecords()) {
                    Long goodsId = goods.getId();
                    try {
                        log.info("开始处理国家运费数据 goodsId:{}", goodsId);
                        goodsFreightCoreService.initFreightForGoodsLevel(goodsId);
                    } catch (Exception e) {
                        log.info("处理国家运费数据商品维度失败 goodsId:{}", goodsId);
                        e.printStackTrace();
                    }
                }
                log.info("国家运费数据批处理完成 lastGoodsId:{}", pageResult.getRecords().get(pageResult.getRecords().size() - 1).getId());
            });
            pageNow++;
        }

        return "success";
    }

    @NoLogin
    @GetMapping("initFreightForGoodsLevelByGoodsId")
    public String initFreightForGoodsLevel(Long goodsId) {
        log.info("开始处理指定id的国家运费数据 goodsId:{}", goodsId);
        goodsFreightCoreService.initFreightForGoodsLevel(goodsId);
        return "success";
    }

    @NoLogin
    @GetMapping("initFreightForGoodsLevelFromStartGoodsId")
    public String initFreightForGoodsLevelFromStartGoodsId(Long startGoodsId) {
        log.info("开始处理从指定id开始的国家运费数据 startGoodsId:{}", startGoodsId);

        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id");
        queryWrapper.eq("is_del", "0");
        queryWrapper.ge("id", startGoodsId - 1);
        int pageNow = 1;
        long pageEnd = 100000;
        while (pageNow <= pageEnd) {
            Page<Goods> page = new Page<>(pageNow, 200);
            IPage<Goods> pageResult = goodsService.page(page, queryWrapper);
            pageEnd = pageResult.getPages();
            log.info("pageNow:{}, pageEnd:{}", pageNow, pageEnd);
            execute.execute(() -> {
                for (Goods goods : pageResult.getRecords()) {
                    Long goodsId = goods.getId();
                    try {
                        log.info("开始处理国家运费数据 goodsId:{}", goodsId);
                        goodsFreightCoreService.initFreightForGoodsLevel(goodsId);
                    } catch (Exception e) {
                        log.info("处理国家运费数据商品维度失败 goodsId:{}", goodsId);
                        e.printStackTrace();
                    }
                }
                log.info("国家运费数据批处理完成 lastGoodsId:{}", pageResult.getRecords().get(pageResult.getRecords().size() - 1).getId());
            });
            pageNow++;
        }

        return "success";
    }


    @NoLogin
    @PostMapping("batchUpdateGoodsCategory")
    @ApiOperation(value = "变更商品类目", tags = "变更商品类目")
    public Boolean batchUpdateGoodsCategory(@RequestBody GoodsCategoryUpdateCondition condition) {
        LogUtils.info(log, "变更商品类目 condition:{}", condition);
        if (CollectionUtils.isEmpty(condition.getParams())) {
            return false;
        }
        for (GoodsCategoryUpdateCondition.CategoryUpdateParam param : condition.getParams()) {
            log.info("batchUpdateGoodsCategory oldCategoryId:{}, newCategoryId:{}", param.getOldCategoryId(), param.getNewCategoryId());
            if (Objects.isNull(param.getNewCategoryId()) || Objects.isNull(param.getOldCategoryId()) || param.getOldCategoryId().equals(param.getNewCategoryId())) {
                continue;
            }
            List<Goods> goodsList = goodsService.lambdaQuery()
                    .eq(Goods::getCategoryId, param.getOldCategoryId())
                    .eq(Goods::getIsDel, 0)
                    .select(Goods::getId)
                    .list();
            log.info("batchUpdateGoodsCategory goodsList.size:{}", param.getOldCategoryId(), param.getNewCategoryId(), goodsList.size());
            if (CollectionUtils.isNotEmpty(goodsList)) {
                int batch = 1;
                for (List<Goods> list : Lists.partition(goodsList, 100)) {
                    log.info("batchUpdateGoodsCategory batch:{}", batch);
                    for (Goods goods : list) {
                        goods.setCategoryId(param.getNewCategoryId());
                        goods.setUpdateTime(LocalDateTime.now());
                    }
                    goodsService.updateBatchById(list);
                    batch++;
                }
            }

            //更新es
            HashMap<String, Object> map = Maps.newHashMap();
            map.put("categoryId", param.getNewCategoryId());
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("categoryId", param.getOldCategoryId()))
                    .must(QueryBuilders.termQuery("isDel", 0));
            BulkByScrollResponse bulkByScrollResponse = goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), queryBuilder, map);
            if (CollectionUtils.isNotEmpty(bulkByScrollResponse.getBulkFailures())) {
                for (BulkItemResponse.Failure bulkFailure : bulkByScrollResponse.getBulkFailures()) {
                    LogUtils.info(log, "batchUpdateGoodsCategory 商品迁移es执行异常:{}", bulkFailure.getMessage());
                }
            }

            //覆盖listing和机会商品

        }
        //todo 跑完通知数据组
        return true;
    }


    @NoLogin
    @ApiOperation(value = "初始化商品拼团价", tags = "初始化商品拼团价")
    @GetMapping("initGoodsGrouponPrice")
    public Boolean initGoodsGrouponPrice() {
        log.info("初始化商品拼团价");
        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0);
        queryWrapper.ge("id", 6876500);
        queryWrapper.isNull("min_groupon_price");
        queryWrapper.select("id", "is_del", "min_groupon_price", "max_groupon_price");
        Long lastId = 13000000L;
        while (true) {
            log.info("initGoodsGrouponPrice ===>> lastId:{}", lastId);
            Page<Goods> page = new Page<>(1, 100);
            queryWrapper.ge("id", lastId);
            IPage<Goods> pageResult = goodsService.page(page, queryWrapper);
            if (pageResult.getTotal() == 0 || CollectionUtils.isEmpty(pageResult.getRecords())) {
                break;
            }
            List<Goods> goodsList = pageResult.getRecords();
            lastId = goodsList.get(goodsList.size() - 1).getId();
            execute.execute(() -> {
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    List<Long> goodsIds = goodsList.stream().map(Goods::getId).distinct().collect(Collectors.toList());
                    QueryWrapper<GoodsItem> goodsItemQueryWrapper = new QueryWrapper<>();
                    goodsItemQueryWrapper.in("goods_id", goodsIds);
                    goodsItemQueryWrapper.select("id", "goods_id", "orginal_price", "default_delivery");
                    List<GoodsItem> allGoodsItems = goodsItemService.list(goodsItemQueryWrapper);
                    allGoodsItems.forEach(goodsItem -> {
                        if (goodsItem.getOrginalPrice() == null || goodsItem.getDefaultDelivery() == null) {
                            log.info("initGoodsGrouponPrice ===>> sku价格或运费缺失:{}", JSON.toJSONString(goodsItem));
                            if (goodsItem.getOrginalPrice() == null) {
                                goodsItem.setOrginalPrice(BigDecimal.ZERO);
                            }
                            if (goodsItem.getDefaultDelivery() == null) {
                                goodsItem.setDefaultDelivery(BigDecimal.ZERO);
                            }
                            goodsItem.setPrice(goodsItem.getOrginalPrice().add(goodsItem.getDefaultDelivery()));
                        }
                        goodsItem.setOriginalGrouponPrice(goodsItem.getOrginalPrice().multiply(new BigDecimal("0.8")).setScale(2, BigDecimal.ROUND_HALF_UP));
                        goodsItem.setGrouponPrice(goodsItem.getOriginalGrouponPrice().add(goodsItem.getDefaultDelivery()));
                    });
                    Map<Long, List<GoodsItem>> goodsItemMap = allGoodsItems.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));
                    for (Goods goods : goodsList) {
                        List<GoodsItem> itemList = goodsItemMap.get(goods.getId());
                        if (CollectionUtils.isNotEmpty(itemList)) {
                            goods.setMinGrouponPrice(itemList.stream().map(GoodsItem::getGrouponPrice).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                            goods.setMaxGrouponPrice(itemList.stream().map(GoodsItem::getGrouponPrice).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                        }
                    }
                    goodsService.updateBatchById(goodsList);
                    if (CollectionUtils.isNotEmpty(allGoodsItems)) {
                        goodsItemService.updateBatchById(allGoodsItems);
                    }
                    log.info("initGoodsGrouponPrice ===>> 更新成功: goodsList.size:{}, goodsItem.size:{}", goodsList.size(), allGoodsItems.size());
                    goodsIds.forEach(goodsId -> {
                        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                        goodsSyncModel.setGoodsId(goodsId);
                        goodsSyncModel.setSyncTime(System.currentTimeMillis());
                        goodsSyncModel.setBusiness("初始化商品拼团价(手动)");
                        goodsSyncModel.setSourceService("vp");
                        mqSender.send("SYNC_GOODS_TOPIC", JSON.toJSONString(goodsSyncModel));
                    });
                }
            });
        }

        return true;
    }

    @NoLogin
    @ApiOperation(value = "初始化指定商品拼团价", tags = "初始化指定商品拼团价")
    @GetMapping("initGoodsGrouponPriceById")
    public Boolean initGoodsGrouponPriceById(@Param("goodsId") Long goodsId) {
        log.info("初始化商品拼团价");
        Goods goods = goodsService.getById(goodsId);
        QueryWrapper<GoodsItem> goodsItemQueryWrapper = new QueryWrapper<>();
        goodsItemQueryWrapper.eq("goods_id", goodsId);
        List<GoodsItem> allGoodsItems = goodsItemService.list(goodsItemQueryWrapper);
        allGoodsItems.forEach(goodsItem -> {
            if (goodsItem.getOrginalPrice() == null || goodsItem.getDefaultDelivery() == null) {
                log.info("initGoodsGrouponPrice ===>> sku价格或运费缺失:{}", JSON.toJSONString(goodsItem));
            } else {
                goodsItem.setOriginalGrouponPrice(goodsItem.getOrginalPrice().multiply(new BigDecimal("0.8")).setScale(2, BigDecimal.ROUND_HALF_UP));
                goodsItem.setGrouponPrice(goodsItem.getOriginalGrouponPrice().add(goodsItem.getDefaultDelivery()));
            }
        });
        if (CollectionUtils.isNotEmpty(allGoodsItems)) {
            goods.setMinGrouponPrice(allGoodsItems.stream().map(GoodsItem::getGrouponPrice).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
            goods.setMaxGrouponPrice(allGoodsItems.stream().map(GoodsItem::getGrouponPrice).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
        }
        goodsService.updateById(goods);
        if (CollectionUtils.isNotEmpty(allGoodsItems)) {
            goodsItemService.updateBatchById(allGoodsItems);
        }
        log.info("initGoodsGrouponPrice ===>> 更新成功: goodsList.size:{}, goodsItem.size:{}", 1, allGoodsItems.size());

        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(goodsId);
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("初始化指定商品拼团价(手动)");
        goodsSyncModel.setSourceService("vp");
        mqSender.send("SYNC_GOODS_TOPIC", JSON.toJSONString(goodsSyncModel));

        return true;
    }


    @NoLogin
    @Async("goodsInfoPool")
    @GetMapping("calculateAllGoodsSortV2")
    public Boolean calculateAllGoodsSortV2() {
        goodsCoreService.calculateAllGoodsSortV2();
        return true;
    }


    @NoLogin
    @ApiOperation(value = "删除东南亚商品", tags = "删除东南亚商品")
    @GetMapping("removeSoutheastAsiaGoods")
    public Boolean removeSoutheastAsiaGoods() {
        Long maxId = 1L;
        List<Goods> list = new ArrayList<>();
        while (CollectionUtils.isNotEmpty(list = goodsService.querySourtyAsiaGoods(maxId, "SEA"))) {
            log.info("当前删除商品起始id:{}", maxId);
            List<Long> ids = list.stream().map(Goods::getId).collect(Collectors.toList());
            log.info("待删除商品id:", ids);
            goodsService.deleteByIds(ids);
            maxId = list.get(list.size() - 1).getId() + 1;
        }
        return true;
    }

    @NoLogin
    @PostMapping("syncDirectDeliveryShopGoods")
    @ApiOperation(value = "根据直发商家id更新所属店铺商品的海外仓标签", tags = "根据直发商家id更新所属店铺商品的海外仓标签")
    public Boolean syncDirectDeliveryShopGoods(@RequestBody Long shopId) {
        log.info("===>》 开始根据直发商家id更新所属店铺商品的海外仓标签 shopId:{}", shopId);
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);
        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.selectById(shopId);
        CheckUtils.check(faMerchantsApply == null || !DeliveryTypeEnum.DIRECT.getCode().equals(faMerchantsApply.getDeliveryType()), ProductResultCode.WAREHOUSE_OVERSEA_JUST_SUPPORT_DIRECT_DELIVERY);
        execute.execute(() -> warehouseOverseasConfigCoreService.syncDirectDeliveryShopGoods(shopId, null));
        return true;
    }

    @NoLogin
    @ApiOperation(value = "查询属性名称为'系统填充'的商品", tags = "查询属性名称为'系统填充'的商品")
    @GetMapping("queryInvalidPropertyNameGoods")
    public void queryInvalidPropertyNameGoods() {
        log.info("查询属性名称为'系统填充'的商品 start");
        execute.execute(() -> {
            Set<Long> goodsIds = Sets.newHashSet();
            long pageNow = 1;
            long pageEnd = 9999;
            while (pageNow <= pageEnd) {
                log.info("pageEnd:{}, pageNow:{}", pageEnd, pageNow);
                Page<Goods> goodsPageCondition = new Page<>(pageNow, 100);
                QueryWrapper<Goods> goodsQueryWrapper = new QueryWrapper<>();
                goodsQueryWrapper.eq("is_del", 0);
                goodsQueryWrapper.select("id");
                IPage<Goods> goodsIPage = goodsService.page(goodsPageCondition, goodsQueryWrapper);
                pageEnd = goodsIPage.getPages();

                List<Long> batchGoodsIds = goodsIPage.getRecords().stream().map(Goods::getId).collect(Collectors.toList());
                List<GoodsItem> goodsItemList = goodsItemService.lambdaQuery().select(GoodsItem::getGoodsId, GoodsItem::getPvalueDesc).in(GoodsItem::getGoodsId, batchGoodsIds).list();
                Map<Long, List<GoodsItem>> itemMap = goodsItemList.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

                for (Map.Entry<Long, List<GoodsItem>> entry : itemMap.entrySet()) {
                    List<GoodsItem> goodsItems = entry.getValue();
                    Long goodsId = entry.getKey();
                    for (GoodsItem goodsItem : goodsItems) {
                        if (goodsItem.getPvalueDesc().equals("系统填充,请修正(属性值)")) {
                            goodsIds.add(goodsId);
                            break;
                        }
                    }
                }
                pageNow++;
            }

            log.info("查询属性名称为'系统填充'的商品 total:{}", goodsIds.size());

            List<InvalidPropertyNameExportVo> allExportVos = Lists.newArrayList();

            List<List<Long>> partition = Lists.partition(Lists.newArrayList(goodsIds), 1000);
            for (List<Long> partitionGoodsIds : partition) {
                List<Goods> list = goodsService.lambdaQuery()
                        .select(Goods::getId, Goods::getIsDel, Goods::getShopId)
                        .in(Goods::getId, partitionGoodsIds)
                        .list();
                List<InvalidPropertyNameExportVo> invalidPropertyNameExportVos = BeanCopyUtil.transformList(list, InvalidPropertyNameExportVo.class);
                allExportVos.addAll(invalidPropertyNameExportVos);
            }

            allExportVos.sort((o1, o2) -> {
                long flag = o1.getShopId() - o2.getShopId();
                if (flag != 0) {
                    return Math.toIntExact(flag);
                }
                flag = o1.getGoodsId() - o2.getGoodsId();
                return Math.toIntExact(flag);
            });

            try {
                FileUtils.write(new File("/data/ROOT/onlest/voghion-product-admin/InvalidPropertyNameGoods.txt"), JSON.toJSONString(allExportVos), Charset.defaultCharset());
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }

    @NoLogin
    @GetMapping("queryNoMinPriceGoods")
//    @Async("goodsInfoPool")
    public void queryNoMinPriceGoods() {
        log.info("queryNoMinPriceGoods start !!!");
        execute.execute(() -> {
            try {
                goodsEsRemoteService.queryNoMinPriceGoods();
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }


    @NoLogin
    @GetMapping("initGoodsLockInfo1")
    @ApiOperation(value = "初始化商品锁定信息", tags = "初始化商品锁定信息")
    @Async("goodsInfoPool")
    public void initGoodsLockInfo() {
        log.info("===>》初始化商品锁定信息 start");
        HashMap<Long, List<Integer>> goodsLabelsMap = Maps.newHashMap();

        //1投放商品池
        List<GoodsMonitorChannel> launchGoodsList = goodsMonitorChannelService.lambdaQuery()
                .eq(GoodsMonitorChannel::getIsDel, 0)
                .eq(GoodsMonitorChannel::getPutStatus, 10)
                .select(GoodsMonitorChannel::getGoodsId)
                .list();
        List<Long> launchGoodsIds = launchGoodsList.stream().map(GoodsMonitorChannel::getGoodsId).distinct().collect(Collectors.toList());
        log.info("初始化商品锁定信息 投放商品池商品数量:{}", launchGoodsIds.size());
        for (Long launchGoodsId : launchGoodsIds) {
            List<Integer> labels = goodsLabelsMap.get(launchGoodsId);
            if (labels == null) {
                labels = Lists.newArrayList();
            }
            labels.add(GoodsLockLabelTypEnums.LAUNCH.getCode());
            goodsLabelsMap.put(launchGoodsId, labels);
        }

        //2flashDeal
        List<Long> flashDealGoodsIds = activityRemoteClientFactory.queryFlashDealGoods();
        log.info("初始化商品锁定信息 flashDeal商品数量:{}", flashDealGoodsIds.size());
        for (Long flashDealGoodsId : flashDealGoodsIds) {
            List<Integer> labels = goodsLabelsMap.get(flashDealGoodsId);
            if (labels == null) {
                labels = Lists.newArrayList();
            }
            labels.add(GoodsLockLabelTypEnums.FLASH_DEAL.getCode());
            goodsLabelsMap.put(flashDealGoodsId, labels);
        }

        //3七日达
        List<Long> sevenDayDeliveryGoodsIds = activityRemoteClientFactory.querySevenDayDeliveryGoods();
        log.info("初始化商品锁定信息 七日达商品数量:{}", sevenDayDeliveryGoodsIds.size());
        for (Long sevenDayDeliveryGoodsId : sevenDayDeliveryGoodsIds) {
            List<Integer> labels = goodsLabelsMap.get(sevenDayDeliveryGoodsId);
            if (labels == null) {
                labels = Lists.newArrayList();
            }
            labels.add(GoodsLockLabelTypEnums.SEVEN_DAY_DELIVERY.getCode());
            goodsLabelsMap.put(sevenDayDeliveryGoodsId, labels);
        }

        //4满减
        List<Long> fullReductionGoodsIds = activityRemoteClientFactory.queryFullReductionGoods();
        log.info("初始化商品锁定信息 满减商品数量:{}", fullReductionGoodsIds.size());
        for (Long fullReductionGoodsId : fullReductionGoodsIds) {
            List<Integer> labels = goodsLabelsMap.get(fullReductionGoodsId);
            if (labels == null) {
                labels = Lists.newArrayList();
            }
            labels.add(GoodsLockLabelTypEnums.FULL_DECREASE.getCode());
            goodsLabelsMap.put(fullReductionGoodsId, labels);
        }

        //5测款
        GoodsTestQueryInput goodsTestQueryInput = new GoodsTestQueryInput();
        goodsTestQueryInput.setStatus(1);
        goodsTestQueryInput.setPageNow(1);
        goodsTestQueryInput.setPageSize(999);
        PageView<GoodsTestSetV2DTO> goodsTestSetV2DTOPageView = goodsTestSetCoreService.queryTestSetV2(goodsTestQueryInput);
        log.info("初始化商品锁定信息 测款数量:{}", goodsTestSetV2DTOPageView.getRecords().size());
        if (CollectionUtils.isNotEmpty(goodsTestSetV2DTOPageView.getRecords())) {
            for (GoodsTestSetV2DTO goodsTestSetV2DTO : goodsTestSetV2DTOPageView.getRecords()) {
                List<Integer> labels = goodsLabelsMap.get(goodsTestSetV2DTO.getGoodsId());
                if (labels == null) {
                    labels = Lists.newArrayList();
                }
                labels.add(GoodsLockLabelTypEnums.TEST.getCode());
                goodsLabelsMap.put(goodsTestSetV2DTO.getGoodsId(), labels);
            }
        }


        addLockByApi(goodsLabelsMap);
    }

    @NoLogin
    @GetMapping("initGoodsLockInfo2")
    @ApiOperation(value = "初始化商品锁定信息", tags = "初始化商品锁定信息")
    @Async("goodsInfoPool")
    public void initGoodsLockInfo2() {
        log.info("===>》初始化商品锁定信息2 start");

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("isDel", 0))
                .must(QueryBuilders.termQuery("isLock", 1));

        SearchRequest firstSearchRequest = new SearchRequest(EsEnums.GOODS_ES.getIndex())
                .scroll(new Scroll(TimeValue.timeValueMinutes(1)))
                .searchType(SearchType.DEFAULT)
                .source(new SearchSourceBuilder().query(boolQueryBuilder).size(500).sort("id", SortOrder.ASC).fetchSource(new String[]{"id"}, null));
        SearchResponse searchGoodsIdResponse = null;
        try {
            searchGoodsIdResponse = restHighLevelClient.search(firstSearchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("初始化商品锁定信息2 scroll 111 异常", e);
        }
        if (searchGoodsIdResponse == null) {
            return;
        }

        SearchHit[] hits = searchGoodsIdResponse.getHits().getHits();
        String scrollId = searchGoodsIdResponse.getScrollId();
        int batch = 1;

        while (ArrayUtils.isNotEmpty(hits)) {
            List<Long> goodsIds = new ArrayList<>();
            for (SearchHit hit : hits) {
                Object goodsId = hit.getSourceAsMap().get("id");
                if (Objects.nonNull(goodsId)) {
                    goodsIds.add(Long.parseLong(goodsId.toString()));
                }
            }
            log.info("初始化商品锁定信息2 batch:{}, size:{}, firstGoodsId:{}", batch, goodsIds.size(), goodsIds.get(0));
            Map<Long, List<Integer>> goodsLabelsMap = goodsIds.stream().collect(Collectors.toMap(Function.identity(), goodsId -> Collections.singletonList(GoodsLockLabelTypEnums.HOT.getCode()), (a, b) -> a));
            execute.execute(() -> addLockByApi(goodsLabelsMap));

            SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId).scroll(TimeValue.timeValueMinutes(1));
            try {
                searchGoodsIdResponse = restHighLevelClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                log.error("初始化商品锁定信息2 scroll 222 异常", e);
            }
            if (searchGoodsIdResponse == null) {
                break;
            }
            scrollId = searchGoodsIdResponse.getScrollId();
            hits = searchGoodsIdResponse.getHits().getHits();
            batch++;
        }

//        long lastGoodsId = 1829561L;
//        IPage<Goods> page = goodsService.lambdaQuery()
//                .select(Goods::getId)
//                .eq(Goods::getIsDel, 0)
//                .eq(Goods::getIsLock, 1)
//                .gt(Goods::getId, lastGoodsId)
//                .orderByAsc(Goods::getId)
//                .page(new Page<>(1, 100));
//
//        while (CollectionUtils.isNotEmpty(page.getRecords())) {
//            List<Goods> records = page.getRecords();
//            Map<Long, List<Integer>> goodsLabelsMap = records.stream().collect(Collectors.toMap(Goods::getId, goods -> Collections.singletonList(GoodsLockLabelTypEnums.HOT.getCode()), (a, b) -> a));
//            addLockByApi(goodsLabelsMap);
//
//            lastGoodsId = records.get(records.size() - 1).getId();
//            page = goodsService.lambdaQuery()
//                    .select(Goods::getId)
//                    .eq(Goods::getIsDel, 0)
//                    .eq(Goods::getIsLock, 1)
//                    .gt(Goods::getId, lastGoodsId)
//                    .orderByAsc(Goods::getId)
//                    .page(new Page<>(1, 100));
//            log.info("初始化商品锁定信息2 lastGoodsId:{}, size:{}", lastGoodsId, page.getRecords().size());
//
//        }
    }

    public void addLockByApi(Map<Long, List<Integer>> map) {
        List<Long> allGoodsIds = Lists.newArrayList(map.keySet());
        List<List<Long>> partition = Lists.partition(allGoodsIds, 100);

        for (List<Long> goodsIds : partition) {
            List<Goods> goodsList = goodsService.lambdaQuery()
                    .in(Goods::getId, goodsIds)
                    .eq(Goods::getIsDel, 0)
                    .select(Goods::getId, Goods::getName, Goods::getIsLock, Goods::getCategoryId, Goods::getMainImage, Goods::getShopId, Goods::getShopName)
                    .list();
            Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));

            Date now = new Date();
            List<GoodsLockInfo> goodsLockInfoList = goodsLockInfoService.lambdaQuery().in(GoodsLockInfo::getGoodsId, goodsIds).eq(GoodsLockInfo::getIsDel, 0).list();
            Map<Long, GoodsLockInfo> goodsLockInfoMap = goodsLockInfoList.stream().collect(Collectors.toMap(GoodsLockInfo::getGoodsId, Function.identity(), (a, b) -> a));

            List<GoodsLockInfo> updateLockInfoList = Lists.newArrayList();
            List<Goods> updateGoodsList = Lists.newArrayList();
            Map<Long, Integer> data = new HashMap<>();

            for (Long goodsId : goodsIds) {
                List<Integer> labels = map.get(goodsId);
                Goods goods = goodsMap.get(goodsId);
                if (Objects.isNull(goods)) {
                    continue;
                }
                GoodsLockInfo goodsLockInfo = goodsLockInfoMap.get(goodsId);

                if (goodsLockInfo == null) {
                    goodsLockInfo = BeanCopyUtil.transform(goods, GoodsLockInfo.class);
                    goodsLockInfo.setCreateUser("系统自动加锁");
                    goodsLockInfo.setCreateTime(now);

                    goodsLockInfo.setLabel(StringUtils.join(labels, ","));
                    goodsLockInfo.setUnlockStatus(0);
                    goodsLockInfo.setIsDel(0);
                } else {
                    Set<Integer> originalLabels = Arrays.stream(goodsLockInfo.getLabel().split(",")).map(Integer::parseInt).collect(Collectors.toSet());
                    originalLabels.addAll(labels);
                    goodsLockInfo.setLabel(StringUtils.join(originalLabels, ","));
                }
                goodsLockInfo.setGoodsId(goods.getId());
                goodsLockInfo.setGoodsName(goods.getName());
                goodsLockInfo.setUpdateTime(now);
                goodsLockInfo.setUpdateUser("系统自动加锁");
                updateLockInfoList.add(goodsLockInfo);

                if (goods.getIsLock() == 0) {
                    goods.setIsLock(1);
                    goods.setUpdateTime(LocalDateTime.now());
                    updateGoodsList.add(goods);

                    data.put(goodsId, 1);
                }
            }

            if (CollectionUtils.isNotEmpty(updateLockInfoList)) {
                log.info("初始化商品锁定信息 updateLockInfoList:{}", updateLockInfoList.size());
                goodsLockInfoService.saveOrUpdateBatch(updateLockInfoList);
            }

            if (CollectionUtils.isNotEmpty(updateGoodsList)) {
                log.info("初始化商品锁定信息 updateGoodsList:{}", updateGoodsList.size());
                goodsService.updateBatchById(updateGoodsList);
//                elasticsearchHandler.syncGoodsEsField(2, data, MDC.get("traceId"));
            }
        }

    }

    @NoLogin
    @GetMapping("handleInvalidLockApply")
    @ApiOperation(value = "处理失效解锁申请", tags = "处理失效解锁申请")
    @Async("goodsInfoPool")
    public void handleInvalidLockApply() {
        long startId = 0;
        IPage<GoodsLockApply> page = goodsLockApplyService.lambdaQuery()
                .eq(GoodsLockApply::getIsDel, 0)
                .gt(GoodsLockApply::getId, startId)
                .and(goodsLockApplyLambdaQueryWrapper -> goodsLockApplyLambdaQueryWrapper
                        .eq(GoodsLockApply::getFirstAuditStatus, 0)
                        .or()
                        .eq(GoodsLockApply::getFirstAuditStatus, 1)
                        .eq(GoodsLockApply::getBusinessAuditStatus, 0)
                        .in(GoodsLockApply::getBelongType, Arrays.asList(1, 2))
                        .or()
                        .eq(GoodsLockApply::getFirstAuditStatus, 1)
                        .eq(GoodsLockApply::getOperationAuditStatus, 0)
                        .in(GoodsLockApply::getBelongType, Arrays.asList(1, 3)))
                .orderByAsc(GoodsLockApply::getId)
                .page(new Page<>(1, 200));
        LogUtils.info(log, "first page size:{}", page.getRecords().size());

        while (CollectionUtils.isNotEmpty(page.getRecords())) {
            LogUtils.info(log, "startId:{}", startId);
            List<GoodsLockApply> goodsLockApplyList = page.getRecords();

            List<Long> goodsIds = goodsLockApplyList.stream().map(GoodsLockApply::getGoodsId).distinct().collect(Collectors.toList());
            List<GoodsLockInfo> goodsLockInfoList = goodsLockInfoService.lambdaQuery().eq(GoodsLockInfo::getIsDel, 0).in(GoodsLockInfo::getGoodsId, goodsIds).list();
            List<Long> existLockInfoGoodsIds = goodsLockInfoList.stream().map(GoodsLockInfo::getGoodsId).distinct().collect(Collectors.toList());

            List<GoodsLockApply> invalidList = goodsLockApplyList.stream()
                    .filter(goodsLockApply -> !existLockInfoGoodsIds.contains(goodsLockApply.getGoodsId()))
                    .collect(Collectors.toList());

            Date now = new Date();
            if (CollectionUtils.isNotEmpty(invalidList)) {
                LogUtils.info(log, "invalidList size:{}", invalidList.size());
                for (GoodsLockApply goodsLockApply : invalidList) {
                    goodsLockApply.setFirstAuditRemark("触发自动解标签，申请失效");
                    goodsLockApply.setFirstAuditUser("自动解标签");
                    goodsLockApply.setFirstAuditTime(now);
                    goodsLockApply.setFirstAuditStatus(-1);
                }
                goodsLockApplyService.updateBatchById(invalidList);
            }

            startId = goodsLockApplyList.get(goodsLockApplyList.size() - 1).getId();
            page = goodsLockApplyService.lambdaQuery()
                    .eq(GoodsLockApply::getIsDel, 0)
                    .gt(GoodsLockApply::getId, startId)
                    .and(goodsLockApplyLambdaQueryWrapper -> goodsLockApplyLambdaQueryWrapper
                            .eq(GoodsLockApply::getFirstAuditStatus, 0)
                            .or()
                            .eq(GoodsLockApply::getFirstAuditStatus, 1)
                            .eq(GoodsLockApply::getBusinessAuditStatus, 0)
                            .in(GoodsLockApply::getBelongType, Arrays.asList(1, 2))
                            .or()
                            .eq(GoodsLockApply::getFirstAuditStatus, 1)
                            .eq(GoodsLockApply::getOperationAuditStatus, 0)
                            .in(GoodsLockApply::getBelongType, Arrays.asList(1, 3)))
                    .orderByAsc(GoodsLockApply::getId)
                    .page(new Page<>(1, 200));
        }

    }

    @NoLogin
    @GetMapping("handleRepeatLockInfo")
    @ApiOperation(value = "处理重复解锁信息", tags = "处理重复解锁信息")
    @Async("goodsInfoPool")
    public void handleRepeatLockInfo() {
        List<GoodsLockInfo> list = goodsLockInfoService.lambdaQuery()
                .select(GoodsLockInfo::getGoodsId)
                .eq(GoodsLockInfo::getIsDel, 0)
                .groupBy(GoodsLockInfo::getGoodsId)
                .having("count(1) > 1", new Object[0])
                .list();

        List<Long> goodsIds = list.stream().map(GoodsLockInfo::getGoodsId).collect(Collectors.toList());
        LogUtils.info(log, "goodsIds size: {}", goodsIds.size());

        List<GoodsLockInfo> goodsLockInfoList = goodsLockInfoService.lambdaQuery()
                .in(GoodsLockInfo::getGoodsId, goodsIds)
                .eq(GoodsLockInfo::getIsDel, 0)
                .list();
        Map<Long, List<GoodsLockInfo>> goodsLockInfoMap = goodsLockInfoList.stream().collect(Collectors.groupingBy(GoodsLockInfo::getGoodsId));

        List<GoodsLockInfo> deleteList = Lists.newArrayList();
        for (Map.Entry<Long, List<GoodsLockInfo>> entry : goodsLockInfoMap.entrySet()) {
            List<GoodsLockInfo> goodsLockInfos = entry.getValue();
            if (goodsLockInfos.size() < 2) {
                continue;
            }
            GoodsLockInfo toDeleteObject = goodsLockInfos.stream().min(Comparator.comparing(GoodsLockInfo::getUpdateTime)).orElse(null);
            toDeleteObject.setIsDel(1);
            deleteList.add(toDeleteObject);
        }
        LogUtils.info(log, "deleteList size: {}", goodsIds.size());
        if (CollectionUtils.isNotEmpty(deleteList)) {
            goodsLockInfoService.updateBatchById(deleteList);
        }
    }

    @NoLogin
    @GetMapping("fixAuditingLockApply1")
    @ApiOperation(value = "修改审核中解锁申请1", tags = "修改审核中解锁申请1")
    @Async("goodsInfoPool")
    public void fixAuditingLockApply1() {
        List<GoodsLockApply> existLockApplyList = goodsLockApplyService.lambdaQuery()
                .eq(GoodsLockApply::getIsDel, 0)
                .and(goodsLockApplyLambdaQueryWrapper -> goodsLockApplyLambdaQueryWrapper
                        .eq(GoodsLockApply::getFirstAuditStatus, 0)
                        .or()
                        .eq(GoodsLockApply::getFirstAuditStatus, 1)
                        .eq(GoodsLockApply::getBusinessAuditStatus, 0)
                        .ne(GoodsLockApply::getOperationAuditStatus, -1)
                        .in(GoodsLockApply::getBelongType, Arrays.asList(1, 2))
                        .or()
                        .eq(GoodsLockApply::getFirstAuditStatus, 1)
                        .eq(GoodsLockApply::getOperationAuditStatus, 0)
                        .ne(GoodsLockApply::getBusinessAuditStatus, -1)
                        .in(GoodsLockApply::getBelongType, Arrays.asList(1, 3)))
                .list();
        LogUtils.info(log, "修改审核中解锁申请 size:{}", existLockApplyList.size());

        Map<Long, GoodsLockApply> goodsLockApplyMap = existLockApplyList.stream().collect(Collectors.toMap(GoodsLockApply::getGoodsId, Function.identity(), (a, b) -> a));

        List<Long> goodsIds = existLockApplyList.stream().map(GoodsLockApply::getGoodsId).collect(Collectors.toList());
        List<List<Long>> partition = Lists.partition(goodsIds, 200);
        for (List<Long> batchGoodsIds : partition) {
            List<GoodsLockInfo> goodsLockInfoList = goodsLockInfoService.lambdaQuery()
                    .in(GoodsLockInfo::getGoodsId, batchGoodsIds)
                    .eq(GoodsLockInfo::getIsDel, 0)
                    .list();
            Map<Long, GoodsLockInfo> goodsLockInfoMap = goodsLockInfoList.stream().collect(Collectors.toMap(GoodsLockInfo::getGoodsId, Function.identity(), (a, b) -> a));
            List<GoodsLockApply> updateList = Lists.newArrayList();
            for (Long goodsId : batchGoodsIds) {
                GoodsLockApply goodsLockApply = goodsLockApplyMap.get(goodsId);
                GoodsLockInfo goodsLockInfo = goodsLockInfoMap.get(goodsId);
                if (goodsLockApply != null && goodsLockInfo != null) {
                    goodsLockApply.setLabel(goodsLockInfo.getLabel());
                    List<Integer> currentLabels = Arrays.stream(goodsLockInfo.getLabel().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                    if (currentLabels.size() == 1 && GoodsLockLabelTypEnums.LAUNCH.getCode().equals(currentLabels.get(0))) {
                        goodsLockApply.setBelongType(2);
                    } else if (currentLabels.contains(GoodsLockLabelTypEnums.LAUNCH.getCode())) {
                        goodsLockApply.setBelongType(1);
                    } else {
                        goodsLockApply.setBelongType(3);
                    }
                    updateList.add(goodsLockApply);
                }
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                LogUtils.info(log, "batch update size:{}", updateList.size());
                goodsLockApplyService.updateBatchById(updateList);
            }
        }
    }

    @NoLogin
    @GetMapping("fixAuditingLockApply2")
    @ApiOperation(value = "修改审核中解锁申请2", tags = "修改审核中解锁申请2")
    @Async("goodsInfoPool")
    public void fixAuditingLockApply2() {
        List<GoodsLockInfo> goodsLockInfoList = goodsLockInfoService.lambdaQuery()
                .eq(GoodsLockInfo::getIsDel, 0)
                .eq(GoodsLockInfo::getUnlockStatus, 1)
                .list();
        LogUtils.info(log, "审核中锁定信息 size:{}", goodsLockInfoList.size());

        List<List<GoodsLockInfo>> partition = Lists.partition(goodsLockInfoList, 200);
        for (List<GoodsLockInfo> batchLockInfos : partition) {
            Map<Long, GoodsLockInfo> goodsLockInfoMap = batchLockInfos.stream().collect(Collectors.toMap(GoodsLockInfo::getGoodsId, Function.identity(), (a, b) -> a));

            List<Long> batchGoodsIds = batchLockInfos.stream().map(GoodsLockInfo::getGoodsId).collect(Collectors.toList());
            List<GoodsLockApply> existLockApplyList = goodsLockApplyService.lambdaQuery()
                    .in(GoodsLockApply::getGoodsId, batchGoodsIds)
                    .eq(GoodsLockApply::getIsDel, 0)
                    .list();
            Map<Long, List<GoodsLockApply>> goodsApplyListMap = existLockApplyList.stream().collect(Collectors.groupingBy(GoodsLockApply::getGoodsId));
            List<GoodsLockApply> updateList = Lists.newArrayList();
            for (Map.Entry<Long, List<GoodsLockApply>> entry : goodsApplyListMap.entrySet()) {
                Long goodsId = entry.getKey();
                List<GoodsLockApply> goodsLockApplyList = entry.getValue();
                goodsLockApplyList.sort((o1, o2) -> o2.getApplyTime().compareTo(o1.getApplyTime()));
                GoodsLockApply goodsLockApply = goodsLockApplyList.get(0);

                GoodsLockInfo goodsLockInfo = goodsLockInfoMap.get(goodsId);
                List<Integer> labels = Arrays.stream(goodsLockInfo.getLabel().split(",")).map(Integer::parseInt).collect(Collectors.toList());
                if (labels.size() == 1 && GoodsLockLabelTypEnums.LAUNCH.getCode().equals(labels.get(0))) {
                    //营销 2
                    goodsLockApply.setBelongType(2);
                } else if (labels.contains(GoodsLockLabelTypEnums.LAUNCH.getCode())) {
                    //all 1
                    goodsLockApply.setBelongType(1);
                } else {
                    //运营 3
                    goodsLockApply.setBelongType(3);
                }
                goodsLockApply.setLabel(goodsLockInfo.getLabel());
                goodsLockApply.setBusinessAuditStatus(0);
                goodsLockApply.setOperationAuditStatus(0);
                updateList.add(goodsLockApply);
            }
            LogUtils.info(log, "batch update size:{}", updateList.size());
            if (CollectionUtils.isNotEmpty(updateList)) {
                goodsLockApplyService.updateBatchById(updateList);
            }
        }
    }

    @NoLogin
    @PostMapping("removeByGoodsId")
    public Boolean removeByGoodsId(@RequestBody List<Long> goodsIds) {
        goodsPriceReductionCoreService.removeByGoodsId(goodsIds);
        return true;
    }

    @NoLogin
    @PostMapping("sendMq")
    public Boolean sendMq(@RequestBody MqTestDto dto) {
        log.info("sendMq test:{}", JSON.toJSONString(dto));
        mqSender.send(dto.getTopic(), dto.getData());
        return true;
    }


    @NoLogin
    @GetMapping("reImportGoodsDataFromExcel")
    @ApiOperation(value = "重新读取excel文件入库", tags = "重新读取excel文件入库")
    public void reImportGoodsDataFromExcel(@RequestParam Long id) {
        CheckUtils.notNull(id, ProductResultCode.PARAMETER_ID_ERROR);
        FileSyncRecord fileSyncRecord = fileSyncRecordService.getById(id);

        CheckUtils.notNull(fileSyncRecord, ProductResultCode.FILE_SYNC_RECORD_NOT_FIND);

        List<FileSyncRecordData> toSaveList = fileSyncRecordDataService.lambdaQuery().eq(FileSyncRecordData::getFileSyncRecordId, id).list();
        if (CollectionUtils.isNotEmpty(toSaveList)) {
            fileSyncRecordDataService.deleteByIds(toSaveList.stream().map(FileSyncRecordData::getId).collect(Collectors.toList()));
        }

        CloseableHttpResponse response = null;
        if (response == null) {
            CheckUtils.check(true, ProductResultCode.DOWNLOAD_ERROR);
            return;
        }
        log.info("获取文件信息成功 statusCode:{}", response.getStatusLine().getStatusCode());

        if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
            log.error("下载导入文件失败:{}", fileSyncRecord.getFileUrl());
            CheckUtils.check(true, ProductResultCode.DOWNLOAD_ERROR);
            return;
        }

        List<GoodsImportVO> list;
        try {
            ExcelNewListener<GoodsImportVO> listener = new ExcelNewListener<>();
            ExcelReader excelReader = EasyExcelFactory.read(response.getEntity().getContent(), GoodsImportVO.class, listener).headRowNumber(2).build();
            excelReader.read(new ReadSheet(0));
            list = listener.getDatas();
            if (CollectionUtils.isEmpty(list)) {
                log.error("excel读出的数据为空:{}", fileSyncRecord.getFileUrl());
                CheckUtils.check(true, ProductResultCode.EXCEL_DATA_EMPTY);
                return;
            }
        } catch (IOException e) {
            LogUtils.error(log, "读取文件数据失败", e);
            return;
        }


        List<FileSyncRecordData> fileSyncRecordDataList = BeanCopyUtil.transformList(list, FileSyncRecordData.class);
        for (FileSyncRecordData data : fileSyncRecordDataList) {
            data.setFileSyncRecordId(fileSyncRecord.getId());
            data.setStatus(0);
        }
        fileSyncRecordDataService.insertBatch(fileSyncRecordDataList);

        fileSyncRecord.setStatus(0);
        fileSyncRecord.setMsg("");
        fileSyncRecordService.updateById(fileSyncRecord);
        LogUtils.info(log, "reImportGoodsDataFromExcel over");
    }

    @NoLogin
    @PostMapping("moveGoodsCategory")
    @ApiOperation(value = "商品类目迁移", tags = "商品类目迁移")
    public Integer moveGoodsCategory(RemoveGoodsCategoryCondition condition) {
        LogUtils.info(log, "商品类目迁移:{}", condition);
        CheckUtils.check(condition.getOldCategoryId() == null || condition.getNewCategoryId() == null, ProductResultCode.PARAMETER_ERROR);
        assert condition.getOldCategoryId() != null;
        if (condition.getOldCategoryId().equals(condition.getNewCategoryId())) {
            return null;
        }

        List<Goods> goodsList = goodsService.lambdaQuery()
                .eq(Goods::getCategoryId, condition.getOldCategoryId())
                .eq(Goods::getIsDel, 0)
                .select(Goods::getId)
                .list();
        LogUtils.info(log, "商品迁移原类目:{}, 新类目:{}, 商品数量:{}", condition.getOldCategoryId(), condition.getNewCategoryId(), goodsList.size());
        goodsList.forEach(goods -> goods.setCategoryId(condition.getNewCategoryId()));
        boolean success = goodsService.updateBatchById(goodsList);
        if (success) {
            List<Long> goodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());
            HashMap<String, Object> map = Maps.newHashMap();
            map.put("categoryId", condition.getNewCategoryId());
            TermsQueryBuilder queryBuilder = QueryBuilders.termsQuery("id", goodsIds);
            BulkByScrollResponse bulkByScrollResponse = goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), queryBuilder, map);
            if (CollectionUtils.isNotEmpty(bulkByScrollResponse.getBulkFailures())) {
                for (BulkItemResponse.Failure bulkFailure : bulkByScrollResponse.getBulkFailures()) {
                    LogUtils.info(log, "商品迁移es执行异常:{}", bulkFailure.getMessage());
                }
            }
//            updateByQuery(EsEnums.GOODS_ES.getIndex(),queryBuilder,map);
        }
        //todo 跑完通知数据组
        return goodsList.size();
    }

//    public BulkByScrollResponse updateByQuery(String index, QueryBuilder query, Map<String, Object> document) {
//        UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest(index);
//        updateByQueryRequest.setQuery(query);
//        StringBuilder script = new StringBuilder();
//        Set<String> keys = document.keySet();
//        for (String key : keys) {
//            String appendValue = "";
//            Object value = document.get(key);
//            if (value instanceof Number) {
//                appendValue = value.toString();
//            } else if (value instanceof String) {
//                appendValue = "'" + value.toString() + "'";
//            } else if (value instanceof List) {
//                appendValue = JSONObject.toJSONString(value);
//            } else {
//                appendValue = value.toString();
//            }
//            script.append("ctx._source.").append(key).append("=").append(appendValue).append(";");
//        }
//        log.info("script:{}", script);
//        updateByQueryRequest.setScript(new Script(script.toString()));
//        return updateByQuery(updateByQueryRequest, RequestOptions.DEFAULT);
//    }
//
//    public BulkByScrollResponse updateByQuery(UpdateByQueryRequest updateByQueryRequest, RequestOptions options) {
//        try {
//            restHighLevelClient.updateByQueryAsync(updateByQueryRequest, options, new ActionListener<BulkByScrollResponse>() {
//                @Override
//                public void onResponse(BulkByScrollResponse bulkByScrollResponse) {
//                }
//
//                @Override
//                public void onFailure(Exception e) {
//                    log.error("错误信息", e);
//                }
//            });
//        } catch (Exception e) {
//            log.info("updateByQuery error", e);
//        }
//        return null;
//    }

    @NoLogin
    @GetMapping("passStockEditInfo")
    @ApiOperation(value = "通过修改库存修改记录未审核审批", tags = "通过修改库存修改记录未审核审批")
    public void passStockEditInfo() {
        LogUtils.info(log, "passStockEditInfo start");
        List<Long> toAuditIdList = goodsEditInfoService.lambdaQuery()
                .eq(GoodsEditInfo::getType, GoodsEditTypeEnums.STOCK.getCode())
                .eq(GoodsEditInfo::getStatus, 0)
                .select(GoodsEditInfo::getId)
                .list()
                .stream().map(GoodsEditInfo::getId).collect(Collectors.toList());
        GoodsEditAuditCondition condition = new GoodsEditAuditCondition();
        int batch = 1;
        LogUtils.info(log, "passStockEditInfo toAuditIdList.size:{}", toAuditIdList);
        for (Long id : toAuditIdList) {
            try {
                condition.setIds(Collections.singletonList(id));
                Boolean success = goodsEditInfoCoreService.pass(condition);
                LogUtils.info(log, "passStockEditInfo batch:{}, id:{}, success:{}", batch, id, success);
            } catch (Exception e) {
                LogUtils.info(log, "passStockEditInfo error id:{}, message:{}", id, e.getMessage(), e);
            }
            batch++;
        }
    }

    @NoLogin
    @PostMapping("rePassGoodsEditInfo")
    @ApiOperation(value = "通过修改库存修改记录未审核审批", tags = "通过修改库存修改记录未审核审批")
    public void rePassGoodsEditInfo(@RequestBody List<Long> ids) {
        LogUtils.info(log, "passGoodsEditInfo start ---> ids:{}", ids);
        List<GoodsEditInfo> goodsEditInfos = Lists.newArrayList(goodsEditInfoService.selectByIds(ids));

        GoodsEditAuditCondition condition = new GoodsEditAuditCondition();
        condition.setType(2);
        for (GoodsEditInfo goodsEditInfo : goodsEditInfos) {
            if (goodsEditInfo.getStatus() != 1 || goodsEditInfo.getAuditUser().equals("system")) {
                LogUtils.info(log, "rePassGoodsEditInfo 该需改记录状态or审核人不符，跳过 id:{}", goodsEditInfo.getId());
                continue;
            }

            condition.setIds(Collections.singletonList(goodsEditInfo.getId()));
            condition.setOperator(goodsEditInfo.getAuditUser());
            goodsEditInfoCoreService.pass(condition);
            LogUtils.info(log, "rePassGoodsEditInfo 结束 id:{}:{}", goodsEditInfo.getId());
        }
    }


    @NoLogin
    @GetMapping("fixDeleteListingFollowGoods")
    @ApiOperation(value = "处理因同步listing而删除的商品", tags = "处理因同步listing而删除的商品")
    public void fixDeleteListingFollowGoods() {
        LogUtils.info(log, "处理因同步listing而删除的商品 start");

        List<ListingInfo> listingInfoList = listingInfoService.lambdaQuery().eq(ListingInfo::getIsDel, 0).select(ListingInfo::getId).list();
        for (ListingInfo listingInfo : listingInfoList) {
            String traceId = MDC.get("traceId");
            traceId = traceId + "_" + listingInfo.getId();
            syncListingFollowGoods(listingInfo.getId(), traceId);
        }

    }


    private void syncListingFollowGoods(Long listingId, String traceId) {
        LogUtils.info(log, "listing模板更新，同步跟卖商品->syncListingFollowGoods listingId:{}, traceId:{}", listingId, traceId);
        if (listingId == null) {
            return;
        }
        ListingInfo listingInfo = listingInfoService.getById(listingId);
        Long listingGoodsId = listingInfo.getGoodsId();

        Goods listingGoods = goodsService.getById(listingGoodsId);
        CheckUtils.notNull(listingGoods, CustomResultCode.fill(ProductResultCode.GOODS_NOT_EXIST_EXT, listingGoodsId.toString()));

        Product listingProduct = productService.getById(listingGoods.getProductId());
        CheckUtils.notNull(listingProduct, CustomResultCode.fill(ProductResultCode.PRODUCT_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsItem> listingGoodsItems = goodsItemService.queryGoodsItemByGoodsId(listingGoodsId);
        CheckUtils.isEmpty(listingGoodsItems, CustomResultCode.fill(ProductResultCode.GOODS_ITEM_NOT_EXIST, listingGoodsId.toString()));

        List<ProductSku> listingProductSkuList = productSkuService.queryProductSkuByProductId(listingGoods.getProductId());
        CheckUtils.isEmpty(listingProductSkuList, CustomResultCode.fill(ProductResultCode.PRODUCT_SKU_NOT_EXIST, listingGoodsId.toString()));

        GoodsDetail listingGoodsDetail = goodsDetailService.queryGoodsDetailByGoodsId(listingGoodsId);
        CheckUtils.notNull(listingGoodsDetail, CustomResultCode.fill(ProductResultCode.GOODS_DETAIL_NOT_EXIST_EXT, listingGoodsId.toString()));

        GoodsExtDetail listingGoodsExtDetail = goodsExtDetailService.lambdaQuery().eq(GoodsExtDetail::getGoodsId, listingGoodsId).one();
        CheckUtils.notNull(listingGoodsExtDetail, CustomResultCode.fill(ProductResultCode.GOODS_EXT_DETAIL_NOT_EXIST, listingGoodsId.toString()));

//        GoodsExtCategory goodsExtCategory = goodsExtCategoryService.lambdaQuery().eq(GoodsExtCategory::getGoodsId, goodsId).one();
//        CheckUtils.notNull(goodsExtCategory, CustomResultCode.fill(ProductResultCode.GOODS_EXT_CATEGORY_NOT_EXIST, goodsId.toString()));

        List<GoodsExtDetailImg> listingGoodsExtDetailImgList = goodsExtDetailImgService.queryByGoodsId(listingGoodsId);
        CheckUtils.isEmpty(listingGoodsExtDetailImgList, CustomResultCode.fill(ProductResultCode.GOODS_EXT_DETAIL_IMAGE_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsImage> listingGoodsImages = goodsImageService.queryListByGoodsId(listingGoodsId);
        CheckUtils.isEmpty(listingGoodsImages, CustomResultCode.fill(ProductResultCode.GOODS_IMAGE_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsExtConfig> listingTagList = goodsExtConfigService.lambdaQuery()
                .eq(GoodsExtConfig::getGoodsId, listingGoodsId)
                .in(GoodsExtConfig::getTagId, Arrays.asList(40, 45, 50))
                .eq(GoodsExtConfig::getIsDel, 0)
                .list();

        Set<Long> listingPropertyIds = Sets.newHashSet();
        Set<Long> listingPropertyValueIds = Sets.newHashSet();
        List<String> listingPvalueStrList = Lists.newArrayList();
        for (GoodsItem goodsItem : listingGoodsItems) {
            for (String s : goodsItem.getPvalueStr().split(";")) {
                if (org.apache.commons.lang.StringUtils.isBlank(s)) {
                    continue;
                }
                listingPropertyIds.add(Long.parseLong(s.split(":")[0]));
                listingPropertyValueIds.add(Long.parseLong(s.split(":")[1]));
            }
            listingPvalueStrList.add(goodsItem.getPvalueStr());
        }
        List<PropertyImgDetail> listingPropertyImgDetails = propertyInfoService.queryByImgList(listingProduct.getId(), Lists.newArrayList(listingPropertyValueIds));

        List<PropertyGoodsInfoVO> listingPropertyGoodsInfoVOS = propertyGoodsInfoCoreService.queryByGoodsId(listingGoodsId);

        List<Property> properties = propertyInfoService.queryPropertyByIds(Lists.newArrayList(listingPropertyIds));
        boolean propertyContainSize = properties.stream().anyMatch(property -> "size".equals(property.getName()));

        List<Long> followGoodsIds = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, listingId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .list().stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        LogUtils.info(log, "syncListingFollowGoods followGoodsIds:{},traceId", followGoodsIds, traceId);
        if (CollectionUtils.isEmpty(followGoodsIds)) {
            return;
        }
        List<Goods> goodsList = goodsService.lambdaQuery().in(Goods::getId, followGoodsIds).list();
        followGoodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());

        List<GoodsItem> allGoodsItemList = goodsItemService.queryGoodsIdsList(followGoodsIds);
        Map<Long, List<GoodsItem>> groupGoodsItemMap = allGoodsItemList.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

        List<Long> productIds = goodsList.stream().map(Goods::getProductId).collect(Collectors.toList());
        List<Product> productList = Lists.newArrayList(productService.selectByIds(productIds));
        Map<Long, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getId, Function.identity(), (v1, v2) -> v1));

        List<ProductSku> allProductSkuList = productSkuService.lambdaQuery().in(ProductSku::getProductId, productIds).eq(ProductSku::getIsDel, 0).list();
        Map<Long, List<ProductSku>> groupProductSkuMap = allProductSkuList.stream().collect(Collectors.groupingBy(ProductSku::getProductId));

        List<GoodsDetail> goodsDetailList = goodsDetailService.queryGoodsDetailByGoodsIds(followGoodsIds);
        Map<Long, GoodsDetail> goodsDetailMap = goodsDetailList.stream().collect(Collectors.toMap(GoodsDetail::getGoodsId, Function.identity(), (v1, v2) -> v1));

        List<GoodsExtDetail> goodsExtDetailList = goodsExtDetailService.queryGoodsExtDetailByGoodsIds(followGoodsIds);
        Map<Long, GoodsExtDetail> goodsExtDetailMap = goodsExtDetailList.stream().collect(Collectors.toMap(GoodsExtDetail::getGoodsId, Function.identity(), (v1, v2) -> v1));

        List<PropertyImgDetail> allPropertyImgDetailList = propertyImgDetailService.lambdaQuery().in(PropertyImgDetail::getSpuId, productIds).list();
        Map<Long, List<PropertyImgDetail>> groupPropertyImgDetailMap = allPropertyImgDetailList.stream().collect(Collectors.groupingBy(PropertyImgDetail::getSpuId));

        //goodsExtConfig
        if (CollectionUtils.isNotEmpty(listingTagList)) {
            for (GoodsExtConfig goodsExtConfig : listingTagList) {
                BindTagDTO bindTagDTO = new BindTagDTO();
                bindTagDTO.setTagId(goodsExtConfig.getTagId());
                bindTagDTO.setGoodsIds(followGoodsIds);
                goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
            }
        }

        for (Goods goods : goodsList) {
            Long goodsId = goods.getId();
            LogUtils.info(log, "syncListingFollowGoods 开始同步商品id:{}, traceId:{}", goodsId, traceId);
            //goods
            goods.setName(listingGoods.getName());
            goods.setMainImage(listingGoods.getMainImage());
            goods.setCategoryId(listingGoods.getCategoryId());
            goods.setLogisticsProperty(listingGoods.getLogisticsProperty());
            if (CollectionUtils.isEmpty(groupGoodsItemMap.get(goodsId)) && goods.getIsDel() == 1) {
                goods.setIsDel(0);
                goods.setIsShow(GoodsIsShowEnums.TAKE_OFF.getType().toString());
            }
            goods.setUpdateTime(LocalDateTime.now());
            goodsService.updateById(goods);

            //product
            Product product = productMap.get(goods.getProductId());
            product.setName(product.getName());
            product.setCategoryId(goods.getCategoryId());
            productService.updateById(product);

            //product sku
            List<ProductSku> originalProductSkuList = groupProductSkuMap.get(product.getId());
            Map<String, ProductSku> pvalueStrProductSkuMap = originalProductSkuList.stream().collect(Collectors.toMap(ProductSku::getPvalueStr, Function.identity(), (v1, v2) -> v1));
            List<String> toDeletePvalueList = Lists.newArrayList(pvalueStrProductSkuMap.keySet());
            toDeletePvalueList.removeAll(listingPvalueStrList);

            List<ProductSku> updateProductSkuList = Lists.newArrayList();
            for (ProductSku listingProductSku : listingProductSkuList) {
                ProductSku productSku = pvalueStrProductSkuMap.get(listingProductSku.getPvalueStr());
                if (productSku == null) {
                    productSku = new ProductSku();
                    BeanCopyUtil.copyProperties(listingProductSku, productSku);
                    productSku.setId(null);
                    productSku.setProductId(product.getId());
                    productSku.setCreateTime(LocalDateTime.now());
                }
                productSku.setName(listingProductSku.getName());
                updateProductSkuList.add(productSku);
            }
            if (CollectionUtils.isNotEmpty(toDeletePvalueList)) {
                pvalueStrProductSkuMap.forEach((key, toDeleteProductSku) -> {
                    if (toDeletePvalueList.contains(key)) {
                        toDeleteProductSku.setIsDel(1);
                        updateProductSkuList.add(toDeleteProductSku);
                    }
                });
            }
            productSkuService.saveOrUpdateBatch(updateProductSkuList);
            Map<String, Long> skuIdPvalueMap = updateProductSkuList.stream().collect(Collectors.toMap(ProductSku::getPvalueStr, ProductSku::getId, (v1, v2) -> v1));
//            LogUtils.info(log, "skuIdPvalueMap:{}", skuIdPvalueMap);


            //goodsItem
            List<GoodsItem> originalGoodsItemList = groupGoodsItemMap.get(goodsId);
            Map<String, GoodsItem> originalPvalueStrGoodsItemMap = Maps.newHashMap();
            Map<String, String> reversePvalueStrMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(originalGoodsItemList)) {
                originalPvalueStrGoodsItemMap = originalGoodsItemList.stream().collect(Collectors.toMap(GoodsItem::getPvalueStr, Function.identity(), (v1, v2) -> v1));
                reversePvalueStrMap = Maps.newHashMap();

                if (propertyContainSize && originalGoodsItemList.get(0).getPvalueStr().split(";").length == 2) {
                    for (GoodsItem goodsItem : originalGoodsItemList) {
                        List<String> pvalueList = Lists.newArrayList(goodsItem.getPvalueStr().split(";"));
                        Collections.reverse(pvalueList);
                        String reversePvalueStr = org.apache.commons.lang.StringUtils.join(pvalueList, ";");
                        originalPvalueStrGoodsItemMap.put(reversePvalueStr, goodsItem);

                        reversePvalueStrMap.put(goodsItem.getPvalueStr(), reversePvalueStr);
                        reversePvalueStrMap.put(reversePvalueStr, goodsItem.getPvalueStr());
                    }
                }
//            LogUtils.info(log, "pvalueStrGoodsItemMap:{}", pvalueStrGoodsItemMap);
            }


            List<GoodsItem> updateGoodsItemList = Lists.newArrayList();
            for (GoodsItem listingGoodsItem : listingGoodsItems) {
                GoodsItem goodsItem = originalPvalueStrGoodsItemMap.get(listingGoodsItem.getPvalueStr());
                if (goodsItem == null) {
                    goodsItem = new GoodsItem();
                    BeanCopyUtil.copyProperties(listingGoodsItem, goodsItem);
                    goodsItem.setId(null);
                    goodsItem.setGoodsId(goodsId);
                    goodsItem.setSkuId(skuIdPvalueMap.get(listingGoodsItem.getPvalueStr()));
                    goodsItem.setCreateTime(LocalDateTime.now());
                    goodsItem.setStock(0L);
                }
                goodsItem.setPvalueStr(listingGoodsItem.getPvalueStr());
                goodsItem.setPvalueDesc(listingGoodsItem.getPvalueDesc());
                goodsItem.setName(listingGoodsItem.getName());
                goodsItem.setSkuImage(goodsItem.getSkuImage());
                goodsItem.setUpdateTime(new Date());
                updateGoodsItemList.add(goodsItem);

                String pvalueStr = goodsItem.getPvalueStr();
                String reversePvalueStr = reversePvalueStrMap.get(goodsItem.getPvalueStr());
                toDeletePvalueList.removeIf(s -> s.equals(pvalueStr) || org.apache.commons.lang.StringUtils.isNotBlank(reversePvalueStr) && s.equals(reversePvalueStr));
            }
//            LogUtils.info(log, "updateGoodsItemList:{}", updateGoodsItemList);
            goodsItemService.saveOrUpdateBatch(updateGoodsItemList);

            List<Long> toDeleteItemIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(toDeletePvalueList)) {
                originalPvalueStrGoodsItemMap.forEach((key, toDeleteGoodsItem) -> {
                    if (toDeletePvalueList.contains(key)) {
                        toDeleteItemIds.add(toDeleteGoodsItem.getId());
                    }
                });
            }
//            LogUtils.info(log, "toDeleteItemIds:{}", toDeleteItemIds);
            if (CollectionUtils.isNotEmpty(toDeleteItemIds)) {
                goodsItemService.deleteByIds(toDeleteItemIds);
            }


            //goodsDetail
            GoodsDetail originalGoodsDetail = goodsDetailMap.get(goodsId);
            GoodsDetail goodsDetail = BeanCopyUtil.transform(listingGoodsDetail, GoodsDetail.class);
            goodsDetail.setId(originalGoodsDetail.getId());
            goodsDetail.setGoodsId(originalGoodsDetail.getGoodsId());
            goodsDetail.setSizeImage(null);
            goodsDetail.setCreateTime(originalGoodsDetail.getCreateTime());
            goodsDetailService.updateById(goodsDetail);


            //goodsExtDetail
            GoodsExtDetail originalGoodsExtDetail = goodsExtDetailMap.get(goodsId);
            GoodsExtDetail goodsExtDetail = BeanCopyUtil.transform(listingGoodsExtDetail, GoodsExtDetail.class);
            goodsExtDetail.setId(originalGoodsExtDetail.getId());
            goodsExtDetail.setGoodsId(originalGoodsExtDetail.getGoodsId());
            goodsExtDetail.setShopUrl(goods.getShopId().toString());
            goodsExtDetail.setShopName(goods.getShopName());
            goodsExtDetail.setCreateTime(originalGoodsExtDetail.getCreateTime());
            goodsExtDetailService.updateById(goodsExtDetail);


            //goodsExtDetailImg
            goodsExtDetailImgService.deleteByGoodsId(goodsId);
            List<GoodsExtDetailImg> goodsExtDetailImgs = BeanCopyUtil.transformList(listingGoodsExtDetailImgList, GoodsExtDetailImg.class);
            goodsExtDetailImgs.forEach(goodsExtDetailImg -> {
                goodsExtDetailImg.setId(null);
                goodsExtDetailImg.setGoodsId(goodsId);
                goodsExtDetailImg.setCreateTime(LocalDate.now());
            });
            goodsExtDetailImgService.saveBatch(goodsExtDetailImgs);


            //goodsImage
            goodsImageService.deleteByGoodsId(goodsId);
            List<GoodsImage> goodsImageList = BeanCopyUtil.transformList(listingGoodsImages, GoodsImage.class);
            goodsImageList.forEach(goodsImage -> {
                goodsImage.setId(null);
                goodsImage.setGoodsId(goodsId);
                goodsImage.setCreateTime(LocalDateTime.now());
            });
            goodsImageService.saveBatch(goodsImageList);

            //propertyImgDetail
            List<PropertyImgDetail> propertyImgDetails = groupPropertyImgDetailMap.get(product.getId());
            if (CollectionUtils.isNotEmpty(propertyImgDetails)) {
                propertyImgDetailService.deleteByIds(propertyImgDetails.stream().map(PropertyImgDetail::getId).collect(Collectors.toList()));
            }

            List<PropertyImgDetail> propertyImgDetailList = BeanCopyUtil.transformList(listingPropertyImgDetails, PropertyImgDetail.class);
            for (PropertyImgDetail propertyImgDetail : propertyImgDetailList) {
                propertyImgDetail.setId(null);
                propertyImgDetail.setSpuId(product.getId());
                propertyImgDetail.setCreateTime(new Date());
            }
            propertyImgDetailService.saveBatch(propertyImgDetailList);


            //propertyGoodsInfo
            if (CollectionUtils.isNotEmpty(listingPropertyGoodsInfoVOS)) {
                listingPropertyGoodsInfoVOS.forEach(propertyGoodsInfoVO -> propertyGoodsInfoVO.setGoodsId(goodsId));
                propertyGoodsInfoCoreService.saveOrUpdatePropertyGoods(listingPropertyGoodsInfoVOS);
            }


            LogUtils.info(log, "syncListingFollowGoods 结束同步商品id:{}, traceId:{}", goodsId, traceId);
            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(goodsId);
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("处理因同步listing而删除的商品(手动)");
            goodsSyncModel.setSourceService("vp");
            mqSender.send("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel));
            log.info("mq发送成功,商品id:{}", goodsSyncModel.getGoodsId());

            GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                    .goodsId(goodsId)
                    .type(OperationLogTypeEnums.UPDATE_GOODS_FROM_SYNC_LISTING)
                    .content(OperationLogTypeEnums.UPDATE_GOODS_FROM_SYNC_LISTING.getDesc())
                    .status(1)
                    .user("system");
            mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
        }

    }


    @NoLogin
    @GetMapping("fixHolidayShops")
    @ApiOperation(value = "已休假店铺备货仓商品初始化", tags = "已休假店铺备货仓商品初始化")
    public void fixHolidayShops(@Param("shopId") Long shopId) {
        LogUtils.info(log, "fixHolidayShops->已休假店铺备货仓商品初始化 start");

        List<FaMerchantsApply> shopList = faMerchantsApplyService.lambdaQuery()
                .eq(FaMerchantsApply::getIsSale, 2)
                .eq(shopId != null, FaMerchantsApply::getId, shopId)
                .list();
        for (FaMerchantsApply shop : shopList) {
            LogUtils.info(log, "fixHolidayShops->开始处理该店铺商品 shopId:{}", shop.getId());

            execute.execute(() -> {
                List<Goods> goodsList = goodsService.lambdaQuery()
                        .eq(Goods::getIsDel, 0)
                        .eq(Goods::getShopId, shop.getId())
                        .list();
                LogUtils.info(log, "fixHolidayShops->处理店铺商品ing... shopId:{}, goods.size:{}", shop.getId(), goodsList.size());
                if (CollectionUtils.isEmpty(goodsList)) {
                    return;
                }

                List<Long> goodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());
                Map<Long, List<GoodsItem>> goodsItemGroupMap = goodsItemService.lambdaQuery()
                        .in(GoodsItem::getGoodsId, goodsIds)
                        .list()
                        .stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

                Map<Long, List<WarehouseStockGoods>> stockUpMap = warehouseStockGoodsService.lambdaQuery()
                        .in(WarehouseStockGoods::getGoodsId, goodsIds)
                        .gt(WarehouseStockGoods::getShowNums, 0)
                        .eq(WarehouseStockGoods::getStatus, 0)
                        .list().stream().collect(Collectors.groupingBy(WarehouseStockGoods::getGoodsId));

                goodsList.forEach(goods -> {
                    Long goodsId = goods.getId();
                    //isShow <10
                    int isShow = Integer.parseInt(goods.getIsShow());
//                    if (isShow <= 10) {
                    if (isShow < 200) {
                        List<GoodsStockBackUp> saveStockBackUpList = Lists.newArrayList();
                        List<GoodsItem> updateGoodsItemList = Lists.newArrayList();

                        List<WarehouseStockGoods> warehouseStockGoods = stockUpMap.get(goodsId);
                        if (CollectionUtils.isNotEmpty(warehouseStockGoods)) {
                            //备货仓库存置换 && 备份
                            List<GoodsItem> goodsItems = goodsItemGroupMap.get(goodsId);
                            List<GoodsStockBackUp> goodsStockBackUpList = goodsItems.stream().map(goodsItem -> {
                                GoodsStockBackUp stockBackUp = new GoodsStockBackUp();
                                stockBackUp.setGoodsId(goodsId);
                                stockBackUp.setSkuId(goodsItem.getSkuId());
                                stockBackUp.setStock(goodsItem.getStock());
                                stockBackUp.setIsDel(0);
                                stockBackUp.setCreateTime(LocalDateTime.now());
                                stockBackUp.setUpdateTime(LocalDateTime.now());
                                return stockBackUp;
                            }).collect(Collectors.toList());
                            saveStockBackUpList.addAll(goodsStockBackUpList);

                            Map<Long, Integer> skuStockUpMap = warehouseStockGoods.stream().collect(Collectors.toMap(WarehouseStockGoods::getSkuId, WarehouseStockGoods::getShowNums, (v1, v2) -> v1));
                            goodsItems.forEach(goodsItem -> goodsItem.setStock(skuStockUpMap.getOrDefault(goodsItem.getSkuId(), 0).longValue()));
                            updateGoodsItemList.addAll(goodsItems);

                            List<GoodsItem> validItems = goodsItems.stream().collect(Collectors.toList());
                            validItems.stream().map(GoodsItem::getPrice).max(BigDecimal::compareTo).ifPresent(goods::setMaxPrice);
                            validItems.stream().map(GoodsItem::getPrice).min(BigDecimal::compareTo).ifPresent(goods::setMinPrice);

                            if (isShow >= 100) {
                                goods.setIsShow(String.valueOf(isShow - 100));
                            }
                        } else {
                            isShow = isShow >= 100 ? isShow + 100 : isShow + 200;
                            goods.setIsShow(String.valueOf(isShow));
                        }
                        goods.setUpdateTime(LocalDateTime.now());
                        goodsService.updateById(goods);
                        if (CollectionUtils.isNotEmpty(saveStockBackUpList)) {
                            goodsStockBackUpService.saveBatch(saveStockBackUpList);
                        }
                        if (CollectionUtils.isNotEmpty(updateGoodsItemList)) {
                            goodsItemService.updateBatchById(updateGoodsItemList);
                        }

                        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                                .goodsId(goods.getId())
                                .type(OperationLogTypeEnums.PHP_SHOP_CEASE)
                                .content(OperationLogTypeEnums.PHP_SHOP_CEASE.getDesc())
                                .newData(goods.getIsShow())
                                .status(1)
                                .user("trigger触发");
                        //发送同步操作记录mq
                        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
                        mqSender.send("SYNC_RECOMMEND_GOODS_ID", goods.getId());
                        // 刷新商品缓存
                        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                        goodsSyncModel.setGoodsId(goods.getId());
                        goodsSyncModel.setSyncTime(System.currentTimeMillis());
                        goodsSyncModel.setBusiness("已休假店铺备货仓商品初始化(手动)");
                        goodsSyncModel.setSourceService("vp");
                        mqSender.sendDelay("SYNC_GOODS_TOPIC_BATCH", goodsSyncModel, MqDelayLevel.TWO_MIN);
                    }
                });

            });
        }


    }


    @NoLogin
    @GetMapping("updateReductionDisCount")
    @ApiOperation(value = "更新降价建议折扣", tags = "更新降价建议折扣")
    public void updateReductionDisCount() {
        long pageNow = 1L;
        long pageEnd = 1000L;
        while (pageNow < pageEnd) {
            IPage<GoodsPriceReduction> page = goodsPriceReductionService.lambdaQuery()
                    .gt(GoodsPriceReduction::getId, 8429)
                    .eq(GoodsPriceReduction::getStatus, 1)
                    .page(new Page<>(pageNow, 100L));
            pageEnd = page.getPages();
            pageNow++;

            List<GoodsPriceReduction> goodsPriceReductionList = page.getRecords();
            if (CollectionUtils.isEmpty(goodsPriceReductionList)) {
                break;
            }

            List<Long> reductionIds = goodsPriceReductionList.stream().map(GoodsPriceReduction::getId).collect(Collectors.toList());
            List<SkuReductionPrice> skuReductionPriceList = skuReductionPriceService.queryByReductionIds(reductionIds, 0);

            for (SkuReductionPrice skuReductionPrice : skuReductionPriceList) {
                skuReductionPrice.setPrice(skuReductionPrice.getPrice().multiply(BigDecimal.valueOf(9)).divide(BigDecimal.valueOf(7), 2, RoundingMode.HALF_UP));
            }
            skuReductionPriceService.updateBatchById(skuReductionPriceList);
        }
    }

    @NoLogin
    @GetMapping("initGoodsItemPropertyValueRecordSnap")
    @ApiOperation(value = "生成sku的规格值记录", tags = "生成sku的规格值记录")
    public void initGoodsItemPropertyValueRecordSnap(@Param("startGoodsId") Long startGoodsId) {
        LogUtils.info(log, "startGoodsId:{}", startGoodsId);

        long pageNow = 1;
        long pageEnd = 10000;
        while (pageNow <= pageEnd) {
            LogUtils.info(log, "initGoodsItemPropertyValueRecordSnap ===> initing... pageNow:{}, pageEnd:{}", pageNow, pageEnd);
            IPage<Goods> page = goodsService.lambdaQuery()
                    .eq(Goods::getIsDel, 0)
                    .le(startGoodsId != null, Goods::getId, startGoodsId)
                    .orderByDesc(Goods::getId)
                    .select(Goods::getId)
                    .page(new Page<>(pageNow, 100));
            pageEnd = page.getPages();
            pageNow++;


            List<Long> goodsIds = page.getRecords().stream().map(Goods::getId).collect(Collectors.toList());
            List<GoodsItem> allGoodsItemList = goodsItemService.queryGoodsIdsList(goodsIds);
            Map<Long, List<GoodsItem>> goodsItemGroupMap = allGoodsItemList.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

            for (Map.Entry<Long, List<GoodsItem>> entry : goodsItemGroupMap.entrySet()) {
                Long goodsId = entry.getKey();
                List<GoodsItem> goodsItemList = entry.getValue();
                LogUtils.info(log, "initGoodsItemPropertyValueRecordSnap ===> initing... goodsId:{}, goodsItemList.size:{}", goodsId, goodsItemList.size());

                List<Long> pvalueIds = Lists.newArrayList();
                for (GoodsItem goodsItem : goodsItemList) {
                    for (String s : goodsItem.getPvalueStr().split(";")) {
                        pvalueIds.add(Long.parseLong(s.split(":")[1]));
                    }
                }

                List<PropertyValue> propertyValues = propertyInfoService.queryPropertyValueByIds(pvalueIds);
                if (CollectionUtils.isEmpty(propertyValues)) {
                    LogUtils.info(log, "initGoodsItemPropertyValueRecordSnap ===> 查不到规格值 goodsId:{}, pvalueIds:{}", goodsId, pvalueIds);
                    continue;
                }
                Map<Long, String> propertyValueMap = propertyValues.stream().collect(Collectors.toMap(PropertyValue::getId, PropertyValue::getValue, (v1, v2) -> v1));

                Set<String> propertyValueStrList = Sets.newHashSet(propertyValueMap.values());
                List<PropertyValueRecord> propertyValueRecordList = propertyValueRecordService.lambdaQuery()
                        .in(PropertyValueRecord::getName, propertyValueStrList)
                        .list();
                Map<String, Long> propertyValueRecordMap = propertyValueRecordList.stream().collect(Collectors.toMap(PropertyValueRecord::getName, PropertyValueRecord::getId, (v1, v2) -> v1));

                List<PropertyValueRecord> saveList = Lists.newArrayList();
                for (String propertyValue : propertyValueStrList) {
                    Long propertyValueRecordId = propertyValueRecordMap.get(propertyValue);
                    if (propertyValueRecordId == null) {
                        saveList.add(new PropertyValueRecord(propertyValue));
                    }
                }
                if (CollectionUtils.isNotEmpty(saveList)) {
                    propertyValueRecordService.saveBatch(saveList);
                    propertyValueRecordList.addAll(saveList);
                    propertyValueRecordMap = propertyValueRecordList.stream().collect(Collectors.toMap(PropertyValueRecord::getName, PropertyValueRecord::getId, (v1, v2) -> v1));
                }

                for (GoodsItem goodsItem : goodsItemList) {
                    List<Long> propertyValueRecordIdList = Lists.newArrayList();
                    for (String s : goodsItem.getPvalueStr().split(";")) {
                        String propertyValue = propertyValueMap.get(Long.parseLong(s.split(":")[1]));
                        if (StringUtils.isBlank(propertyValue)) {
                            LogUtils.error(log, "initGoodsItemPropertyValueRecordSnap ===> propertyValue is null ===> goodsId:{}, pvalueStr:{}", goodsId, goodsItem.getPvalueStr());
                            LogUtils.error(log, "initGoodsItemPropertyValueRecordSnap ===> propertyValue is null ===> propertyValueMap:{}", propertyValueMap);
                            continue;
                        }
                        Long propertyValueRecordId = propertyValueRecordMap.get(propertyValue);
                        if (propertyValueRecordId == null) {
                            LogUtils.error(log, "initGoodsItemPropertyValueRecordSnap ===> propertyValueRecordId is null ===> goodsId:{}, pvalueStr:{}", goodsId, goodsItem.getPvalueStr());
                            LogUtils.error(log, "initGoodsItemPropertyValueRecordSnap ===> propertyValueRecordId is null ===> propertyValueRecordMap:{}", propertyValueRecordMap);
                            continue;
                        }
                        propertyValueRecordIdList.add(propertyValueRecordId);
                    }
                    if (CollectionUtils.isNotEmpty(propertyValueRecordIdList)) {
                        String propertyValueRecordSnap = goodsId + "-" + propertyValueRecordIdList.stream().sorted(Long::compareTo).map(Objects::toString).collect(Collectors.joining("-"));
                        goodsItem.setPropertyValueRecordSnap(propertyValueRecordSnap);
                    }
                }
                goodsItemService.updateBatchById(goodsItemList);
            }
        }
    }


    @NoLogin
    @GetMapping("initGoodsLogisticsValues")
    @ApiOperation(value = "", tags = "初始化新类目物流属性")
    public void initGoodsLogisticsValues() {
        List<GoodsLogistics> goodsLogisticsList = goodsLogisticsService.lambdaQuery()
                .isNull(GoodsLogistics::getStatus)
                .list();

        if (CollectionUtils.isNotEmpty(goodsLogisticsList)) {
            Map<Integer, String> logisticsPropertyNameMap = logisticsPropertyConfigService.lambdaQuery()
                    .eq(LogisticsPropertyConfig::getIsDel, 0)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(LogisticsPropertyConfig::getValue, LogisticsPropertyConfig::getBackendDesc, (v1, v2) -> v1));

            Date now = new Date();
            List<GoodsLogisticsValue> goodsLogisticsValueList = Lists.newArrayList();
            for (GoodsLogistics goodsLogistics : goodsLogisticsList) {
                goodsLogistics.setStatus(1);
                goodsLogistics.setUpdateTime(now);

                GoodsLogisticsValue goodsLogisticsValue = new GoodsLogisticsValue();
                goodsLogisticsValue.setGoodsLogisticsId(goodsLogistics.getId());
                goodsLogisticsValue.setLogisticsPropertyCode(goodsLogistics.getLogisticsProperty());
                goodsLogisticsValue.setLogisticsPropertyName(logisticsPropertyNameMap.get(goodsLogistics.getLogisticsProperty()));
                goodsLogisticsValue.setIsDefault(0);
                goodsLogisticsValueList.add(goodsLogisticsValue);
            }

            goodsLogisticsService.updateBatchById(goodsLogisticsList);
            goodsLogisticsValueService.saveBatch(goodsLogisticsValueList);
        }
    }

    @NoLogin
    @GetMapping("triggerRefreshWholesaleGoodsFreight")
    @ApiOperation(value = "", tags = "手动触发所有批发商品更新国家运费")
    public void triggerRefreshGoodsFreight() {
        LogUtils.info(log, "手动触发所有批发商品更新国家运费 start");

        int batch = 1;
        Long firstGoodsId = 0L;

        while (true) {
            LogUtils.info(log, "手动触发所有批发商品更新国家运费 ===> init ing... batch:{}", batch);
            IPage<Goods> page = goodsService.lambdaQuery()
                    .eq(Goods::getIsDel, 0)
//                    .eq(Goods::getType, 3)
                    .gt(Goods::getId, firstGoodsId)
                    .orderByDesc(Goods::getId)
                    .select(Goods::getId)
                    .page(new Page<>(1, 100));

            List<Goods> goodsList = page.getRecords();
            if (CollectionUtils.isEmpty(goodsList)) {
                LogUtils.info(log, "手动触发所有批发商品更新国家运费 商品列表为空 ===> stop");
                break;
            }

            firstGoodsId = goodsList.stream().map(Goods::getId).max(Long::compareTo).orElse(null);
            if (firstGoodsId == null) {
                LogUtils.info(log, "手动触发所有批发商品更新国家运费 获取不到该批次最小商品id ===> stop");
                break;
            }
            batch++;


            for (Goods goods : goodsList) {
                try {
                    goodsCoreService.refreshGoodsFreight(goods.getId());
                } catch (CustomException e) {
                    LogUtils.info(log, "手动触发所有批发商品更新国家运费，自定义异常:{}, goodsId:{}", e.getBusinessResultCode().getMsg(), goods.getId(), e);
                } catch (Exception e) {
                    LogUtils.info(log, "手动触发所有批发商品更新国家运费，异常:{}, goodsId:{}", e.getMessage(), goods.getId(), e);
                }
            }
        }
    }

    @NoLogin
    @GetMapping("triggerRefreshGoodsFreight")
    @ApiOperation(value = "", tags = "手动触发所有商品更新国家运费")
    public void triggerRefreshGoodsFreight(@Param("lastGoodsId") Long lastGoodsId) {
        LogUtils.info(log, "手动触发所有商品更新国家运费 lastGoodsId:{}", lastGoodsId);

        if (lastGoodsId == null) {
            LogUtils.info(log, "手动触发所有商品更新国家运费 return");
            return;
        }

        String traceId = MDC.get("traceId");
        int batch = 1;
        while (true) {
            LogUtils.info(log, "手动触发所有商品更新国家运费 ===> init ing... batch:{}, lastGoodsId:{}", batch, lastGoodsId);
            IPage<Goods> page = goodsService.lambdaQuery()
                    .eq(Goods::getIsDel, 0)
                    .lt(Goods::getId, lastGoodsId)
                    .orderByDesc(Goods::getId)
                    .select(Goods::getId)
                    .page(new Page<>(1, 100));

            List<Goods> goodsList = page.getRecords();
            if (CollectionUtils.isEmpty(goodsList)) {
                LogUtils.info(log, "手动触发所有商品更新国家运费 商品列表为空 ===> stop");
                break;
            }

            lastGoodsId = goodsList.stream().map(Goods::getId).min(Long::compareTo).orElse(null);
            if (lastGoodsId == null) {
                LogUtils.info(log, "手动触发所有商品更新国家运费 获取不到该批次最小商品id ===> stop");
                break;
            }
            batch++;

            List<Long> negativeGoodsIds = goodsExtConfigService.lambdaQuery()
                    .in(GoodsExtConfig::getGoodsId, goodsList.stream().map(Goods::getId).collect(Collectors.toList()))
                    .in(GoodsExtConfig::getTagId, Arrays.asList(40, 45, 50))
                    .eq(GoodsExtConfig::getIsDel, 0)
                    .list()
                    .stream()
                    .map(GoodsExtConfig::getGoodsId)
                    .collect(Collectors.toList());

            for (Goods goods : goodsList) {
                if (negativeGoodsIds.contains(goods.getId())) {
                    LogUtils.info(log, "手动触发所有商品更新国家运费 包含负向标签，跳过 goodsId:{}", goods.getId());
                    continue;
                }

                try {
                    RefreshGoodsFreightEventDto eventDto = new RefreshGoodsFreightEventDto();
                    eventDto.setGoodsId(goods.getId());
                    eventDto.setTraceId(traceId);
                    eventDto.setMsg("trigger手动触发刷新商品国家运费");
                    mqSender.send("REFRESH_GOODS_FREIGHT_TOPIC", eventDto);
                } catch (CustomException e) {
                    LogUtils.info(log, "手动触发所有商品更新国家运费，自定义异常:{}, goodsId:{}", e.getBusinessResultCode().getMsg(), goods.getId(), e);
                } catch (Exception e) {
                    LogUtils.info(log, "手动触发所有商品更新国家运费，异常:{}, goodsId:{}", e.getMessage(), goods.getId(), e);
                }
            }
        }
    }

    @NoLogin
    @PostMapping("triggerRefreshGoodsFreightByGoodsIds")
    @ApiOperation(value = "", tags = "根据商品id触发更新国家运费")
    public void triggerRefreshGoodsFreightByGoodsIds(@RequestBody List<Long> allGoodsIds, @RequestParam("type") Integer type) {
        LogUtils.info(log, "根据商品id触发更新国家运费 type:{}, allGoodsIds:{}", type, allGoodsIds);
        type = type == null ? 0 : type;

        if (CollectionUtils.isEmpty(allGoodsIds)) {
            LogUtils.info(log, "根据商品id触发更新国家运费 return");
            return;
        }

        String traceId = MDC.get("traceId");
        int batch = 1;

        for (List<Long> goodsIds : ListUtils.partition(allGoodsIds, 100)) {
            LogUtils.info(log, "根据商品id触发更新国家运费 ===> init ing... batch:{}", batch);
            List<Goods> goodsList = goodsService.lambdaQuery()
                    .in(Goods::getId, goodsIds)
                    .eq(Goods::getIsDel, 0)
                    .select(Goods::getId)
                    .list();
            if (CollectionUtils.isEmpty(goodsList)) {
                LogUtils.info(log, "根据商品id触发更新国家运费 商品列表为空 ===> stop");
                continue;
            }

            for (Goods goods : goodsList) {
                try {
                    RefreshGoodsFreightEventDto eventDto = new RefreshGoodsFreightEventDto();
                    eventDto.setGoodsId(goods.getId());
                    eventDto.setTraceId(traceId);
                    eventDto.setMsg("trigger手动触发刷新商品国家运费");
                    mqSender.send("REFRESH_GOODS_FREIGHT_TOPIC", eventDto);
                } catch (CustomException e) {
                    LogUtils.info(log, "根据商品id触发更新国家运费，自定义异常:{}, goodsId:{}", e.getBusinessResultCode().getMsg(), goods.getId(), e);
                } catch (Exception e) {
                    LogUtils.info(log, "根据商品id触发更新国家运费，异常:{}, goodsId:{}", e.getMessage(), goods.getId(), e);
                }
            }

            batch++;
        }
    }

    @NoLogin
    @GetMapping("triggerRefreshWholesaleGoodsPrice")
    @ApiOperation(value = "", tags = "手动触发所有批发商品更新sku价格")
    public void triggerRefreshWholesaleGoodsPrice(@Param("firstGoodsId") Long firstGoodsId) {
        LogUtils.info(log, "手动触发所有批发商品更新sku价格 start ===> firstGoodsId:{}", firstGoodsId);

        if (firstGoodsId == null) {
            LogUtils.info(log, "手动触发所有批发商品更新sku价格 return");
            return;
        }

        Long batchMaxGoodsId = firstGoodsId + 200000;

        int batch = 1;
        while (true) {
            LogUtils.info(log, "手动触发所有批发商品更新sku价格 ===> init ing... batch:{}, firstGoodsId:{}, batchMaxGoodsId:{}", batch, firstGoodsId, batchMaxGoodsId);
            IPage<Goods> page = goodsService.lambdaQuery()
                    .eq(Goods::getIsDel, 0)
                    .eq(Goods::getType, 3)
                    .gt(Goods::getId, firstGoodsId)
                    .le(Goods::getId, batchMaxGoodsId)
                    .orderByDesc(Goods::getId)
                    .select(Goods::getId)
                    .page(new Page<>(1, 100));

            List<Goods> goodsList = page.getRecords();
            if (CollectionUtils.isEmpty(goodsList)) {
                LogUtils.info(log, "手动触发所有批发商品更新sku价格 商品列表为空 ===> stop");
                break;
            }

            firstGoodsId = goodsList.stream().map(Goods::getId).max(Long::compareTo).orElse(null);
            if (firstGoodsId == null) {
                LogUtils.info(log, "手动触发所有批发商品更新sku价格 获取不到该批次最小商品id ===> stop");
                break;
            }
            batch++;


            for (Goods goods : goodsList) {
                try {
                    goodsCoreService.refreshWholesaleGoodsPrice(goods.getId(), 1, true);
                } catch (CustomException e) {
                    LogUtils.info(log, "手动触发所有批发商品更新sku价格，自定义异常:{}, goodsId:{}", e.getBusinessResultCode().getMsg(), goods.getId(), e);
                } catch (Exception e) {
                    LogUtils.info(log, "手动触发所有批发商品更新sku价格，异常:{}, goodsId:{}", e.getMessage(), goods.getId(), e);
                }
            }
        }
    }


    @NoLogin
    @PostMapping("triggerRefreshWholesaleGoodsPriceByIds")
    @ApiOperation(value = "", tags = "手动触发根据商品id更新批发商品sku价格")
    public void triggerRefreshWholesaleGoodsPriceByIds(@RequestBody List<Long> allGoodsIds, @RequestParam("show") Integer show) {
        LogUtils.info(log, "手动触发根据商品id更新批发商品sku价格 show:{}, allGoodsIds:{}", show, allGoodsIds);

        if (CollectionUtils.isEmpty(allGoodsIds)) {
            LogUtils.info(log, "手动触发根据商品id更新批发商品sku价格 return");
            return;
        }

        int batch = 1;

        for (List<Long> goodsIds : ListUtils.partition(allGoodsIds, 100)) {
            LogUtils.info(log, "手动触发根据商品id更新批发商品sku价格 ===> init ing... batch:{}", batch);
            List<Goods> goodsList = goodsService.lambdaQuery()
                    .in(Goods::getId, goodsIds)
                    .eq(Goods::getIsDel, 0)
                    .select(Goods::getId)
                    .list();
            if (CollectionUtils.isEmpty(goodsList)) {
                LogUtils.info(log, "手动触发根据商品id更新批发商品sku价格 商品列表为空 ===> stop");
                continue;
            }

            goodsList.forEach(goods -> {
                try {
                    goodsCoreService.refreshWholesaleGoodsPrice(goods.getId(), 1, 1 == show);
                } catch (CustomException e) {
                    LogUtils.info(log, "手动触发根据商品id更新批发商品sku价格，自定义异常:{}, goodsId:{}", e.getBusinessResultCode().getMsg(), goods.getId(), e);
                } catch (Exception e) {
                    LogUtils.info(log, "手动触发根据商品id更新批发商品sku价格，异常:{}, goodsId:{}", e.getMessage(), goods.getId(), e);
                }
            });

            batch++;
        }
    }

    @NoLogin
    @PostMapping("triggerRefreshGoodsShopName")
    @ApiOperation(value = "", tags = "刷新商品的店铺名称")
    public void triggerRefreshGoodsShopName(@RequestBody List<Long> shopIds) {
        LogUtils.info(log, "刷新商品的店铺名称 shopIds:{}", shopIds);
        if (CollectionUtils.isEmpty(shopIds)) {
            LogUtils.info(log, "刷新商品的店铺名称 return");
            return;
        }

        for (FaMerchantsApply faMerchantsApply : faMerchantsApplyService.listByIds(shopIds)) {
            LogUtils.info(log, "刷新商品的店铺名称 shopId:{}, shopName:{}", faMerchantsApply.getId(), faMerchantsApply.getShopname());

            Long startGoodsId = 0L;
            int batch = 1;

            List<Goods> updateGoodsList = Lists.newArrayList();
            while (true) {
                IPage<Goods> page = goodsService.lambdaQuery()
                        .gt(Goods::getId, startGoodsId)
                        .eq(Goods::getShopId, faMerchantsApply.getId())
                        .ne(Goods::getShopName, faMerchantsApply.getShopname())
                        .eq(Goods::getIsDel, 0)
                        .orderByAsc(Goods::getId)
                        .select(Goods::getId)
                        .page(new Page<>(1, 100));
                if (CollectionUtils.isEmpty(page.getRecords())) {
                    break;
                }
                LogUtils.info(log, "刷新商品的店铺名称 shopId:{}, batch:{}, startGoodsId:{}", faMerchantsApply.getId(), batch, startGoodsId);

                List<Goods> records = page.getRecords();
                startGoodsId = records.stream().map(Goods::getId).max(Long::compareTo).orElse(99999999L);
                records.stream()
                        .peek(goods -> goods.setShopName(faMerchantsApply.getShopname()))
                        .forEach(updateGoodsList::add);
                batch++;
            }
            LogUtils.info(log, "刷新商品的店铺名称 shopId:{}, updateGoodsList.size:{}", faMerchantsApply.getId(), updateGoodsList.size());

            if (CollectionUtils.isNotEmpty(updateGoodsList)) {
                goodsService.updateBatchById(updateGoodsList);

                List<Long> goodsIds = updateGoodsList.stream().map(Goods::getId).collect(Collectors.toList());
                HashMap<String, Object> map = Maps.newHashMap();
                map.put("shopName", faMerchantsApply.getShopname());
                TermsQueryBuilder queryBuilder = QueryBuilders.termsQuery("id", goodsIds);
                BulkByScrollResponse bulkByScrollResponse = goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), queryBuilder, map);
                if (CollectionUtils.isNotEmpty(bulkByScrollResponse.getBulkFailures())) {
                    for (BulkItemResponse.Failure bulkFailure : bulkByScrollResponse.getBulkFailures()) {
                        LogUtils.info(log, "刷新商品的店铺名称 es执行异常:{}", bulkFailure.getMessage());
                    }
                }
            }
            LogUtils.info(log, "刷新商品的店铺名称 shopId:{}, over~~~", faMerchantsApply.getId());
        }
    }


    @NoLogin
    @GetMapping("initListingTemplateBasePrice")
    @ApiOperation(value = "", tags = "初始化listing模板的基准价")
    public void initListingTemplateBasePrice() {
        LogUtils.info(log, "初始化listing模板的基准价 start");
        List<ListingInfo> listingInfoList = Lists.newArrayList();
        long pageNow = 1;
        long pageEnd = 10000;

        while (pageNow <= pageEnd) {
            IPage<ListingInfo> page = listingInfoService.lambdaQuery()
                    .eq(ListingInfo::getIsDel, 0)
//                    .isNull(ListingInfo::getBasePrice)
                    .orderByAsc(ListingInfo::getCreateTime)
                    .select(ListingInfo::getId, ListingInfo::getGoodsId)
                    .page(new Page<>(pageNow, 100));
            List<ListingInfo> records = page.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            listingInfoList.addAll(records);

            pageEnd = page.getPages();
            pageNow++;
        }
        LogUtils.info(log, "初始化listing模板的基准价 listing模板数量:{}", listingInfoList.size());

        if (CollectionUtils.isEmpty(listingInfoList)) {
            LogUtils.info(log, "初始化listing模板的基准价 模板数量为空，返回1");
            return;
        }


        List<Long> goodsIds = listingInfoList.stream().map(ListingInfo::getGoodsId).collect(Collectors.toList());
        Map<Long, Goods> goodsMap = goodsService.queryGoodsByIds(goodsIds).stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

        Map<String, VatDto> vatMap = goodsEsService.getVatMap();

        for (ListingInfo listingInfo : listingInfoList) {
            Long goodsId = listingInfo.getGoodsId();
            Goods goods = goodsMap.get(goodsId);
            if (goods == null) {
                LogUtils.info(log, "初始化listing模板的基准价 查不到商品信息goodsId:{}, listingId:{} 跳过", goodsId, listingInfo.getId());
                continue;
            }


            //动态调价
            BigDecimal vatRate;
            GoodsVatConfig goodsVatConfig = goodsVatConfigService.lambdaQuery().eq(GoodsVatConfig::getGoodsId, goodsId).eq(GoodsVatConfig::getEffectStatus, 1).one();
            if (goodsVatConfig != null) {
                vatRate = goodsVatConfig.getVat();
                LogUtils.info(log, "初始化listing模板的基准价 存在商品级vat配置 源goodsId:{}, vatRate:{}", goodsId, vatRate);
            } else {
                List<Long> negativeTagIds = goodsExtConfigService.lambdaQuery()
                        .eq(GoodsExtConfig::getGoodsId, goodsId)
                        .in(GoodsExtConfig::getTagId, Arrays.asList(40, 50))
                        .eq(GoodsExtConfig::getIsDel, 0)
                        .list()
                        .stream().map(GoodsExtConfig::getTagId).collect(Collectors.toList());

                GoodsExtConfigModel goodsExtConfigModel = new GoodsExtConfigModel();
                goodsExtConfigModel.setTagIds(negativeTagIds);
                vatRate = goodsEsService.getVatRate(
                        new VatCondition()
                                .setGoodsId(goodsId)
                                .setCountry(SaleableCountryEnum.DE.name())
                                .setShopId(goods.getShopId())
                                .setCategoryId(goods.getCategoryId())
                                .setDeliveryType(0)
                                .setGoodsExtConfigModel(goodsExtConfigModel)
                                .setVatMap(vatMap));
                LogUtils.info(log, "初始化listing模板的基准价 走默认配置 源goodsId:{}, vatRate:{}，含负向标签:{}", goodsId, vatRate, CollectionUtils.isNotEmpty(negativeTagIds));
            }

            listingInfo.setBasePrice(goodsEsService.getVatPrice(vatRate, goods.getMaxPrice()));
            listingInfo.setUpdateTime(LocalDateTime.now());
        }
        listingInfoService.updateBatchById(listingInfoList);

    }

    @NoLogin
    @GetMapping("initGoodsEditInfoDetail")
    @ApiOperation(value = "", tags = "刷新修改历史content")
    public void initGoodsEditInfoDetail(@RequestParam("minEditId") Long minEditId) {
        LogUtils.info(log, "刷新修改历史content start ... minEditId:{}", minEditId);
        minEditId = minEditId == null ? 0 : minEditId;
        int batch = 1;

        while (true) {
            LogUtils.info(log, "刷新修改历史content batch:{}, minEditId:{}", batch, minEditId);
            IPage<GoodsEditInfo> page = goodsEditInfoService.lambdaQuery()
                    .gt(GoodsEditInfo::getId, minEditId)
                    .select(GoodsEditInfo::getId, GoodsEditInfo::getGoodsId, GoodsEditInfo::getContent)
                    .orderByAsc(GoodsEditInfo::getId)
                    .page(new Page<>(1, 1000));
            List<GoodsEditInfo> records = page.getRecords();

            if (CollectionUtils.isEmpty(records)) {
                LogUtils.info(log, "刷新修改历史content 查询结果为空，over");
                break;
            }

            List<GoodsEditInfoDetail> existEditDetails = goodsEditInfoDetailService.lambdaQuery()
                    .in(GoodsEditInfoDetail::getEditId, records.stream().map(GoodsEditInfo::getId).collect(Collectors.toList()))
                    .list();
            List<Long> existEditId = existEditDetails.stream().map(GoodsEditInfoDetail::getEditId).collect(Collectors.toList());
            Map<Long, GoodsEditInfoDetail> editContentMap = existEditDetails.stream().collect(Collectors.toMap(GoodsEditInfoDetail::getEditId, Function.identity(), (v1, v2) -> v1));

            List<GoodsEditInfoDetail> saveList = records.stream()
                    .filter(goodsEditInfo -> goodsEditInfo.getType().equals(GoodsEditTypeEnums.DETAIL.getCode()) || !existEditId.contains(goodsEditInfo.getId()))
                    .map(goodsEditInfo -> {
                        GoodsEditInfoDetail detail = editContentMap.getOrDefault(goodsEditInfo.getId(), new GoodsEditInfoDetail());
                        detail.setEditId(goodsEditInfo.getId());
                        detail.setGoodsId(goodsEditInfo.getGoodsId());
                        detail.setContent(goodsEditInfo.getContent());
                        return detail;
                    })
                    .collect(Collectors.toList());
            LogUtils.info(log, "刷新修改历史content saveList.size:{}", saveList.size());
            if (CollectionUtils.isNotEmpty(saveList)) {
                goodsEditInfoDetailService.saveOrUpdateBatch(saveList);
            }

            minEditId = records.stream().map(GoodsEditInfo::getId).max(Long::compareTo).orElse(999999999L);
            batch++;
        }
    }

    @NoLogin
    @PostMapping("/resetGoodsMove")
    @ApiOperation(value = "重置爬虫任务状态并发送mq")
    public Result<Boolean> resetGoodsMove(@RequestBody List<Long> ids) {
        CheckUtils.isEmpty(ids, ProductResultCode.PARAMETER_ID_ERROR);
        List<GoodsMove> goodsMoves = Lists.newArrayList(goodsMoveService.listByIds(ids));

        for (GoodsMove goodsMove : goodsMoves) {
            goodsMove.setErrorMsg("");
            goodsMove.setStatus(0);
            goodsMove.setSyncCounts(0);
        }
        goodsMoveService.updateBatchById(goodsMoves);

        for (GoodsMove goodsMove : goodsMoves) {
            Notice1688Dto notice1688Dto = new Notice1688Dto();
            notice1688Dto.setGoodsMoveId(goodsMove.getId());
            notice1688Dto.setGoodsUrl(goodsMove.getGoodsUrl());
            notice1688Dto.setSourceType(Integer.parseInt(goodsMove.getSource()));
            mqSender.send("TOPIC_SPIDER_GOODS_1688_RECEIVE", notice1688Dto);
        }

        return Result.success(Boolean.TRUE);
    }

    @NoLogin
    @PostMapping("/testEsUpdateObjectByQuery")
    public Result<Boolean> testEsUpdateObjectByQuery(@RequestBody Map<String, Object> map) {
        Object goodsIdObj = map.get("goodsId");
        CheckUtils.notNull(goodsIdObj, ProductResultCode.PARAMETER_ID_ERROR);

        Long goodsId = Long.parseLong(goodsIdObj.toString());
        LogUtils.info(log, "testEsUpdateObjectByQuery ===> goodsId:{}", goodsId);

        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("id", goodsId);
        map.remove("goodsId");
        if (MapUtils.isEmpty(map)) {
            CheckUtils.check(true, ProductResultCode.PARAMETER_ERROR);
        }
        goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), termQueryBuilder, map);
//        if (CollectionUtils.isNotEmpty(bulkByScrollResponse.getBulkFailures())) {
//            for (BulkItemResponse.Failure bulkFailure : bulkByScrollResponse.getBulkFailures()) {
//                LogUtils.info(log, "testEsUpdateObjectByQuery ===> es执行异常:{}", bulkFailure.getMessage());
//            }
//        }
        return Result.success(Boolean.TRUE);
    }

    @NoLogin
    @GetMapping("triggerSyncListingFollowGoods")
    public void triggerSyncListingFollowGoods(@RequestParam("listingId") Long listingId) {
        updateGoodsInfoCoreService.triggerSyncListingFollowGoods(listingId);
    }

    @NoLogin
    @PostMapping("/queryBQ")
    public Result<List<HotGoodsMonitorDto>> queryBQ() {
        String sqlPrepare = "select \n" +
                " aa.create_day,\n" +
                " aa.goods_id,\n" +
                "max(aa.category_id) as category_id,\n" +
                "sum(deal_sum_value) pay_money,\n" +
                "sum(case when aa.goods_id=bb.goods_id then bb.us_amount else 0 end) as us_amount,\n" +
                "sum(aa.deal_sum_cnt) as deal_sum_cnt,\n" +
                "sum(case when aa.goods_id=bb.goods_id then bb.deal_cnt else 0 end) as deal_us_cnt\n" +
                "from\n" +
                " DMM.dmm_goods_action_day aa\n" +
                "  left join\n" +
                " (select\n" +
                " date(a.pay_time) as create_day,\n" +
                "  a.goods_id,\n" +
                " sum(a.amount)-sum(a.total_discount_amount    ) as us_amount,\n" +
                " count(distinct a.show_detail_id) as deal_cnt\n" +
                " from\n" +
                "`DWD.dwd_order_detail_d`a\n" +
                "  left join `DWD.dwd_order_info_d` b on a.order_id=b.order_id\n" +
                "  where  \n" +
                "date(a.pay_time)='%s'\n" +
                "   AND b.country_name like ('%United States%')\n" +
                "  and a.pay_time is not null\n" +
                "  and a.device_code not in(\n" +
                "    select device_code from `events.test_device_code` where device_code is not null\n" +
                "  )\n" +
                "  and a.user_id not in(\n" +
                "     select user_id from `events.test_device_code` where user_id is not null\n" +
                "  )\n" +
                "  group by 1,2\n" +
                " )bb on aa.create_day=bb.create_day and aa.goods_id=bb.goods_id\n" +
                " where \n" +
                " aa.create_day='%s'\n" +
                " and is_show=1 and is_del=0\n" +
                " group by 1,2\n" +
                " order by us_amount desc;";


        //dmm_goods_action_day create_day是n-1的，不会存在当天数据
        String date = LocalDate.now().minusDays(1).toString();
        String sql = String.format(sqlPrepare, Lists.newArrayList(date, date).toArray());
        TableResult result = bigQueryUtil.query(sql);
        List<HotGoodsMonitorDto> hotGoodsMonitorDtoList = BigQueryUtil.toEntryList(result, HotGoodsMonitorDto.class);
        return Result.success(hotGoodsMonitorDtoList);
    }

    @NoLogin
    @PostMapping("testHotGoodsMonitorOfUs")
    public void testHotGoodsMonitorOfUs() {
        String sqlPrepare2 = "select \n" +
                " aa.create_day,\n" +
                " aa.goods_id,\n" +
                "max(aa.category_id) as category_id,\n" +
                "sum(deal_sum_value) pay_money,\n" +
                "sum(case when aa.goods_id=bb.goods_id then bb.us_amount else 0 end) as us_amount,\n" +
                "sum(aa.deal_sum_cnt) as deal_sum_cnt,\n" +
                "sum(case when aa.goods_id=bb.goods_id then bb.deal_cnt else 0 end) as deal_us_cnt\n" +
                "from\n" +
                " DMM.dmm_goods_action_day aa\n" +
                "  left join\n" +
                " (select\n" +
                " date(a.pay_time) as create_day,\n" +
                "  a.goods_id,\n" +
                " sum(a.amount)-sum(a.total_discount_amount    ) as us_amount,\n" +
                " count(distinct a.show_detail_id) as deal_cnt\n" +
                " from\n" +
                "`DWD.dwd_order_detail_d`a\n" +
                "  left join `DWD.dwd_order_info_d` b on a.order_id=b.order_id\n" +
                "  where  \n" +
                "date(a.pay_time)=\"%s\" \n" +
                "   AND b.country_name like ('%%United States%%')\n" +
                "  and a.pay_time is not null\n" +
                "  and a.device_code not in(\n" +
                "    select device_code from `events.test_device_code` where device_code is not null\n" +
                "  )\n" +
                "  and a.user_id not in(\n" +
                "     select user_id from `events.test_device_code` where user_id is not null\n" +
                "  )\n" +
                "  group by 1,2\n" +
                " )bb on aa.create_day=bb.create_day and aa.goods_id=bb.goods_id\n" +
                " where \n" +
                " aa.create_day=\"%s\" \n" +
                " and is_show=1 and is_del=0\n" +
                " group by 1,2\n" +
                " order by deal_us_cnt desc\n" +
                " limit 1000;";


        //dmm_goods_action_day create_day是n-1的，不会存在当天数据
        String date = LocalDate.now().minusDays(1).toString();
        String sql2 = String.format(sqlPrepare2, Lists.newArrayList(date, date).toArray());
        TableResult result2 = bigQueryUtil.query(sql2);
        List<HotGoodsMonitorDto> hotGoodsMonitorDtoList2 = BigQueryUtil.toEntryList(result2, HotGoodsMonitorDto.class);

        List<Long> goodsIds = hotGoodsMonitorDtoList2.stream().map(HotGoodsMonitorDto::getGoodsId).collect(Collectors.toList());
        List<Goods> goodsList = goodsService.queryGoodsByIds(goodsIds);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

        for (HotGoodsMonitorDto hotGoodsMonitorDto : hotGoodsMonitorDtoList2) {
            Goods goods = goodsMap.get(hotGoodsMonitorDto.getGoodsId());
            boolean containsUs = goods.getCountry().contains("US");
            log.info("goodsId:{}, usAmount:{}, dealUsCnt:{}, country:{}, containsUs:{}", hotGoodsMonitorDto.getGoodsId(), hotGoodsMonitorDto.getUsAmount(), hotGoodsMonitorDto.getDealUsCnt(), goods.getCountry(), containsUs);
            if (containsUs) {
                log.info("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
            }
        }
    }


    @NoLogin
    @PostMapping("/fixGoodsItemPvalue")
    @ApiOperation(value = "fix商品sku历史遗留的pvalue格式问题")
    public Result<Boolean> fixGoodsItemPvalue(@RequestBody List<Long> ids) {
        CheckUtils.isEmpty(ids, ProductResultCode.PARAMETER_ID_ERROR);
        List<Goods> goodsList = goodsService.queryGoodsByIds(ids);
        List<GoodsItem> goodsItemList = goodsItemService.queryGoodsIdsList(ids);

        List<Long> productIds = goodsList.stream().map(Goods::getProductId).collect(Collectors.toList());
        List<ProductSku> productSkuList = productSkuService.queryProductSkuByProductIds(productIds);


        List<ProductSku> productSkuUpdateList = Lists.newArrayList();
        for (ProductSku productSku : productSkuList) {
            if (productSku.getName().contains("-") && productSku.getName().replace("-", "").equals(productSku.getPvalueDesc())) {
                productSku.setPvalueDesc(productSku.getName().replace("-","&gt;"));
                productSkuUpdateList.add(productSku);
            }
        }
        if (CollectionUtils.isNotEmpty(productSkuUpdateList)) {
            productSkuService.updateBatchById(productSkuUpdateList);
        }

        List<GoodsItem> goodsItemUpdateList = Lists.newArrayList();
        for (GoodsItem goodsItem : goodsItemList) {
            if (goodsItem.getName().contains("-") && goodsItem.getName().replace("-", "").equals(goodsItem.getPvalueDesc())) {
                goodsItem.setPvalueDesc(goodsItem.getName().replace("-","&gt;"));
                goodsItemUpdateList.add(goodsItem);
            }
        }
        if (CollectionUtils.isNotEmpty(productSkuUpdateList)) {
            goodsItemService.updateBatchById(goodsItemUpdateList);
        }

        ids.forEach(id -> {
            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(id);
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("fix商品sku历史遗留的pvalue格式问题(手动)");
            goodsSyncModel.setSourceService("vp");
            mqSender.send("SYNC_GOODS_TOPIC_UPDATE", goodsSyncModel);
        });
        return Result.success(Boolean.TRUE);
    }

    @NoLogin
    @PostMapping("/fixTemplateGoodsIsShow")
    @ApiOperation(value = "fix模板商品在架状态")
    public Result<Boolean> fixTemplateGoodsIsShow(@RequestParam("startGoodsId") Long startGoodsId) {
        LogUtils.info(log, "fix模板商品在架状态 start");
        startGoodsId = startGoodsId == null ? 0L : startGoodsId;
        Long endGoodsId = startGoodsId + 5000000;
        int batch = 1;

        Map<String, Object> map = new HashMap<>();
        map.put("isShow", GoodsIsShowEnums.TEMPLATE.getType().toString());

        while (true) {
            IPage<Goods> page = goodsService.lambdaQuery()
                    .gt(Goods::getId, startGoodsId)
                    .le(Goods::getId, endGoodsId)
                    .in(Goods::getShopId, Arrays.asList(11L, 13L))
                    .ne(Goods::getIsShow, GoodsIsShowEnums.TEMPLATE.getType().toString())
                    .eq(Goods::getIsDel, 0)
                    .orderByAsc(Goods::getId)
                   .page(new Page<>(1, 100));
            if (CollectionUtils.isEmpty(page.getRecords())) {
                break;
            }

            List<Goods> records = page.getRecords();
            LogUtils.info(log, "fix模板商品在架状态 batch:{}, startGoodsId:{} =======>  goodsIds:{}", batch, startGoodsId, records.stream().map(Goods::getId).collect(Collectors.toList()));

            startGoodsId = records.stream().map(Goods::getId).max(Long::compareTo).orElse(99999999L);
            batch++;

            records.forEach(goods -> goods.setIsShow(GoodsIsShowEnums.TEMPLATE.getType().toString()));
            goodsService.updateBatchById(records);

            for (Goods goods : records) {
                BulkByScrollResponse bulkByScrollResponse = goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), QueryBuilders.termQuery("id", goods.getId()), map);
                if (CollectionUtils.isNotEmpty(bulkByScrollResponse.getBulkFailures())) {
                    for (BulkItemResponse.Failure bulkFailure : bulkByScrollResponse.getBulkFailures()) {
                        LogUtils.info(log, "fix模板商品在架状态 es执行异常:{}", bulkFailure.getMessage());
                    }
                }
            }

        }
        LogUtils.info(log, "fix模板商品在架状态 end");
        return Result.success(null);
    }

    @NoLogin
    @GetMapping("fixGoodsEditSpecialTag")
    @ApiOperation(value = "", tags = "fix商品修改历史特殊标签")
    public void fixGoodsEditSpecialTag(@RequestParam("minId") Long minId) {
        LogUtils.info(log, "fix商品修改历史特殊标签 start ... minId:{}", minId);
        minId = minId == null ? 26000000 : minId;
        Long maxId = minId + 1000000;
        int batch = 1;

        List<String> tags = Arrays.asList("flashDeal", "listing", "满减大促", "运营选品");

        while (true) {
            LogUtils.info(log, "fix商品修改历史特殊标签 batch:{}, minEditId:{}", batch, minId);
            IPage<GoodsEditInfo> page = goodsEditInfoService.lambdaQuery()
                    .gt(GoodsEditInfo::getId, minId)
                    .le(GoodsEditInfo::getId, maxId)
                    .select(GoodsEditInfo::getId, GoodsEditInfo::getSpecialTag)
                    .orderByAsc(GoodsEditInfo::getId)
                    .page(new Page<>(1, 1000));
            List<GoodsEditInfo> records = page.getRecords();

            if (CollectionUtils.isEmpty(records)) {
                LogUtils.info(log, "fix商品修改历史特殊标签 查询结果为空，over");
                break;
            }


            List<GoodsEditInfo> updateList = Lists.newArrayList();
            for (GoodsEditInfo editInfo : records) {
                if (StringUtils.isNotBlank(editInfo.getSpecialTag())) {
                    String newSpecialTag = Arrays.stream(editInfo.getSpecialTag().split(","))
                            .filter(tags::contains)
                            .sorted()
                            .collect(Collectors.joining(","));
                    if (!newSpecialTag.equals(editInfo.getSpecialTag())) {
                        editInfo.setSpecialTag(newSpecialTag);
                        updateList.add(editInfo);
                    }
                }
            }

            LogUtils.info(log, "fix商品修改历史特殊标签 batch:{}, updateList.size:{}", batch, updateList.size());
            if (CollectionUtils.isNotEmpty(updateList)) {
                goodsEditInfoService.updateBatchById(updateList);
            }

            minId = records.stream().map(GoodsEditInfo::getId).max(Long::compareTo).orElse(999999999L);
            batch++;
        }
    }


    @NoLogin
    @GetMapping("fixRepeatGoodsFreight")
    @ApiOperation(value = "", tags = "fix商品重复国家运费")
    public void fixRepeatGoodsFreight(@RequestParam("goodsId") Long goodsId) {
        LogUtils.info(log, "fix商品重复国家运费 goodsId:{}", goodsId);
        CheckUtils.notNull(goodsId, ProductResultCode.PARAMETER_GOODS_ID_NULL);

        List<GoodsFreight> allGoodsFreightList = goodsFreightService.lambdaQuery()
                .eq(GoodsFreight::getGoodsId, goodsId)
                .eq(GoodsFreight::getIsDel, 0)
                .and(wrapper ->
                        wrapper.eq(GoodsFreight::getGradientNum, 1)
                                .or()
                                .isNull(GoodsFreight::getGradientNum))
                .list();
        Map<String, List<GoodsFreight>> countryGroupMap = allGoodsFreightList.stream().collect(Collectors.groupingBy(GoodsFreight::getCode));

        List<GoodsFreight> updateList = Lists.newArrayList();

        for (Map.Entry<String, List<GoodsFreight>> entry : countryGroupMap.entrySet()) {
            List<GoodsFreight> sameCountryList = entry.getValue();
            if (sameCountryList.size() > 1) {
                Long maxId = sameCountryList.stream().map(GoodsFreight::getId).max(Long::compareTo).orElse(0L);
                sameCountryList.stream()
                        .filter(goodsFreight -> !goodsFreight.getId().equals(maxId))
                        .forEach(goodsFreight -> {
                            goodsFreight.setIsDel(1);
                            updateList.add(goodsFreight);
                        });
            }
        }

        LogUtils.info(log, "fix商品重复国家运费 goodsId:{}, 删除size:{}", goodsId, updateList.size());
        if (CollectionUtils.isNotEmpty(updateList)) {
            goodsFreightService.updateBatchById(updateList);
        }
    }


    @NoLogin
    @GetMapping("fixInValidMainImageGoods")
    @ApiOperation(value = "", tags = "处理主图url未转化商品")
    public void fixInValidMainImageGoods() {
        goodsCoreService.fixInValidMainImageGoods();
    }

    @NoLogin
    @GetMapping("initHotGoodsMonitorTagType")
    @ApiOperation(value = "", tags = "初始化热卖商品正负向标签")
    public void initHotGoodsMonitorTagType() {
        LogUtils.info(log, "初始化热卖商品正负向标签 --> start");
        Long minId = 2021487L;
        int batch = 1;
        while (true) {
            List<HotGoodsMonitor> list = hotGoodsMonitorService.lambdaQuery()
                    .gt(HotGoodsMonitor::getId, minId)
                    .eq(HotGoodsMonitor::getIsDel, 0)
                    .isNull(HotGoodsMonitor::getTagType)
                    .select(HotGoodsMonitor::getId, HotGoodsMonitor::getGoodsId)
                    .orderByAsc(HotGoodsMonitor::getId)
                    .last("limit 1000")
                    .list();
            LogUtils.info(log, "初始化热卖商品正负向标签 --> batch:{}, minId:{}", batch, minId);

            if (CollectionUtils.isEmpty(list)) {
                LogUtils.info(log,"初始化热卖商品正负向标签 --> 查询为空，结束");
                break;
            }

            List<Long> goodsIds = list.stream().map(HotGoodsMonitor::getGoodsId).distinct().collect(Collectors.toList());
            List<Long> negativeGoodsIds = goodsExtConfigCoreService.selectNegativeGoodsIds(goodsIds);
            for (HotGoodsMonitor hotGoodsMonitor : list) {
                hotGoodsMonitor.setTagType(negativeGoodsIds.contains(hotGoodsMonitor.getGoodsId()) ? 0 : 1);
            }
            boolean success = hotGoodsMonitorService.updateBatchById(list);
            LogUtils.info(log, "初始化热卖商品正负向标签 --> success:{}, negativeGoodsIds.size:{}", success, negativeGoodsIds.size());

            minId = list.stream().map(HotGoodsMonitor::getId).max(Long::compareTo).orElse(null);
            batch++;
        }
    }

    @NoLogin
    @GetMapping("fixListingAndChanceGoodsCategorySync")
    @ApiOperation(value = "", tags = "处理listing和机会商品类目信息未同步问题")
    public void fixListingAndChanceGoodsCategorySync() {
        Long startGoodsId = 0L;
        Page<ListingInfo> listingInfoPage = new Page<>(1, 200);
        while (true) {
            LogUtils.info(log, "fixListingAndChanceGoodsCategorySync listing处理 startGoodsId:{}", startGoodsId);
            IPage<ListingInfo> page = listingInfoService.lambdaQuery()
                    .gt(ListingInfo::getGoodsId, startGoodsId)
                    .select(ListingInfo::getId, ListingInfo::getCategoryId, ListingInfo::getGoodsId,ListingInfo::getIsDel)
                    .page(listingInfoPage);
            if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
                LogUtils.info(log, "fixListingAndChanceGoodsCategorySync listing搜索结果为空，break");
                break;
            }

            List<ListingInfo> listingInfoList = page.getRecords();
            List<Long> goodsIds = listingInfoList.stream().map(ListingInfo::getGoodsId).collect(Collectors.toList());
            Map<Long, Goods> goodsMap = goodsService.queryGoodsByIds(goodsIds).stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

            List<Goods> updateGoodsList = Lists.newArrayList();
            List<ListingInfo> updateListingList = Lists.newArrayList();
            for (ListingInfo listingInfo : listingInfoList) {
                Goods goods = goodsMap.get(listingInfo.getGoodsId());
                if (goods == null) {
                    continue;
                }
                if (!listingInfo.getCategoryId().equals(goods.getCategoryId())) {
                    listingInfo.setCategoryId(goods.getCategoryId());
                    updateListingList.add(listingInfo);
                }
                if (listingInfo.getIsDel() == 1 && goods.getIsDel() == 0) {
                    goods.setIsDel(1);
                    goods.setUpdateTime(LocalDateTime.now());
                    updateGoodsList.add(goods);
                }
            }
            LogUtils.info(log, "fixListingAndChanceGoodsCategorySync listing处理 listingInfo.size:{}, goods.size:{}", updateListingList.size(), updateGoodsList.size());

            if (CollectionUtils.isNotEmpty(updateListingList)) {
                listingInfoService.updateBatchById(updateListingList);
            }
            if (CollectionUtils.isNotEmpty(updateGoodsList)) {
                goodsService.updateBatchById(updateGoodsList);

                CustomizedGoodsSyncModel goodsSyncModel = new CustomizedGoodsSyncModel();
                goodsSyncModel.setGoodsIds(updateGoodsList.stream().map(Goods::getId).distinct().collect(Collectors.toList()));
                goodsSyncModel.setValues(Collections.singletonList(CustomizedGoodsSyncEnums.GOODS_STATUS.getVal()));
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("处理listing和机会商品类目信息未同步问题(手动)");
                goodsSyncModel.setSourceService("vp");
                mqSender.send("CUSTOMIZED_SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel));
            }
            startGoodsId = goodsIds.stream().max(Long::compareTo).orElse(null);
        }


        Long chanceGoodsTemplateStartId = 0L;
        Page<ChanceGoodsTemplate> chanceGoodsTemplatePage = new Page<>(1, 200);
        while (true) {
            LogUtils.info(log, "fixListingAndChanceGoodsCategorySync 机会商品模板处理 chanceGoodsTemplateStartId:{}", chanceGoodsTemplateStartId);
            IPage<ChanceGoodsTemplate> page = chanceGoodsTemplateService.lambdaQuery()
                    .ge(ChanceGoodsTemplate::getId, chanceGoodsTemplateStartId)
                    .select(ChanceGoodsTemplate::getId, ChanceGoodsTemplate::getCategoryId, ChanceGoodsTemplate::getGoodsId)
                    .page(chanceGoodsTemplatePage);
            if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
                LogUtils.info(log, "fixListingAndChanceGoodsCategorySync 机会商品模板搜索结果为空，break");
                break;
            }

            List<ChanceGoodsTemplate> chanceGoodsTemplateList = page.getRecords();
            List<Long> goodsIds = chanceGoodsTemplateList.stream().map(ChanceGoodsTemplate::getGoodsId).collect(Collectors.toList());
            Map<Long, Goods> goodsMap = goodsService.queryGoodsByIds(goodsIds).stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

            List<Goods> updateGoodsList = Lists.newArrayList();
            List<ChanceGoodsTemplate> updateTemplateList = Lists.newArrayList();
            for (ChanceGoodsTemplate chanceGoodsTemplate : chanceGoodsTemplateList) {
                Long goodsId = chanceGoodsTemplate.getGoodsId();
                Goods goods = goodsMap.get(goodsId);
                if (goods == null) {
                    continue;
                }

                if (!chanceGoodsTemplate.getCategoryId().equals(goods.getCategoryId())) {
                    chanceGoodsTemplate.setCategoryId(goods.getCategoryId());
                    updateTemplateList.add(chanceGoodsTemplate);
                }

                if (chanceGoodsTemplate.getIsDel() == 1 && goods.getIsDel() == 0) {
                    goods.setIsDel(1);
                    goods.setUpdateTime(LocalDateTime.now());
                    updateGoodsList.add(goods);
                }
            }
            LogUtils.info(log, "fixListingAndChanceGoodsCategorySync listing处理 chanceGoodsTemplate.size:{}, goods.size:{}", updateTemplateList.size(), updateGoodsList.size());

            if (CollectionUtils.isNotEmpty(updateTemplateList)) {
                chanceGoodsTemplateService.updateBatchById(updateTemplateList);
            }
            if (CollectionUtils.isNotEmpty(updateGoodsList)) {
                goodsService.updateBatchById(updateGoodsList);

                CustomizedGoodsSyncModel goodsSyncModel = new CustomizedGoodsSyncModel();
                goodsSyncModel.setGoodsIds(updateGoodsList.stream().map(Goods::getId).distinct().collect(Collectors.toList()));
                goodsSyncModel.setValues(Collections.singletonList(CustomizedGoodsSyncEnums.GOODS_STATUS.getVal()));
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("处理机会商品模板未同步问题(手动)");
                goodsSyncModel.setSourceService("vp");
                mqSender.send("CUSTOMIZED_SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel));
            }
            chanceGoodsTemplateStartId = chanceGoodsTemplateList.stream().map(ChanceGoodsTemplate::getId).max(Long::compareTo).orElse(null);
        }

        LogUtils.info(log, "fixListingAndChanceGoodsCategorySync end");
    }

    @NoLogin
    @GetMapping("fixFileSyncRecordSuccessAndFailedCount")
    @ApiOperation(value = "", tags = "处理excel导入文件int成功失败商品数")
    public void fixFileSyncRecordSuccessAndFailedCount() {
        LogUtils.info(log, "处理excel导入文件int成功失败商品数 start");

        List<FileSyncRecord> fileSyncRecordList = fileSyncRecordService.lambdaQuery()
                .isNull(FileSyncRecord::getFailedGoodsCount)
                .select(FileSyncRecord::getId)
                .list();
        LogUtils.info(log, "处理excel导入文件int成功失败商品数 待处理任务数量:{}", fileSyncRecordList.size());

        if (CollectionUtils.isEmpty(fileSyncRecordList)) {
            return;
        }

        for (FileSyncRecord fileSyncRecord : fileSyncRecordList) {
            List<FileSyncRecordData> dataList = fileSyncRecordDataService.lambdaQuery()
                    .eq(FileSyncRecordData::getFileSyncRecordId, fileSyncRecord.getId())
                    .select(FileSyncRecordData::getStatus, FileSyncRecordData::getItemCode)
                    .list();
            Map<Integer, List<FileSyncRecordData>> statusGroupMap = dataList.stream().collect(Collectors.groupingBy(FileSyncRecordData::getStatus));

            List<FileSyncRecordData> failedList = statusGroupMap.get(-1);
            Integer failedCount = Math.toIntExact(CollectionUtils.isEmpty(failedList) ? 0 : failedList.stream().map(FileSyncRecordData::getItemCode).distinct().count());
            fileSyncRecord.setFailedGoodsCount(failedCount);

            List<FileSyncRecordData> successList = statusGroupMap.get(1);
            Integer successCount = Math.toIntExact(CollectionUtils.isEmpty(successList) ? 0 : successList.stream().map(FileSyncRecordData::getItemCode).distinct().count());
            fileSyncRecord.setSuccessGoodsCount(successCount);

            LogUtils.info(log, "处理excel导入文件int成功失败商品数 recordId:{}, successCount:{}, failedCount:{}", fileSyncRecord.getId(), successCount, failedCount);
        }

        for (List<FileSyncRecord> fileSyncRecords : Lists.partition(fileSyncRecordList, 1000)) {
            fileSyncRecordService.updateBatchById(fileSyncRecords);
            LogUtils.info(log, "处理excel导入文件int成功失败商品数 updateBatchById.size:{}", fileSyncRecords.size());
        }
        LogUtils.info(log, "处理excel导入文件int成功失败商品数 end");
    }


    @NoLogin
    @GetMapping("syncListingTdTag")
    @ApiOperation(value = "", tags = "同步listing的td标签")
    public void syncListingTdTag() {
        Long startGoodsId = 0L;
        Page<ListingInfo> listingInfoPage = new Page<>(1, 100);
        while (true) {
            LogUtils.info(log, "syncListingTdTag listing处理 startGoodsId:{}", startGoodsId);
            IPage<ListingInfo> page = listingInfoService.lambdaQuery()
                    .gt(ListingInfo::getGoodsId, startGoodsId)
                    .select(ListingInfo::getId, ListingInfo::getCategoryId, ListingInfo::getGoodsId, ListingInfo::getIsDel)
                    .page(listingInfoPage);
            if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
                LogUtils.info(log, "syncListingTdTag listing搜索结果为空，break");
                break;
            }

            List<ListingInfo> listingInfoList = page.getRecords();
            List<Long> sourceGoodsIds = listingInfoList.stream().map(ListingInfo::getSourceGoodsId).collect(Collectors.toList());

            Map<Long, List<GoodsTdTag>> goodsTdTagGroup = goodsTdTagService.lambdaQuery()
                    .in(GoodsTdTag::getGoodsId, sourceGoodsIds)
                    .list()
                    .stream().collect(Collectors.groupingBy(GoodsTdTag::getGoodsId));
            LogUtils.info(log, "syncListingTdTag ===> goodsTdTagGroup:{}", goodsTdTagGroup);

            if (MapUtils.isNotEmpty(goodsTdTagGroup)) {
                Set<Long> tdSourceGoodsIds = goodsTdTagGroup.keySet();

                List<ListingInfo> toSyncListingList = listingInfoList.stream().filter(listingInfo -> tdSourceGoodsIds.contains(listingInfo.getSourceGoodsId())).collect(Collectors.toList());
                List<Long> toSyncListingIds = toSyncListingList.stream().map(ListingInfo::getId).collect(Collectors.toList());

                Map<Long, List<ListingFollowGoods>> listingFollowGoodsGroup = listingFollowGoodsService.lambdaQuery()
                        .in(ListingFollowGoods::getListingId, toSyncListingIds)
                        .notIn(ListingFollowGoods::getGoodsId, sourceGoodsIds)
                        .eq(ListingFollowGoods::getIsDel, 0)
                        .list()
                        .stream().collect(Collectors.groupingBy(ListingFollowGoods::getListingId));

                List<GoodsTdTag> saveList = Lists.newArrayList();

                Map<Long, ListingInfo> sourceGoodsListingMap = listingInfoList.stream().collect(Collectors.toMap(ListingInfo::getSourceGoodsId, Function.identity(), (v1, v2) -> v1));
                for (Map.Entry<Long, List<GoodsTdTag>> entry : goodsTdTagGroup.entrySet()) {
                    Long sourceGoodsId = entry.getKey();
                    List<String> tagNames = entry.getValue().stream().map(GoodsTdTag::getTagName).distinct().collect(Collectors.toList());

                    ListingInfo listingInfo = sourceGoodsListingMap.get(sourceGoodsId);
                    if (listingInfo == null) {
                        continue;
                    }

                    for (String tagName : tagNames) {
                        GoodsTdTag goodsTdTag = new GoodsTdTag();
                        goodsTdTag.setGoodsId(listingInfo.getGoodsId());
                        goodsTdTag.setTagName(tagName);
                        goodsTdTag.setCreateTime(LocalDateTime.now());
                        goodsTdTag.setUpdateTime(LocalDateTime.now());
                        saveList.add(goodsTdTag);
                    }

                    List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsGroup.get(listingInfo.getId());
                    if (CollectionUtils.isNotEmpty(listingFollowGoodsList)) {
                        List<Long> followGoodsIds = listingFollowGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
                        List<GoodsTdTag> existTdTags = goodsTdTagService.lambdaQuery().in(GoodsTdTag::getGoodsId).in(GoodsTdTag::getTagName, tagNames).list();
                        Map<Long, List<GoodsTdTag>> existGoodsTdTagGroup = existTdTags.stream().collect(Collectors.groupingBy(GoodsTdTag::getGoodsId));

                        for (Long followGoodsId : followGoodsIds) {
                            List<GoodsTdTag> exitsGoodsTdTags = existGoodsTdTagGroup.get(followGoodsId);
                            List<String> existTagNames = CollectionUtils.isEmpty(exitsGoodsTdTags) ? Lists.newArrayList() : exitsGoodsTdTags.stream().map(GoodsTdTag::getTagName).collect(Collectors.toList());

                            for (String tagName : tagNames) {
                                if (existTagNames.contains(tagName)) {
                                    continue;
                                }

                                GoodsTdTag goodsTdTag = new GoodsTdTag();
                                goodsTdTag.setGoodsId(followGoodsId);
                                goodsTdTag.setTagName(tagName);
                                goodsTdTag.setCreateTime(LocalDateTime.now());
                                goodsTdTag.setUpdateTime(LocalDateTime.now());
                                saveList.add(goodsTdTag);
                            }
                        }
                    }
                }

                LogUtils.info(log, "syncListingTdTag ===> saveList:{}", saveList);
                if (CollectionUtils.isNotEmpty(saveList)) {
                    goodsTdTagService.saveBatch(saveList);
                }
            }

            startGoodsId = listingInfoList.stream().mapToLong(ListingInfo::getGoodsId).max().orElse(999999990);
        }



    }


    @NoLogin
    @PostMapping("triggerSendSyncShopChangeMQ")
    @ApiOperation(value = "", tags = "发送变更店铺类型mq")
    public void triggerSendSyncShopChangeMQ(@RequestBody BackendTopicShopChangeDto dto) {
        LogUtils.info(log, "发送变更店铺类型mq:{}", dto);
        mqSender.send("marketing_BACKEND_TOPIC_SHOP_CHANGE", dto);
    }


    @NoLogin
    @GetMapping("triggerRetry1688")
    public void triggerRetry1688(@RequestParam("goodsMoveId") Long goodsMoveId) {
        GoodsMove goodsMove = goodsMoveService.getById(goodsMoveId);
        LogUtils.info(log, "triggerRetry1688 goodsMove: {}", goodsMove);

        if (goodsMove == null) {
            LogUtils.info(log, "triggerRetry1688 爬虫记录有误，return 111");
            return;
        }

        if (StringUtils.isBlank(goodsMove.getCrawlData())) {
            LogUtils.info(log, "triggerRetry1688 爬虫记录有误，return 222");
            return;
        }

        if (goodsMove.getGoodsId() != null) {
            LogUtils.info(log, "triggerRetry1688 爬虫记录有误，return 333");
            return;
        }

        if (goodsMove.getStatus() == 2) {
            LogUtils.info(log, "triggerRetry1688 爬虫记录有误，return 444");
            return;
        }


        DataVO<For1688VO> vo = for1688Decoder.decode(goodsMove.getCrawlData());
        vo.setGoodsMove(goodsMove);
        ProductInfoInput productInfoInput = for1688Assembler.assembleGoods(vo);
        LogUtils.info(log, "爬虫处理 组装数据 goodsMoveId:{}, productInfoInput:{}", goodsMoveId, productInfoInput);
        Long goodsId = newAddGoodsCoreService.shopAddGoods(productInfoInput);

        goodsMoveService.lambdaUpdate()
                .set(GoodsMove::getGoodsId, goodsId)
                .set(GoodsMove::getStatus, 2)
                .set(GoodsMove::getErrorMsg, "")
                .set(GoodsMove::getSyncTime, LocalDateTime.now())
                .eq(GoodsMove::getId, goodsMoveId)
                .update();
    }

    @NoLogin
    @PostMapping("refresh1688Price")
    @ApiOperation(value = "刷新自营店铺1688爬虫商品价格",tags = "刷新自营店铺1688爬虫商品价格")
    public void refresh1688Price(@RequestBody Long minId){
        LogUtils.info(log, "刷新自营店铺1688爬虫商品价格 start minId:{}", minId);
//        if (id == null) {
//            return;
//        }
        List<Long> shopIds = faMerchantsApplyService.lambdaQuery()
                .notIn(FaMerchantsApply::getId, Arrays.asList(1015784, 1015806, 1015807, 1020494, 1020513))
                .eq(FaMerchantsApply::getDeliveryType, 3)
                .eq(FaMerchantsApply::getIsDel, 0)
                .select(FaMerchantsApply::getId)
                .list()
                .stream().map(FaMerchantsApply::getId).collect(Collectors.toList());
        minId = minId == null ? 0L : minId;

        BigDecimal profitRatio = new BigDecimal("0.25");
        BigDecimal refundRatio = new BigDecimal("0.1");

        while (true) {
            List<GoodsMove> goodsMoveList = goodsMoveService.lambdaQuery()
//                    .eq(id != null, GoodsMove::getId, id)
                    .gt(GoodsMove::getId, minId)
                    .in(GoodsMove::getShopId, shopIds)
                    .eq(GoodsMove::getStatus, 2)
                    .in(GoodsMove::getSource, Arrays.asList("2", "1688"))
                    .isNotNull(GoodsMove::getGoodsId)
                    .orderByAsc(GoodsMove::getId)
                    .last("limit 100")
                    .list();
            LogUtils.info(log, "刷新自营店铺1688爬虫商品价格 minId:{}, size:{}", minId, goodsMoveList.size());
            if (CollectionUtils.isEmpty(goodsMoveList)) {
                return;
            }

            List<Long> goodsIds = goodsMoveList.stream().map(goodsMove -> goodsMove.getGoodsId().longValue()).collect(Collectors.toList());
            List<Goods> allGoodsList = goodsService.queryGoodsByIds(goodsIds);
            Map<Long, Goods> goodsMap = allGoodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v2));

            List<GoodsItem> allGoodsItemList = goodsItemService.queryGoodsIdsList(goodsIds);
            Map<Long, List<GoodsItem>> goodsItemGroupMap = allGoodsItemList.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

            Map<Long, Long> goodsListingIdMap = listingFollowGoodsService.lambdaQuery()
                    .in(ListingFollowGoods::getGoodsId, goodsIds)
                    .eq(ListingFollowGoods::getIsDel, 0)
                    .eq(ListingFollowGoods::getStatus, 1)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(ListingFollowGoods::getGoodsId, ListingFollowGoods::getListingId, (v1, v2) -> v1));

            Map<Long, String> goodsLockLabelMap = goodsLockInfoService.lambdaQuery()
                    .in(GoodsLockInfo::getGoodsId, goodsIds)
                    .eq(GoodsLockInfo::getIsDel, 0)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(GoodsLockInfo::getGoodsId, GoodsLockInfo::getLabel, (v1, v2) -> v1));

            for (GoodsMove goodsMove : goodsMoveList) {
                Long goodsId = goodsMove.getGoodsId().longValue();
                Goods goods = goodsMap.get(goodsId);
                if (goods.getIsDel() == 1) {
                    LogUtils.info(log, "刷新自营店铺1688爬虫商品价格 --> 商品已删除 goodsId:{}", goodsId);
                    continue;
                }

                List<GoodsItem> goodsItemList = goodsItemGroupMap.get(goodsId);
                if (CollectionUtils.isEmpty(goodsItemList)) {
                    LogUtils.info(log, "刷新自营店铺1688爬虫商品价格 --> 查询不到goodsItem goodsId:{}", goodsId);
                    continue;
                }

                List<BigDecimal> costPriceList = goodsItemList.stream().map(GoodsItem::getCostPrice).filter(price -> price != null && price.compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(costPriceList)) {
                    LogUtils.info(log, "刷新自营店铺1688爬虫商品价格 --> goodsItem采购价为空 goodsId:{}", goodsId);
                    continue;
                }


                BigDecimal avgPrice = costPriceList.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(costPriceList.size()), 2, RoundingMode.HALF_UP);
                AliGoodsCategoryPriceConfig aliGoodsCategoryPriceConfig = aliGoodsCategoryPriceConfigService.lambdaQuery()
                        .eq(AliGoodsCategoryPriceConfig::getLastCategoryId, goods.getCategoryId())
                        .le(AliGoodsCategoryPriceConfig::getStartPrice, avgPrice)
                        .gt(AliGoodsCategoryPriceConfig::getEndPrice, avgPrice)
                        .one();

                if (aliGoodsCategoryPriceConfig == null) {
                    aliGoodsCategoryPriceConfig = aliGoodsCategoryPriceConfigService.lambdaQuery()
                            .eq(AliGoodsCategoryPriceConfig::getLastCategoryId, 0)
                            .le(AliGoodsCategoryPriceConfig::getStartPrice, avgPrice)
                            .gt(AliGoodsCategoryPriceConfig::getEndPrice, avgPrice)
                            .one();
                }
                if (aliGoodsCategoryPriceConfig == null) {
                    LogUtils.info(log, "刷新自营店铺1688爬虫商品价格 --> 查询不到类目mapping goodsId:{}, categoryId:{}, avgPrice:{}", goodsId, goods.getCategoryId(), avgPrice);
                    continue;
                }


                Date now = new Date();
                List<GoodsEditPriceDto.SkuChangeDto> skuChangeDtoList = Lists.newArrayList();
                for (GoodsItem goodsItem : goodsItemList) {
                    BigDecimal newPrice = goodsItem.getCostPrice()
                            .divide(new BigDecimal("7.5"), 4, RoundingMode.HALF_UP)
                            .add(goodsItem.getDefaultDelivery())
                            .divide(BigDecimal.ONE.subtract(profitRatio).subtract(refundRatio), 2, RoundingMode.HALF_UP)
                            .add(aliGoodsCategoryPriceConfig.getFreight())
                            .add(aliGoodsCategoryPriceConfig.getProfit())
                            .subtract(goodsItem.getDefaultDelivery())
                            .setScale(2, RoundingMode.HALF_UP);

                    skuChangeDtoList.add(new GoodsEditPriceDto.SkuChangeDto(goodsItem.getId(), goodsItem.getOrginalPrice(), newPrice, goodsItem.getStock(), goodsItem.getStock()));
                    goodsItem.setOrginalPrice(newPrice);
                    goodsItem.setPrice(newPrice.add(goodsItem.getDefaultDelivery()));
                    goodsItem.setUpdateTime(now);
                }
                goodsItemService.updateBatchById(goodsItemList);

                goodsItemList.stream().map(GoodsItem::getPrice).min(BigDecimal::compareTo).ifPresent(goods::setMinPrice);
                goodsItemList.stream().map(GoodsItem::getPrice).max(BigDecimal::compareTo).ifPresent(goods::setMaxPrice);
                goods.setUpdateTime(LocalDateTime.now());
                goodsService.updateById(goods);





                //自动退出flashDeal
                Long activityId = activityRemoteClientFactory.exitFlashDeal(goodsId);
                if (activityId != null) {
                    LogUtils.info(log, "刷新自营店铺1688爬虫商品价格 --> 自动退出flashDeal,退出活动 goodsId:{}, activityId:{}", goodsId, activityId);
                    //删除活动价格备份信息
                    List<ActivityOriginalPrice> activityOriginalPriceList = activityOriginalPriceService.queryPriceByActivityIdAndGoodsId(activityId, goodsId);
                    if (CollectionUtils.isNotEmpty(activityOriginalPriceList)) {
                        activityOriginalPriceService.deleteByIds(activityOriginalPriceList.stream().map(ActivityOriginalPrice::getId).collect(Collectors.toList()));
//                        LogUtils.info(log, "刷新自营店铺1688爬虫商品价格 --> 自动退出flashDeal,删除活动价格备份信息 goodsId:{}, size:{}", goodsId, activityOriginalPriceList.size());
                    }

                    //解除锁定标签
                    UpdateLockLabelCondition condition = new UpdateLockLabelCondition(Collections.singletonList(goodsId), Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()),"自动解标签(改价自动退出flashDeal)", OperationLogTypeEnums.UNLOCK_AUTO.getCode());
                    goodsLockCoreService.removeLockByApi(condition);

                    //删除活动标签
                    BindTagDTO bindTagDTO = new BindTagDTO();
                    bindTagDTO.setGoodsIds(Collections.singletonList(goodsId));
                    bindTagDTO.setTagId(CommonConstants.FLASH_DEAL_TAG_ID);
                    goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
//                    LogUtils.info(log, "刷新自营店铺1688爬虫商品价格 --> 自动退出flashDeal，解除锁定标签 && 删除活动标签 goodsId:{}", goodsId);

                }



                //记录修改历史和操作记录
                GoodsEditPriceDto goodsEditPriceDto = new GoodsEditPriceDto();
                goodsEditPriceDto.setSkuChangeDtoList(skuChangeDtoList);

                List<String> specialTagList = Lists.newArrayList();
                String lockLabel = goodsLockLabelMap.get(goodsId);
                if (lockLabel != null) {
                    specialTagList = GoodsLockLabelTypEnums.listNamesByCodes(lockLabel);
                }
                if (goodsListingIdMap.get(goodsId) != null) {
                    specialTagList.add("listing");
                }

                GoodsEditInfo editInfo = new GoodsEditInfo();
                editInfo.setGoodsId(goods.getId());
                editInfo.setCategoryId(goods.getCategoryId());
                editInfo.setShopId(goods.getShopId());
                editInfo.setShopName(goods.getShopName());
                editInfo.setAuditUser("system");
                editInfo.setAuditTime(LocalDateTime.now());
                editInfo.setStatus(1);
                editInfo.setApplyUser("自营店铺刷新1688价格(自动退出flashDeal)");
                editInfo.setApplyTime(LocalDateTime.now());
                editInfo.setApplyReason("自营店铺刷新1688价格(自动退出flashDeal)");
                editInfo.setIsDel(0);
                editInfo.setType(GoodsEditTypeEnums.PRICE.getCode());
                editInfo.setContent(JSON.toJSONString(goodsEditPriceDto));
                editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
                editInfo.setUpdateTime(LocalDateTime.now());
                goodsEditInfoService.save(editInfo);

                GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
                goodsEditInfoDetail.setEditId(editInfo.getId());
                goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
                goodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditPriceDto));
                goodsEditInfoDetailService.save(goodsEditInfoDetail);

                Map<Long, BigDecimal> oldPriceSnap = skuChangeDtoList.stream().collect(Collectors.toMap(GoodsEditPriceDto.SkuChangeDto::getId, GoodsEditPriceDto.SkuChangeDto::getOldPrice, (v1, v2) -> v1));
                Map<Long, BigDecimal> newPriceSnap = skuChangeDtoList.stream().collect(Collectors.toMap(GoodsEditPriceDto.SkuChangeDto::getId, GoodsEditPriceDto.SkuChangeDto::getNewPrice, (v1, v2) -> v1));
                GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                        .goodsId(goodsId)
                        .oldData(JSON.toJSONString(oldPriceSnap))
                        .newData(JSON.toJSONString(newPriceSnap))
                        .type(OperationLogTypeEnums.REFRESH_GOODS_PRICE_BY_UPDATE_COST_PRICE)
                        .content(OperationLogTypeEnums.REFRESH_GOODS_PRICE_BY_UPDATE_COST_PRICE.getDesc())
                        .status(1)
                        .user("system");
                mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);

                GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                goodsSyncModel.setGoodsId(goodsId);
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("自营店铺刷新1688价格(手动)");
                goodsSyncModel.setSourceService("vp");
                mqSender.send("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel));
            }

            minId = goodsMoveList.stream().map(GoodsMove::getId).max(Long::compareTo).orElse(999999999L);
        }
    }

    @NoLogin
    @PostMapping("/repairSelfSupportShopForWholesaleGoods")
    @ApiOperation(value = "修复重新计算采购价的自营店铺中来源为1688的批发商品")
    public void repairSelfSupportShopForWholesaleGoods(@RequestBody Long minId) {
        long start;
        List<Long> shopIds = faMerchantsApplyService.lambdaQuery()
                .notIn(FaMerchantsApply::getId, Arrays.asList(1015784, 1015806, 1015807, 1020494, 1020513))
                .eq(FaMerchantsApply::getDeliveryType, 3)
                .eq(FaMerchantsApply::getIsDel, 0)
                .select(FaMerchantsApply::getId)
                .list()
                .stream().map(FaMerchantsApply::getId).collect(Collectors.toList());
        minId = minId == null ? 0L : minId;
        while (true) {
            start = System.currentTimeMillis();
            List<GoodsMove> goodsMoveList = goodsMoveService.lambdaQuery()
                    .gt(GoodsMove::getId, minId)
                    .in(GoodsMove::getShopId, shopIds)
                    .eq(GoodsMove::getStatus, 2)
                    .in(GoodsMove::getSource, Arrays.asList("2", "1688"))
                    .isNotNull(GoodsMove::getGoodsId)
                    .orderByAsc(GoodsMove::getId)
                    .last("limit 100")
                    .list();
            LogUtils.info(log, "修复重新计算采购价的自营店铺中来源为1688的批发商品 minId:{}, size:{}，耗时:{} ms", minId, goodsMoveList.size(), System.currentTimeMillis() - start);
            if (CollectionUtils.isEmpty(goodsMoveList)) {
                return;
            }

            List<Long> goodsIds = goodsMoveList.stream().map(goodsMove -> goodsMove.getGoodsId().longValue()).collect(Collectors.toList());
            List<Goods> wholesaleGoodsList = goodsService.lambdaQuery()
                    .in(Goods::getId, goodsIds)
                    .eq(Goods::getIsDel, 0)
                    .eq(Goods::getType, 3)
                    .select(Goods::getId)
                    .list();
            LogUtils.info(log, "修复重新计算采购价的自营店铺中来源为1688的批发商品 批发商品.size:{}，耗时:{} ms", wholesaleGoodsList.size(), System.currentTimeMillis() - start);
            if (CollectionUtils.isNotEmpty(wholesaleGoodsList)) {
                wholesaleGoodsList.forEach(goods -> {
                    try {
                        goodsCoreService.refreshWholesaleGoodsPrice(goods.getId(), 1, false);
                    } catch (CustomException e) {
                        LogUtils.info(log, "修复重新计算采购价的自营店铺中来源为1688的批发商品 刷新批发价失败 id:{}, msg:{}", goods.getId(), e.getBusinessResultCode().getMsg());
                    } catch (Exception e) {
                        LogUtils.info(log, "修复重新计算采购价的自营店铺中来源为1688的批发商品 刷新批发价失败 id:{}", goods.getId());
                    }
                });
            }
            LogUtils.info(log, "修复重新计算采购价的自营店铺中来源为1688的批发商品 batch总耗时:{} ms", System.currentTimeMillis() - start);
            minId = goodsMoveList.stream().map(GoodsMove::getId).max(Long::compareTo).orElse(999999999L);
        }
    }


    @NoLogin
    @PostMapping("initHotGoodsMonitorPrincipal")
    @ApiOperation(value = "填充热卖商品监控的负责小二",tags = "填充热卖商品监控的负责小二")
    public void initHotGoodsMonitorPrincipal(){
        //todo
        Long minId = 3861900L;
        while (true) {
            List<HotGoodsMonitor> hotGoodsMonitorList = hotGoodsMonitorService.lambdaQuery()
                    .gt(HotGoodsMonitor::getId, minId)
                    .eq(HotGoodsMonitor::getIsDel, 0)
                    .isNull(HotGoodsMonitor::getPrincipal)
                    .isNotNull(HotGoodsMonitor::getShopId)
                    .select(HotGoodsMonitor::getId, HotGoodsMonitor::getShopId)
                    .orderByAsc(HotGoodsMonitor::getId)
                    .last("limit 1000")
                    .list();
            if (CollectionUtils.isEmpty(hotGoodsMonitorList)) {
                log.info("填充热卖商品监控的负责小二 list为空，return minId:{}", minId);
                break;
            }

            List<Long> shopIds = hotGoodsMonitorList.stream().map(HotGoodsMonitor::getShopId).distinct().collect(Collectors.toList());
            Map<Long, String> shopIdPrincipalMap = faMerchantsApplyService.lambdaQuery()
                    .in(FaMerchantsApply::getId, shopIds)
                    .isNotNull(FaMerchantsApply::getPrincipal)
                    .select(FaMerchantsApply::getId, FaMerchantsApply::getPrincipal)
                    .list()
                    .stream().collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getPrincipal, (v1, v2) -> v1));

            List<HotGoodsMonitor> updateList = hotGoodsMonitorList.stream()
                    .peek(dto -> dto.setPrincipal(shopIdPrincipalMap.get(dto.getShopId())))
                    .filter(dto -> StringUtils.isNotBlank(dto.getPrincipal()))
                    .collect(Collectors.toList());
            log.info("填充热卖商品监控的负责小二 minId: {}, update.size: {}", minId, updateList.size());
            if (CollectionUtils.isNotEmpty(updateList)) {
                hotGoodsMonitorService.updateBatchById(updateList);
            }

            minId = hotGoodsMonitorList.stream().map(HotGoodsMonitor::getId).max(Long::compareTo).orElse(999999999L);
        }
    }

    @NoLogin
    @PostMapping("refreshDobaGoodsCountryAndBingTag")
    @ApiOperation("刷新doba发布商品可售国家+美国并打99标")
    public void refreshDobaGoodsCountryAndBingTag(){
        Long minId = 514729L;
        while (true) {

            List<DobaGoods> dobaGoodsList = dobaGoodsService.lambdaQuery()
                    .gt(DobaGoods::getId, minId)
                    .eq(DobaGoods::getPassStatus, 1)
                    .eq(DobaGoods::getSourceType, 1)
                    .orderByAsc(DobaGoods::getId)
                    .last("limit 1000")
                    .list();
            if (CollectionUtils.isEmpty(dobaGoodsList)) {
                log.info("刷新doba发布商品可售国家 列表为空，return; minId:{}", minId);
                break;
            }
            List<Long> goodsIds = dobaGoodsList.stream().map(DobaGoods::getGoodsId).collect(Collectors.toList());
            List<Goods> goodsList = goodsService.lambdaQuery()
                    .in(Goods::getId, goodsIds)
                    .eq(Goods::getIsDel, 0)
                    .eq(Goods::getCountry, "")
                    .list();

            log.info("刷新doba发布商品可售国家 size:{}, minId:{}", goodsList.size(), minId);
            if (CollectionUtils.isNotEmpty(goodsList)) {
                List<Long> updateGoodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());

                ManualRefreshGoodsCountryDTO manualRefreshGoodsCountryDTO = new ManualRefreshGoodsCountryDTO();
                manualRefreshGoodsCountryDTO.setGoodsIds(updateGoodsIds);
                manualRefreshGoodsCountryDTO.setOperate("A");
                manualRefreshGoodsCountryDTO.setCountryCodeList(Lists.newArrayList("US"));
                manualRefreshGoodsCountryDTO.setNeedTonDun(false);
                logisticsPropertyConfigCoreService.manualRefreshGoodsCountry(manualRefreshGoodsCountryDTO);

                BindTagDTO bindTagDTO = new BindTagDTO();
                bindTagDTO.setGoodsIds(updateGoodsIds);
                bindTagDTO.setTagId(CommonConstants.PASS_TAG_ID);
                goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);

                updateGoodsIds.forEach(goodsId -> {
                    GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                    goodsSyncModel.setGoodsId(goodsId);
                    goodsSyncModel.setSyncTime(System.currentTimeMillis());
                    goodsSyncModel.setBusiness("刷新doba发布商品可售国家(手动)");
                    goodsSyncModel.setSourceService("vp");
                    mqSender.send("SYNC_GOODS_TOPIC_TRIGGER", goodsSyncModel);
                });
            }

            minId = dobaGoodsList.stream().map(DobaGoods::getId).max(Long::compareTo).orElse(999999999L);
        }
    }


    @NoLogin
    @PostMapping("initGoodsTagByCategoryId")
    @ApiOperation("根据类目给历史商品打标")
    public void initGoodsTagByCategoryId(@RequestBody BindGoodsTagByCategoryCondition condition) {
        LogUtils.info(log, "根据类目给历史商品打标 condition:{}", condition);
        CheckUtils.check(condition == null || condition.getTagId() == null || CollectionUtils.isEmpty(condition.getCategoryIds()), ProductResultCode.PARAMETER_ERROR);
        assert condition != null;

        Long minId = 0L;
        while (true) {
            List<Long> goodsIds = goodsService.lambdaQuery()
                    .gt(Goods::getId, minId)
                    .in(Goods::getCategoryId, condition.getCategoryIds())
                    .eq(Goods::getIsDel, 0)
                    .orderByAsc(Goods::getId)
                    .last("limit 100")
                    .select(Goods::getId)
                    .list()
                    .stream().map(Goods::getId).collect(Collectors.toList());
            LogUtils.info(log, "根据类目给历史商品打标 minId:{}, size:{}", minId, goodsIds.size());

            if (CollectionUtils.isEmpty(goodsIds)) {
                return;
            }

            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setTagId(condition.getTagId());
            bindTagDTO.setGoodsIds(goodsIds);
            goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);

            minId = goodsIds.stream().max(Long::compareTo).orElse(999999999L);
        }
    }

    @NoLogin
    @PostMapping("/syncGoodsByIds")
    @ApiOperation(value = "根据goodsId同步商品信息到es")
    public Result<Boolean> syncGoodsByIds(@RequestBody List<Long> list) {
        log.info("根据goodsId同步商品信息到es:{}", JSON.toJSONString(list));
        for (List<Long> batchList : Lists.partition(list, 1000)) {
            for (Long goodsId : batchList) {
                try {
                    GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                    goodsSyncModel.setGoodsId(goodsId);
                    goodsSyncModel.setSyncTime(System.currentTimeMillis());
                    goodsSyncModel.setBusiness("根据goodsId同步商品信息到es(手动)");
                    goodsSyncModel.setSourceService("vp");
                    mqSender.send("NOT_SYNC_IMAGE_TOPIC", goodsSyncModel);
                } catch (Exception e) {
                    log.info("同步商品失败:{}", goodsId, e);
                }
            }
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        return Result.success(Boolean.TRUE);
    }

    @NoLogin
    @PostMapping("/syncGoodsByShopIds")
    @ApiOperation(value = "根据店铺Id同步商品信息到es")
    public Result<Boolean> syncGoodsByShopIds(@RequestBody List<Long> shopIds) {
        log.info("根据店铺Id同步商品信息到es:{}", JSON.toJSONString(shopIds));

        Long minId = 0L;
        while (true) {
            List<Goods> goodsList = goodsService.lambdaQuery()
                    .gt(Goods::getId, minId)
                    .in(Goods::getShopId, shopIds)
                    .orderByAsc(Goods::getId)
                    .last("limit 100")
                    .select(Goods::getId)
                    .list();
            log.info("根据店铺Id同步商品信息到es minId:{},size:{}", minId, goodsList.size());
            if (CollectionUtils.isEmpty(goodsList)) {
                break;
            }

            List<Long> goodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());
            for (Long goodsId : goodsIds) {
                GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                goodsSyncModel.setGoodsId(goodsId);
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("根据店铺Id同步商品信息到es(手动)");
                goodsSyncModel.setSourceService("vp");
                mqSender.send("SYNC_GOODS_TOPIC_TRIGGER", goodsSyncModel);
            }

            minId = goodsIds.stream().max(Long::compareTo).orElse(99999999L);
        }
        return Result.success(Boolean.TRUE);
    }

    @NoLogin
    @PostMapping("/syncGoodsByUpdateTime")
    @ApiOperation(value = "根据updateTime同步商品信息到es")
    public Result<Boolean> syncGoodsByUpdateTime(@Param("startTime") String startTime, @Param("endTime") String endTime) {
        log.info("根据updateTime同步商品信息到es startTime:{} - endTime:{}", startTime, endTime);
        LocalDateTime queryStartTime = StringUtils.isNotBlank(startTime) ? LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        LocalDateTime queryEndTime = StringUtils.isNotBlank(endTime) ? LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null;
        Long minId = 0L;
        while (true) {
            List<Goods> goodsList = goodsService.lambdaQuery()
                    .gt(Goods::getId, minId)
                    .gt(queryStartTime != null, Goods::getUpdateTime, queryStartTime)
                    .lt(queryEndTime != null, Goods::getUpdateTime, queryEndTime)
                    .orderByAsc(Goods::getId)
                    .last("limit 100")
                    .select(Goods::getId)
                    .list();
            log.info("根据updateTime同步商品信息到es minId:{},size:{}", minId, goodsList.size());
            if (CollectionUtils.isEmpty(goodsList)) {
                break;
            }

            List<Long> goodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());
            for (Long goodsId : goodsIds) {
                GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                goodsSyncModel.setGoodsId(goodsId);
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("根据updateTime同步商品信息到es(手动)");
                goodsSyncModel.setSourceService("vp");
                mqSender.send("SYNC_GOODS_TOPIC_TRIGGER", goodsSyncModel);
            }

            minId = goodsIds.stream().max(Long::compareTo).orElse(99999999L);
        }

        return Result.success(Boolean.TRUE);
    }
}
