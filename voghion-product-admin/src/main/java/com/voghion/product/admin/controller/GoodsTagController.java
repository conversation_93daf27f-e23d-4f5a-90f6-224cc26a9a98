package com.voghion.product.admin.controller;


import autovalue.shaded.com.google.common.collect.Lists;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.core.GoodsTagCoreService;
import com.voghion.product.core.impl.ManagerAuth;
import com.voghion.product.model.dto.AuthResourceDTO;
import com.voghion.product.model.vo.GoodsTagVO;
import com.voghion.product.model.vo.condition.GoodsTagCondition;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品标签 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-09
 */
@RestController
@RequestMapping("/goodsTag")
@Api(tags = "商品标签")
public class GoodsTagController {
    @Resource
    private GoodsTagCoreService goodsTagCoreService;

    @Resource
    private ManagerAuth managerAuth;

    @ApiOperation("新增标签配置")
    @PostMapping("add")
    public Result<Boolean> addGoodsTag(@RequestBody GoodsTagVO goodsTagVO) {
        goodsTagCoreService.addGoodsTag(goodsTagVO);
        return Result.success(true);
    }

    @ApiOperation("修改标签配置")
    @PostMapping("update")
    public Result<Boolean> updateGoodsTag(@RequestBody GoodsTagVO goodsTagVO) {
        goodsTagCoreService.updateGoodsTag(goodsTagVO);
        return Result.success(true);
    }

    @ApiOperation("修改标签权重")
    @PostMapping("updateSort")
    public Result<Boolean> updateGoodsTagSort(@RequestBody GoodsTagVO goodsTagVO) {
        goodsTagCoreService.updateGoodsTagSort(goodsTagVO);
        return Result.success(true);
    }

    @ApiOperation("修改标签展示隐藏")
    @PostMapping("updateShow")
    public Result<Boolean> updateGoodsTagShow(@RequestBody GoodsTagVO goodsTagVO) {
        goodsTagCoreService.updateGoodsTagShow(goodsTagVO);
        return Result.success(true);
    }

    //废弃删除标签方法
    //@ApiOperation("删除标签配置")
    //@DeleteMapping("delete/{id}")
    //public Result<Boolean> deleteGoodsTag(@PathVariable Long id) {
    //    goodsTagCoreService.deleteGoodsTag(id);
    //    return Result.success(true);
    //}

    @ApiOperation("获取标签配置")
    @PostMapping("list")
    public Result<PageView<GoodsTagVO>> listGoodsTag(@RequestBody GoodsTagCondition goodsTagCondition) {
        return Result.success(goodsTagCoreService.queryPageByCondition(goodsTagCondition));
    }

    @ApiOperation("根据标签id获取标签配置")
    @GetMapping("get/{tagId}")
    public Result<GoodsTagVO> getGoodsTagById(@PathVariable Long tagId) {
        return Result.success(goodsTagCoreService.getGoodsTagById(tagId));
    }


    @ApiOperation("新增标签配置")
    @PostMapping("addNew")
    public Result<Boolean> addGoodsTagNew(@RequestBody GoodsTagVO goodsTagVO) {
        goodsTagCoreService.addNewGoodsTag(goodsTagVO);
        return Result.success(true);
    }

    @ApiOperation("修改标签配置")
    @PostMapping("updateNew")
    public Result<Boolean> updateGoodsTagNew(@RequestBody GoodsTagVO goodsTagVO) {
        goodsTagCoreService.updateNewGoodsTag(goodsTagVO);
        return Result.success(true);
    }

    @ApiOperation("获取标签配置")
    @PostMapping("listNew")
    public Result<PageView<GoodsTagVO>> listGoodsTagNew(@RequestBody GoodsTagCondition goodsTagCondition) {
        List<AuthResourceDTO> userAuthResource = managerAuth.getUserAuthResource();
        List<AuthResourceDTO> matchedResource = managerAuth.searchAuthResource("/operation/newTagManager", "-type-[0-9]+", userAuthResource, (s,d)->{
            return d.getUri().matches(s);
        });
        List<Integer> authTypeIds = matchedResource.stream().map(AuthResourceDTO::getUri).map(s ->{
            String[] split = s.split("-");
            return split.length>2 ? split[2] : "";
        }).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
        goodsTagCondition.setTypeList(authTypeIds);

        if(CollectionUtils.isEmpty(authTypeIds)){
            return Result.success(new PageView<>());
        }
        return Result.success(goodsTagCoreService.queryNewPageByCondition(goodsTagCondition));
    }

    @ApiOperation("查询所有")
    @PostMapping("/listAll")
    public Result<List<GoodsTagVO>> listAll(){
        GoodsTagCondition goodsTagCondition = new GoodsTagCondition();
        List<AuthResourceDTO> userAuthResource = managerAuth.getUserAuthResource();
        List<AuthResourceDTO> matchedResource = managerAuth.searchAuthResource("/operation/newTagManager", "-type-[0-9]+", userAuthResource, (s,d)->{
            return d.getUri().matches(s);
        });
        List<Integer> authTypeIds = matchedResource.stream().map(AuthResourceDTO::getUri).map(s ->{
            String[] split = s.split("-");
            return split.length>2 ? split[2] : "";
        }).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());
        goodsTagCondition.setTypeList(authTypeIds);
        if(CollectionUtils.isEmpty(authTypeIds)){
            return Result.success(Lists.newArrayList());
        }
        return Result.success(goodsTagCoreService.listAll(goodsTagCondition));
    }
}
