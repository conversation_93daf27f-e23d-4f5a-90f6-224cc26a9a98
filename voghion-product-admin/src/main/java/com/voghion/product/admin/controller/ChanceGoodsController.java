package com.voghion.product.admin.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.colorlight.base.model.constants.NoLogin;
import com.voghion.product.core.BullProductTransferCoreService;
import com.voghion.product.core.GoodsCoreService;
import com.voghion.product.listener.*;
import com.voghion.product.model.vo.*;
import com.voghion.product.model.vo.condition.*;
import com.voghion.product.core.ChanceGoodsCoreService;
import com.voghion.product.service.ChanceGoodsTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @date: 2022/12/16 上午10:33
 * @author: jashley
 */
@Api(tags = "机会商品")
@Slf4j
@RestController
@RequestMapping("chanceGoods")
public class ChanceGoodsController {
    @Resource
    private ChanceGoodsCoreService chanceGoodsCoreService;

    @Resource
    private GoodsCoreService goodsCoreService;

    @Resource
    private BullProductTransferCoreService bullProductTransferCoreService;

    @PostMapping("/randomChanceGoodsList/{pageSize}")
    @ApiOperation("随机选择部分机会商品列表【商家】")
    public Result<List<ChanceGoodsTemplateVo>> randomChanceGoodsList(@PathVariable Integer pageSize){
        return Result.success(chanceGoodsCoreService.randomChanceGoodsList(pageSize));
    }

    @PostMapping("/template/list")
    @ApiOperation("查询机会商品模板列表【运营】")
    public Result<PageView<ChanceGoodsTemplateVo>> listChanceGoodsTemplate(@RequestBody ChanceGoodsTemplateQueryCondition condition) {
        PageView<ChanceGoodsTemplateVo> pageView = chanceGoodsCoreService.listTemplate(condition);
        return Result.success(pageView);
    }

    @PostMapping("/template/list/export")
    @ApiOperation("导出机会商品模板列表【运营】")
    public Result<Boolean> exportChanceGoodsTemplate(@RequestBody ChanceGoodsTemplateQueryCondition condition) throws IOException {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletResponse response = requestAttributes.getResponse();
        assert response != null;
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=chanceGoods.xlsx");
        List<ChanceGoodsTemplateExportVo> list = chanceGoodsCoreService.exportTemplateList(condition);
        EasyExcel.write(response.getOutputStream(), ChanceGoodsTemplateExportVo.class).sheet().doWrite(list);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/template/detail")
    @ApiOperation(value = "查询模板商品详情")
    public Result<ChanceGoodsTemplateDetailInfoVO> queryGoodsById(@RequestBody ChanceGoodsTemplateDetailCondition condition) {
        ChanceGoodsTemplateDetailInfoVO vo = chanceGoodsCoreService.queryTemplateDetailById(condition.getGoodsId());
        return Result.success(vo);
    }

    @PostMapping("/template/add")
    @ApiOperation(value = "添加机会商品模板")
    public Result<Long> addTemplateGoods(@RequestBody ChanceGoodsTemplateSaveCondition condition) {
        return Result.success(chanceGoodsCoreService.addTemplate(condition));
    }

    @PostMapping("/template/update")
    @ApiOperation(value = "编辑机会商品模板")
    public Result<Boolean> updateTemplateGoods(@RequestBody ChanceGoodsTemplateSaveCondition condition) {
        chanceGoodsCoreService.updateTemplate(condition);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/template/updateStatus")
    @ApiOperation(value = "模板开放/关闭")
    public Result<Boolean> updateTemplateStatus(@RequestBody ChanceGoodsTemplateUpdateStatusCondition condition) {
        chanceGoodsCoreService.updateTemplateStatus(condition);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/template/updateHot")
    @ApiOperation(value = "修改模板热度")
    public Result<Boolean> updateTemplateHot(@RequestBody ChanceGoodsTemplateUpdateHotCondition condition) {
        chanceGoodsCoreService.updateTemplateHot(condition);
        return Result.success(Boolean.TRUE);
    }

    @ApiOperation(value = "导入机会商品模板批量新增模板")
    @PostMapping("import/batchSaveChanceGoods")
    public Result<Boolean> importBatchSaveChanceGoodsTemplate(@RequestParam("file") MultipartFile file) throws IOException {
        EasyExcelFactory.read(file.getInputStream(), BatchSaveChanceGoodsVo.class, new ChanceGoodsBatchSaveListener(chanceGoodsCoreService))
                .sheet()
                .doRead();
        return Result.success(Boolean.TRUE);
    }

    @ApiOperation("下载机会商品模板批量新增模板")
    @PostMapping("downloadTemplate/batchSaveChanceGoods")
    public Result<Boolean> downloadBatchSaveChanceGoodsTemplate() throws IOException {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletResponse response = requestAttributes.getResponse();
        assert response != null;
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=batchSaveTemplate.xlsx");
        List<CategoryUpdateVO> str = new ArrayList<>();
        EasyExcel.write(response.getOutputStream(), BatchSaveChanceGoodsVo.class).sheet().doWrite(str);
        return Result.success(Boolean.TRUE);
    }

    @ApiOperation(value = "导入批量修改机会商品模板的状态和热度模板")
    @PostMapping("import/batchUpdateStatusAndHot")
    public Result<Boolean> importBatchUpdateStatusAndHotTemplate(@RequestParam("file") MultipartFile file) throws IOException {
        EasyExcelFactory.read(file.getInputStream(), BatchUpdateStatusAndHotVo.class, new ChanceGoodsBatchUpdateStatusAndHotListener(chanceGoodsCoreService))
                .sheet()
                .doRead();
        return Result.success(Boolean.TRUE);
    }

    @ApiOperation("下载批量修改机会商品模板的状态和热度模板")
    @PostMapping("downloadTemplate/batchUpdateStatusAndHot")
    public Result<Boolean> downloadBatchUpdateStatusAndHotTemplate() throws IOException {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletResponse response = requestAttributes.getResponse();
        assert response != null;
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=batchUpdateStatusAndHot.xlsx");
        List<CategoryUpdateVO> str = new ArrayList<>();
        EasyExcel.write(response.getOutputStream(), BatchUpdateStatusAndHotVo.class).sheet().doWrite(str);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/goods/list")
    @ApiOperation("根据模板id查询机会商品列表")
    public Result<List<ChanceGoodsVo>> listChanceGoodsByTemplateId(@RequestBody ChanceGoodsQueryCondition condition) {
        List<ChanceGoodsVo> list = chanceGoodsCoreService.listChanceGoodsByTemplateId(condition);
        return Result.success(list);
    }

    @PostMapping("/goods/list/export")
    @ApiOperation("导出商品【运营】")
    public Result<Boolean> exportChanceGoods(@RequestBody ChanceGoodsQueryCondition condition) throws IOException {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletResponse response = requestAttributes.getResponse();
        assert response != null;
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=chanceGoods.xlsx");
        List<ChanceGoodsExportVo> list = chanceGoodsCoreService.exportChanceGoods(condition);
        EasyExcel.write(response.getOutputStream(), ChanceGoodsExportVo.class).sheet().doWrite(list);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/goods/select")
    @ApiOperation("加精机会商品(精选)")
    public Result<Boolean> selectChanceGoods(@RequestBody ChanceGoodsSelectCondition condition) {
        chanceGoodsCoreService.selectChanceGoods(condition);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/template/list/shop")
    @ApiOperation("机会商品报名(模板）列表【商家】")
    public Result<PageView<ChanceGoodsTemplateForSignUpVo>> listChanceGoodsTemplateForSignUp(@RequestBody ChanceGoodsTemplateQueryForSignUpCondition condition) {
        PageView<ChanceGoodsTemplateForSignUpVo> pageView = chanceGoodsCoreService.listChanceGoodsTemplateForSignUp(condition);
        return Result.success(pageView);
    }

    @PostMapping("/goods/matchSame")
    @ApiOperation("我有同款")
    public Result<Boolean> matchSameChanceGoods(@RequestBody ChanceGoodsMatchSameCondition condition) {
        chanceGoodsCoreService.matchSameChanceGoods(condition);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/goods/createSame")
    @ApiOperation("发布同款")
    public Result<Boolean> createSameChanceGoods(@RequestBody ChanceGoodsCreateSameCondition condition) {
        chanceGoodsCoreService.createSameChanceGoods(condition);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/goods/audit/list")
    @ApiOperation("机会商品审核列表")
    public Result<PageView<ChanceGoodsForAuditVo>> listChanceGoodsAudit(@RequestBody ChanceGoodsQueryForAuditCondition condition) {
        PageView<ChanceGoodsForAuditVo> list = chanceGoodsCoreService.listChanceGoodsAudit(condition);
        return Result.success(list);
    }

    @PostMapping("/goods/audit/list/export")
    @ApiOperation("机会商品审核列表导出")
    public Result<Boolean> exportChanceGoodsAudit(@RequestBody ChanceGoodsQueryForAuditCondition condition) throws IOException {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletResponse response = requestAttributes.getResponse();
        assert response != null;
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=chanceGoodsAudit.xlsx");
        List<ChanceGoodsForAuditExportVo> list = chanceGoodsCoreService.exportChanceGoodsAudit(condition);
        EasyExcel.write(response.getOutputStream(), ChanceGoodsForAuditExportVo.class).sheet().doWrite(list);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/goods/audit/choose")
    @ApiOperation("审核(入选/落选)")
    public Result<Boolean> chooseChanceGoods(@RequestBody ChanceGoodsChooseCondition condition) {
        chanceGoodsCoreService.chooseChanceGoods(condition);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/goods/list/selected")
    @ApiOperation("精选机会商品列表")
    public Result<PageView<ChanceGoodsSelectedVo>> listSelectedChanceGoods(@RequestBody ChanceGoodsSelectedQueryCondition condition) {
        PageView<ChanceGoodsSelectedVo> pageView = chanceGoodsCoreService.listSelectedChanceGoods(condition);
        return Result.success(pageView);
    }

    @PostMapping("/goods/list/selected/export")
    @ApiOperation("精选机会商品列表导出")
    public Result<Boolean> exportSelectedChanceGoods(@RequestBody ChanceGoodsSelectedQueryCondition condition) throws IOException {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert requestAttributes != null;
        HttpServletResponse response = requestAttributes.getResponse();
        assert response != null;
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=selectedChanceGoods.xlsx");
        List<ChanceGoodsSelectedExportVo> list = chanceGoodsCoreService.exportSelectedChanceGoods(condition);
        EasyExcel.write(response.getOutputStream(), ChanceGoodsSelectedExportVo.class).sheet().doWrite(list);
        return Result.success(Boolean.TRUE);
    }

    @PostMapping("/updateCategory")
    @ApiOperation("修改机会商品类目")
    public Result<Boolean> updateCategory(@RequestBody ChanceGoodsUpdateCategoryCondition condition) {
        chanceGoodsCoreService.updateCategory(condition);
        return Result.success(Boolean.TRUE);
    }

    @NoLogin
    @GetMapping("transferTemplateByProId")
    @ApiOperation(value = "根据牛魔王ProId生成机会商品模板", tags = "根据牛魔王ProId生成机会商品模板")
    @Async("goodsInfoPool")
    public void transferTemplateByProId(@RequestParam("proId") Long proId) {
        bullProductTransferCoreService.transferThirdGoods(proId, null);
    }

    @NoLogin
    @GetMapping("batchTransferTemplate")
    @ApiOperation(value = "从牛魔王数据批量生成机会商品模板", tags = "从牛魔王数据批量生成机会商品模板")
    @Async("goodsInfoPool")
    public void batchTransferTemplate(@RequestParam("startProId") Long startProId) {
        bullProductTransferCoreService.batchTransfer(startProId);
    }
}
