package com.voghion.product.admin.configuration;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;

import static com.voghion.product.utils.CommonConstants.ASYNC_BATCH_UPDATE_GOODS;

@Configuration
@EnableAsync
public class AsyncConfig {
    @Value("${threadPool.similarGoodsPool.corePoolSize:4}")
    private int similarGoodsPoolCorePoolSize;

    @Bean("userInfoPool")
    public Executor getExecutor() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("consumer-queue-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(5);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(10);
        // 缓存队列
        executor.setQueueCapacity(25);
        //线程名
        executor.setThreadFactory(namedThreadFactory);
        // 线程池初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "goodsInfoPool")
    public Executor getGoodsExecutor() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("consumer-queue-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(5);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(10);
        // 缓存队列
        executor.setQueueCapacity(10000);
        //线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }

    @Bean(ASYNC_BATCH_UPDATE_GOODS)
    public ThreadPoolTaskExecutor createAsyncAutoSaveGoodsMappingsExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(5);
        // 最大线程数
        executor.setMaxPoolSize(10);
        // 缓存队列
        executor.setQueueCapacity(10000);
        // 线程名
        executor.setThreadNamePrefix(ASYNC_BATCH_UPDATE_GOODS + "-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 线程池初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "bizTaskThreadPool")
    public Executor taskExecutor() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("biz-task-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(2);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(2);
        // 缓存队列
        executor.setQueueCapacity(10000);
        //线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "filterItemPool")
    public ThreadPoolTaskExecutor filterPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("filter-item-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(5);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(10);
        // 缓存队列
        executor.setQueueCapacity(100);
        //线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }


    @Bean(name = "aliUrlGoodsPool")
    public ThreadPoolTaskExecutor aliGoodsPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("ali-goods-sync-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(5);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(10);
        // 缓存队列
        executor.setQueueCapacity(100000);
        //线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "similarGoodsPool")
    public ThreadPoolTaskExecutor similarGoodsPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("similar-goods-sync-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(similarGoodsPoolCorePoolSize);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(similarGoodsPoolCorePoolSize * 2);
        // 缓存队列
        executor.setQueueCapacity(10000);
        //线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "asyncBqPool")
    public ThreadPoolTaskExecutor asyncBqPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("bq-goods-sync-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(1);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(5);
        // 缓存队列
        executor.setQueueCapacity(100000);
        //线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }
    @Bean(name = "nihaoPool")
    public ThreadPoolTaskExecutor nihaoPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("bq-goods-sync-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(5);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(5);
        // 缓存队列
        executor.setQueueCapacity(100000);
        //线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "goodsStandardCleanPool")
    public Executor goodsStandardCleanPoolExecutor() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("goods-standard-clean-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(5);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(5);
        // 缓存队列
        executor.setQueueCapacity(5);
        //线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "addFullGoodsInfoPool")
    public ThreadPoolTaskExecutor addFullGoodsInfoPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("add-full-goods-info-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(5);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(10);
        // 缓存队列
        executor.setQueueCapacity(10 * 1024);
        //线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }

    @Bean(name = "syncListingFollowGoodsPool")
    public ThreadPoolTaskExecutor syncListingFollowGoodsPool() {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("sync-listing-follow-goods-thread-%d").build();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 线程池维护线程的最少数量
        executor.setCorePoolSize(5);
        // 线程池维护线程的最大数量
        executor.setMaxPoolSize(10);
        // 缓存队列
        executor.setQueueCapacity(10 * 1024);
        // 线程名
        executor.setThreadFactory(namedThreadFactory);

        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        // 线程池初始化
        executor.initialize();
        return executor;
    }

}
