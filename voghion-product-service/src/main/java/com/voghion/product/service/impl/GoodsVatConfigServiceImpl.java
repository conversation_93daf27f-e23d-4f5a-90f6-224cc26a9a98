package com.voghion.product.service.impl;

import com.voghion.product.model.po.GoodsVatConfig;
import com.voghion.product.dal.mapper.GoodsVatConfigMapper;
import com.voghion.product.service.GoodsVatConfigService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-27
 */
@Service
public class GoodsVatConfigServiceImpl extends BaseServiceImpl<GoodsVatConfigMapper, GoodsVatConfig> implements GoodsVatConfigService {

    @Override
    public GoodsVatConfig findEffectVatConfigByGoodsId(Long goodsId) {
        if (goodsId == null) {
            return null;
        }

        return lambdaQuery().eq(GoodsVatConfig::getGoodsId, goodsId)
                .eq(GoodsVatConfig::getEffectStatus, 1)
                .one();
    }

}
