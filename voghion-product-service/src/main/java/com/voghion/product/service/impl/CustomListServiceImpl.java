package com.voghion.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voghion.product.dal.mapper.CustomListMapper;
import com.voghion.product.model.enums.SystemStatusEnum;
import com.voghion.product.model.po.CustomList;
import com.voghion.product.model.vo.CustomListQueryVO;
import com.voghion.product.service.CustomListService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class CustomListServiceImpl  extends BaseServiceImpl<CustomListMapper, CustomList> implements CustomListService {

    @Resource
    private CustomListMapper customListMapper;


    @Override
    public List<CustomList> findCustomList(CustomListQueryVO customListQueryVO) {

        if (null == customListQueryVO) {
            customListQueryVO = new CustomListQueryVO();
        }
        QueryWrapper<CustomList> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(customListQueryVO.getTitle()))
            queryWrapper.eq("title",customListQueryVO.getTitle());

        if(customListQueryVO.getType()!=null)
            queryWrapper.eq("type",customListQueryVO.getType());

        if(customListQueryVO.getIsShow()!=null)
            queryWrapper.eq("is_show",customListQueryVO.getIsShow());

        queryWrapper.eq("is_del", SystemStatusEnum.STATUS_IS_DEL_OK.getStatus());
        queryWrapper.orderByAsc("sort");
        return list(queryWrapper);
    }

    @Override
    public IPage<CustomList> PageCustomList(Page<CustomList> page, CustomListQueryVO customListQueryVO){
        IPage<CustomList> ipage = new Page<>()    ;
        if (null == customListQueryVO) {
            customListQueryVO = new CustomListQueryVO();
        }
        QueryWrapper<CustomList> queryWrapper = new QueryWrapper<>();
        if(customListQueryVO.getCustomId()!=null)
            queryWrapper.eq("id",customListQueryVO.getCustomId());

        if(customListQueryVO.getInsertCustomId()!=null)
            queryWrapper.eq("insert_custom_id",customListQueryVO.getInsertCustomId());

        if(CollectionUtils.isNotEmpty(customListQueryVO.getCustomListIds()))
            queryWrapper.in("id",customListQueryVO.getCustomListIds());

        if(StringUtils.isNotBlank(customListQueryVO.getTitle()))
            queryWrapper.like("title",customListQueryVO.getTitle());

        if(customListQueryVO.getType()!=null)
            queryWrapper.eq("type",customListQueryVO.getType());

        if(customListQueryVO.getIsShow()!=null)
            queryWrapper.eq("is_show",customListQueryVO.getIsShow());
        if (CollectionUtils.isNotEmpty(customListQueryVO.getCountryCodeList()))
            queryWrapper.like("country_code",customListQueryVO.getCountryCodeList().get(0));

        if (StringUtils.isNotBlank(customListQueryVO.getCreateUser()))
            queryWrapper.like("create_user",customListQueryVO.getCreateUser().trim());

        if (StringUtils.isNotBlank(customListQueryVO.getUpdateUser()))
            queryWrapper.like("update_user",customListQueryVO.getUpdateUser().trim());

//        if(customListQueryVO.getDepartmentType()!=null){
//            queryWrapper.eq("department_type",customListQueryVO.getDepartmentType());
//        }
        if (CollectionUtils.isNotEmpty(customListQueryVO.getDepartmentTypeList())) {
            queryWrapper.in("department_type", customListQueryVO.getDepartmentTypeList());
        }
        if (customListQueryVO.getPlacementType() != null) {
            queryWrapper.eq("placement_type", customListQueryVO.getPlacementType());

        }

        if(customListQueryVO.getUseAlgorithm()!=null){
            queryWrapper.eq("use_algorithm",customListQueryVO.getUseAlgorithm());

        }

        if(customListQueryVO.getInsertQuantity()!=null){
            queryWrapper.eq("insert_quantity",customListQueryVO.getInsertQuantity());
        }


        queryWrapper.eq("is_del", SystemStatusEnum.STATUS_IS_DEL_OK.getStatus());
        queryWrapper.orderByDesc("id");
        ipage = customListMapper.selectPage(page,queryWrapper);
        return ipage;

    }

    @Override
    public Boolean isExist(CustomList customList){
        QueryWrapper queryWrapper = new QueryWrapper();
        if(StringUtils.isNotBlank(customList.getTitle()))
            queryWrapper.eq("title",customList.getTitle());
        queryWrapper.eq("is_del",0);
        queryWrapper.ne("id",customList.getId());
        if(Objects.isNull(customListMapper.selectOne(queryWrapper)))
            return false;
        return true;
    }

    @Override
    public Boolean deleteCustomListByIds(List<Long> ids) {
        QueryWrapper queryWrapper =new QueryWrapper();
        queryWrapper.in("id",ids);
        CustomList customList = new CustomList();
        customList.setIsDel(1);
        int i = customListMapper.update(customList,queryWrapper);
        if(i<=0) return false;
        return true;
    }


}
