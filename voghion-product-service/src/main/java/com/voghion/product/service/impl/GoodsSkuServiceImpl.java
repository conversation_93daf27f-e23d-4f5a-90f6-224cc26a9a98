package com.voghion.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.voghion.product.dal.sharding.mapper.GoodsSkuMapper;
import com.voghion.product.model.po.goods.GoodsSkuPo;
import com.voghion.product.service.GoodsSkuService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.shardingsphere.sharding.spi.KeyGenerateAlgorithm;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/4/14 16:35
 */
@Service
public class GoodsSkuServiceImpl extends ServiceImpl<GoodsSkuMapper, GoodsSkuPo> implements GoodsSkuService {

    @Resource(name = "shardingKeyGenerator")
    private KeyGenerateAlgorithm shardingKeyGenerator;

    @Override
    public List<GoodsSkuPo> findListByGoodsId(Long goodsId) {
        if (goodsId == null) {
            return Collections.emptyList();
        }

        return lambdaQuery()
                .eq(GoodsSkuPo::getGoodsId, goodsId)
                .eq(GoodsSkuPo::getDeleted, false)
                .list();
    }

    @Override
    public List<GoodsSkuPo> findListByGoodsSkuIds(List<Long> skuIds, Long goodsId) {
        if (CollectionUtils.isEmpty(skuIds) || goodsId == null) {
            return Collections.emptyList();
        }

        return lambdaQuery()
                .in(GoodsSkuPo::getId, skuIds)
                .eq(GoodsSkuPo::getDeleted, false)
                .eq(GoodsSkuPo::getGoodsId, goodsId)
                .list();
    }

    @Override
    public boolean delByGoodsSkuIds(List<Long> skuIds, Long goodsId) {
        if (CollectionUtils.isEmpty(skuIds) || goodsId == null) {
            return false;
        }

        return lambdaUpdate()
                .set(GoodsSkuPo::getDeleted, true)
                .in(GoodsSkuPo::getId, skuIds)
                .eq(GoodsSkuPo::getDeleted, false)
                .eq(GoodsSkuPo::getGoodsId, goodsId)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchByIdAndGoodsId(Collection<GoodsSkuPo> entityList) {
        Assert.notEmpty(entityList, "error: entityList must not be empty");
        String sqlStatement = sqlStatement(SqlMethod.UPDATE);
        try (SqlSession batchSqlSession = sqlSessionBatch()) {
            int i = 0;
            for (GoodsSkuPo entity : entityList) {
                Long goodsId = entity.getGoodsId();
                Long id = entity.getId();
                Assert.notNull(goodsId, "error: goodsId is empty.");
                Assert.notNull(id, "error: id is empty.");

                UpdateWrapper<GoodsSkuPo> updateWrapper = new UpdateWrapper<GoodsSkuPo>()
                        .eq("goods_id", goodsId)
                        .eq("id", id);
                Map<String, Object> map = new HashMap<>(2);
                map.put(Constants.ENTITY, entity);
                map.put(Constants.WRAPPER, updateWrapper);
                batchSqlSession.update(sqlStatement, map);
                if (i >= 1 && i % 1000 == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            batchSqlSession.flushStatements();
        }

        return true;
    }

    @Override
    public List<GoodsSkuPo> findListByGoodsIds(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyList();
        }

        return lambdaQuery()
                .in(GoodsSkuPo::getGoodsId, goodsIds)
                .eq(GoodsSkuPo::getDeleted, false)
                .list();
    }

    @Override
    public GoodsSkuPo findOneByIdAndGoodsId(Long id, Long goodsId) {
        if (id == null || goodsId == null) {
            return null;
        }

        return lambdaQuery()
                .eq(GoodsSkuPo::getId, id)
                .eq(GoodsSkuPo::getGoodsId, goodsId)
                .one();
    }

    @Override
    public boolean removeBatchByGoodsId(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return false;
        }

        QueryWrapper<GoodsSkuPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("goods_id", goodsIds);
        return remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateBatch(Collection<GoodsSkuPo> entityList, int batchSize) {
        Assert.notEmpty(entityList, "error: entityList must not be empty");

        try (SqlSession batchSqlSession = sqlSessionBatch()) {
            int i = 0;
            for (GoodsSkuPo entity : entityList) {
                Long goodsIdVal = entity.getGoodsId();
                Assert.notNull(goodsIdVal, "error: goodsIdVal is empty.");

                Long idVal = entity.getId();
                if (StringUtils.checkValNull(idVal)) {
                    entity.setId(((Number)shardingKeyGenerator.generateKey()).longValue());
                    batchSqlSession.insert(sqlStatement(SqlMethod.INSERT_ONE), entity);
                } else if (Objects.isNull(findOneByIdAndGoodsId(idVal, goodsIdVal))) {
                    // 有值，但是没有查到，就说明是老的，或者分片不对
                    entity.setId(idVal);
                    batchSqlSession.insert(sqlStatement(SqlMethod.INSERT_ONE), entity);
                } else {
                    UpdateWrapper<GoodsSkuPo> updateWrapper = new UpdateWrapper<GoodsSkuPo>()
                            .eq("goods_id", goodsIdVal)
                            .eq("id", idVal);
                    Map<String, Object> map = new HashMap<>(2);
                    map.put(Constants.ENTITY, entity);
                    map.put(Constants.WRAPPER, updateWrapper);
                    batchSqlSession.update(sqlStatement(SqlMethod.UPDATE), map);
                }

                // 达到批次大小时刷写
                if (i > 0 && i % batchSize == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }

            // 刷写剩余批量语句
            batchSqlSession.flushStatements();
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(Collection<GoodsSkuPo> entityList, int batchSize) {
        String sqlStatement = sqlStatement(SqlMethod.INSERT_ONE);
        try (SqlSession batchSqlSession = sqlSessionBatch()) {
            int i = 0;
            for (GoodsSkuPo anEntityList : entityList) {
                if (anEntityList.getId() == null) {
                    // 要兼容历史商品的id，后续没有了可以考虑直接使用配置生成id
                    anEntityList.setId(((Number)shardingKeyGenerator.generateKey()).longValue());
                }

                batchSqlSession.insert(sqlStatement, anEntityList);
                if (i >= 1 && i % batchSize == 0) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            batchSqlSession.flushStatements();
        }

        return true;
    }
}
