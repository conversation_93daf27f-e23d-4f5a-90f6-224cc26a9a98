package com.voghion.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voghion.product.dal.mapper.SizeChartTemplateMapper;
import com.voghion.product.enums.SizeTemplateTypeEnum;
import com.voghion.product.model.po.SizeChartTemplate;
import com.voghion.product.model.vo.condition.SizeChartTemplateQueryCondition;
import com.voghion.product.service.SizeChartTemplateService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 尺码图 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-17
 */
@Service
public class SizeChartTemplateServiceImpl extends BaseServiceImpl<SizeChartTemplateMapper, SizeChartTemplate> implements SizeChartTemplateService {

    @Override
    public SizeChartTemplate selectSystemTemplateByCategoryId(Long categoryId) {
        return lambdaQuery().eq(SizeChartTemplate::getCategoryId, categoryId)
                .eq(SizeChartTemplate::getType, SizeTemplateTypeEnum.SYSTEM.getCode())
                .one();
    }

    @Override
    public List<SizeChartTemplate> selectSystemTemplateByCategoryIds(List<Long> categoryIds) {
        return lambdaQuery().in(SizeChartTemplate::getCategoryId, categoryIds)
                .eq(SizeChartTemplate::getType, SizeTemplateTypeEnum.SYSTEM.getCode())
                .eq(SizeChartTemplate::getIsDel, 0)
                .list();
    }

    @Override
    public IPage<SizeChartTemplate> selectPageByCondition(SizeChartTemplateQueryCondition condition) {
        Page<SizeChartTemplate> page = new Page<>(condition.getPageNow(), condition.getPageSize());
        QueryWrapper<SizeChartTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(condition.getName()), "name", condition.getName());
        queryWrapper.in(CollectionUtils.isNotEmpty(condition.getCategoryIds()), "category_id", condition.getCategoryIds());
        queryWrapper.eq(condition.getType() != null, "type", condition.getType());
        queryWrapper.eq(SizeTemplateTypeEnum.SHOP.getCode().equals(condition.getType()), "shop_id", condition.getShopId());
        return this.page(page, queryWrapper);
    }

    @Override
    public List<SizeChartTemplate> selectByCondition(SizeChartTemplateQueryCondition condition) {
        return lambdaQuery().eq(StringUtils.isNotBlank(condition.getName()), SizeChartTemplate::getName, condition.getName())
                .eq(condition.getShopId() != null, SizeChartTemplate::getShopId, condition.getShopId())
                .eq(condition.getCategoryId() != null, SizeChartTemplate::getCategoryId, condition.getCategoryId())
                .eq(condition.getType() != null, SizeChartTemplate::getType, condition.getType())
                .eq(condition.getRuleId() != null, SizeChartTemplate::getRuleId, condition.getRuleId())
                .list();
    }
}
