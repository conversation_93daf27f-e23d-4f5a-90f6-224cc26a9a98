package com.voghion.product.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.voghion.product.model.po.SizeChartTemplate;
import com.voghion.product.model.vo.condition.SizeChartTemplateQueryCondition;

import java.util.List;

/**
 * <p>
 * 尺码图 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-17
 */
public interface SizeChartTemplateService extends BaseService<SizeChartTemplate> {

    SizeChartTemplate selectSystemTemplateByCategoryId(Long categoryId);

    List<SizeChartTemplate> selectSystemTemplateByCategoryIds(List<Long> categoryIds);

    IPage<SizeChartTemplate> selectPageByCondition(SizeChartTemplateQueryCondition condition);

    List<SizeChartTemplate> selectByCondition(SizeChartTemplateQueryCondition condition);
}
