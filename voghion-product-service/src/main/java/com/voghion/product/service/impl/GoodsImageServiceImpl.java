package com.voghion.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.voghion.product.model.po.GoodsImage;
import com.voghion.product.dal.mapper.GoodsImageMapper;
import com.voghion.product.model.po.GoodsItem;
import com.voghion.product.service.GoodsImageService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 商品的商品图片表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
@Service
public class GoodsImageServiceImpl extends BaseServiceImpl<GoodsImageMapper, GoodsImage> implements GoodsImageService {

    /**
     * 根据goodsIds 获取商品主图
     *
     * @param goodsIds
     * @return
     */
    @Override
    public List<GoodsImage> queryListByGoodsIds(List<Long> goodsIds) {
        if(CollectionUtils.isEmpty(goodsIds)){
            return new ArrayList<>();
        }
        QueryWrapper<GoodsImage> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("goods_id",goodsIds);
        return list(queryWrapper);
    }

    /**
     * 根据商品id 删除图片
     *
     * @param goodsId
     * @return
     */
    @Override
    public Boolean deleteByGoodsId(Long goodsId) {
        if (null == goodsId || goodsId <= 0) {
            return false;
        }
        QueryWrapper<GoodsImage> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("goods_id",goodsId);
        return remove(queryWrapper);
    }

    /**
     * 根据goodsId 获取商品主图
     *
     * @param goodsId
     * @return
     */
    @Override
    public List<GoodsImage> queryListByGoodsId(Long goodsId) {
        if (null == goodsId || goodsId <= 0) {
            return null;
        }
        QueryWrapper<GoodsImage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("goods_id",goodsId);
        return list(queryWrapper);
    }

    @Override
    public void deleteByGoodsIds(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return;
        }
        QueryWrapper<GoodsImage> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("goods_id", goodsIds);
        remove(queryWrapper);
    }
}
