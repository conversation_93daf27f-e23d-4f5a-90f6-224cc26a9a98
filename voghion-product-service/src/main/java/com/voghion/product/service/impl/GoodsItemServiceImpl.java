package com.voghion.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.colorlight.base.utils.CheckUtils;
import com.google.common.collect.Lists;
import com.voghion.product.dal.mapper.GoodsItemMapper;
import com.voghion.product.model.dto.GoodsItemDTO;
import com.voghion.product.model.dto.GoodsMinStockDto;
import com.voghion.product.model.enums.ProductResultCode;
import com.voghion.product.model.po.GoodsItem;
import com.voghion.product.service.GoodsItemService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 商品子项表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
@Service
public class GoodsItemServiceImpl extends BaseServiceImpl<GoodsItemMapper, GoodsItem> implements GoodsItemService {

    @Resource
    private GoodsItemMapper goodsItemMapper;

    /**
     * 根据skuIds 查询商品的sku信息
     *
     * @param skuIds
     * @return
     */
    @Override
    public List<GoodsItem> queryListBySkuIds(List<Long> skuIds) {
        List<GoodsItem> goodsItemList = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuIds)) {
            return goodsItemList;
        }
        QueryWrapper<GoodsItem> wrapper = new QueryWrapper<>();
        wrapper.in("sku_id", skuIds);
        goodsItemList = goodsItemMapper.selectList(wrapper);
        return goodsItemList;
    }

    @Override
    public List<GoodsItem> queryByGoodsId(Long goodsId) {
        CheckUtils.notNull(goodsId, ProductResultCode.PARAMETER_GOODS_ID_NULL);
        return lambdaQuery()
                .eq(GoodsItem::getGoodsId, goodsId)
                .list();
    }

    /**
     * 根据信息删除数据
     *
     * @param goodsItemInput
     * @return
     */
    @Override
    public boolean deleteGoodsItemByOption(GoodsItemDTO goodsItemInput) {
        if (null == goodsItemInput) {
            return false;
        }
        Long goodsId = goodsItemInput.getGoodsId();
        List<Long> goodsIds = goodsItemInput.getGoodsIds();
        Long skuId = goodsItemInput.getSkuId();
        List<Long> skuIds = goodsItemInput.getSkuIds();
        List<Long> ids = goodsItemInput.getIds();

        QueryWrapper<GoodsItem> queryWrapper = new QueryWrapper<>();

        if (null != goodsId && goodsId > 0) {
            queryWrapper.eq("goods_id", goodsId);
        }

        if (null != skuId && skuId > 0) {
            queryWrapper.eq("sku_id", skuId);
        }

        if (CollectionUtils.isNotEmpty(goodsIds)) {
            queryWrapper.in("goods_id", goodsIds);
        }

        if (CollectionUtils.isNotEmpty(skuIds)) {
            queryWrapper.in("sku_id", skuIds);
        }

        if (CollectionUtils.isNotEmpty(ids)) {
            queryWrapper.in("id", ids);
        }


        return remove(queryWrapper);
    }

    @Override
    public void deleteByGoodsIds(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return;
        }
        QueryWrapper<GoodsItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("goods_id", goodsIds);
        remove(queryWrapper);
    }

    @Override
    public boolean updateByGoodsId(GoodsItemDTO goodsItem) {
        if (null == goodsItem || goodsItem.getGoodsId() == null || goodsItem.getGoodsId() <= 0) {
            return false;
        }
        QueryWrapper<GoodsItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("goods_id", goodsItem.getGoodsId());

        return update(goodsItem, queryWrapper);
    }

    @Override
    public boolean updateBySkuId(GoodsItem goodsItem) {
        if (null == goodsItem || goodsItem.getSkuId() == null) {
            return false;
        }
        UpdateWrapper<GoodsItem> queryWrapper = new UpdateWrapper<>();
        queryWrapper.eq("sku_id", goodsItem.getSkuId());

        return update(goodsItem, queryWrapper);
    }

    /**
     * 根据商品id查询数据
     *
     * @param goodsId
     * @return
     */
    @Override
    public List<GoodsItem> queryGoodsItemByGoodsId(Long goodsId) {
        if (null == goodsId || goodsId <= 0) {
            return new ArrayList<>();
        }
        QueryWrapper<GoodsItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("goods_id", goodsId);
        return list(queryWrapper);
    }

    @Override
    public List<GoodsItem> queryGoodsIdsList(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Lists.newArrayList();
        }
        return lambdaQuery().in(GoodsItem::getGoodsId, goodsIds).list();
    }

    @Override
    public void batchUpdateStock(List<GoodsItem> updateList) {
        goodsItemMapper.batchUpdateStock(updateList);
    }
    @Override
    public List<GoodsMinStockDto> queryMinStockByGoodsIds(List<Long> goodsIds) {
        if (goodsIds.size() > 5000) {
            return Collections.emptyList();
        }
        return goodsItemMapper.queryMinStockByGoodsIds(goodsIds);
    }

    @Override
    public void cleanMarketPriceByGoodsId(Long goodsId) {
        lambdaUpdate()
                .eq(GoodsItem::getGoodsId, goodsId)
                .set(GoodsItem::getOrginalMarketPrice, null)
                .set(GoodsItem::getMarketPrice, null)
                .set(GoodsItem::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public List<GoodsItem> queryOutOffStockItemsByGoodsIds(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Lists.newArrayList();
        }
        QueryWrapper<GoodsItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id","sku_id","goods_id","stock","lock_stock");
        queryWrapper.eq("sku_status",1);
        queryWrapper.le("stock",0);
        queryWrapper.le("lock_stock",0);
        queryWrapper.in("goods_id",goodsIds);
        return this.baseMapper.selectList(queryWrapper);
    }


}
