package com.voghion.product.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.voghion.product.model.po.GoodsTag;
import com.voghion.product.model.vo.condition.GoodsTagCondition;

/**
 * <p>
 * 商品标签 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-09
 */
public interface GoodsTagService extends BaseService<GoodsTag> {

    /**
     * 根据搜索条件查询
     *
     * @param goodsTagCondition
     * @return
     */
    IPage<GoodsTag> queryPageByCondition(GoodsTagCondition goodsTagCondition);
}
