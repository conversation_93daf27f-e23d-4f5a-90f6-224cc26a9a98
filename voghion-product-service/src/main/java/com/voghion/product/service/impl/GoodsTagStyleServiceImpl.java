package com.voghion.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.voghion.product.dal.mapper.GoodsTagStyleMapper;
import com.voghion.product.model.po.GoodsTagStyle;
import com.voghion.product.service.GoodsTagStyleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商品标签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-30
 */
@Service
public class GoodsTagStyleServiceImpl extends BaseServiceImpl<GoodsTagStyleMapper, GoodsTagStyle> implements GoodsTagStyleService {

    @Override
    public void deleteByTagId(Long tagId) {
        QueryWrapper<GoodsTagStyle> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tag_id", tagId);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public List<GoodsTagStyle> selectByTagIds(List<Long> tagIds) {
        return lambdaQuery().in(GoodsTagStyle::getTagId, tagIds).list();
    }
}
