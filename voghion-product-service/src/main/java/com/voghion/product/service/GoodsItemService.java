package com.voghion.product.service;

import com.voghion.product.model.dto.GoodsItemDTO;
import com.voghion.product.model.dto.GoodsMinStockDto;
import com.voghion.product.model.po.GoodsItem;

import java.util.List;
/**
 * <p>
 * 商品子项表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
public interface GoodsItemService extends BaseService<GoodsItem> {

    /**
     * 根据skuIds 查询所有的商品的sku信息
     * @param skuIds
     * @return
     */
    List<GoodsItem>  queryListBySkuIds(List<Long> skuIds);

    List<GoodsItem> queryByGoodsId(Long goodsId);

    /**
     * 根据信息删除数据
     *
     * @param goodsItemInput
     * @return
     */
    boolean deleteGoodsItemByOption(GoodsItemDTO goodsItemInput);

    void deleteByGoodsIds(List<Long> goodsIds);

    boolean updateByGoodsId(GoodsItemDTO goodsItem);

    boolean updateBySkuId(GoodsItem goodsItem);

    /**
     * 根据商品id查询数据
     * @param goodsId
     * @return
     */
    List<GoodsItem> queryGoodsItemByGoodsId(Long goodsId);


    List<GoodsItem> queryGoodsIdsList(List<Long> goodsId);

    void batchUpdateStock(List<GoodsItem> updateList);

    List<GoodsMinStockDto> queryMinStockByGoodsIds(List<Long> goodsIds);
    /**
     * 清空划线价
     *
     * @param goodsId 商品id
     */
    void cleanMarketPriceByGoodsId(Long goodsId);

    List<GoodsItem> queryOutOffStockItemsByGoodsIds(List<Long> goodsIds);

}
