package com.voghion.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.voghion.product.model.po.goods.GoodsSkuPo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/14 16:34
 */
public interface GoodsSkuService extends IService<GoodsSkuPo> {

    List<GoodsSkuPo> findListByGoodsId(Long goodsId);

    List<GoodsSkuPo> findListByGoodsSkuIds(List<Long> skuIds, Long goodsId);

    boolean delByGoodsSkuIds(List<Long> skuIds, Long goodsId);

    boolean updateBatchByIdAndGoodsId(Collection<GoodsSkuPo> entityList);

    List<GoodsSkuPo> findListByGoodsIds(List<Long> goodsIds);

    GoodsSkuPo findOneByIdAndGoodsId(Long id, Long goodsId);

    boolean removeBatchByGoodsId(List<Long> goodsIds);

}
