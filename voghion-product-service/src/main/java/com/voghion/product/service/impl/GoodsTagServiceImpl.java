package com.voghion.product.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.voghion.product.dal.mapper.GoodsTagMapper;
import com.voghion.product.model.po.GoodsTag;
import com.voghion.product.model.vo.condition.GoodsTagCondition;
import com.voghion.product.service.GoodsTagService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品标签 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-09
 */
@Service
public class GoodsTagServiceImpl extends BaseServiceImpl<GoodsTagMapper, GoodsTag> implements GoodsTagService {

    @Override
    public IPage<GoodsTag> queryPageByCondition(GoodsTagCondition goodsTagCondition) {
        List<Long> tagIds = null;
        if (StringUtils.isNotEmpty(goodsTagCondition.getTagIdListStr())) {
            tagIds = Arrays.stream(goodsTagCondition.getTagIdListStr().split("\n"))
                    .filter(StringUtils::isNotBlank)
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toList());
        }

        return lambdaQuery().and(CollectionUtils.isNotEmpty(goodsTagCondition.getCountryList()), wp -> {
                    List<String> countryList = goodsTagCondition.getCountryList();
                    if (CollectionUtils.isNotEmpty(countryList)) {
                        for (int i = 0; i < countryList.size(); i++) {
                            wp.like(GoodsTag::getCountry, countryList.get(i));
                            if (i != countryList.size() - 1) {
                                wp.or();
                            }
                        }
                    }
                    return wp;
                })
                .like(StringUtils.isNotBlank(goodsTagCondition.getTagName()), GoodsTag::getTagName, goodsTagCondition.getTagName())
                .like(StringUtils.isNotBlank(goodsTagCondition.getUpdateUser()), GoodsTag::getUpdateUser, goodsTagCondition.getUpdateUser())
                .eq(goodsTagCondition.getIsShow() != null, GoodsTag::getIsShow, goodsTagCondition.getIsShow())
                .eq(goodsTagCondition.getType() != null, GoodsTag::getType, goodsTagCondition.getType())
                .in(CollectionUtils.isNotEmpty(goodsTagCondition.getTypeList()), GoodsTag::getType, goodsTagCondition.getTypeList())
                .in(CollectionUtils.isNotEmpty(tagIds), GoodsTag::getId, tagIds)
                .eq(GoodsTag::getIsDel, 0)
                .orderByDesc(GoodsTag::getSort)
                .page(new Page<>(goodsTagCondition.getPageNow(), goodsTagCondition.getPageSize()));
    }
}
