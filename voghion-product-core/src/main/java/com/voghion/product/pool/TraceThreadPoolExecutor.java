package com.voghion.product.pool;

import com.voghion.product.context.TraceContext;
import org.slf4j.MDC;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/4/7 18:17
 */
public class TraceThreadPoolExecutor extends ThreadPoolExecutor {

    public TraceThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    @Override
    public void execute(Runnable command) {
        Runnable runnable = runnableWarp(command);
        super.execute(runnable);
    }

    private static Runnable runnableWarp(Runnable command) {
        String trace = TraceContext.trace();
        return () -> {
            try {
                TraceContext.traceInit(trace);
                MDC.put(TraceContext.TRACE_ID, trace);
                command.run();
            } catch (Exception e) {
                throw e;
            } finally {
                TraceContext.remove();
                MDC.remove(TraceContext.TRACE_ID);
            }
        };
    }

}
