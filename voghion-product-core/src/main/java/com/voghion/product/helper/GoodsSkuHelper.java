package com.voghion.product.helper;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.voghion.product.api.dto.GoodsInfoInputVO;
import com.voghion.product.api.dto.GoodsItemVO;
import com.voghion.product.model.bo.*;
import com.voghion.product.model.constant.CheckConstant;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.po.goods.*;
import com.voghion.product.model.vo.ProductInfoInput;
import com.voghion.product.service.*;
import com.voghion.product.util.AssertsUtils;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.util.GoodsExtDetailUtils;
import com.voghion.product.util.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/15 10:53
 */
@Slf4j
@Component
public class GoodsSkuHelper {

    @Resource
    private PropertyHelper propertyHelper;

    @Resource
    private GoodsPropertyRelevantService goodsPropertyRelevantService;

    @Resource
    private GoodsPropertyImgRelevantService goodsPropertyImgRelevantService;

    @Resource
    private GoodsPropertySkuRelevantService goodsPropertySkuRelevantService;

    @Resource
    private GoodsSkuService goodsSkuService;

    @Resource
    private PropertyAssociationService propertyAssociationService;

    /**
     * 规格名 删除 | 新增
     */
    public List<GoodsSkuPo> addOrDelPropertyNameAndReturnSku(Long goodsId, ProductInfoInput goodsInfoInput) {
        List<PropertyDTO> properties = goodsInfoInput.getProperties();
        List<GoodsPropertyRelevantPo> goodsPropertyRelevantPos = goodsPropertyRelevantService.findListByGoodsId(goodsId);
        if (CollectionUtils.isNotEmpty(goodsPropertyRelevantPos)) {
            delPropertyRelevantInfo(goodsPropertyRelevantPos, Collections.emptyList(), goodsId);
        }

        return addGoodsSkuList(goodsId, goodsInfoInput, properties);
    }

    /**
     * 规格值 删除 | 新增（不用全删）
     * <br />
     * 需要考虑老商品的skuId(更新的保存不变)
     */
    public List<GoodsSkuPo> addOrDelPropertyValueAndReturnSku(Long goodsId, ProductInfoInput goodsInfoInput) {
        List<PropertyDTO> properties = goodsInfoInput.getProperties();
        List<GoodsSkuPo> originalGoodsSkuList = goodsSkuService.findListByGoodsId(goodsId);
        if (CollectionUtils.isEmpty(originalGoodsSkuList)) {
            return addGoodsSkuList(goodsId, goodsInfoInput, properties);
        }

        List<GoodsPropertyRelevantPo> originalGoodsPropertyRelevantPos = goodsPropertyRelevantService.findListByGoodsId(goodsId);
        List<Long> propertyRelevantIdList = properties.stream().map(PropertyDTO::getValues).flatMap(Collection::stream).map(PropertyValueDTO::getGoodsPropertyRelevantId).filter(Objects::nonNull).collect(Collectors.toList());

        // 删除
        delPropertyRelevantInfo(originalGoodsPropertyRelevantPos, propertyRelevantIdList, goodsId);

        // 非删除
        List<GoodsPropertyRelevantPo> propertyRelevantList = originalGoodsPropertyRelevantPos.stream().filter(po -> propertyRelevantIdList.contains(po.getId())).collect(Collectors.toList());

        // 全量的
        Map<String, ProductReplaceBo> replaceMap = propertyHelper.getProductReplaceMap(properties);
        List<NewAddPropertyBo> newAddPropertyBos = getNewAddPropertyBos(properties, replaceMap);

        // 新增 修改
        List<GoodsPropertyRelevantPo> goodsPropertyRelevantList = getGoodsPropertyRelevantPos(goodsId, properties, newAddPropertyBos, propertyRelevantList);
        if (CollectionUtils.isEmpty(goodsPropertyRelevantList)) {

            // 规格没有变化，判断图片是否修改
            goodsPropertyRelevantList = goodsPropertyRelevantService.findListByGoodsId(goodsId);

            // 由于原来不管是更新还是新增，规格id都会变更，所以需要修改
            List<PropertyAssociationPo> propertyAssociationPos = getPropertyAssociationPos(goodsId, properties, goodsPropertyRelevantList);
            AssertsUtils.isValidateTrue(propertyAssociationService.saveOrUpdateBatch(propertyAssociationPos), "propertyAssociationPos saveOrUpdateBatch fail. propertyAssociationPos:" + JSONObject.toJSONString(propertyAssociationPos));

            Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap = getGoodsPropertyRelevantMap(goodsPropertyRelevantList, propertyAssociationPos);
            // 修改图片
            List<GoodsPropertyImgRelevantPo> goodsPropertyImgRelevantPos = modifyImgRelevantInfo(goodsId, newAddPropertyBos, goodsPropertyRelevantMap);

            // 外来数据skuInfo可能和规格对不上
            List<GoodsItemDTO> completeSkuList = goodsInfoInput.getSkuInfo().stream().filter(skuDto -> skuDto.getSkuId() == null).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(completeSkuList)) {
                // 新增
                List<GoodsSkuPo> goodsSkuList = Lists.newArrayList();
                List<GoodsPropertySkuRelevantPo> skuRelevantPos = Lists.newArrayList();

                Map<Long, Optional<GoodsPropertyImgRelevantPo>> goodsPropertyImgMap = getGoodsPropertyImgMap(goodsPropertyImgRelevantPos);
                for (GoodsItemDTO goodsItemDTO : completeSkuList) {
                    List<GoodsPropertyRelevantPo> relevantList = getRelevantList(replaceMap, goodsPropertyRelevantMap, goodsItemDTO.getSkuProperty());
                    String skuCode = String.format("%d-%d", goodsId, SnowflakeUtil.generateId());
                    skuRelevantPos.addAll(getSkuRelevantList(goodsId, relevantList, skuCode));
                    goodsSkuList.add(getGoodsSkuPo(goodsInfoInput, goodsId, goodsItemDTO, skuCode, relevantList, goodsPropertyImgMap));
                }

                AssertsUtils.isValidateTrue(goodsPropertySkuRelevantService.saveOrUpdateBatch(skuRelevantPos), "skuRelevantPos saveBatch fail. skuRelevantPos:" + JSONObject.toJSONString(skuRelevantPos));
                AssertsUtils.isValidateTrue(goodsSkuService.saveBatch(goodsSkuList), "goodsSkuList saveBatch fail. goodsSkuList:" + JSONObject.toJSONString(goodsSkuList));

                modifySkuInfoMutualBinding(skuRelevantPos, goodsSkuList, goodsId);
            }

            // sku有可能被删除，重新查询，上面的新增更新不是一个session，走一级缓存，拿到的是缓存数据
            originalGoodsSkuList = goodsSkuService.findListByGoodsIds(Collections.singletonList(goodsId));

            // 修改sku信息
            modifySkuInfo(goodsInfoInput, goodsPropertyImgRelevantPos, originalGoodsSkuList, replaceMap, goodsPropertyRelevantMap, propertyAssociationPos, goodsId);

            return originalGoodsSkuList;
        }

        AssertsUtils.isValidateTrue(CollectionUtils.isNotEmpty(goodsPropertyRelevantList), "goodsPropertyRelevantList is empty. goodsInfoInput:" + JSONObject.toJSONString(goodsInfoInput));
        AssertsUtils.isValidateTrue(goodsPropertyRelevantService.saveOrUpdateBatch(goodsPropertyRelevantList), "goodsPropertyRelevantList saveBatch fail. goodsPropertyRelevantList:" + JSONObject.toJSONString(goodsPropertyRelevantList));

        fillGoodsPropertyRelevantPos(propertyRelevantList, goodsPropertyRelevantList);
        List<PropertyAssociationPo> propertyAssociationPos = getPropertyAssociationPos(goodsId, properties, goodsPropertyRelevantList);
        AssertsUtils.isValidateTrue(propertyAssociationService.saveOrUpdateBatch(propertyAssociationPos), "propertyAssociationPos saveOrUpdateBatch fail. propertyAssociationPos:" + JSONObject.toJSONString(propertyAssociationPos));

        // key 为老ppId
        Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap = getGoodsPropertyRelevantMap(goodsPropertyRelevantList, propertyAssociationPos);

        // 修改图片
        List<GoodsPropertyImgRelevantPo> goodsPropertyImgRelevantPos = modifyImgRelevantInfo(goodsId, newAddPropertyBos, goodsPropertyRelevantMap);
        Map<Long, Optional<GoodsPropertyImgRelevantPo>> goodsPropertyImgMap = getGoodsPropertyImgMap(goodsPropertyImgRelevantPos);

        // sku有可能被删除，重新查询
        originalGoodsSkuList = goodsSkuService.findListByGoodsIds(Collections.singletonList(goodsId));

        // 新增
        List<GoodsSkuPo> goodsSkuList = Lists.newArrayList();
        updateAndAddGoodsSkuInfo(goodsId, goodsInfoInput, propertyAssociationPos, originalGoodsSkuList, replaceMap, goodsPropertyRelevantMap, goodsSkuList, goodsPropertyImgMap);
        return goodsSkuList;
    }

    private void modifySkuInfo(ProductInfoInput goodsInfoInput, List<GoodsPropertyImgRelevantPo> goodsPropertyImgRelevantPos, List<GoodsSkuPo> originalGoodsSkuList,
                               Map<String, ProductReplaceBo> replaceMap, Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap, List<PropertyAssociationPo> propertyAssociationPos,
                               Long goodsId) {
        Map<Long, Optional<GoodsPropertyImgRelevantPo>> goodsPropertyImgMap = getGoodsPropertyImgMap(goodsPropertyImgRelevantPos);

        Map<Long, Map<Long, PropertyAssociationPo>> propertyAssociationMap = propertyAssociationPos.stream().collect(Collectors.groupingBy(PropertyAssociationPo::getPropertyId, Collectors.mapping(Function.identity(), Collectors.toMap(PropertyAssociationPo::getPropertyValueId, Function.identity()))));

        List<AssembleGoodsPropertySkuRelevantBo> assemblePropertySkuRelevantList = getAssembleGoodsPropertySkuRelevantBos(goodsId);

        Map<Long, GoodsSkuPo> originalSkuMap = originalGoodsSkuList.stream().collect(Collectors.toMap(GoodsSkuPo::getId, Function.identity()));

        for (GoodsItemDTO goodsItemDTO : goodsInfoInput.getSkuInfo()) {
            Long skuId = Optional.ofNullable(goodsItemDTO.getGoodsSkuId()).orElse(goodsItemDTO.getSkuId());
            if (skuId == null) {
                continue;
            }

            List<GoodsPropertyRelevantPo> relevantList = getRelevantList(replaceMap, goodsPropertyRelevantMap, goodsItemDTO.getSkuProperty());
            AssembleGoodsPropertySkuRelevantBo skuRelevantBo = getAssembleGoodsPropertySkuRelevantBo(goodsItemDTO, propertyAssociationMap, assemblePropertySkuRelevantList, goodsInfoInput.getProperties());
            if (skuRelevantBo != null) {
                GoodsSkuPo skuPo = originalSkuMap.get(skuId);
                AssertsUtils.isValidateTrue(skuPo != null, "skuPo is empty. goodsSkuId:" + skuRelevantBo.getGoodsSkuId());
                skuPo.setOriginalSkuId(goodsItemDTO.getOriginalSkuId());
                skuPo.setOriginalPrice(goodsItemDTO.getOrginalPrice());
                skuPo.setPrice(goodsItemDTO.getPrice());
                skuPo.setMarketPrice(skuPo.getPrice().divide(goodsInfoInput.getDiscount(), 2, BigDecimal.ROUND_HALF_UP));
                skuPo.setOriginalMarketPrice(getOriginalMarketPrice(goodsItemDTO.getOrginalMarketPrice(), skuPo));
                skuPo.setCostPrice(goodsItemDTO.getCostPrice());
                skuPo.setSkuImage(getSkuImage(goodsItemDTO.getImageUrl(), relevantList, goodsPropertyImgMap));
                skuPo.setStock(goodsItemDTO.getStock());
                skuPo.setSold(goodsItemDTO.getSold());
                skuPo.setPackageSize(GoodsExtDetailUtils.getGoodsItemPackageSizeString(goodsInfoInput.getPackageSize()));
                skuPo.setWeight(GoodsExtDetailUtils.getWeight(goodsInfoInput.getWeight()));
                skuPo.setDefaultDelivery(goodsItemDTO.getDefaultDelivery());
                skuPo.setMinPurchaseQuantity(Optional.ofNullable(goodsItemDTO.getMinPurchaseQuantity()).orElse(1));
            }
        }

        goodsSkuService.updateBatchByIdAndGoodsId(originalGoodsSkuList);
    }

    private static void fillGoodsPropertyRelevantPos(List<GoodsPropertyRelevantPo> propertyRelevantList, List<GoodsPropertyRelevantPo> goodsPropertyRelevantList) {
        Map<Long, GoodsPropertyRelevantPo> relevantPoMap = goodsPropertyRelevantList.stream().collect(Collectors.toMap(GoodsPropertyRelevantPo::getId, Function.identity()));
        for (GoodsPropertyRelevantPo relevantPo : propertyRelevantList) {
            GoodsPropertyRelevantPo modifyRelevantPo = relevantPoMap.get(relevantPo.getId());
            if (modifyRelevantPo == null) {
                goodsPropertyRelevantList.add(relevantPo);
            }
        }
    }

    private void updateAndAddGoodsSkuInfo(Long goodsId, ProductInfoInput goodsInfoInput, List<PropertyAssociationPo> propertyAssociationPos, List<GoodsSkuPo> originalGoodsSkuList, Map<String, ProductReplaceBo> replaceMap,
                                          Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap, List<GoodsSkuPo> goodsSkuList, Map<Long, Optional<GoodsPropertyImgRelevantPo>> goodsPropertyImgMap) {
        // 修改
        List<GoodsSkuPo> updateGoodsSkuList = Lists.newArrayList();

        List<GoodsPropertySkuRelevantPo> skuRelevantPos = Lists.newArrayList();
        // Map<Long, Optional<GoodsPropertyImgRelevantPo>> goodsPropertyImgMap = getGoodsPropertyImgMap(goodsId);

        // 需要匹配数据
        Map<Long, Map<Long, PropertyAssociationPo>> propertyAssociationMap = propertyAssociationPos.stream().collect(Collectors.groupingBy(PropertyAssociationPo::getPropertyId, Collectors.mapping(Function.identity(), Collectors.toMap(PropertyAssociationPo::getPropertyValueId, Function.identity()))));
        Map<Long, GoodsSkuPo> originalSkuMap = originalGoodsSkuList.stream().collect(Collectors.toMap(GoodsSkuPo::getId, Function.identity()));
        List<AssembleGoodsPropertySkuRelevantBo> assemblePropertySkuRelevantList = getAssembleGoodsPropertySkuRelevantBos(goodsId);
        for (GoodsItemDTO goodsItemDTO : goodsInfoInput.getSkuInfo()) {
            List<GoodsPropertyRelevantPo> relevantList = getRelevantList(replaceMap, goodsPropertyRelevantMap, goodsItemDTO.getSkuProperty());
            AssembleGoodsPropertySkuRelevantBo skuRelevantBo = getAssembleGoodsPropertySkuRelevantBo(goodsItemDTO, propertyAssociationMap, assemblePropertySkuRelevantList, goodsInfoInput.getProperties());
            if (skuRelevantBo != null) {
                GoodsSkuPo skuPo = changeGoodsSku(goodsId, goodsInfoInput, goodsItemDTO, originalSkuMap, skuRelevantBo, relevantList, skuRelevantPos, goodsPropertyImgMap);
                updateGoodsSkuList.add(skuPo);
                continue;
            }

            String skuCode = String.format("%d-%d", goodsId, SnowflakeUtil.generateId());
            skuRelevantPos.addAll(getSkuRelevantList(goodsId, relevantList, skuCode));
            goodsSkuList.add(getGoodsSkuPo(goodsInfoInput, goodsId, goodsItemDTO, skuCode, relevantList, goodsPropertyImgMap));
        }

        AssertsUtils.isValidateTrue(goodsPropertySkuRelevantService.saveOrUpdateBatch(skuRelevantPos), "skuRelevantPos saveBatch fail. skuRelevantPos:" + JSONObject.toJSONString(skuRelevantPos));
        if (CollectionUtils.isNotEmpty(goodsSkuList)) {
            AssertsUtils.isValidateTrue(goodsSkuService.saveBatch(goodsSkuList), "goodsSkuList saveBatch fail. goodsSkuList:" + JSONObject.toJSONString(goodsSkuList));
        }

        if (CollectionUtils.isNotEmpty(updateGoodsSkuList)) {
            AssertsUtils.isValidateTrue(goodsSkuService.updateBatchByIdAndGoodsId(updateGoodsSkuList), "updateGoodsSkuList updateBatchByIdAndGoodsId fail. goodsSkuList:" + JSONObject.toJSONString(updateGoodsSkuList));
        }

        modifySkuInfoMutualBinding(skuRelevantPos, goodsSkuList, goodsId);

        goodsSkuList.addAll(updateGoodsSkuList);
    }

    @NotNull
    private Map<Long, Optional<GoodsPropertyImgRelevantPo>> getGoodsPropertyImgMap(Long goodsId) {
        List<GoodsPropertyImgRelevantPo> imgRelevantPoList = goodsPropertyImgRelevantService.findListByGoodsId(goodsId);
        return getGoodsPropertyImgMap(imgRelevantPoList);
    }

    @NotNull
    private static Map<Long, Optional<GoodsPropertyImgRelevantPo>> getGoodsPropertyImgMap(List<GoodsPropertyImgRelevantPo> imgRelevantPoList) {
        if (CollectionUtils.isEmpty(imgRelevantPoList)) {
            return Maps.newHashMap();
        }

        return imgRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertyImgRelevantPo::getGoodsPropertyRelevantId, Collectors.mapping(Function.identity(), Collectors.minBy(Comparator.comparing(GoodsPropertyImgRelevantPo::getSort)))));
    }

    private List<AssembleGoodsPropertySkuRelevantBo> getAssembleGoodsPropertySkuRelevantBos(Long goodsId) {
        List<GoodsPropertySkuRelevantPo> originalGoodsPropertySkuRelevantList = goodsPropertySkuRelevantService.findListByGoodsId(goodsId);
        if (CollectionUtils.isEmpty(originalGoodsPropertySkuRelevantList)) {
            return Collections.emptyList();
        }

        return buildAssemblePropertySkuRelevantList(originalGoodsPropertySkuRelevantList);
    }

    @NotNull
    private static GoodsSkuPo changeGoodsSku(Long goodsId, ProductInfoInput goodsInfoInput, GoodsItemDTO goodsItemDTO, Map<Long, GoodsSkuPo> originalSkuMap, AssembleGoodsPropertySkuRelevantBo skuRelevantBo, List<GoodsPropertyRelevantPo> relevantList, List<GoodsPropertySkuRelevantPo> skuRelevantPos, Map<Long, Optional<GoodsPropertyImgRelevantPo>> goodsPropertyImgMap) {
        GoodsSkuPo skuPo = originalSkuMap.get(skuRelevantBo.getGoodsSkuId());
        AssertsUtils.isValidateTrue(skuPo != null, "skuPo is empty. goodsSkuId:" + skuRelevantBo.getGoodsSkuId());

        List<GoodsPropertySkuRelevantPo> propertySkuRelevantPos = getGoodsPropertySkuRelevantByPPRelevantId(skuRelevantBo, relevantList, goodsId);
        skuRelevantPos.addAll(propertySkuRelevantPos);

        String skuRelevantIdStr = propertySkuRelevantPos.stream().sorted(Comparator.comparing(GoodsPropertySkuRelevantPo::getSort)).map(GoodsPropertySkuRelevantPo::getId).map(String::valueOf).collect(Collectors.joining("_"));
        skuPo.setSkuPropertyRelevantStr(goodsId + "_" + skuRelevantIdStr);
        skuPo.setName(getName(relevantList));
        skuPo.setOriginalSkuId(goodsItemDTO.getOriginalSkuId());
        skuPo.setOriginalPrice(goodsItemDTO.getOrginalPrice());
        skuPo.setPrice(goodsItemDTO.getPrice());
        skuPo.setMarketPrice(skuPo.getPrice().divide(goodsInfoInput.getDiscount(), 2, BigDecimal.ROUND_HALF_UP));
        skuPo.setOriginalMarketPrice(getOriginalMarketPrice(goodsItemDTO.getOrginalMarketPrice(), skuPo));
        skuPo.setCostPrice(goodsItemDTO.getCostPrice());
        skuPo.setSkuImage(getSkuImage(goodsItemDTO.getImageUrl(), relevantList, goodsPropertyImgMap));
        skuPo.setStock(goodsItemDTO.getStock());
        skuPo.setSold(goodsItemDTO.getSold());
        skuPo.setPackageSize(GoodsExtDetailUtils.getGoodsItemPackageSizeString(goodsInfoInput.getPackageSize()));
        skuPo.setWeight(GoodsExtDetailUtils.getWeight(goodsInfoInput.getWeight()));
        skuPo.setDefaultDelivery(goodsItemDTO.getDefaultDelivery());
        skuPo.setMinPurchaseQuantity(Optional.ofNullable(goodsItemDTO.getMinPurchaseQuantity()).orElse(1));
        skuPo.setSkuStatus(CheckConstant.GOODS_NORMAL_STATUS);
        return skuPo;
    }

    private static String getSkuImage(String imageUrl, List<GoodsPropertyRelevantPo> relevantList, Map<Long, Optional<GoodsPropertyImgRelevantPo>> goodsPropertyImgMap) {
        if (StringUtils.isNotBlank(imageUrl)) {
            return imageUrl;
        }

        return relevantList.stream().map(po -> {
            Optional<GoodsPropertyImgRelevantPo> imgRelevantPo = goodsPropertyImgMap.get(po.getId());
            if (imgRelevantPo == null || !imgRelevantPo.isPresent()) {
                return null;
            }

            return imgRelevantPo.get();
        }).filter(Objects::nonNull).findFirst().map(GoodsPropertyImgRelevantPo::getImgUrl).orElse(null);
    }

    @NotNull
    private static List<GoodsPropertySkuRelevantPo> getGoodsPropertySkuRelevantByPPRelevantId(AssembleGoodsPropertySkuRelevantBo skuRelevantBo, List<GoodsPropertyRelevantPo> relevantList, Long goodsId) {
        Map<Long, Long> propertyAndSkuRelevantMap = skuRelevantBo.getPropertyAndSkuRelevantMap();
        AtomicInteger atomicIndex = new AtomicInteger(0);
        List<GoodsPropertySkuRelevantPo> skuRelevantPos = relevantList.stream()
                .sorted(Comparator.comparing(GoodsPropertyRelevantPo::getNameSort).thenComparing(GoodsPropertyRelevantPo::getValueSort))
                .map(relevantPo -> {
                    Long relevantId = relevantPo.getId();
                    GoodsPropertySkuRelevantPo skuRelevantPo = new GoodsPropertySkuRelevantPo();
                    skuRelevantPo.setId(propertyAndSkuRelevantMap.get(relevantId));
                    skuRelevantPo.setGoodsId(goodsId);
                    skuRelevantPo.setSort(atomicIndex.getAndIncrement());
                    return skuRelevantPo;
                }).collect(Collectors.toList());
        return skuRelevantPos;
    }

    private AssembleGoodsPropertySkuRelevantBo getAssembleGoodsPropertySkuRelevantBo(GoodsItemDTO goodsItemDTO, Map<Long, Map<Long, PropertyAssociationPo>> propertyAssociationMap,
                                                                                     List<AssembleGoodsPropertySkuRelevantBo> assemblePropertySkuRelevantList, List<PropertyDTO> properties) {
        // 不能直接使用goodsItemDto中的pvalueStr，在修改别名时会有问题
        Map<String, Long> propertyNameMap = Maps.newHashMap();
        Map<String, Map<String, Long>> propertyValueIdMap = Maps.newHashMap();
        for (PropertyDTO propertyDTO : properties) {
            List<PropertyValueDTO> propertyDTOValues = propertyDTO.getValues();
            Map<String, Long> valueMap = propertyDTOValues.stream().collect(Collectors.toMap(PropertyValueDTO::getValue, PropertyValueDTO::getId, (v1, v2) -> v1));
            propertyValueIdMap.put(propertyDTO.getName(), valueMap);
            propertyNameMap.put(propertyDTO.getName(), propertyDTO.getId());
        }

        List<Long> relevantIdList = Lists.newArrayList();
        for (Map.Entry<String, String> entry : goodsItemDTO.getSkuProperty().entrySet()) {
            String key = entry.getKey();
            Long propertyId = propertyNameMap.get(key);
            Long propertyValueId = propertyValueIdMap.get(key).get(entry.getValue());
            PropertyAssociationPo associationPo = propertyAssociationMap.get(propertyId).get(propertyValueId);
            if (associationPo == null) {
                break;
            }

            relevantIdList.add(associationPo.getPropertyRelevantId());

        }

        for (AssembleGoodsPropertySkuRelevantBo skuRelevantBo : assemblePropertySkuRelevantList) {
            if (CollectionUtils.isEqualCollection(skuRelevantBo.getPropertyRelevantIdList(), relevantIdList)) {
                return skuRelevantBo;
            }
        }

        return null;
    }

    public List<GoodsSkuPo> changePropertyAndReturnSku(Long goodsId, ProductInfoInput goodsInfoInput) {
        List<PropertyDTO> properties = goodsInfoInput.getProperties();
        List<GoodsPropertyRelevantPo> goodsPropertyRelevantPos = goodsPropertyRelevantService.findListByGoodsId(goodsId);
        boolean majorModifications = majorModifications(goodsPropertyRelevantPos, properties);
        if (majorModifications) {
            log.info("【修改sku】majorModifications.");
            return addOrDelPropertyNameAndReturnSku(goodsId, goodsInfoInput);
        }

        return addOrDelPropertyValueAndReturnSku(goodsId, goodsInfoInput);
    }

    private List<GoodsPropertyImgRelevantPo> modifyImgRelevantInfo(Long goodsId, List<NewAddPropertyBo> newAddPropertyBos, Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap) {
        // 判断图片是否有修改，暂时这样，后续入参要有GoodsPropertyImgRelevantVo
        List<GoodsPropertyImgRelevantPo> oldImgRelevantList = goodsPropertyImgRelevantService.findListByGoodsId(goodsId);
        List<GoodsPropertyImgRelevantPo> newImgRelevantList = GoodsPropertyRelevantHelper.getGoodsPropertyImgRelevantPosByPropertyId(newAddPropertyBos, goodsPropertyRelevantMap, goodsId);

//        Map<Long, Map<String, Long>> oldImgRelevantMap = oldImgRelevantList.stream().collect(Collectors.groupingBy(GoodsPropertyImgRelevantPo::getGoodsPropertyRelevantId, Collectors.mapping(Function.identity(), Collectors.toMap(GoodsPropertyImgRelevantPo::getImgUrl, GoodsPropertyImgRelevantPo::getId))));
//        for (GoodsPropertyImgRelevantPo imgRelevantPo : newImgRelevantList) {
//            Long imgRelevantId = oldImgRelevantMap.get(imgRelevantPo.getGoodsPropertyRelevantId()).get(imgRelevantPo.getImgUrl());
//            if (imgRelevantId != null && imgRelevantPo.getId() == null) {
//                imgRelevantPo.setId(imgRelevantId);
//            }
//        }

        List<GoodsPropertyImgRelevantPo> imgRelevantList = oldImgRelevantList;
        List<Long> imgRelevantIds = newImgRelevantList.stream().map(GoodsPropertyImgRelevantPo::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> delImgRelevantIds = oldImgRelevantList.stream().map(GoodsPropertyImgRelevantPo::getId).filter(id -> !imgRelevantIds.contains(id)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(delImgRelevantIds)) {
            AssertsUtils.isValidateTrue(goodsPropertyImgRelevantService.delImgRelevantByIds(delImgRelevantIds, goodsId), "delImgRelevantIds fail. delImgRelevantIds:" + JSONObject.toJSONString(delImgRelevantIds));
            imgRelevantList = oldImgRelevantList.stream().filter(po -> !delImgRelevantIds.contains(po.getId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(newImgRelevantList)) {
            AssertsUtils.isValidateTrue(goodsPropertyImgRelevantService.saveOrUpdateBatch(newImgRelevantList), "saveOrUpdateBatch fail. newImgRelevantList:" + JSONObject.toJSONString(newImgRelevantList));
            imgRelevantList.addAll(newImgRelevantList);
        }

        return imgRelevantList;
    }

    @NotNull
    public static Map<Long, Map<Long, GoodsPropertyRelevantPo>> getGoodsPropertyRelevantMap(List<GoodsPropertyRelevantPo> goodsPropertyRelevantList, List<PropertyAssociationPo> propertyAssociationPos) {
        Map<Long, GoodsPropertyRelevantPo> relevantPoMap = goodsPropertyRelevantList.stream().collect(Collectors.toMap(GoodsPropertyRelevantPo::getId, Function.identity()));
        Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap = Maps.newHashMap();
        for (PropertyAssociationPo associationPo : propertyAssociationPos) {
            Map<Long, GoodsPropertyRelevantPo> propertyRelevantPoMap = goodsPropertyRelevantMap.get(associationPo.getPropertyId());
            if (MapUtils.isEmpty(propertyRelevantPoMap)) {
                propertyRelevantPoMap = Maps.newHashMap();
            }

            propertyRelevantPoMap.put(associationPo.getPropertyValueId(), relevantPoMap.get(associationPo.getPropertyRelevantId()));
            goodsPropertyRelevantMap.put(associationPo.getPropertyId(), propertyRelevantPoMap);
        }

        return goodsPropertyRelevantMap;
    }

    @NotNull
    public List<PropertyAssociationPo> getPropertyAssociationPos(Long goodsId, List<PropertyDTO> properties, List<GoodsPropertyRelevantPo> goodsPropertyRelevantList) {
        List<PropertyAssociationPo> originalPropertyAssociations = propertyAssociationService.findListByGoodsId(goodsId);
        Map<Long, PropertyAssociationPo> originalPropertyAssociationMap = originalPropertyAssociations.stream().collect(Collectors.toMap(PropertyAssociationPo::getPropertyRelevantId, Function.identity(), (v1, v2) -> v1));

        List<PropertyAssociationPo> propertyAssociationPos = Lists.newArrayList();
        Map<String, Map<String, GoodsPropertyRelevantPo>> collect = goodsPropertyRelevantList.stream().collect(Collectors.groupingBy(GoodsPropertyRelevantPo::getPropertyAliasName, Collectors.mapping(Function.identity(), Collectors.toMap(GoodsPropertyRelevantPo::getPropertyValueAliasName, Function.identity()))));
        for (PropertyDTO property : properties) {
            Map<String, GoodsPropertyRelevantPo> goodsPropertyRelevantPoMap = collect.get(property.getName());
            for (PropertyValueDTO value : property.getValues()) {
                GoodsPropertyRelevantPo relevantPo = goodsPropertyRelevantPoMap.get(value.getValue());
                PropertyAssociationPo propertyAssociationPo = originalPropertyAssociationMap.get(relevantPo.getId());
                if (propertyAssociationPo == null) {
                    propertyAssociationPo = new PropertyAssociationPo();
                    propertyAssociationPo.setPropertyRelevantId(relevantPo.getId());
                }

                propertyAssociationPo.setGoodsId(goodsId);
                propertyAssociationPo.setPropertyId(property.getId());
                propertyAssociationPo.setPropertyValueId(value.getId());
                propertyAssociationPos.add(propertyAssociationPo);
            }
        }

        return propertyAssociationPos;
    }

    private List<GoodsPropertyRelevantPo> getGoodsPropertyRelevantPos(Long goodsId, List<PropertyDTO> properties, List<NewAddPropertyBo> propertyBos, List<GoodsPropertyRelevantPo> goodsPropertyRelevantPos) {
        List<GoodsPropertyRelevantPo> goodsPropertyRelevantList = Lists.newArrayList();

        // 获取新增的规格信息
        List<PropertyDTO> newAddProperties = getNewAddProperties(properties);
        if (CollectionUtils.isNotEmpty(newAddProperties)) {
            List<NewAddPropertyBo> newAddPropertyBos = filterNewAddPropertyBos(newAddProperties, propertyBos);

            List<GoodsPropertyRelevantPo> allRelevantPos = goodsPropertyRelevantService.findAllListByGoodsId(goodsId);
            Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap = allRelevantPos.stream().collect(Collectors.groupingBy(GoodsPropertyRelevantPo::getGlobalPropertyId, Collectors.mapping(Function.identity(), Collectors.toMap(GoodsPropertyRelevantPo::getGlobalPropertyValueId, Function.identity()))));
            List<GoodsPropertyRelevantPo> newGoodsPropertyRelevantList = GoodsPropertyRelevantHelper.convert2GoodsPropertyRelevantPos(newAddPropertyBos, goodsPropertyRelevantMap, goodsId);

            goodsPropertyRelevantList.addAll(newGoodsPropertyRelevantList);
        }


        List<GoodsPropertyRelevantPo> modifyGoodsPropertyRelevantList = getModifyGoodsPropertyRelevantList(propertyBos, goodsPropertyRelevantPos);
        if (CollectionUtils.isNotEmpty(modifyGoodsPropertyRelevantList)) {
            goodsPropertyRelevantList.addAll(modifyGoodsPropertyRelevantList);
        }

        return goodsPropertyRelevantList;
    }

    private List<NewAddPropertyBo> filterNewAddPropertyBos(List<PropertyDTO> newAddProperties, List<NewAddPropertyBo> propertyBos) {
        List<NewAddPropertyBo> newAddPropertyList = Lists.newArrayList();
        Map<Long, List<Long>> ppMap = newAddProperties.stream().collect(Collectors.toMap(PropertyDTO::getId, (dto) -> {
            return dto.getValues().stream().map(PropertyValueDTO::getId).distinct().collect(Collectors.toList());
        }));

        for (NewAddPropertyBo propertyBo : propertyBos) {
            List<Long> pvIdList = ppMap.get(propertyBo.getPropertyId());
            if (CollectionUtils.isEmpty(pvIdList)) {
                continue;
            }

            List<NewAddPropertyBo> subPropertyList = propertyBo.getSubNewAddPropertyList().stream().filter(bo -> pvIdList.contains(bo.getPropertyId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subPropertyList)) {
                continue;
            }

            NewAddPropertyBo newPp = BeanCopyUtil.transform(propertyBo, NewAddPropertyBo.class);
            newPp.setSubNewAddPropertyList(subPropertyList);
            newAddPropertyList.add(newPp);
        }

        return newAddPropertyList;
    }

    @NotNull
    private static List<NewAddPropertyBo> getNewAddPropertyBos(List<PropertyDTO> newAddProperties, Map<String, ProductReplaceBo> replaceMap) {
        if (CollectionUtils.isEmpty(newAddProperties)) {
            return Collections.emptyList();
        }

        return PropertyHelper.conversion2NewAddPropertyList(newAddProperties, replaceMap);
    }

    @NotNull
    private static List<GoodsPropertyRelevantPo> getModifyGoodsPropertyRelevantList(List<NewAddPropertyBo> properties, List<GoodsPropertyRelevantPo> goodsPropertyRelevantPos) {
        if (CollectionUtils.isEmpty(goodsPropertyRelevantPos)) {
            return Collections.emptyList();
        }

        List<GoodsPropertyRelevantPo> modifyGoodsPropertyRelevantList = Lists.newArrayList();
        Map<Long, GoodsPropertyRelevantPo> relevantPoMap = goodsPropertyRelevantPos.stream().collect(Collectors.toMap(GoodsPropertyRelevantPo::getId, Function.identity()));
        for (NewAddPropertyBo property : properties) {
            for (NewAddPropertyBo value : property.getSubNewAddPropertyList()) {
                GoodsPropertyRelevantPo relevantPo = relevantPoMap.get(value.getGoodsPropertyRelevantId());
                boolean isModify = relevantPo != null && (!StringUtils.equals(relevantPo.getPropertyAliasName(), property.getName()) ||
                        !StringUtils.equals(relevantPo.getPropertyValueAliasName(), value.getAliasName()) ||
                        !Objects.equals(relevantPo.getNameSort(), property.getSort()) ||
                        !Objects.equals(relevantPo.getValueSort(), value.getSort()) ||
                        !Objects.equals(relevantPo.getMainProperty(), property.isMainProperty()));
                if (isModify) {
                    relevantPo.setPropertyAliasName(property.getName());
                    relevantPo.setNameSort(property.getSort());
                    relevantPo.setPropertyValueAliasName(value.getAliasName());
                    relevantPo.setValueSort(value.getSort());
                    relevantPo.setMainProperty(property.isMainProperty());
                    modifyGoodsPropertyRelevantList.add(relevantPo);
                }
            }
        }
        return modifyGoodsPropertyRelevantList;
    }

    public List<AssembleGoodsPropertySkuRelevantBo> buildAssemblePropertySkuRelevantList(List<GoodsPropertySkuRelevantPo> goodsPropertySkuRelevantList) {
        List<AssembleGoodsPropertySkuRelevantBo> assembleGoodsPropertySkuRelevantBos = Lists.newArrayList();
        Map<Long, List<GoodsPropertySkuRelevantPo>> skuRelevantMap = goodsPropertySkuRelevantList.stream().collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuId));
        for (Map.Entry<Long, List<GoodsPropertySkuRelevantPo>> entry : skuRelevantMap.entrySet()) {
            List<Long> propertyRelevantIdList = entry.getValue().stream().map(GoodsPropertySkuRelevantPo::getGoodsPropertyRelevantId).distinct().collect(Collectors.toList());
            GoodsPropertySkuRelevantPo relevantPo = entry.getValue().get(0);

            Map<Long, Long> map = entry.getValue().stream().collect(Collectors.toMap(GoodsPropertySkuRelevantPo::getGoodsPropertyRelevantId, GoodsPropertySkuRelevantPo::getId));
            AssembleGoodsPropertySkuRelevantBo build = AssembleGoodsPropertySkuRelevantBo.builder().goodsId(relevantPo.getGoodsId()).goodsSkuId(relevantPo.getGoodsSkuId())
                    .goodsSkuCode(relevantPo.getGoodsSkuCode()).propertyRelevantIdList(propertyRelevantIdList).propertyAndSkuRelevantMap(map).build();
            assembleGoodsPropertySkuRelevantBos.add(build);
        }

        return assembleGoodsPropertySkuRelevantBos;
    }

    @NotNull
    private static List<PropertyDTO> getNewAddProperties(List<PropertyDTO> properties) {
        List<PropertyDTO> newAddProperties = Lists.newArrayList();
        for (PropertyDTO property : properties) {
            List<PropertyValueDTO> newPropertrValueList = Lists.newArrayList();
            for (PropertyValueDTO value : property.getValues()) {
                if (value.getGoodsPropertyRelevantId() == null) {
                    newPropertrValueList.add(value);
                }
            }

            if (CollectionUtils.isNotEmpty(newPropertrValueList)) {
                PropertyDTO newProperty = BeanCopyUtil.transform(property, PropertyDTO.class);
                newProperty.setValues(newPropertrValueList);
                newAddProperties.add(newProperty);
            }
        }
        return newAddProperties;
    }

    /**
     * 删除关联的sku信息
     */
    private void delPropertyRelevantInfo(List<GoodsPropertyRelevantPo> goodsPropertyRelevantPos, List<Long> propertyRelevantIds, Long goodsId) {
        if (CollectionUtils.isEmpty(goodsPropertyRelevantPos)) {
            return;
        }

        List<GoodsPropertyRelevantPo> delPropertyRelevantList = goodsPropertyRelevantPos.stream().filter(po -> !propertyRelevantIds.contains(po.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(delPropertyRelevantList)) {
            return;
        }

        log.info("【修改sku-删除sku相关信息】delPropertyRelevantList:{}", JSONObject.toJSONString(delPropertyRelevantList));
        List<Long> goodsPropertyRelevantIds = delPropertyRelevantList.stream().map(GoodsPropertyRelevantPo::getId).distinct().collect(Collectors.toList());
        boolean del = goodsPropertyRelevantService.delBatchByIdAndGoodsId(goodsPropertyRelevantIds, goodsId);
        AssertsUtils.isValidateTrue(del, "delPropertyRelevant fail. delPropertyRelevantList:" + JSONObject.toJSONString(delPropertyRelevantList));

        boolean delAssociation = propertyAssociationService.delBatchByPropertyRelevantIdsAndGoodsId(goodsPropertyRelevantIds, goodsId);
        AssertsUtils.isValidateTrue(delAssociation, "delBatchByPropertyRelevantIdsAndGoodsId fail. delPropertyRelevantList:" + JSONObject.toJSONString(delPropertyRelevantList));

        // 需要先判断是否有图，再去做删除
        List<GoodsPropertyImgRelevantPo> goodsPropertyImgRelevantPos = goodsPropertyImgRelevantService.findListByGoodsPropertyRelevantIds(goodsPropertyRelevantIds, goodsId);
        if (CollectionUtils.isNotEmpty(goodsPropertyImgRelevantPos)) {
            boolean delImgs = goodsPropertyImgRelevantService.delImgRelevantByGoodsPropertyRelevantIds(goodsPropertyRelevantIds, goodsId);
            AssertsUtils.isValidateTrue(delImgs, "delImgRelevant fail. goodsPropertyRelevantIds:" + JSONObject.toJSONString(goodsPropertyRelevantIds));
        }

        List<GoodsPropertySkuRelevantPo> skuRelevantList = goodsPropertySkuRelevantService.findListByGoodsPropertyRelevantIds(goodsPropertyRelevantIds, goodsId);
        if (CollectionUtils.isEmpty(skuRelevantList)) {
            // erp等三方可以传递的规格多于sku的信息
            return;
        }
        // AssertsUtils.isValidateTrue(CollectionUtils.isNotEmpty(skuRelevantList), "obtain skuRelevantList fail. goodsPropertyRelevantIds:" + JSONObject.toJSONString(goodsPropertyRelevantIds));

        List<Long> goodsSkuIds = skuRelevantList.stream().map(GoodsPropertySkuRelevantPo::getGoodsSkuId).distinct().collect(Collectors.toList());

        boolean delGoodsPropertySkuRelevant = goodsPropertySkuRelevantService.delByGoodsSkuIds(goodsSkuIds, goodsId);
        AssertsUtils.isValidateTrue(delGoodsPropertySkuRelevant, "delGoodsPropertySkuRelevant fail. goodsSkuIds:" + JSONObject.toJSONString(goodsSkuIds));

        boolean delGoodsSku = goodsSkuService.delByGoodsSkuIds(goodsSkuIds, goodsId);
        AssertsUtils.isValidateTrue(delGoodsSku, "delGoodsSku fail. goodsSkuIds:" + JSONObject.toJSONString(goodsSkuIds));
    }

    /**
     * 判断规格是否有大的变更
     */
    private boolean majorModifications(List<GoodsPropertyRelevantPo> goodsPropertyRelevantPos, List<PropertyDTO> properties) {
        if (CollectionUtils.isEmpty(goodsPropertyRelevantPos)) {
            return true;
        }

        // 判断是否为新增
        for (PropertyDTO property : properties) {
            boolean isNewAdd = property.getValues().stream().map(PropertyValueDTO::getGoodsPropertyRelevantId).allMatch(Objects::isNull);
            if (isNewAdd) {
                return true;
            }
        }

        return properties.size() != goodsPropertyRelevantPos.stream().map(GoodsPropertyRelevantPo::getGlobalPropertyId).distinct().count();
    }

    public void fillGoodsSkuAndSkuRelevant(ProductInfoInput goodsInfoInput, Long goodsId, List<GoodsItemDTO> skuList, Map<String, ProductReplaceBo> replaceMap, Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap,
                                           List<GoodsPropertySkuRelevantPo> skuRelevantPos, List<GoodsSkuPo> goodsSkuList) {
        Map<Long, Optional<GoodsPropertyImgRelevantPo>> goodsPropertyImgMap = getGoodsPropertyImgMap(goodsId);
        for (GoodsItemDTO goodsItemDTO : skuList) {
            String skuCode = String.format("%d-%d", goodsId, SnowflakeUtil.generateId());
            List<GoodsPropertyRelevantPo> relevantList = getRelevantList(replaceMap, goodsPropertyRelevantMap, goodsItemDTO.getSkuProperty());

            skuRelevantPos.addAll(getSkuRelevantList(goodsId, relevantList, skuCode));

            goodsSkuList.add(getGoodsSkuPo(goodsInfoInput, goodsId, goodsItemDTO, skuCode, relevantList, goodsPropertyImgMap));
        }

        AssertsUtils.isValidateTrue(goodsPropertySkuRelevantService.saveBatch(skuRelevantPos), "skuRelevantPos saveBatch fail. skuRelevantPos:" + JSONObject.toJSONString(skuRelevantPos));
        AssertsUtils.isValidateTrue(goodsSkuService.saveBatch(goodsSkuList), "goodsSkuList saveBatch fail. goodsSkuList:" + JSONObject.toJSONString(goodsSkuList));
    }

    private static GoodsSkuPo getGoodsSkuPo(ProductInfoInput goodsInfoInput, Long goodsId, GoodsItemDTO goodsItemDTO, String skuCode, List<GoodsPropertyRelevantPo> relevantList, Map<Long, Optional<GoodsPropertyImgRelevantPo>> goodsPropertyImgMap) {
        GoodsSkuPo skuPo = new GoodsSkuPo();
        skuPo.setGoodsId(goodsId);
        skuPo.setSkuCode(skuCode);
        skuPo.setName(getName(relevantList));
        skuPo.setOriginalSkuId(goodsItemDTO.getOriginalSkuId());
        skuPo.setOriginalPrice(goodsItemDTO.getOrginalPrice());
        skuPo.setPrice(goodsItemDTO.getPrice());
        skuPo.setMarketPrice(skuPo.getPrice().divide(goodsInfoInput.getDiscount(), 2, BigDecimal.ROUND_HALF_UP));
        skuPo.setOriginalMarketPrice(getOriginalMarketPrice(goodsItemDTO.getOrginalMarketPrice(), skuPo));
        skuPo.setCostPrice(goodsItemDTO.getCostPrice());
        skuPo.setSkuImage(getSkuImage(goodsItemDTO.getImageUrl(), relevantList, goodsPropertyImgMap));
        skuPo.setStock(goodsItemDTO.getStock());
        skuPo.setSold(goodsItemDTO.getSold());
        skuPo.setPackageSize(GoodsExtDetailUtils.getGoodsItemPackageSizeString(goodsInfoInput.getPackageSize()));
        skuPo.setWeight(GoodsExtDetailUtils.getWeight(goodsInfoInput.getWeight()));
        skuPo.setDefaultDelivery(goodsItemDTO.getDefaultDelivery());
        skuPo.setMinPurchaseQuantity(Optional.ofNullable(goodsItemDTO.getMinPurchaseQuantity()).orElse(1));
        skuPo.setSkuStatus(CheckConstant.GOODS_NORMAL_STATUS);
        if (goodsItemDTO.getSkuId() != null) {
            skuPo.setId(goodsItemDTO.getSkuId());
        }
        return skuPo;
    }

    private static BigDecimal getOriginalMarketPrice(BigDecimal orginalMarketPrice, GoodsSkuPo skuPo) {
        if (orginalMarketPrice == null || orginalMarketPrice.equals(BigDecimal.ZERO)) {
            return skuPo.getMarketPrice();
        }

        return orginalMarketPrice;
    }

    public static List<GoodsPropertySkuRelevantPo> getSkuRelevantList(Long goodsId, List<GoodsPropertyRelevantPo> relevantList, String skuCode) {
        AtomicInteger atomicIndex = new AtomicInteger(0);
        return relevantList.stream()
                .sorted(Comparator.comparing(GoodsPropertyRelevantPo::getNameSort).thenComparing(GoodsPropertyRelevantPo::getValueSort))
                .map(relevantPo -> {
                    Long relevantId = relevantPo.getId();
                    GoodsPropertySkuRelevantPo skuRelevantPo = new GoodsPropertySkuRelevantPo();
                    skuRelevantPo.setGoodsId(goodsId);
                    skuRelevantPo.setGoodsPropertyRelevantId(relevantId);
                    skuRelevantPo.setGoodsSkuCode(skuCode);
                    skuRelevantPo.setSort(atomicIndex.getAndIncrement());
                    return skuRelevantPo;
                }).collect(Collectors.toList());
    }

    private static List<GoodsPropertyRelevantPo> getRelevantList(Map<String, ProductReplaceBo> replaceMap, Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap, Map<String, String> skuProperty) {
        List<GoodsPropertyRelevantPo> relevantList = Lists.newArrayList();
        skuProperty.forEach((name, value) -> {
            ProductReplaceBo productReplaceBo = replaceMap.get(name);
            Long propertyId = productReplaceBo.getPropertyId();
            Long propertyValueId = productReplaceBo.getProductReplaceMap().get(value).getPropertyId();
            // 拿不到就是上面的数据有问题
            GoodsPropertyRelevantPo relevantPo = goodsPropertyRelevantMap.get(propertyId).get(propertyValueId);
            relevantList.add(relevantPo);
        });

        return relevantList;
    }

    private static List<GoodsPropertyRelevantPo> getRelevantListByGlobalPropertyId(Map<String, ProductReplaceBo> replaceMap, Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap, Map<String, String> skuProperty) {
        List<GoodsPropertyRelevantPo> relevantList = Lists.newArrayList();
        skuProperty.forEach((name, value) -> {
            ProductReplaceBo productReplaceBo = replaceMap.get(name);
            Long propertyId = productReplaceBo.getGlobalPropertyId();
            Long propertyValueId = productReplaceBo.getProductReplaceMap().get(value).getGlobalPropertyId();
            // 拿不到就是上面的数据有问题
            GoodsPropertyRelevantPo relevantPo = goodsPropertyRelevantMap.get(propertyId).get(propertyValueId);
            relevantList.add(relevantPo);
        });

        return relevantList;
    }

    public static String getName(List<GoodsPropertyRelevantPo> relevantList) {
        return relevantList.stream()
                .sorted(Comparator.comparing(GoodsPropertyRelevantPo::getNameSort).thenComparing(GoodsPropertyRelevantPo::getValueSort))
                .map(GoodsPropertyRelevantPo::getPropertyValueAliasName)
                .collect(Collectors.joining("-"));
    }

    public void modifySkuInfoMutualBinding(List<GoodsPropertySkuRelevantPo> skuRelevantPos, List<GoodsSkuPo> goodsSkuList, Long goodsId) {
        skuRelevantPos = skuRelevantPos.stream().filter(po -> StringUtils.isNotBlank(po.getGoodsSkuCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuRelevantPos)) {
            return;
        }

        Map<String, List<GoodsPropertySkuRelevantPo>> propertySkuRelevantMap = skuRelevantPos.stream().collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuCode));
        for (GoodsSkuPo skuPo : goodsSkuList) {
            List<GoodsPropertySkuRelevantPo> propertySkuRelevantPos = propertySkuRelevantMap.get(skuPo.getSkuCode());
            String skuRelevantIdStr = propertySkuRelevantPos.stream().sorted(Comparator.comparing(GoodsPropertySkuRelevantPo::getSort)).map(GoodsPropertySkuRelevantPo::getId).map(String::valueOf).collect(Collectors.joining("_"));
            skuPo.setSkuPropertyRelevantStr(goodsId + "_" + skuRelevantIdStr);
            propertySkuRelevantPos.forEach(skuRelevant -> {
                skuRelevant.setGoodsSkuId(skuPo.getId());
            });
        }

        AssertsUtils.isValidateTrue(goodsPropertySkuRelevantService.updateBatchByIdAndGoodsId(skuRelevantPos), "skuRelevantPos updateBatchById fail. skuRelevantPos:" + JSONObject.toJSONString(skuRelevantPos));
        AssertsUtils.isValidateTrue(goodsSkuService.updateBatchByIdAndGoodsId(goodsSkuList), "goodsSkuList updateBatchById fail. goodsSkuList:" + JSONObject.toJSONString(goodsSkuList));
    }

    public void fillGoodsSkuAndSkuRelevant(GoodsInfoInputVO goodsInfoInput, Long goodsId, List<GoodsItemVO> skuList, Map<String, ProductReplaceBo> replaceMap, Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap, List<GoodsPropertySkuRelevantPo> skuRelevantPos, List<GoodsSkuPo> goodsSkuList) {
        for (GoodsItemVO goodsItemVo : skuList) {
            String skuCode = String.format("%d-%d", goodsId, SnowflakeUtil.generateId());
            List<GoodsPropertyRelevantPo> relevantList = getRelevantList(replaceMap, goodsPropertyRelevantMap, goodsItemVo.getSkuProperty());

            skuRelevantPos.addAll(getSkuRelevantList(goodsId, relevantList, skuCode));

            goodsSkuList.add(getGoodsSkuPo(goodsInfoInput, goodsId, goodsItemVo, skuCode, relevantList));
        }

        AssertsUtils.isValidateTrue(goodsPropertySkuRelevantService.saveBatch(skuRelevantPos), "skuRelevantPos saveBatch fail. skuRelevantPos:" + JSONObject.toJSONString(skuRelevantPos));
        AssertsUtils.isValidateTrue(goodsSkuService.saveBatch(goodsSkuList), "goodsSkuList saveBatch fail. goodsSkuList:" + JSONObject.toJSONString(goodsSkuList));
    }

    private static GoodsSkuPo getGoodsSkuPo(GoodsInfoInputVO goodsInfoInput, Long goodsId, GoodsItemVO goodsItemDTO, String skuCode, List<GoodsPropertyRelevantPo> relevantList) {
        GoodsSkuPo skuPo = new GoodsSkuPo();
        skuPo.setGoodsId(goodsId);
        skuPo.setSkuCode(skuCode);
        skuPo.setName(getName(relevantList));
        skuPo.setOriginalSkuId(goodsItemDTO.getOriginalSkuId());
        skuPo.setOriginalPrice(goodsItemDTO.getOrginalPrice());
        skuPo.setPrice(goodsItemDTO.getPrice());
        skuPo.setMarketPrice(skuPo.getPrice().divide(goodsInfoInput.getDiscount(), 2, BigDecimal.ROUND_HALF_UP));
        skuPo.setOriginalMarketPrice(getOriginalMarketPrice(goodsItemDTO.getOrginalMarketPrice(), skuPo));
        skuPo.setCostPrice(goodsItemDTO.getCostPrice());
        skuPo.setStock(goodsItemDTO.getStock());
        skuPo.setSkuImage(goodsItemDTO.getSkuImage());
        skuPo.setSold(0L);
        skuPo.setPackageSize(GoodsExtDetailUtils.getPackageSizeString(goodsInfoInput.getPackageSize()));
        skuPo.setWeight(GoodsExtDetailUtils.getWeight(goodsInfoInput.getWeight()));
        skuPo.setDefaultDelivery(goodsItemDTO.getDefaultDelivery());
        skuPo.setMinPurchaseQuantity(Optional.ofNullable(goodsItemDTO.getMinPurchaseQuantity()).orElse(1));
        skuPo.setSkuStatus(CheckConstant.GOODS_NORMAL_STATUS);
        return skuPo;
    }

    public void fillGoodsSkuAndSkuRelevant(ItemGoodsInfoInputDto dto, ItemGoodsInfoExtBo extBo, List<ItemGoodsSkuInfoDto> skuInfo, Map<String, ProductReplaceBo> replaceMap, Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap, List<GoodsPropertySkuRelevantPo> skuRelevantPos, List<GoodsSkuPo> goodsSkuList) {
        Long goodsId = extBo.getGoodsId();
        for (ItemGoodsSkuInfoDto sku : skuInfo) {
            String skuCode = String.format("%d-%d", goodsId, SnowflakeUtil.generateId());
            // 新增可以使用，后续修改时可以使用sku中的skuPropertyRelevantStr，来更加精准的找到
            List<GoodsPropertyRelevantPo> relevantList = getRelevantListByGlobalPropertyId(replaceMap, goodsPropertyRelevantMap, sku.getSkuProperty());

            skuRelevantPos.addAll(getSkuRelevantList(goodsId, relevantList, skuCode));

            goodsSkuList.add(getGoodsSkuPo(dto, extBo, goodsId, sku, skuCode, relevantList));
        }

        AssertsUtils.isValidateTrue(goodsPropertySkuRelevantService.saveBatch(skuRelevantPos), "skuRelevantPos saveBatch fail. skuRelevantPos:" + JSONObject.toJSONString(skuRelevantPos));
        AssertsUtils.isValidateTrue(goodsSkuService.saveBatch(goodsSkuList), "goodsSkuList saveBatch fail. goodsSkuList:" + JSONObject.toJSONString(goodsSkuList));
    }

    private static GoodsSkuPo getGoodsSkuPo(ItemGoodsInfoInputDto dto, ItemGoodsInfoExtBo extBo, Long goodsId, ItemGoodsSkuInfoDto sku, String skuCode, List<GoodsPropertyRelevantPo> relevantList) {
        GoodsSkuPo skuPo = new GoodsSkuPo();
        skuPo.setGoodsId(goodsId);
        skuPo.setSkuCode(skuCode);
        skuPo.setName(getName(relevantList));
        skuPo.setOriginalSkuId(sku.getOriginalSkuId());
        skuPo.setOriginalPrice(sku.getOrginalPrice());
        skuPo.setPrice(sku.getPrice());
        skuPo.setMarketPrice(skuPo.getPrice().divide(extBo.getVirtualDiscount(), 2, RoundingMode.HALF_UP));
        skuPo.setOriginalMarketPrice(getOriginalMarketPrice(sku.getOrginalMarketPrice(), skuPo));
        skuPo.setCostPrice(sku.getCostPrice());
        skuPo.setSkuImage(sku.getImageUrl());
        skuPo.setStock(sku.getStock());
        skuPo.setPackageSize(GoodsExtDetailUtils.getGoodsItemPackageSizeString(dto.getPackageSize()));
        skuPo.setWeight(GoodsExtDetailUtils.getWeight(dto.getWeight()));
        skuPo.setDefaultDelivery(sku.getDefaultDelivery());
        skuPo.setMinPurchaseQuantity(Optional.ofNullable(sku.getMinPurchaseQuantity()).orElse(1));
        skuPo.setSkuStatus(CheckConstant.GOODS_NORMAL_STATUS);
        return skuPo;
    }

    @NotNull
    public List<GoodsSkuPo> addGoodsSkuList(Long goodsId, ProductInfoInput goodsInfoInput, List<PropertyDTO> properties) {
        Map<String, ProductReplaceBo> replaceMap = propertyHelper.getProductReplaceMap(properties);
        AssertsUtils.isValidateTrue(MapUtils.isNotEmpty(replaceMap), "propertyValueRecordMap is empty");
        log.info("【addSkuRelatedInfo】replaceMap:{}", JSONObject.toJSONString(replaceMap));

        List<NewAddPropertyBo> newAddPropertyBos = PropertyHelper.conversion2NewAddPropertyList(properties, replaceMap);
        log.info("【addSkuRelatedInfo】newAddPropertyBos:{}", JSONObject.toJSONString(newAddPropertyBos));

        List<GoodsPropertyRelevantPo> allRelevantPos = goodsPropertyRelevantService.findAllListByGoodsId(goodsId);
        Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantMap = allRelevantPos.stream().collect(Collectors.groupingBy(GoodsPropertyRelevantPo::getGlobalPropertyId, Collectors.mapping(Function.identity(), Collectors.toMap(GoodsPropertyRelevantPo::getGlobalPropertyValueId, Function.identity()))));
        List<GoodsPropertyRelevantPo> goodsPropertyRelevantList = GoodsPropertyRelevantHelper.convert2GoodsPropertyRelevantPos(newAddPropertyBos, goodsPropertyRelevantMap, goodsId);
        AssertsUtils.isValidateTrue(goodsPropertyRelevantService.saveOrUpdateBatch(goodsPropertyRelevantList), "goodsPropertyRelevantList saveBatch fail. goodsPropertyRelevantList:" + JSONObject.toJSONString(goodsPropertyRelevantList));

        // 绑定新老规格关联关系
        List<PropertyAssociationPo> propertyAssociationPos = getPropertyAssociationPos(goodsId, properties, goodsPropertyRelevantList);
        // List<PropertyAssociationPo> propertyAssociationPos = PropertyHelper.getPropertyAssociationPos(goodsPropertyRelevantList, properties, goodsId, PropertyDTO::getName, PropertyDTO::getId, PropertyDTO::getValues, PropertyValueDTO::getValue, PropertyValueDTO::getId);
        AssertsUtils.isValidateTrue(propertyAssociationService.saveOrUpdateBatch(propertyAssociationPos), "propertyAssociationPos saveBatch fail. propertyAssociationPos:" + JSONObject.toJSONString(propertyAssociationPos));

        Map<Long, Map<Long, GoodsPropertyRelevantPo>> goodsPropertyRelevantByPropertyIdMap = GoodsSkuHelper.getGoodsPropertyRelevantMap(goodsPropertyRelevantList, propertyAssociationPos);

        List<GoodsPropertyImgRelevantPo> goodsPropertyImgRelevantPos = GoodsPropertyRelevantHelper.getGoodsPropertyImgRelevantPosByPropertyId(newAddPropertyBos, goodsPropertyRelevantByPropertyIdMap, goodsId);
        if (CollectionUtils.isNotEmpty(goodsPropertyImgRelevantPos)) {
            AssertsUtils.isValidateTrue(goodsPropertyImgRelevantService.saveOrUpdateBatch(goodsPropertyImgRelevantPos), "goodsPropertyImgRelevantPos saveBatch fail. goodsPropertyImgRelevantPos:" + JSONObject.toJSONString(goodsPropertyImgRelevantPos));
        }

        List<GoodsPropertySkuRelevantPo> skuRelevantPos = Lists.newArrayList();
        List<GoodsSkuPo> goodsSkuList = Lists.newArrayList();
        fillGoodsSkuAndSkuRelevant(goodsInfoInput, goodsId, goodsInfoInput.getSkuInfo(), replaceMap, goodsPropertyRelevantByPropertyIdMap, skuRelevantPos, goodsSkuList);

        modifySkuInfoMutualBinding(skuRelevantPos, goodsSkuList, goodsId);
        return goodsSkuList;
    }

}
