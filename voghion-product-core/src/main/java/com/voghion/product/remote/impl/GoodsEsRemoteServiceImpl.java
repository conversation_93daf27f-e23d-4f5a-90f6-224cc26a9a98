package com.voghion.product.remote.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.common.log.LogAnnotation;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.ThreadContext;
import com.colorlight.base.model.constants.SystemConstants;
import com.colorlight.base.utils.CheckUtils;
import com.colorlight.base.utils.CollectionUtil;
import com.colorlight.base.utils.DateUtil;
import com.colorlight.base.utils.TransferUtils;
import com.colorlight.translate.enums.AwsLang;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.voghion.es.dto.VatCondition;
import com.voghion.es.dto.VatDto;
import com.voghion.es.service.GoodsEsService;
import com.voghion.product.api.dto.GoodsPriceDTO;
import com.voghion.product.api.input.GoodsFreightDTO;
import com.voghion.product.api.input.*;
import com.voghion.product.api.output.*;
import com.voghion.product.api.service.GoodsEsRemoteService;
import com.voghion.product.client.CountryCurLangClientFactory;
import com.voghion.product.client.GoodsLogClientFactory;
import com.voghion.product.client.TranslateClientFactory;
import com.voghion.product.core.TaxVatCoreService;
import com.voghion.product.core.TongDunGoodsImageCoreService;
import com.voghion.product.inner.dto.GoodsFreightLogDto;
import com.voghion.product.inner.dto.GoodsItemLogDto;
import com.voghion.product.inner.dto.GoodsLogQueryDto;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.enums.EsEnums;
import com.voghion.product.model.enums.GoodsIsShowEnums;
import com.voghion.product.model.enums.ProductResultCode;
import com.voghion.product.model.po.*;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.CategoryService;
import com.voghion.product.service.GoodsDetailService;
import com.voghion.product.service.GoodsExtDetailService;
import com.voghion.product.service.GoodsFreightService;
import com.voghion.product.support.ElasticsearchHandler;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.utils.GoodsTransferUtils;
import com.voghion.user.dto.countrycurlang.AppCountryCurLangVO;
import com.voghion.user.dto.countrycurlang.CountryCurDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.opensearch.action.ActionListener;
import org.opensearch.action.DocWriteRequest;
import org.opensearch.action.bulk.BulkRequest;
import org.opensearch.action.bulk.BulkResponse;
import org.opensearch.action.delete.DeleteRequest;
import org.opensearch.action.get.GetRequest;
import org.opensearch.action.get.MultiGetItemResponse;
import org.opensearch.action.get.MultiGetRequest;
import org.opensearch.action.get.MultiGetResponse;
import org.opensearch.action.index.IndexRequest;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.action.search.SearchScrollRequest;
import org.opensearch.action.search.SearchType;
import org.opensearch.action.support.WriteRequest;
import org.opensearch.client.HttpAsyncResponseConsumerFactory;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.client.core.CountRequest;
import org.opensearch.client.core.CountResponse;
import org.opensearch.common.unit.TimeValue;
import org.opensearch.common.xcontent.XContentType;
import org.opensearch.index.query.*;
import org.opensearch.rest.RestStatus;
import org.opensearch.search.Scroll;
import org.opensearch.search.SearchHit;
import org.opensearch.search.SearchHits;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.opensearch.common.unit.TimeValue.timeValueMillis;

@Service
@Slf4j
public class GoodsEsRemoteServiceImpl implements GoodsEsRemoteService {


    /**
     * 当前的esClient
     */
    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;
    @Resource
    private RedisApi redisApi;
    @Resource
    private TaxVatCoreService taxVatCoreService;
    @Resource
    private GoodsEsService goodsEsService;

    private static final String TONG_DUN_CATEGORY_REDIS = "tong-dun-category";

    private final String currentIndex = "currentIndex";

    private final String currentGcrIndex = "currentGcrIndex";

    private final String COUNTRY_CURRENCY_MAPPING = "COUNTRY_CURRENCY_MAPPING";
    @Autowired
    private MqSender mqSender;

    @Resource
    private ElasticsearchHandler elasticsearchHandler;

    @Resource
    private GoodsDetailService goodsDetailService;

    @Resource
    private GoodsFreightService goodsFreightService;

    @Resource
    private GoodsLogClientFactory goodsLogClientFactory;

    @Resource
    private GoodsExtDetailService goodsExtDetailService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;
    @Resource
    private TongDunGoodsImageCoreService tongDunGoodsImageCoreService;

    @Resource
    private TranslateClientFactory translateClientFactory;

    private static final String defaultCountryCode = "minprice.GB";

    private static final String defaultGrouponCountryCode = "mingrouponprice.GB";

    @Resource
    private CategoryService categoryService;

    @Resource
    private CountryCurLangClientFactory countryCurLangClientFactory;

    @Override
    @LogAnnotation(message = "根据商品id查询国家运费")
    public Map<String, BigDecimal> queryPriceByGoodsIdAndCountry(List<String> goodIds, String country) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<String, BigDecimal> prices = Maps.newLinkedHashMap();
        String countryCode = "minprice." + country;
        MultiGetRequest request = new MultiGetRequest();
        if (CollectionUtils.isNotEmpty(goodIds)) {
            goodIds.stream().forEach(goodId -> {
                request.add(EsEnums.GOODS_ES.getIndex(), EsEnums.GOODS_ES.getType(), goodId);
            });
        } else {
            return prices;
        }
        Map<String, VatDto> vatMap = goodsEsService.getVatMap();
        MultiGetItemResponse[] multiGetItemResponses = null;
        try {
            multiGetItemResponses = restHighLevelClient.mget(request, builder()).getResponses();
            if (multiGetItemResponses != null && multiGetItemResponses.length > 0) {
                for (MultiGetItemResponse response : multiGetItemResponses) {
                    String jsonResponseStr = response.getResponse().getSourceAsString();
                    JSONObject jsonObject = JSON.parseObject(jsonResponseStr);
                    if (jsonObject == null) {
                        continue;
                    }
                    BigDecimal vatRate = goodsEsService.getVatRate(
                            new VatCondition()
                                    .setGoodsId(jsonObject.getLong("id"))
                                    .setCountry(country)
                                    .setShopId(jsonObject.getLong("shopId"))
                                    .setCategoryId(jsonObject.getLong("categoryId"))
                                    .setGoodsExtConfigModel(jsonObject.getObject("goodsExtConfigModel", com.voghion.es.model.GoodsExtConfigModel.class))
                                    .setDeliveryType(jsonObject.getInteger("deliveryType"))
                                    .setVatRate(jsonObject.getBigDecimal("vatRate"))
                                    .setVatMap(vatMap)
                    );
                    BigDecimal price = jsonObject.getBigDecimal(countryCode);
                    if (price == null) {
                        price = jsonObject.getBigDecimal(defaultCountryCode);
                    }
                    //增加vat税费
                    if (price != null) {
                        price = getVatPrice(vatRate, price);
                        String id = jsonObject.getString("id");
                        prices.put(id, price);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        stopWatch.stop();
        log.info("根据商品id查询国家运费消耗时间:{}", stopWatch.getTime() + "ms");
        return prices;
    }

    @Override
    @LogAnnotation(message = "根据商品id查询国家运费")
    public List<GoodsPriceDTO> queryAllPriceByGoodsIdAndCountry(List<String> goodIds, String country) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<GoodsPriceDTO> result = Lists.newArrayList();
        String grouponCountryCode = "mingrouponprice." + country;
        String countryCode = "minprice." + country;
        MultiGetRequest request = new MultiGetRequest();
        if (CollectionUtils.isNotEmpty(goodIds)) {
            goodIds.forEach(goodId -> request.add(EsEnums.GOODS_ES.getIndex(), goodId));
            MultiGetItemResponse[] multiGetItemResponses;
            try {
                multiGetItemResponses = restHighLevelClient.mget(request, builder()).getResponses();
                if (multiGetItemResponses != null && multiGetItemResponses.length > 0) {
                    Map<String, VatDto> vatMap = goodsEsService.getVatMap();
                    for (MultiGetItemResponse response : multiGetItemResponses) {
                        String jsonResponseStr = response.getResponse().getSourceAsString();
                        JSONObject jsonObject = JSON.parseObject(jsonResponseStr);
                        if (jsonObject == null) {
                            continue;
                        }
                        GoodsPriceDTO entity = new GoodsPriceDTO();
                        String id = jsonObject.getString("id");
                        entity.setGoodsId(Long.valueOf(id));

                        BigDecimal price = jsonObject.getBigDecimal(countryCode);
                        if (price == null) {
                            price = jsonObject.getBigDecimal(defaultCountryCode);
                        }
                        BigDecimal vatRate = goodsEsService.getVatRate(
                                new VatCondition()
                                        .setGoodsId(jsonObject.getLong("id"))
                                        .setCountry(country)
                                        .setShopId(jsonObject.getLong("shopId"))
                                        .setCategoryId(jsonObject.getLong("categoryId"))
                                        .setGoodsExtConfigModel(jsonObject.getObject("goodsExtConfigModel", com.voghion.es.model.GoodsExtConfigModel.class))
                                        .setDeliveryType(jsonObject.getInteger("deliveryType"))
                                        .setVatRate(jsonObject.getBigDecimal("vatRate"))
                                        .setVatMap(vatMap)
                               );
                        //增加vat税费
                        if (price != null) {
                            price = getVatPrice(vatRate, price);
                            entity.setPrice(price);
                        }

                        BigDecimal grouponPrice = jsonObject.getBigDecimal(grouponCountryCode);
                        if (grouponPrice == null) {
                            grouponPrice = jsonObject.getBigDecimal(defaultGrouponCountryCode);
                        }
                        //增加vat税费
                        if (grouponPrice != null) {
                            grouponPrice = getVatPrice(vatRate, grouponPrice);
                            entity.setGrouponPrice(grouponPrice);
                        }
                        result.add(entity);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        stopWatch.stop();
        log.info("根据商品id查询团购国家运费消耗时间:{}", stopWatch.getTime() + "ms");
        return result;
    }

    private BigDecimal getVatPrice(BigDecimal vatRate, BigDecimal originPrice) {
        if (vatRate == null || vatRate.compareTo(BigDecimal.ZERO) == 0 || originPrice == null) {
            return originPrice;
        }
        return originPrice.add(originPrice.multiply(vatRate).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
    }

    @Override
    public List<GoodsESModelVo> queryGoodsByGoodsIdAndCountry(Map<Long, List<Long>> listMap, String country) {
        List<GoodsESModelVo> goodsESModelVos = Lists.newArrayList();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Long> goodsIds = Lists.newArrayList(listMap.keySet());
        List<Long> goodsIdsCache = Lists.newArrayList();
        //从缓存里取商品信息
        if (!CollectionUtils.isEmpty(goodsIds)) {
            goodsIds.forEach(goodsId -> {
                GoodsESModelVo goodsESModelVo = redisApi.get("goodsId:" + goodsId);
                if (goodsESModelVo != null) {
                    goodsIdsCache.add(goodsId);
                    goodsESModelVos.add(goodsESModelVo);
                }
            });
        }
        if (goodsIds.size() == goodsIdsCache.size()) {
            return goodsESModelVos;
        } else if (CollectionUtils.isNotEmpty(goodsIdsCache)) {
            goodsIds.removeAll(goodsIdsCache);
        }
        try {
            MultiGetRequest request = new MultiGetRequest();
            if (CollectionUtils.isNotEmpty(goodsIds)) {
                goodsIds.stream().forEach(goodId -> {
                    request.add(EsEnums.GOODS_ES.getIndex(), goodId.toString());
                });
            }
            MultiGetItemResponse[] multiGetItemResponses = restHighLevelClient.mget(request, builder()).getResponses();
            if (multiGetItemResponses != null && multiGetItemResponses.length > 0) {
                for (MultiGetItemResponse response : multiGetItemResponses) {
                    String jsonResponseStr = response.getResponse().getSourceAsString();
                    if (StringUtils.isNotEmpty(jsonResponseStr)) {
                        GoodsESModelVo goodsESModelVo = JSON.parseObject(jsonResponseStr, GoodsESModelVo.class);
                        //step1 排除多余skuid
                        List<GoodsItemESModel> goodsItemESModelList = goodsESModelVo.getItemList();
                        List<GoodsItemESModel> goodsItemES = Lists.newArrayList();
                        if (CollectionUtils.isNotEmpty(goodsItemESModelList)) {
                            if (goodsESModelVo.getIsDel() == 0 && "1".equals(goodsESModelVo.getIsShow())) {
                                redisApi.set("goodsId:" + goodsESModelVo.getId(), goodsESModelVo, TimeUnit.SECONDS.toSeconds(8 * 60 * 60));
                            }
                        }
                        goodsESModelVo.setItemList(goodsItemES);
                        goodsESModelVos.add(goodsESModelVo);
                    }
                }
            }
            stopWatch.stop();
            log.info("新的根据商品id查询商品详情消耗时间:{}", stopWatch.getTime() + "ms");
            return goodsESModelVos;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("根据商品id查询商品详情异常{}", e.getCause());
            mqSender.send("waring_error_topic", "根据商品id查询商品详情异常");
        }
        return goodsESModelVos;
    }


    @Override
    public List<GoodsESModelVo> queryGoodsByGoodsId(Map<Long, List<Long>> listMap) {
        List<GoodsESModelVo> goodsESModelVos = Lists.newArrayList();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //获取商品id集合
        List<Long> goodsIds = Lists.newArrayList(listMap.keySet());
//        List<Long> goodsIdsCache = Lists.newArrayList();
//        //从缓存里取商品信息
//        if (!CollectionUtils.isEmpty(goodsIds)) {
//            goodsIds.stream().forEach(goodsId -> {
//                GoodsESModelVo goodsESModelVo = redisApi.get("goodsId:" + goodsId);
//                if (goodsESModelVo != null) {
//                    log.info("缓存命中,商品id{}", goodsESModelVo.getId());
//                    goodsIdsCache.add(goodsId);
//                    goodsESModelVos.add(goodsESModelVo);
//                }
//            });
//        }
//        if (goodsIds.size() == goodsIdsCache.size()) {
//            return goodsESModelVos;
//        } else if (CollectionUtils.isNotEmpty(goodsIdsCache)) {
//            goodsIds.removeAll(goodsIdsCache);
//        }

        try {
            MultiGetRequest request = new MultiGetRequest();
            if (CollectionUtils.isNotEmpty(goodsIds)) {
                goodsIds.stream().forEach(goodId -> {
                    request.add(EsEnums.GOODS_ES.getIndex(), goodId.toString());
                });
            }
            MultiGetItemResponse[] multiGetItemResponses = restHighLevelClient.mget(request, builder()).getResponses();
            if (multiGetItemResponses != null && multiGetItemResponses.length > 0) {
                for (MultiGetItemResponse response : multiGetItemResponses) {
                    String jsonResponseStr = response.getResponse().getSourceAsString();
                    if (StringUtils.isNotEmpty(jsonResponseStr)) {
                        GoodsESModelVo goodsESModelVo = JSON.parseObject(jsonResponseStr, GoodsESModelVo.class);
                        //step1 排除多余skuid
                        List<GoodsItemESModel> goodsItemESModelList = goodsESModelVo.getItemList();
                        List<GoodsItemESModel> goodsItemES = Lists.newArrayList();
                        if (goodsESModelVo != null && CollectionUtils.isNotEmpty(goodsItemESModelList)) {
//                            if (goodsESModelVo.getIsDel() == 0 && "1".equals(goodsESModelVo.getIsShow())) {
//                                redisApi.set("goodsId:" + goodsESModelVo.getId(), goodsESModelVo, TimeUnit.SECONDS.toSeconds(60));
//                            }
                            List<Long> skuIds = listMap.get(goodsESModelVo.getId());
                            if (CollectionUtils.isNotEmpty(skuIds)) {
                                goodsItemESModelList.stream().forEach(goodsItemESModel -> {
                                    if (skuIds.contains(goodsItemESModel.getSkuId())) {
                                        goodsItemES.add(goodsItemESModel);
                                    }
                                });
                            }
                        }
                        goodsESModelVo.setItemList(goodsItemES);
                        goodsESModelVos.add(goodsESModelVo);
                    }
                }
            }
            stopWatch.stop();
            log.info("新的根据商品id查询商品详情消耗时间:{}", stopWatch.getTime() + "ms");
            return goodsESModelVos;
        } catch (
                Exception e) {
            e.printStackTrace();
            log.error("新的根据商品id查询商品详情异常{}", e.getCause());
        }
        return goodsESModelVos;
    }


    @Override
    public List<RecommentGoodsListESModelVo> queryRecommendGoodsByContition(RecommentGoodsListESModelDTO recommentGoodsListESModelDTO) {
        List<RecommentGoodsListESModelVo> recommentGoodsListESModelVos = Lists.newArrayList();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(recommentGoodsListESModelDTO.getCountryName())) {
            TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("countryName.keyword", dealCountry(recommentGoodsListESModelDTO.getCountryName()));
            boolBuilder.must(termQueryBuilder);
        }

        if (ObjectUtils.isNotEmpty(recommentGoodsListESModelDTO.getCategoryId())) {
            TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("categoryId", recommentGoodsListESModelDTO.getCategoryId());
            boolBuilder.must(termsQueryBuilder);
        }
        if (ObjectUtils.isNotEmpty(recommentGoodsListESModelDTO.getGender())) {
            TermsQueryBuilder termQueryBuilder = QueryBuilders.termsQuery("gender", dealGender(recommentGoodsListESModelDTO.getGender()));
            boolBuilder.must(termQueryBuilder);
        }


        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
        if (recommentGoodsListESModelDTO.getPageSize() > 2000) {
            sourceBuilder.from((recommentGoodsListESModelDTO.getPageNow() - 1) * 20);
            sourceBuilder.size(2000);
        } else {
            sourceBuilder.from((recommentGoodsListESModelDTO.getPageNow() - 1) * recommentGoodsListESModelDTO.getPageSize());
            sourceBuilder.size(recommentGoodsListESModelDTO.getPageSize());
        }
        sourceBuilder.sort("rn", SortOrder.ASC);
        sourceBuilder.query(boolBuilder);
        try {
            //查询索引对象
            String index = "";
            if (StringUtils.isEmpty(recommentGoodsListESModelDTO.getIndex())
                    && StringUtils.isEmpty(recommentGoodsListESModelDTO.getGcrIndex())
            ) {
                index = redisApi.get(currentIndex);
                if (StringUtils.isEmpty(index)) {
                    GetRequest getRequest = new GetRequest(EsEnums.ES_INDEX_INFO_ENUMS.getIndex(), EsEnums.ES_INDEX_INFO_ENUMS.getType(),
                            "1"
                    );
                    Map<String, Object> getResponse =
                            restHighLevelClient.get(getRequest, builder()).getSourceAsMap();
                    if (getResponse != null && !getResponse.isEmpty()) {
                        index = String.valueOf(getResponse.get("indexName"));
                        redisApi.set(currentIndex, index, 60 * 5);
                    }
                }
            } else {
                index = redisApi.get(currentGcrIndex);
                if (StringUtils.isEmpty(index)) {
                    GetRequest getRequest = new GetRequest(EsEnums.GCR_ES_INDEX_INFO_ENUMS.getIndex(), EsEnums.GCR_ES_INDEX_INFO_ENUMS.getType(),
                            "1"
                    );
                    Map<String, Object> getResponse =
                            restHighLevelClient.get(getRequest, builder()).getSourceAsMap();
                    if (getResponse != null && !getResponse.isEmpty()) {
                        index = String.valueOf(getResponse.get("indexName"));
                        redisApi.set(currentGcrIndex, index, 60 * 5);
                    }
                }
            }
            log.info("index:{}", index);
            SearchRequest searchRequest = new SearchRequest(index);
            searchRequest.types(EsEnums.GOODS_ES.getType());
            searchRequest.source(sourceBuilder);
            SearchResponse response = restHighLevelClient.search(searchRequest, builder());
            if (response.status().getStatus() == 200) {
                SearchHit[] results = response.getHits().getHits();
                if (results != null && results.length > 0) {
                    Arrays.stream(results).forEach(result -> {
                        String json = result.getSourceAsString();
                        if (StringUtils.isNotEmpty(json)) {
                            RecommentGoodsListESModelVo recommentGoodsListESModelVo = JSON.parseObject(json, RecommentGoodsListESModelVo.class);
                            recommentGoodsListESModelVos.add(recommentGoodsListESModelVo);
                        } else {
                            log.error("error data id:{}", result.getId());
                        }
                    });
                }
            }
            stopWatch.stop();
            log.info("根据性别类目或国家查询推荐商品消耗时间:{}", stopWatch.getTime() + "ms");
            if (stopWatch.getTime() > 10 * 1000) {
                mqSender.send("waring_error_topic", "根据性别类目或国家查询推荐商品异常");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("根据性别类目或国家查询推荐商品详情异常{}", e.getCause());
            mqSender.send("waring_error_topic", "根据性别类目或国家查询推荐商品详情异常");
        }

        return recommentGoodsListESModelVos;
    }

    @Override
    public List<GoodsESModelVo> queryGoodsByGoodsIdAndCountry(RecommentGoodsListESModelDTO recommentGoodsListESModelDTO) {
        log.info("根据国家和商品id查询商品:{}", JSON.toJSONString(recommentGoodsListESModelDTO));
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<GoodsESModelVo> goodsESModelVos = Lists.newArrayList();
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        TermQueryBuilder isShow = QueryBuilders.termQuery("isShow", "1");
        TermQueryBuilder isDel = QueryBuilders.termQuery("isDel", 0);
        boolBuilder.must(isShow);
        boolBuilder.must(isDel);
        if (ObjectUtils.isNotEmpty(recommentGoodsListESModelDTO.getGoodsId())) {
            TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("id", recommentGoodsListESModelDTO.getGoodsId());
            boolBuilder.must(termsQueryBuilder);
        }

        buildCountryTermQuery(recommentGoodsListESModelDTO.getCountryName(), boolBuilder);

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
        sourceBuilder.query(boolBuilder);
        if (recommentGoodsListESModelDTO.getPageSize() > 400 || recommentGoodsListESModelDTO.getPageNow() > 400) {
            sourceBuilder.from((recommentGoodsListESModelDTO.getPageNow() - 1) * 20);
            sourceBuilder.size(20);
        } else {
            sourceBuilder.from((recommentGoodsListESModelDTO.getPageNow() - 1) * recommentGoodsListESModelDTO.getPageSize());
            sourceBuilder.size(recommentGoodsListESModelDTO.getPageSize());
        }
        try {
            SearchRequest searchRequest = new SearchRequest(EsEnums.GOODS_ES.getIndex());
            searchRequest.types(EsEnums.GOODS_ES.getType());
            searchRequest.source(sourceBuilder);
            SearchResponse response = restHighLevelClient.search(searchRequest, builder());
            if (response.status().getStatus() == 200) {
                SearchHit[] results = response.getHits().getHits();
                if (results != null && results.length > 0) {
                    Map<String, VatDto> vatMap = goodsEsService.getVatMap();
                    Arrays.stream(results).forEach(result -> {
                        try {
                            JSONObject jsonObject = JSON.parseObject(result.getSourceAsString());
                            if (jsonObject != null) {
                                BigDecimal vatRate = goodsEsService.getVatRate(
                                        new VatCondition()
                                                .setGoodsId(jsonObject.getLong("id"))
                                                .setCountry(recommentGoodsListESModelDTO.getCountryName())
                                                .setShopId(jsonObject.getLong("shopId"))
                                                .setCategoryId(jsonObject.getLong("categoryId"))
                                                .setGoodsExtConfigModel(jsonObject.getObject("goodsExtConfigModel", com.voghion.es.model.GoodsExtConfigModel.class))
                                                .setDeliveryType(jsonObject.getInteger("deliveryType"))
                                                .setVatRate(jsonObject.getBigDecimal("vatRate"))
                                                .setVatMap(vatMap)
                                        );
                                String countryCode = "minprice." + recommentGoodsListESModelDTO.getCountryName().toUpperCase();
                                BigDecimal price = jsonObject.getBigDecimal(countryCode);
                                if (price == null) {
                                    price = jsonObject.getBigDecimal(defaultCountryCode);
                                }
                                BigDecimal minPrice = jsonObject.getBigDecimal("minPrice");
                                GoodsESModelVo goodsESModelVo = JSON.parseObject(result.getSourceAsString(), GoodsESModelVo.class);
                                if (price != null) {
                                    //增加税费计算
                                    price = getVatPrice(vatRate, price);
                                    goodsESModelVo.setCountryPrice(price);
                                } else {
                                    //增加税费计算
                                    minPrice = getVatPrice(vatRate, minPrice);
                                    goodsESModelVo.setCountryPrice(minPrice != null ? minPrice : BigDecimal.ZERO);
                                }
                                goodsESModelVos.add(goodsESModelVo);
                            }
                        } catch (Exception e) {
                            log.error("data error{}", e);
                        }
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("data error{}", e);
            mqSender.send("waring_error_topic", "根据国家和商品id查询商品异常"+JSONObject.toJSONString(recommentGoodsListESModelDTO));
        }
        stopWatch.stop();
        log.info("根据国家和商品id查询商品消耗时间:{}", stopWatch.getTime() + "ms");
        if (stopWatch.getTime() > 10 * 1000) {
            mqSender.send("waring_error_topic", "根据性别类目或国家查询推荐商品异常" + JSONObject.toJSONString(recommentGoodsListESModelDTO));
        }
        return dealGoods(goodsESModelVos, recommentGoodsListESModelDTO.getGoodsId(), recommentGoodsListESModelDTO.getOrderBy());
    }

    @Override
    public List<GoodsESModelVo> queryGoodsInfo(GoodsInput goodsInput) {
        if(CollectionUtils.isEmpty(goodsInput.getGoodsIds())){
            return new ArrayList<>();
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<GoodsESModelVo> goodsESModelVos = Lists.newArrayList();
        MultiGetRequest request = new MultiGetRequest();
        if (CollectionUtils.isNotEmpty(goodsInput.getGoodsIds())) {
            goodsInput.getGoodsIds().stream().forEach(goodId -> {
                request.add(EsEnums.GOODS_ES.getIndex(), EsEnums.GOODS_ES.getType(), String.valueOf(goodId));
            });
        }
        MultiGetItemResponse[] multiGetItemResponses = null;
        try {
            multiGetItemResponses = restHighLevelClient.mget(request, builder()).getResponses();
            if (multiGetItemResponses != null && multiGetItemResponses.length > 0) {
                for (MultiGetItemResponse response : multiGetItemResponses) {
                    String jsonResponseStr = response.getResponse().getSourceAsString();
                    if (StringUtils.isNotEmpty(jsonResponseStr)) {
                        GoodsESModelVo goodsESModelVo = JSON.parseObject(jsonResponseStr, GoodsESModelVo.class);
                        goodsESModelVos.add(goodsESModelVo);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        stopWatch.stop();
        log.info("查询商品信息消耗时间:{}", stopWatch.getTime() + "ms");
        return goodsESModelVos;
    }

    @Override
    public List<GoodsESModelVo> queryGoodsByCategoryIdAndCountry(RecommentGoodsListESModelDTO
                                                                         recommentGoodsListESModelDTO) {
        log.info("根据国家和类目查询商品:{}", JSON.toJSONString(recommentGoodsListESModelDTO));
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<GoodsESModelVo> goodsESModelVos = Lists.newArrayList();
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        TermQueryBuilder isShow = QueryBuilders.termQuery("isShow", "1");
        TermQueryBuilder isDel = QueryBuilders.termQuery("isDel", 0);
        boolBuilder.must(isShow);
        boolBuilder.must(isDel);
        if (ObjectUtils.isNotEmpty(recommentGoodsListESModelDTO.getCategoryId())) {
            TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("categoryId", recommentGoodsListESModelDTO.getCategoryId());
            boolBuilder.must(termsQueryBuilder);
        }

        buildCountryTermQuery(recommentGoodsListESModelDTO.getCountryName(), boolBuilder);

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
        sourceBuilder.query(boolBuilder);
        if (recommentGoodsListESModelDTO.getPageSize() > 400 || recommentGoodsListESModelDTO.getPageNow() > 200) {
            sourceBuilder.from((recommentGoodsListESModelDTO.getPageNow() - 1) * 20);
            sourceBuilder.size(20);
        } else {
            sourceBuilder.from((recommentGoodsListESModelDTO.getPageNow() - 1) * recommentGoodsListESModelDTO.getPageSize());
            sourceBuilder.size(recommentGoodsListESModelDTO.getPageSize());
        }
        try {
            SearchRequest searchRequest = new SearchRequest(EsEnums.GOODS_ES.getIndex());
            searchRequest.types(EsEnums.GOODS_ES.getType());
            searchRequest.source(sourceBuilder);
            SearchResponse response = restHighLevelClient.search(searchRequest, builder());
            if (response.status().getStatus() == 200) {
                SearchHit[] results = response.getHits().getHits();
                if (results != null && results.length > 0) {
                    Map<String, VatDto> vatMap = goodsEsService.getVatMap();
                    Arrays.stream(results).forEach(result -> {
                        try {
                            JSONObject jsonObject = JSON.parseObject(result.getSourceAsString());
                            if (jsonObject != null) {
                                BigDecimal vatRate = goodsEsService.getVatRate(
                                        new VatCondition()
                                                .setGoodsId(jsonObject.getLong("id"))
                                                .setCountry(recommentGoodsListESModelDTO.getCountryName())
                                                .setShopId(jsonObject.getLong("shopId"))
                                                .setCategoryId(jsonObject.getLong("categoryId"))
                                                .setGoodsExtConfigModel(jsonObject.getObject("goodsExtConfigModel", com.voghion.es.model.GoodsExtConfigModel.class))
                                                .setDeliveryType(jsonObject.getInteger("deliveryType"))
                                                .setVatRate(jsonObject.getBigDecimal("vatRate"))
                                                .setVatMap(vatMap)
                                        );
                                String countryCode = "minprice." + recommentGoodsListESModelDTO.getCountryName().toUpperCase();
                                BigDecimal price = jsonObject.getBigDecimal(countryCode);
                                if (price == null) {
                                    price = jsonObject.getBigDecimal(defaultCountryCode);
                                }
                                BigDecimal minPrice = jsonObject.getBigDecimal("minPrice");
                                GoodsESModelVo goodsESModelVo = JSON.parseObject(result.getSourceAsString(), GoodsESModelVo.class);
                                if (price != null) {
                                    price = getVatPrice(vatRate, price);
                                    goodsESModelVo.setCountryPrice(price);
                                } else {
                                    minPrice = getVatPrice(vatRate, minPrice);
                                    goodsESModelVo.setCountryPrice(minPrice != null ? minPrice : BigDecimal.ZERO);
                                }
                                goodsESModelVos.add(goodsESModelVo);
                            }
                        } catch (Exception e) {
                            log.error("data error{}", e);
                        }
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("data error{}", e);
            mqSender.send("waring_error_topic", "根据国家和类目查询商品异常");
        }
        stopWatch.stop();
        log.info("根据国家和类目查询商品消耗时间:{}", stopWatch.getTime() + "ms");
        if (stopWatch.getTime() > 10 * 1000) {
            mqSender.send("waring_error_topic", "根据国家和类目查询商品异常");
        }
        return goodsESModelVos;
    }

    @Override
    public PageView<GoodsESModel> queryGoods(GoodsEsQueryInput input) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String words = input.getName();
        if (StringUtils.isBlank(words)) {
            return null;
        }

        List<String> keywordsList = Lists.newArrayList();
        if (words.indexOf(",") != -1) {
            String[] keyWords = words.split(",");
            keywordsList.addAll(Arrays.asList(keyWords));
        } else if (words.indexOf(" ") != -1) {
            String[] keyWords = words.split("\\s+");
            keywordsList.addAll(Arrays.asList(keyWords));
        }

        if (CollectionUtils.isEmpty(keywordsList)) {
            keywordsList.add(words);
        }
        //属性值
        GoodsESModel goodsESModel = new GoodsESModel();
        goodsESModel.setName(input.getName());
        goodsESModel.setIsShow("1");
        goodsESModel.setIsDel(0);
        goodsESModel.setCountry(input.getCountry());
        if (null != words && words.startsWith("@")) {
            goodsESModel.setId(Long.parseLong(words.replace("@", "")));
        } else {
            goodsESModel.setLikeName(keywordsList);
            if (null != input.getPageSize() && null != input.getPageNow()) {
                goodsESModel.setPageSize(input.getPageSize());
                goodsESModel.setPageNow(input.getPageNow());
            }

            if (null != input.getMinPrice()) {
                goodsESModel.setStartMinPrice(input.getMinPrice().doubleValue());
            }

            if (null != input.getMaxPrice()) {
                goodsESModel.setEndMinPrice(input.getMaxPrice().doubleValue());
            }


            //默认按权重排序
            goodsESModel.setSortKey("sortValue");

            Integer orderBy = input.getOrderBy();
            if (null != orderBy) {
                switch (orderBy) {
                    case 1:
                        goodsESModel.setSortKey("minPrice");
                        goodsESModel.setOrderBy("ASC");
                        break;
                    case 2:
                        goodsESModel.setSortKey("minPrice");
                        break;
                    case 3:
                        goodsESModel.setSortKey("sales");
                        goodsESModel.setOrderBy("ASC");
                        break;
                    case 4:
                        goodsESModel.setSortKey("sales");
                        break;
                    case 5:
                        goodsESModel.setSortKey("updateTime");
                        break;
                    case 6:
                        goodsESModel.setSortKey("score");
                        break;
                    case 7:
                        goodsESModel.setSortKey("orginalSales");
                        break;
                    case 8:
                        goodsESModel.setSortKey("sortValue");
                        break;
                    case 9:
                        break;
                }
            }
        }

        PageView<GoodsESModel> goodsESModelPageView = this.queryPageByOption(goodsESModel);
        stopWatch.stop();
        log.info("查询推荐商品消耗时间:{}", stopWatch.getTime() + "ms");
        if (stopWatch.getTime() > 10 * 1000) {
            mqSender.send("waring_error_topic", "查询推荐商品异常");
        }
        return goodsESModelPageView;
    }


    public PageView<GoodsESModel> queryPageByOption(GoodsESModel goodsESModel) {
        PageView pageView = new PageView();
        try {
            BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
            TermQueryBuilder isShow = QueryBuilders.termQuery("isShow", 1);
            TermQueryBuilder isDel = QueryBuilders.termQuery("isDel", 0);
            boolBuilder.must(isShow);
            boolBuilder.must(isDel);

            if (goodsESModel.getId() != null) {
                TermQueryBuilder id = QueryBuilders.termQuery("id", goodsESModel.getId());
                boolBuilder.must(id);
            }

            buildCountryTermQuery(goodsESModel.getCountry(), boolBuilder);

            if (StringUtils.isNotEmpty(goodsESModel.getName()) && goodsESModel.getId() == null) {
                MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery("name", goodsESModel.getName()); //must表示and
                boolBuilder.must(matchQueryBuilder);
            }

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
            sourceBuilder.query(boolBuilder);
            if (goodsESModel.getPageSize() > 200 || goodsESModel.getPageNow() > 200) {
                sourceBuilder.from((goodsESModel.getPageNow() - 1) * 20);
                sourceBuilder.size(20);
            } else {
                sourceBuilder.from((goodsESModel.getPageNow() - 1) * goodsESModel.getPageSize());
                sourceBuilder.size(goodsESModel.getPageSize());
            }
            SearchRequest searchRequest = new SearchRequest();
            searchRequest.indices(EsEnums.GOODS_ES.getIndex());
            searchRequest.source(sourceBuilder);

            String sortKey = goodsESModel.getSortKey();
            String orderBy = goodsESModel.getOrderBy();
            if (StringUtils.isNotEmpty(sortKey)) {
                if (StringUtils.isNotEmpty(orderBy)) {
                    sourceBuilder.sort(sortKey, SortOrder.ASC);
                } else {
                    sourceBuilder.sort(sortKey, SortOrder.DESC);
                }
            }
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, builder());
            pageView.setPageSize(goodsESModel.getPageSize());
            pageView.setPageNow(goodsESModel.getPageNow());
            List<GoodsESModel> userDataList = new ArrayList();
            SearchHits searchHits = searchResponse.getHits();
            if (null != searchHits) {
                SearchHit[] searchHitsArr = searchHits.getHits();
                if (null != searchHitsArr && searchHitsArr.length > 0) {
                    searchHits.forEach((hit) -> {
                        GoodsESModel tmp = JSON.parseObject(hit.getSourceAsString(), GoodsESModel.class);
                        userDataList.add(tmp);
                    });
                    pageView.setRowCount(searchHits.getTotalHits().value);
                }
            }

            pageView.setRecords(userDataList);
        } catch (Exception var8) {
            var8.printStackTrace();
        }

        return pageView;
    }


    @Override
    @LogAnnotation(message = "根据国家和商品id处理排序")
    public List<Long> queryGoodsIdByGoodsIdAndCountry(RecommentGoodsListESModelDTO recommentGoodsListESModelDTO) {
        log.info("根据国家和商品id处理排序入参：{}", recommentGoodsListESModelDTO);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Long> goodsId = recommentGoodsListESModelDTO.getGoodsId();
        Integer orderBy = recommentGoodsListESModelDTO.getOrderBy();
        String countryName = recommentGoodsListESModelDTO.getCountryName();
        if (StringUtils.isEmpty(countryName)) {
            log.error("国家未传");
            return Lists.newArrayList();
        }
        SearchRequest searchRequest = new SearchRequest(EsEnums.GOODS_ES.getIndex());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
        if (recommentGoodsListESModelDTO.getPageSize() > 200 || recommentGoodsListESModelDTO.getPageNow() > 200) {
            searchSourceBuilder.from((recommentGoodsListESModelDTO.getPageNow() - 1) * 20);
            searchSourceBuilder.size(20);
        } else {
            searchSourceBuilder.from((recommentGoodsListESModelDTO.getPageNow() - 1) * recommentGoodsListESModelDTO.getPageSize());
            searchSourceBuilder.size(recommentGoodsListESModelDTO.getPageSize());
        }

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if (CollectionUtils.isNotEmpty(goodsId)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("goodsId", goodsId));
        }
        boolQueryBuilder.must(QueryBuilders.termQuery("isShow", "1"));
        boolQueryBuilder.must(QueryBuilders.termQuery("isDel", 0));

        buildCountryTermQuery(recommentGoodsListESModelDTO.getCountryName(), boolQueryBuilder);

        searchSourceBuilder.query(boolQueryBuilder);

        String countryCode = "minprice." + countryName.toUpperCase();

//        if (orderBy != null) {
//            switch (orderBy) {
//                case 1:
//                    searchSourceBuilder.sort(countryCode, SortOrder.ASC);
//                    break;
//                case 2:
//                    searchSourceBuilder.sort(countryCode, SortOrder.DESC);
//                    break;
//                case 3:
//                    searchSourceBuilder.sort("sales", SortOrder.ASC);
//                    break;
//                case 4:
//                    searchSourceBuilder.sort("sales", SortOrder.DESC);
//                    break;
//                case 5:
//                case 6:
//                case 7:
//                case 8:
//                default:
//                    break;
//            }
//        }
        searchRequest.source(searchSourceBuilder);
        SearchResponse search = null;
        try {
            search = restHighLevelClient.search(searchRequest, builder());
        } catch (IOException e) {
            e.printStackTrace();
            log.error("data error", e);
        }
        List<Long> res = new ArrayList<>();
        if (search != null && search.status().getStatus() == RestStatus.OK.getStatus()) {
            SearchHits searchHits = search.getHits();
            if (searchHits != null) {
                SearchHit[] searchHitsHits = searchHits.getHits();
                if (searchHitsHits != null && searchHitsHits.length > 0) {
                    res = Arrays.stream(searchHitsHits)
                            .filter(Objects::nonNull)
                            .map(x -> JSON.parseObject(x.getSourceAsString(), GoodsESModelVo.class).getId())
                            .collect(Collectors.toList());
                }
            }
        }
        stopWatch.stop();
        log.info("根据国家和商品id处理排序消耗时间:{}", stopWatch.getTime() + "ms");
        return res;
    }

    @Override
    public List<GoodsESModelVo> queryGoodsByCondition(GoodsConditionInput goodsConditionInput) {
        log.info("根据后台类目id和店铺id查询商品信息:{}", goodsConditionInput);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<GoodsESModelVo> goodsESModelVos = Lists.newArrayList();
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        TermQueryBuilder isShow = QueryBuilders.termQuery("isShow", "1");
        TermQueryBuilder isDel = QueryBuilders.termQuery("isDel", 0);
        boolBuilder.must(isShow);
        boolBuilder.must(isDel);
        if (ObjectUtils.isNotEmpty(goodsConditionInput.getCategoryIds())) {
            TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("categoryId", goodsConditionInput.getCategoryIds());
            boolBuilder.must(termsQueryBuilder);
        }
        if (ObjectUtils.isNotEmpty(goodsConditionInput.getShopIds())) {
            TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery("shopId", goodsConditionInput.getShopIds());
            boolBuilder.must(termsQueryBuilder);
        }

        buildCountryTermQuery(goodsConditionInput.getCountryName(), boolBuilder);

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
        sourceBuilder.sort("createTime", SortOrder.DESC);
        sourceBuilder.query(boolBuilder);
        if (goodsConditionInput.getPageSize() > 200 || goodsConditionInput.getPageNow() > 200) {
            sourceBuilder.from((goodsConditionInput.getPageNow() - 1) * 20);
            sourceBuilder.size(20);
        } else {
            sourceBuilder.from((goodsConditionInput.getPageNow() - 1) * goodsConditionInput.getPageSize());
            sourceBuilder.size(goodsConditionInput.getPageSize());
        }
        log.info("queryGoodsByCondition sourceBuilder:{}", sourceBuilder);
        try {
            SearchRequest searchRequest = new SearchRequest(EsEnums.GOODS_ES.getIndex());
            searchRequest.types(EsEnums.GOODS_ES.getType());
            searchRequest.source(sourceBuilder);
            SearchResponse response = restHighLevelClient.search(searchRequest, builder());
            if (response.status().getStatus() == 200) {
                SearchHit[] results = response.getHits().getHits();
                if (results != null && results.length > 0) {
                    goodsESModelVos = Arrays.stream(results)
                            .filter(Objects::nonNull)
                            .map(x -> JSON.parseObject(x.getSourceAsString(), GoodsESModelVo.class))
                            .collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("data error{}", e);
            mqSender.send("waring_error_topic", "根据后台类目id和店铺id查询商品信息异常");
        }
        stopWatch.stop();
        log.info("根据后台类目id和店铺id查询商品信息消耗时间:{}", stopWatch.getTime() + "ms");
        if (stopWatch.getTime() > 10 * 1000) {
            mqSender.send("waring_error_topic", "根据后台类目id和店铺id查询商品信息异常");
        }
        return goodsESModelVos;
    }

    /**
     * 国家区域es查询 默认欧美 现在只有PH的是菲律宾的
     *
     * @param countryName      国家名称
     * @param boolQueryBuilder 查询条件
     */
    private void buildCountryTermQuery(String countryName, BoolQueryBuilder boolQueryBuilder) {
        BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
        boolQueryBuilder2.should(QueryBuilders.termQuery("country.keyword", "SYSTEM"));
        if (StringUtils.isNotEmpty(countryName)) {
            boolQueryBuilder2.should(QueryBuilders.matchPhraseQuery("country", countryName));
        }
        boolQueryBuilder.must(boolQueryBuilder2);
    }


    @Override
    @LogAnnotation(message = "根据商品id查询国家运费")
    public List<GoodsFreightVO> queryGoodsFreightFree(List<GoodsFreightDTO> goodsFreightDTOS) {
        if (CollectionUtils.isEmpty(goodsFreightDTOS)) {
            return Lists.newArrayList();
        }
        List<GoodsFreight> goodsFreightsVo = Lists.newArrayList();
        List<Long> skuIds = Lists.newArrayList();
        //取出skuids
        for (GoodsFreightDTO goodsFreightDTO : goodsFreightDTOS) {
            skuIds.addAll(goodsFreightDTO.getSkuIds());
        }
        //根据批量商品id查询所有商品国家运费
        List<GoodsFreight> goodsFreights = goodsFreightService.queryByGoodsIds(goodsFreightDTOS, goodsFreightDTOS.get(0).getCountryName());
        if (!CollectionUtils.isEmpty(goodsFreights)) {
            Map<Long, GoodsFreight> goodsFreightMap = goodsFreights.stream().collect(Collectors.toMap(GoodsFreight::getSkuId, Function.identity(), (v1, v2) -> v2));
            skuIds.stream().forEach(skuId -> {
                GoodsFreight goodsFreight = goodsFreightMap.get(skuId);
                if (goodsFreight != null) {
                    goodsFreightsVo.add(goodsFreight);
                }
            });
        }
        log.info("根据商品id查询国家运费:{}", JSON.toJSONString(goodsFreightsVo));
        List<GoodsFreightVO> goodsFreightVOS = TransferUtils.transferList(goodsFreightsVo, GoodsFreightVO.class);
        return goodsFreightVOS;
    }


    private List<GoodsESModelVo> dealGoods(List<GoodsESModelVo> goodsESModelVos, List<Long> goodsIds, Integer
            orderBy) {
        if (CollectionUtils.isEmpty(goodsESModelVos)) {
            return null;
        }
        List<GoodsESModelVo> collect = new ArrayList<>();
        switch (Integer.valueOf(orderBy)) {
            case 1:
                collect = goodsESModelVos.stream().sorted(Comparator.comparing(GoodsESModelVo::getCountryPrice)).collect(Collectors.toList());
                break;
            case 2:
                collect = goodsESModelVos.stream().sorted(Comparator.comparing(GoodsESModelVo::getCountryPrice).reversed()).collect(Collectors.toList());
                break;
            case 3:
                collect = goodsESModelVos.stream().sorted(Comparator.comparing(GoodsESModelVo::getSales)).collect(Collectors.toList());
                break;
            case 4:
                collect = goodsESModelVos.stream().sorted(Comparator.comparing(GoodsESModelVo::getSales).reversed()).collect(Collectors.toList());
                break;
            case 5:
            case 6:
            case 7:
            case 8:
            default:
                List<GoodsESModelVo> collectNew = new ArrayList<>();
                collect = goodsESModelVos;
                if (CollectionUtils.isNotEmpty(collect)) {
                    Map<Long, GoodsESModelVo> goodsESModelVoMap = collect.stream().collect(Collectors.toMap(GoodsESModelVo::getId, Function.identity(), (key1, key2) -> key2));
                    goodsIds.stream().forEach(goodsId -> {
                        GoodsESModelVo goodsESModelVo = goodsESModelVoMap.get(goodsId);
                        if (goodsESModelVo != null) {
                            collectNew.add(goodsESModelVo);
                        }
                    });
                }
                collect = collectNew;
                break;
        }
        return collect;
    }


    public String dealCountry(String countryName) {
        List<String> countrys = Lists.newArrayList("IT", "FR", "DE"
                , "BE", "AT", "ES", "NL", "AU", "GB");
        if (!countrys.contains(countryName)) {
            return "all";
        }
        return countryName;
    }

    public List<Long> dealGender(Long gender) {
        List<Long> genders = null;
        if (gender.compareTo(1l) == 0) {
            return Lists.newArrayList(1l, 3l);
        } else if (gender.compareTo(2l) == 0) {
            return Lists.newArrayList(2l, 3l);
        } else if (gender.compareTo(3l) == 0) {
            return Lists.newArrayList(1l, 2l, 3l);
        }
        return genders;
    }

    public RequestOptions builder() {
        RequestOptions.Builder builder = RequestOptions.DEFAULT.toBuilder();
        builder.setHttpAsyncResponseConsumerFactory(
                new HttpAsyncResponseConsumerFactory
                        //修改为300MB
                        .HeapBufferedResponseConsumerFactory(600 * 1024 * 1024));
        return builder.build();
    }


    @Override
    public PageView<GoodsOperateLogVo> queryGoodsItemOperationLog(GoodsInput input) {
        CheckUtils.notNull(input.getGoodsIdListStr(), ProductResultCode.GOODS_CODE_NULL_ERROR);
        PageView<GoodsOperateLogVo> page = new PageView<>(input.getPageSize(), input.getPageNow());

        List<Long> ids = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(input.getGoodsIdListStr())) {
            String[] arr = input.getGoodsIdListStr().split("\n");
            for (String str : arr) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(str)) {
                    ids.add(Long.valueOf(str));
                }
            }
        }
        GoodsLogQueryDto queryDto = BeanCopyUtil.transform(input, GoodsLogQueryDto.class);
        queryDto.setGoodsIdList(ids);
        queryDto.setDaysRange(999);
        PageView<GoodsItemLogDto> pageView = goodsLogClientFactory.queryGoodsItemLog(queryDto);
        if (0 == pageView.getRowCount() || CollectionUtils.isEmpty(pageView.getRecords())) {
            return page;
        }
        List<GoodsItemLogDto> records = pageView.getRecords();
        List<GoodsItemLogDto> goodsLogList = records.stream().filter(goodsItemLogDto -> goodsItemLogDto.getSkuId() == null).collect(Collectors.toList());
        List<GoodsItemLogDto> goodsItemLogList = records.stream().filter(goodsItemLogDto -> goodsItemLogDto.getSkuId() != null).collect(Collectors.toList());

        List<GoodsOperateLogVo> vos = new ArrayList<>();
        if (goodsLogList.size() == 1) {
            vos.add(new GoodsOperateLogVo(goodsLogList.get(0)));
        } else {
            goodsLogList.sort(Comparator.comparing(GoodsItemLogDto::getUpdateTime));
            for (int i = 0; i < goodsLogList.size() - 1; i++) {
                vos.add(new GoodsOperateLogVo(goodsLogList.get(i), goodsLogList.get(i + 1)));
            }
        }

        if (goodsItemLogList.size() == 1) {
            vos.add(new GoodsOperateLogVo(goodsItemLogList.get(0)));
        } else {
            goodsItemLogList.sort(Comparator.comparing(GoodsItemLogDto::getUpdateTime));
            for (int i = 0; i < goodsItemLogList.size() - 1; i++) {
                vos.add(new GoodsOperateLogVo(goodsItemLogList.get(i), goodsItemLogList.get(i + 1)));
            }
        }

        List<GoodsOperateLogVo> result = vos.stream().sorted((o1, o2) -> o2.getUpdateTimeDate().compareTo(o1.getUpdateTimeDate()))
                .skip(input.getPageSize() * (input.getPageNow() - 1))
                .limit(input.getPageSize())
                .collect(Collectors.toList());

        page.setRowCount(vos.size());
        page.setRecords(result);
        return page;
    }

    @Override
    public List<GoodsOperateFreightLogVo> queryGoodsFreightOperationLog(GoodsInput input) {
        CheckUtils.notNull(input.getGoodsId(), ProductResultCode.GOODS_CODE_NULL_ERROR);

        GoodsLogQueryDto queryDto = BeanCopyUtil.transform(input, GoodsLogQueryDto.class);
        List<GoodsFreightLogDto> list = goodsLogClientFactory.queryGoodsFreightLog(queryDto);

        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        if (list.size() == 1) {
            return Collections.singletonList(new GoodsOperateFreightLogVo(list.get(0)));
        }

        List<GoodsOperateFreightLogVo> vos = new ArrayList<>();
        list.sort(Comparator.comparing(GoodsFreightLogDto::getUpdateTime));
        for (int i = 0; i < list.size() - 1; i++) {
            vos.add(new GoodsOperateFreightLogVo(list.get(i), list.get(i + 1)));
        }

        return vos;
    }


    @Override
    public Boolean deleteGoodsInES(List<Long> goodsIds) {
        BulkRequest bulkRequest = new BulkRequest();
        for (Long id : goodsIds) {
            DeleteRequest deleteRequest = new DeleteRequest(EsEnums.GOODS_ES.getIndex(), id.toString());
            bulkRequest.add(deleteRequest);
        }

        restHighLevelClient.bulkAsync(bulkRequest, RequestOptions.DEFAULT, new ActionListener<BulkResponse>() {
            @Override
            public void onResponse(BulkResponse bulkItemResponses) {
            }

            @Override
            public void onFailure(Exception e) {
                log.info("删除数据失败", e);
            }
        });

        return true;
    }

    @Override
    public PageView<GoodsInfoDTO> queryPageBackgroundByOption(GoodsDTO goodsDTO) {
        PageView<GoodsInfoDTO> pageView = new PageView<>();
        try {
            BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

            if (goodsDTO.getIsReduction() != null) {
                boolBuilder.must(QueryBuilders.termQuery("isReduction", goodsDTO.getIsReduction()));
            }

            if (goodsDTO.getIsDel() != null) {
                boolBuilder.must(QueryBuilders.termQuery("isDel", goodsDTO.getIsDel()));
            }

            if (goodsDTO.getGoodsId() != null) {
                boolBuilder.must(QueryBuilders.termQuery("id", goodsDTO.getGoodsId()));
            }

            if (CollectionUtils.isNotEmpty(goodsDTO.getGoodsIds())) {
                boolBuilder.must(QueryBuilders.termsQuery("id", goodsDTO.getGoodsIds()));
            }

            if (CollectionUtils.isNotEmpty(goodsDTO.getExcludeGoodsIds())) {
                boolBuilder.mustNot(QueryBuilders.termsQuery("id", goodsDTO.getExcludeGoodsIds()));
            }

            if (goodsDTO.getStatus() != null) {
                TermQueryBuilder isShow1 = QueryBuilders.termQuery("status", goodsDTO.getStatus());
                boolBuilder.must(isShow1);
            }

            if (StringUtils.isNotBlank(goodsDTO.getIsShow())) {
                if (goodsDTO.getIsShow().equals("100")) {
                    boolBuilder.must(QueryBuilders.termsQuery("isShow", GoodsIsShowEnums.getShopProhibitShowTypes()));
                } else if (goodsDTO.getIsShow().equals("200")) {
                    boolBuilder.must(QueryBuilders.termsQuery("isShow", GoodsIsShowEnums.getShopHolidayShowTypes()));
                } else {
                    boolBuilder.must(QueryBuilders.termQuery("isShow", goodsDTO.getIsShow()));
                }
            }
            boolBuilder.mustNot(QueryBuilders.termQuery("isShow", GoodsIsShowEnums.TEMPLATE.getType()));

            if (CollectionUtils.isNotEmpty(goodsDTO.getCategoryIds())) {
                boolBuilder.must(QueryBuilders.termsQuery("categoryId", Sets.newHashSet(goodsDTO.getCategoryIds())));
            }

            if (StringUtils.isNotBlank(goodsDTO.getName())) {
                boolBuilder.must(QueryBuilders.matchQuery("name", goodsDTO.getName()).analyzer("ik_smart"));
            }

            if (StringUtils.isNotEmpty(goodsDTO.getStartTime()) || StringUtils.isNotEmpty(goodsDTO.getEndTime())) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("createTime");
                rangeQueryBuilder.format(DateUtil.newFormat);
                if (StringUtils.isNotEmpty(goodsDTO.getStartTime())) {
                    rangeQueryBuilder.gte(goodsDTO.getStartTime());
                }
                if (StringUtils.isNotBlank(goodsDTO.getEndTime())) {
                    rangeQueryBuilder.lte(goodsDTO.getEndTime());
                }
                boolBuilder.must(rangeQueryBuilder);
            }

            if (Objects.nonNull(goodsDTO.getStartMinPrice()) || Objects.nonNull(goodsDTO.getEndMinPrice())) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("minPrice");
                if (Objects.nonNull(goodsDTO.getStartMinPrice())) {
                    rangeQueryBuilder.gte(goodsDTO.getStartMinPrice());
                }
                if (Objects.nonNull(goodsDTO.getEndMinPrice())) {
                    rangeQueryBuilder.lte(goodsDTO.getEndMinPrice());
                }
                boolBuilder.must(rangeQueryBuilder);
            }

            if (Objects.nonNull(goodsDTO.getStartMaxPrice())) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("maxPrice");
                rangeQueryBuilder.from(goodsDTO.getStartMaxPrice());
                boolBuilder.must(rangeQueryBuilder);
            }

            if (Objects.nonNull(goodsDTO.getEndMaxPrice())) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("maxPrice");
                rangeQueryBuilder.to(goodsDTO.getEndMaxPrice());
                boolBuilder.must(rangeQueryBuilder);
            }

            if (StringUtils.isNotBlank(goodsDTO.getCountry())) {
                if (goodsDTO.getCountry().equals(GoodsTransferUtils.AvailableCountryTypeEnums.ALL.getDetail())) {
                    BoolQueryBuilder countryQueryBuilder = QueryBuilders.boolQuery();
                    countryQueryBuilder.should(QueryBuilders.termQuery("country.keyword", GoodsTransferUtils.DEFAULT_SYSTEM_COUNTRY_LIST));
                    countryQueryBuilder.should(QueryBuilders.termQuery("country.keyword", GoodsTransferUtils.AvailableCountryTypeEnums.ALL.getDetail()));
                    boolBuilder.must(countryQueryBuilder);
                } else{
                    List<String> countryList = Arrays.stream(goodsDTO.getCountry().split(",")).collect(Collectors.toList());
                    BoolQueryBuilder countryQueryBuilder = QueryBuilders.boolQuery();
                    countryList.forEach(value ->{
                        countryQueryBuilder.should(QueryBuilders.matchPhraseQuery("country", value));
                    });
                    boolBuilder.must(countryQueryBuilder);
                }
            }

            if (goodsDTO.getIsLock() != null) {
                boolBuilder.must(QueryBuilders.termQuery("isLock", goodsDTO.getIsLock()));
            }

            if (goodsDTO.getShopId() != null) {
                boolBuilder.must(QueryBuilders.termQuery("shopId", goodsDTO.getShopId()));
            }

            if (CollectionUtils.isNotEmpty(goodsDTO.getShopIds())) {
                boolBuilder.must(QueryBuilders.termsQuery("shopId", goodsDTO.getShopIds()));
            }

            if (CollectionUtils.isNotEmpty(goodsDTO.getExcludeShopIds())) {
                boolBuilder.mustNot(QueryBuilders.termsQuery("shopId", goodsDTO.getExcludeShopIds()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getShopName())) {
                boolBuilder.must(QueryBuilders.matchPhraseQuery("shopName", goodsDTO.getShopName()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getGoodsUrl())) {
                boolBuilder.must(QueryBuilders.termQuery("goodsExtDetailModel.goodsUrl.keyword", goodsDTO.getGoodsUrl()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getSupplierGoodsId())) {
                boolBuilder.must(QueryBuilders.termQuery("goodsExtDetailModel.itemCode.keyword", goodsDTO.getSupplierGoodsId()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getOriginalShopName())) {
                boolBuilder.must(QueryBuilders.termQuery("goodsExtDetailModel.originalShopName.keyword", goodsDTO.getOriginalShopName()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getProcureSupplier())) {
                boolBuilder.must(QueryBuilders.termQuery("goodsExtDetailModel.procureSupplier.keyword", goodsDTO.getProcureSupplier()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getCostUrl())) {
                boolBuilder.must(QueryBuilders.termQuery("goodsExtDetailModel.costUrl.keyword", goodsDTO.getCostUrl()));
            }

            if (goodsDTO.getTranslated() != null) {
                boolBuilder.must(QueryBuilders.termQuery("goodsExtDetailModel.translated", goodsDTO.getTranslated()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getPrincipal())) {
                boolBuilder.must(QueryBuilders.termQuery("principal.keyword", goodsDTO.getPrincipal()));
            }

            if (goodsDTO.getDeliveryType() != null) {
                boolBuilder.must(QueryBuilders.termQuery("deliveryType", goodsDTO.getDeliveryType()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getArrival()) && !"0".equals(goodsDTO.getArrival())) {
                boolBuilder.must(QueryBuilders.termQuery("goodsSelfSupportInfoModel.arrival.keyword", goodsDTO.getArrival()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getArrival()) && "0".equals(goodsDTO.getArrival())) {
                boolBuilder.mustNot(QueryBuilders.existsQuery("goodsSelfSupportInfoModel.arrival"));
            }

            if (goodsDTO.getSelfSupportCreateStartTime() != null || goodsDTO.getSelfSupportCreateEndTime() != null) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("goodsSelfSupportInfoModel.createTime");
                if (goodsDTO.getSelfSupportCreateStartTime() != null) {
                    rangeQueryBuilder.gte(goodsDTO.getSelfSupportCreateStartTime().getTime());
                }
                if (goodsDTO.getSelfSupportCreateEndTime() != null) {
                    rangeQueryBuilder.lte(goodsDTO.getSelfSupportCreateEndTime().getTime());
                }
                boolBuilder.must(rangeQueryBuilder);
            }

            if (goodsDTO.getSelfSupportStatus() != null) {
                boolBuilder.must(QueryBuilders.termQuery("goodsSelfSupportInfoModel.status", goodsDTO.getSelfSupportStatus()));
            }

            if (goodsDTO.getBrandId() != null) {
                boolBuilder.must(QueryBuilders.termQuery("goodsExtDetailModel.brandId", goodsDTO.getBrandId()));
            }

            if (goodsDTO.getIsDistributeArrival() != null && goodsDTO.getIsDistributeArrival() == 1) {
                boolBuilder.must(QueryBuilders.existsQuery("goodsSelfSupportInfoModel.arrival"));
            }

            if (goodsDTO.getIsDistributeArrival() != null && goodsDTO.getIsDistributeArrival() == 0) {
                boolBuilder.mustNot(QueryBuilders.existsQuery("goodsSelfSupportInfoModel.arrival"));
            }

            if (goodsDTO.getIsOnlyListing() != null) {
                if (goodsDTO.getIsOnlyListing() == 1) {
                    boolBuilder.must(QueryBuilders.existsQuery("listingId"));
                }
                if (goodsDTO.getIsOnlyListing() == 0) {
                    boolBuilder.mustNot(QueryBuilders.existsQuery("listingId"));
                }
            }

            if (goodsDTO.getFirstShowStartTime() != null || goodsDTO.getFirstShowEndTime() != null) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("goodsSelfSupportInfoModel.firstShowTime");
                if (goodsDTO.getFirstShowStartTime() != null) {
                    rangeQueryBuilder.gte(goodsDTO.getFirstShowStartTime().getTime());
                }
                if (goodsDTO.getFirstShowEndTime() != null) {
                    rangeQueryBuilder.lte(goodsDTO.getFirstShowEndTime().getTime());
                }
                boolBuilder.must(rangeQueryBuilder);
            }

            if (goodsDTO.getLastEditStartTime() != null || goodsDTO.getLastEditEndTime() != null) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("goodsSelfSupportInfoModel.lastEditTime");
                if (goodsDTO.getLastEditStartTime() != null) {
                    rangeQueryBuilder.gte(goodsDTO.getLastEditStartTime().getTime());
                }
                if (goodsDTO.getLastEditEndTime() != null) {
                    rangeQueryBuilder.lte(goodsDTO.getLastEditEndTime().getTime());
                }
                boolBuilder.must(rangeQueryBuilder);
            }

            if (goodsDTO.getIsSupportStockUp() != null) {
                boolBuilder.must(QueryBuilders.termQuery("isSupportStockUp", goodsDTO.getIsSupportStockUp()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getLockLabel())) {
                boolBuilder.must(QueryBuilders.termQuery("lockLabels.keyword", goodsDTO.getLockLabel()));
            }

            if (StringUtils.isNotBlank(goodsDTO.getTag())) {
                MatchQueryBuilder tag = QueryBuilders.matchQuery("goodsExtConfigModel.tagIds", goodsDTO.getTag());
                boolBuilder.must(tag);
            }

            if (CollectionUtils.isNotEmpty(goodsDTO.getIncludeTags())) {
                TermsQueryBuilder tag = QueryBuilders.termsQuery("goodsExtConfigModel.tagIds", goodsDTO.getIncludeTags());
                boolBuilder.must(tag);
            }

            if (CollectionUtils.isNotEmpty(goodsDTO.getExcludeTags())) {
                TermsQueryBuilder tag = QueryBuilders.termsQuery("goodsExtConfigModel.tagIds", goodsDTO.getExcludeTags());
                boolBuilder.mustNot(tag);
            }

            if (goodsDTO.getGoodsType() != null) {
                boolBuilder.must(QueryBuilders.termQuery("type", goodsDTO.getGoodsType()));
            }

            if (goodsDTO.getHasLeadTime() != null && goodsDTO.getHasLeadTime() == 0) {
                boolBuilder.must(QueryBuilders.termQuery("goodsExtDetailModel.leadTime", 0));
            } else if (goodsDTO.getHasLeadTime() != null && goodsDTO.getHasLeadTime() == 1) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("goodsExtDetailModel.leadTime");
                rangeQueryBuilder.gt(0);
                boolBuilder.must(rangeQueryBuilder);
            }

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));

            sourceBuilder.query(boolBuilder);
            sourceBuilder.from((goodsDTO.getPageNow() - 1) * goodsDTO.getPageSize());
            sourceBuilder.size(goodsDTO.getPageSize());
            sourceBuilder.fetchSource(new String[]{"id", "name", "isShow", "isLock", "isDel", "createTime", "mainImage", "categoryId", "country", "tag",
                    "minPrice", "maxPrice", "minMarketPrice", "maxMarketPrice", "minGrouponPrice", "maxGrouponPrice", "orginalMaxPrice", "sortValue", "shopId", "shopName",
                    "isReduction", "minCostPrice", "maxCostPrice", "goodsExtDetailModel", "countryDelivery", "status", "type", "brandName"}, null);


            if (goodsDTO.getReductionAmountSort() != null && goodsDTO.getReductionAmountSort() == 0) {
                sourceBuilder.sort("backendInfoModel.reductionAmount", SortOrder.DESC);
            } else if (goodsDTO.getReductionAmountSort() != null && goodsDTO.getReductionAmountSort() == 1) {
                sourceBuilder.sort("backendInfoModel.reductionAmount", SortOrder.ASC);
            } else if (goodsDTO.getSortType() != null && goodsDTO.getSortType() == 1) {
                sourceBuilder.sort("goodsSelfSupportInfoModel.createTime", SortOrder.ASC);
            } else if (StringUtils.isBlank(goodsDTO.getName())) {
                sourceBuilder.sort("id", SortOrder.DESC);
            }

            log.info("queryGoodsList sourceBuilder:{}", sourceBuilder);
            SearchRequest searchRequest = new SearchRequest();
            searchRequest.indices(EsEnums.GOODS_ES.getIndex());
            searchRequest.source(sourceBuilder);


            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, builder());
            pageView.setPageSize(goodsDTO.getPageSize());
            pageView.setPageNow(goodsDTO.getPageNow());
            List<GoodsInfoDTO> userDataList = new ArrayList<>();
            SearchHits searchHits = searchResponse.getHits();
            log.info("queryGoodsList 命中size:{}, total:{}", searchHits != null && searchHits.getHits() != null ? searchHits.getHits().length : 0,
                    searchHits != null && searchHits.getTotalHits() != null ? searchHits.getTotalHits().value : 0);
            if (null != searchHits) {
                SearchHit[] searchHitsArr = searchHits.getHits();
                if (null != searchHitsArr && searchHitsArr.length > 0) {
                    searchHits.forEach((hit) -> {
                        GoodsInfoDTO tmp = JSON.parseObject(hit.getSourceAsString(), GoodsInfoDTO.class);
                        userDataList.add(tmp);
                    });
                    pageView.setRowCount(searchHits.getTotalHits().value);
                }
            }

            pageView.setRecords(userDataList);

            if (pageView.getRowCount() >= 10000) {
                CountRequest countRequest = new CountRequest();
                countRequest.indices(EsEnums.GOODS_ES.getIndex());
                countRequest.query(boolBuilder);
                CountResponse count = restHighLevelClient.count(countRequest, builder());
                if (count != null) {
                    pageView.setRowCount(count.getCount());
                }
            }
        } catch (Exception e) {
            log.info("queryGoodsList es查询异常", e);
        }

        return pageView;
    }

    private String initMatchPhraseKey(String key) {
        return String.format("*%s*", key);
    }

    @Override
    public List<GoodsESModelVo> queryGoodsByIdsAndInvalidCategoryIds(List<Long> goodsIds, List<Long> invalidCategoryIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyList();
        }
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        boolBuilder.must(QueryBuilders.termsQuery("id", goodsIds));

        if (CollectionUtils.isNotEmpty(invalidCategoryIds)) {
            boolBuilder.mustNot(QueryBuilders.termsQuery("categoryId", invalidCategoryIds));
        }

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolBuilder);
        sourceBuilder.from((1));
        sourceBuilder.size(goodsIds.size());
//        log.info("debug queryGoodsByIdsAndInvalidCategoryIds sourceBuilder:{}", sourceBuilder);
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(EsEnums.GOODS_ES.getIndex());
        searchRequest.source(sourceBuilder);

        SearchResponse searchResponse;
        try {
            long start = System.currentTimeMillis();
            searchResponse = restHighLevelClient.search(searchRequest, builder());
            log.info("debug search es耗时:{}", (System.currentTimeMillis() - start));
        } catch (IOException e) {
            log.info("debug queryGoodsByIdsAndInvalidCategoryIds查询es异常", e);
            return Collections.emptyList();
        }
        List<GoodsESModelVo> userDataList = new ArrayList<>();
        SearchHits searchHits = searchResponse.getHits();
        if (null != searchHits) {
            SearchHit[] searchHitsArr = searchHits.getHits();
            if (null != searchHitsArr && searchHitsArr.length > 0) {
                searchHits.forEach((hit) -> {
                    GoodsESModelVo tmp = JSON.parseObject(hit.getSourceAsString(), GoodsESModelVo.class);
                    userDataList.add(tmp);
                });
            }
        }
        return userDataList;
    }

    @Override
    public List<GoodsESModelVo> queryGoodsByIdsAndInvalidCategoryIds2(List<Long> goodsIds, List<Long> invalidCategoryIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyList();
        }
        List<GoodsESModelVo> userDataList = new ArrayList<>();
        MultiGetRequest multiGetRequest = new MultiGetRequest();
        goodsIds.forEach(goodsId -> multiGetRequest.add(EsEnums.GOODS_ES.getIndex(), EsEnums.GOODS_ES.getType(), String.valueOf(goodsId)));

        try {
            long start = System.currentTimeMillis();
            MultiGetResponse searchResponse = restHighLevelClient.mget(multiGetRequest, builder());
            log.info("debug mget es耗时:{}", (System.currentTimeMillis() - start));
            for (MultiGetItemResponse multiGetItemResponse : searchResponse.getResponses()) {
                String sourceAsString = multiGetItemResponse.getResponse().getSourceAsString();
                GoodsESModelVo vo = JSON.parseObject(sourceAsString, GoodsESModelVo.class);
                if (invalidCategoryIds.contains(vo.getCategoryId())) {
                    continue;
                }
                userDataList.add(vo);
            }
        } catch (IOException e) {
            log.info("debug queryGoodsByIdsAndInvalidCategoryIds2查询es异常", e);
            return Collections.emptyList();
        }
        return userDataList;
    }

    @Override
    public GoodsEsDetailVo queryGoodsDetail(Long goodsId) {
        try {
            GetRequest getRequest = new GetRequest(EsEnums.GOODS_ES.getIndex(), String.valueOf(goodsId));
            String getResponse = restHighLevelClient.get(getRequest, builder()).getSourceAsString();
            if (StringUtils.isNotEmpty(getResponse)) {
                GoodsEsDetailVo goodsEsDetailVo = JSON.parseObject(getResponse, GoodsEsDetailVo.class);
                if (goodsEsDetailVo != null) {
                    if (goodsEsDetailVo.getGoodsDetailModel() == null) {
                        GoodsDetail goodsDetail = goodsDetailService.queryGoodsDetailByGoodsId(goodsId);
                        if (goodsDetail != null) {
                            GoodsDetailModel goodsDetailModel = new GoodsDetailModel();
                            BeanUtils.copyProperties(goodsDetail, goodsDetailModel);
                            goodsEsDetailVo.setGoodsDetailModel(goodsDetailModel);
                        }
                    }
                    if (goodsEsDetailVo.getGoodsExtDetail() == null) {
                        GoodsExtDetail goodsExtDetail = goodsExtDetailService.queryGoodsExtDetailByGoodsId(goodsId);
                        if (goodsExtDetail != null) {
                            GoodsExtDetailModel goodsExtDetailModel = new GoodsExtDetailModel();
                            BeanUtils.copyProperties(goodsExtDetail, goodsExtDetailModel);
                            goodsEsDetailVo.setGoodsExtDetail(goodsExtDetailModel);
                        }
                    }
                }
                return goodsEsDetailVo;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查询商品查询结果带sku信息
     *
     * @param goodsDTO
     * @return
     */
    @Override
    public List<GoodsInfoItemDTO> queryGoodsBySkuItem(GoodsDTO goodsDTO) {
        try {
            BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
            if (goodsDTO.getIsDel() != null) {
                TermQueryBuilder isDel = QueryBuilders.termQuery("isDel", goodsDTO.getIsDel());
                boolBuilder.must(isDel);
            }

            if (goodsDTO.getGoodsId() != null) {
                TermQueryBuilder id = QueryBuilders.termQuery("id", goodsDTO.getGoodsId());
                boolBuilder.must(id);
            }

            if (CollectionUtils.isNotEmpty(goodsDTO.getGoodsIds())) {
                TermsQueryBuilder id = QueryBuilders.termsQuery("id", goodsDTO.getGoodsIds());
                boolBuilder.must(id);
            }

            if (goodsDTO.getStatus() != null) {
                TermQueryBuilder isShow1 = QueryBuilders.termQuery("status", goodsDTO.getStatus());
                boolBuilder.must(isShow1);
            }

            if (StringUtils.isNotBlank(goodsDTO.getIsShow())) {
                TermQueryBuilder isShow1 = QueryBuilders.termQuery("isShow", goodsDTO.getIsShow());
                boolBuilder.must(isShow1);
            }

            if (CollectionUtils.isNotEmpty(goodsDTO.getCategoryIds())) {
                TermsQueryBuilder isShow1 = QueryBuilders.termsQuery("categoryId", goodsDTO.getCategoryIds());
                boolBuilder.must(isShow1);
            }


            if (StringUtils.isNotBlank(goodsDTO.getName())) {
                MatchQueryBuilder name = QueryBuilders.matchQuery("name", goodsDTO.getName()).analyzer("ik_smart");
                boolBuilder.must(name);
            }


            if (StringUtils.isNotBlank(goodsDTO.getChannel())) {
                TermQueryBuilder channel = QueryBuilders.termQuery("channel", goodsDTO.getChannel());
                boolBuilder.must(channel);
            }

            if (goodsDTO.getUpdateType() != null) {
                TermQueryBuilder shopName = QueryBuilders.termQuery("updateType", goodsDTO.getUpdateType());
                boolBuilder.must(shopName);
            }

            if (StringUtils.isNotEmpty(goodsDTO.getStartTime()) && StringUtils.isNotEmpty(goodsDTO.getEndTime())) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("createTime");
                rangeQueryBuilder.format(DateUtil.newFormat);
                rangeQueryBuilder.from(goodsDTO.getStartTime());
                rangeQueryBuilder.to(goodsDTO.getEndTime());
                boolBuilder.must(rangeQueryBuilder);
            }

            if (Objects.nonNull(goodsDTO.getStartMinPrice())) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("minPrice");
                rangeQueryBuilder.from(goodsDTO.getStartMinPrice());
                boolBuilder.must(rangeQueryBuilder);
            }

            if (Objects.nonNull(goodsDTO.getEndMinPrice())) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("minPrice");
                rangeQueryBuilder.to(goodsDTO.getEndMinPrice());
                boolBuilder.must(rangeQueryBuilder);
            }

            if (Objects.nonNull(goodsDTO.getStartMaxPrice())) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("maxPrice");
                rangeQueryBuilder.from(goodsDTO.getStartMaxPrice());
                boolBuilder.must(rangeQueryBuilder);
            }

            if (Objects.nonNull(goodsDTO.getEndMaxPrice())) {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("maxPrice");
                rangeQueryBuilder.to(goodsDTO.getEndMaxPrice());
                boolBuilder.must(rangeQueryBuilder);
            }

//            if (Objects.nonNull(goodsDTO.getStartMinGrouponPrice())) {
//                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("minGrouponPrice");
//                rangeQueryBuilder.from(goodsDTO.getStartMinGrouponPrice());
//                boolBuilder.must(rangeQueryBuilder);
//            }
//
//            if (Objects.nonNull(goodsDTO.getEndMinGrouponPrice())) {
//                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("minGrouponPrice");
//                rangeQueryBuilder.to(goodsDTO.getEndMinGrouponPrice());
//                boolBuilder.must(rangeQueryBuilder);
//            }
//
//            if (Objects.nonNull(goodsDTO.getStartMaxGrouponPrice())) {
//                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("maxGrouponPrice");
//                rangeQueryBuilder.from(goodsDTO.getStartMaxGrouponPrice());
//                boolBuilder.must(rangeQueryBuilder);
//            }
//
//            if (Objects.nonNull(goodsDTO.getEndMaxGrouponPrice())) {
//                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("maxGrouponPrice");
//                rangeQueryBuilder.to(goodsDTO.getEndMaxGrouponPrice());
//                boolBuilder.must(rangeQueryBuilder);
//            }


            if (goodsDTO.getIsLock() != null) {
                TermQueryBuilder isLock = QueryBuilders.termQuery("isLock", goodsDTO.getIsLock());
                boolBuilder.must(isLock);
            }


            if (goodsDTO.getShopId() != null) {
                TermQueryBuilder shopId = QueryBuilders.termQuery("shopId", goodsDTO.getShopId());
                boolBuilder.must(shopId);
            }

            if (StringUtils.isNotBlank(goodsDTO.getStoreName())) {
                MatchQueryBuilder shopId = QueryBuilders.matchQuery("shopName", goodsDTO.getStoreName());
                boolBuilder.must(shopId);
            }

            if (StringUtils.isNotBlank(goodsDTO.getTag())) {
                MatchQueryBuilder tag = QueryBuilders.matchQuery("tag", goodsDTO.getTag());
                boolBuilder.must(tag);
            }

            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));

            sourceBuilder.query(boolBuilder);
            sourceBuilder.from((goodsDTO.getPageNow() - 1) * goodsDTO.getPageSize());
            sourceBuilder.size(goodsDTO.getPageSize());
            sourceBuilder.fetchSource(new String[]{"id", "name", "isShow", "isDel", "isLock", "createTime", "mainImage", "categoryId", "country", "tag",
                    "minPrice", "maxPrice", "minMarketPrice", "maxMarketPrice", "minGrouponPrice", "maxGrouponPrice", "sortValue", "shopId", "shopName", "itemList"}, null);


            log.info("queryGoodsList sourceBuilder:{}", sourceBuilder);
            SearchRequest searchRequest = new SearchRequest();
            searchRequest.indices(EsEnums.GOODS_ES.getIndex());
            searchRequest.source(sourceBuilder);


            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, builder());

            List<GoodsInfoItemDTO> userDataList = new ArrayList<>();
            SearchHits searchHits = searchResponse.getHits();
            if (null != searchHits) {
                SearchHit[] searchHitsArr = searchHits.getHits();
                if (null != searchHitsArr && searchHitsArr.length > 0) {
                    searchHits.forEach((hit) -> {
                        GoodsInfoItemDTO tmp = JSON.parseObject(hit.getSourceAsString(), GoodsInfoItemDTO.class);
                        userDataList.add(tmp);
                    });
                }
            }

            return userDataList;
        } catch (Exception var8) {
            var8.printStackTrace();
        }
        return new ArrayList<>();
    }


    @Override
    public Long countGoodsByShopId(Long shopId) {
        if (shopId == null) {
            return 0L;
        }
        try {
            BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
            TermQueryBuilder isShow = QueryBuilders.termQuery("isShow", "1");
            TermQueryBuilder isDel = QueryBuilders.termQuery("isDel", 0);
            boolBuilder.must(isShow);
            boolBuilder.must(isDel);
            TermQueryBuilder shopQuery = QueryBuilders.termQuery("shopId", shopId);
            boolBuilder.must(shopQuery);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolBuilder);
            CountRequest countRequest = new CountRequest();
            countRequest.indices(EsEnums.GOODS_ES.getIndex()).source(searchSourceBuilder);
            CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
            return countResponse.getCount();
        } catch (Exception var8) {
            var8.printStackTrace();
        }
        return 0L;
    }

    @Override
    public void addKeyWord(String index, String type, List<CategoryWord> categoryWords) {
        /**
         * 写入数据
         */
        try {
            BulkRequest request = new BulkRequest();
            categoryWords.stream().forEach(categoryWord -> {
                request.add(new IndexRequest(index, type, UUID.randomUUID().toString().replace("-", ""))
                        .opType(DocWriteRequest.OpType.INDEX).source(JSON.toJSONString(categoryWord), XContentType.JSON));
            });
            request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
            restHighLevelClient.bulk(request, RequestOptions.DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void addKeyWord2(String index, String type, List<ForBidWord> forBidWordList) {
        /**
         * 写入数据
         */
        log.info("禁售词入库");
        try {
            BulkRequest request = new BulkRequest();
            forBidWordList.stream().forEach(forBidWord -> {
                request.add(new IndexRequest(index, type, UUID.randomUUID().toString().replace("-", ""))
                        .opType(DocWriteRequest.OpType.INDEX).source(JSON.toJSONString(forBidWord), XContentType.JSON));
            });
            restHighLevelClient.bulkAsync(request, RequestOptions.DEFAULT, new ActionListener() {

                @Override
                public void onResponse(Object o) {
                    log.info("success");
                }

                @Override
                public void onFailure(Exception e) {
                    log.info("fail:{}", e);
                }
            });
            log.info("禁售词入库end");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void addKeyWord3(String index, String type, List<LimitWord> limitWords) {
        /**
         * 写入数据
         */
        try {
            BulkRequest request = new BulkRequest();
            limitWords.stream().forEach(limitWord -> {
                request.add(new IndexRequest(index, type, UUID.randomUUID().toString().replace("-", ""))
                        .opType(DocWriteRequest.OpType.INDEX).source(JSON.toJSONString(limitWord), XContentType.JSON));
            });
            request.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
            restHighLevelClient.bulk(request, RequestOptions.DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void dealGoods(List<String> categoryIds) throws IOException {
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        TermsQueryBuilder categoryId = QueryBuilders.termsQuery("categoryId", categoryIds);
        TermQueryBuilder isShow = QueryBuilders.termQuery("isShow", "1");
        TermQueryBuilder isDel = QueryBuilders.termQuery("isDel", 0);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        boolBuilder.must(categoryId);
        boolBuilder.must(isShow);
        boolBuilder.must(isDel);
        sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
        sourceBuilder.query(boolBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(100);
        sourceBuilder.fetchSource(new String[]{"propertyList", "id", "shopId", "name", "categoryId"}, null);
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(EsEnums.GOODS_ES.getIndex());
        searchRequest.source(sourceBuilder);
        goodsScrollSearchAll(restHighLevelClient, searchRequest);
    }


    public void goodsScrollSearchAll(RestHighLevelClient restHighLevelClient, SearchRequest searchRequest) throws IOException {
        int dealCount = 0;
        Set<Long> sets = new HashSet<>();
        Scroll scroll = new Scroll(timeValueMillis(1));
        searchRequest.scroll(scroll);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        String scrollId = searchResponse.getScrollId();
        SearchHit[] hits = searchResponse.getHits().getHits();
        while (ArrayUtils.isNotEmpty(hits)) {
            for (SearchHit hit : hits) {
                try {
                    String res = hit.getSourceAsString();
                    TongDunGoodsImages tongDunGoodsImages = new TongDunGoodsImages();
                    StringBuffer sb = new StringBuffer();

                    if (StringUtils.isNotEmpty(res)) {
                        GoodsCategoryIds tmp = JSON.parseObject(res, GoodsCategoryIds.class);
                        tongDunGoodsImages.setGoodsId(tmp.getId());
                        tongDunGoodsImages.setShopId(tmp.getShopId());
                        tongDunGoodsImages.setGoodsName(tmp.getName());
                        tongDunGoodsImages.setCategoryId(tmp.getCategoryId());
                        tongDunGoodsImages.setIsType(0L);
                        tongDunGoodsImages.setTdType(99);
                        tongDunGoodsImages.setCreateTime(LocalDateTime.now());
                        tongDunGoodsImages.setInitTime(LocalDateTime.now());


                        //处理调用接口
                        List<PropertyNewModel> propertyNewModelList = tmp.getPropertyList();
                        if (CollectionUtils.isNotEmpty(propertyNewModelList)) {
                            propertyNewModelList.stream().forEach(propertyNewModel -> {
                                int count = 0;
                                List<PropertyValueNewModel> propertyValueNewModels = propertyNewModel.getPropertyValueESModelList();
                                if (CollectionUtils.isNotEmpty(propertyValueNewModels)) {
                                    for (PropertyValueNewModel propertyValueNewModel : propertyValueNewModels) {
                                        if (count > 9) {
                                            sb.deleteCharAt(sb.length() - 1);
                                            break;
                                        }
                                        if (StringUtils.isNotEmpty(propertyValueNewModel.getImgUrl())) {
                                            sb.append(propertyValueNewModel.getImgUrl()).append(",");
                                            count++;
                                        }
                                    }
                                }
                            });
                        }
                        if (!sb.toString().isEmpty() && ",".equals(sb.substring(sb.length() - 1, sb.length()))) {
                            sb.deleteCharAt(sb.length() - 1);
                        }
                        if (!sb.toString().isEmpty()) {
                            log.info("处理数量:{},图片长度:{}", ++dealCount, sb.length());
                            if (!sets.contains(tongDunGoodsImages.getId())) {
                                tongDunGoodsImages.setGoodsImage(sb.toString());

                                Long categoryId = tongDunGoodsImages.getCategoryId();
                                Object categoryName = redisApi.get(TONG_DUN_CATEGORY_REDIS + categoryId);
                                if (null == categoryName) {
                                    List<Category> categoryList = categoryService.queryAll();
                                    for (Category category : categoryList) {
                                        redisApi.set(TONG_DUN_CATEGORY_REDIS + category.getId(), category.getName());
                                    }
                                    categoryName = redisApi.get(TONG_DUN_CATEGORY_REDIS + categoryId);
                                }
                                tongDunGoodsImages.setCategoryName((String) categoryName);
                                tongDunGoodsImageCoreService.saveData(tongDunGoodsImages);
                                sets.add(tmp.getId());
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            try {
                SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
                searchScrollRequest.scroll(scroll);
                SearchResponse searchScrollResponse = restHighLevelClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
                scrollId = searchScrollResponse.getScrollId();
                hits = searchScrollResponse.getHits().getHits();
            } catch (org.opensearch.OpenSearchStatusException e) {
                log.error("openSearch{}", e.getMessage());
                break;

            } catch (Exception e) {
                log.error("searchScroll exception", e);
            }
        }
    }



    @Override
    public List<GoodsFilterVONew> filterGoods(Long shopId, List<Long> goodIds) {
        List<GoodsFilterVONew> goodsFilterVOList = new ArrayList<>();
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        if (shopId != -1) {
            TermQueryBuilder termQuery = QueryBuilders.termQuery("shopId", shopId);
            boolBuilder.must(termQuery);
        }

        if (CollectionUtils.isNotEmpty(goodIds)) {
            TermsQueryBuilder goodsIds = QueryBuilders.termsQuery("id", goodIds);
            boolBuilder.must(goodsIds);
        }

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
        sourceBuilder.query(boolBuilder);
        sourceBuilder.fetchSource(new String[]{"id", "shopId", "shopName", "name"}, null);
        log.info("queryGoodsList sourceBuilder:{}", sourceBuilder);
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(EsEnums.GOODS_ES.getIndex());
        searchRequest.source(sourceBuilder);
        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, builder());
            SearchHits searchHits = searchResponse.getHits();
            if (null != searchHits) {
                SearchHit[] searchHitsArr = searchHits.getHits();
                if (null != searchHitsArr && searchHitsArr.length > 0) {
                    searchHits.forEach((hit) -> {
                        GoodsFilterVONew tmp = JSON.parseObject(hit.getSourceAsString(), GoodsFilterVONew.class);
                        goodsFilterVOList.add(tmp);
                    });
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return goodsFilterVOList;
    }

    @Override
    public List<GoodsFilterVONew> shopIdFilterGoods(Long shopId) {
        List<GoodsFilterVONew> goodsFilterVOList = new ArrayList<>();
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        TermQueryBuilder isShow = QueryBuilders.termQuery("isShow", "1");
        TermQueryBuilder isDel = QueryBuilders.termQuery("isDel", 0);
        TermQueryBuilder termQuery = QueryBuilders.termQuery("shopId", shopId);
        boolBuilder.must(termQuery);
        boolBuilder.must(isShow);
        boolBuilder.must(isDel);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
        sourceBuilder.query(boolBuilder);
        sourceBuilder.from(0);
        sourceBuilder.size(1000);
        sourceBuilder.fetchSource(new String[]{"id", "shopId", "shopName", "name"}, null);
        log.info("queryGoodsList sourceBuilder:{}", sourceBuilder);
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.indices(EsEnums.GOODS_ES.getIndex());
        searchRequest.source(sourceBuilder);
        try {
            syncGoodsScrollSearchAll(restHighLevelClient, searchRequest, EsEnums.GOODS_ES.getIndex(), goodsFilterVOList);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return goodsFilterVOList;
    }


    public void syncGoodsScrollSearchAll(RestHighLevelClient restHighLevelClient, SearchRequest searchRequest, String index, List<GoodsFilterVONew> goodsFilterVOList) throws IOException {
        Scroll scroll = new Scroll(timeValueMillis(1));
        searchRequest.scroll(scroll);
        SearchResponse searchResponse = restHighLevelClient.search(searchRequest, builder());
        String scrollId = searchResponse.getScrollId();
        SearchHit[] hits = searchResponse.getHits().getHits();
        Long lastId = 0l;
        boolean oom = false;
        while (ArrayUtils.isNotEmpty(hits)) {
            for (SearchHit hit : hits) {
                try {
                    Map<String, Object> source = hit.getSourceAsMap();
                    Object id = source.get("id");
                    if (id == null) {
                        continue;
                    }
                    lastId = Long.parseLong(String.valueOf(id));
                    GoodsFilterVONew tmp = JSON.parseObject(hit.getSourceAsString(), GoodsFilterVONew.class);
                    goodsFilterVOList.add(tmp);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            try {
                SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
                searchScrollRequest.scroll(scroll);
                SearchResponse searchScrollResponse = restHighLevelClient.scroll(searchScrollRequest, builder());
                scrollId = searchScrollResponse.getScrollId();
                hits = searchScrollResponse.getHits().getHits();
            } catch (org.opensearch.OpenSearchStatusException e) {
                log.error("openSearch{}", e.getMessage());
                oom = true;
                break;

            } catch (Exception e) {
                log.error("searchScroll exception", e);
            }
        }

        if (oom) {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.rangeQuery("id").gt(lastId));
            SearchRequest searchRequest2 = new SearchRequest(index)
                    .source(new SearchSourceBuilder().query(queryBuilder).size(searchRequest.source().size()));
            syncGoodsScrollSearchAll(restHighLevelClient, searchRequest2, index, goodsFilterVOList);
        }


    }

    public void queryNoMinPriceGoods() throws IOException {
        log.info("queryNoMinPriceGoods start !!!!!!");
        Scroll scroll = new Scroll(TimeValue.timeValueMinutes(1L));
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.existsQuery("minprice.IT"));
//        NestedQueryBuilder nestedQueryBuilder = QueryBuilders.nestedQuery("itemList", boolQueryBuilder, ScoreMode.None);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(200);
        searchSourceBuilder.fetchSource("id", null);
        SearchRequest firstSearchRequest = new SearchRequest(EsEnums.GOODS_ES.getIndex())
                .scroll(scroll)
                .searchType(SearchType.DEFAULT)
                .source(searchSourceBuilder);

        SearchResponse searchResponse = restHighLevelClient.search(firstSearchRequest, builder());
//        log.info("searchResponse:{}", searchResponse);
        SearchHit[] hits = searchResponse.getHits().getHits();
//        log.info("searchResponse hits size:{}", hits.length);
        String scrollId = searchResponse.getScrollId();

        int batch = 1;
        while (org.apache.commons.lang3.ArrayUtils.isNotEmpty(hits)) {
            log.info("queryNoMinPriceGoods batch:{}", batch);
            List<Long> goodsIds = Lists.newArrayList();
            for (SearchHit hit : hits) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                if (sourceAsMap != null && sourceAsMap.get("id") != null) {
                    Object goodsId = sourceAsMap.get("id");
                    goodsIds.add(Long.parseLong(goodsId.toString()));
                }
            }

            try {
                FileUtils.writeLines(new File("/data/ROOT/onlest/voghion-product-admin/needSyncGoodsIds.txt"), goodsIds, true);
            } catch (IOException e) {
                log.info("queryNoMinPriceGoods 文件写入错误", e);
            }

            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(scroll);
            SearchResponse scrollResponse = restHighLevelClient.scroll(scrollRequest, builder());
            hits = scrollResponse.getHits().getHits();
            batch++;
        }
    }

    @Override
    public Map<Long, String> queryGoodsShowStatus(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Maps.newHashMap();
        }

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<Long, String> goodsShowMap = Maps.newHashMap();
        MultiGetRequest request = new MultiGetRequest();
        goodsIds.forEach(goodId -> request.add(EsEnums.GOODS_ES.getIndex(), goodId.toString()));
        MultiGetItemResponse[] multiGetItemResponses;
        try {
            multiGetItemResponses = restHighLevelClient.mget(request, builder()).getResponses();
            if (multiGetItemResponses != null && multiGetItemResponses.length > 0) {
                for (MultiGetItemResponse response : multiGetItemResponses) {
                    String jsonResponseStr = response.getResponse().getSourceAsString();
                    JSONObject jsonObject = JSON.parseObject(jsonResponseStr);
                    if (jsonObject == null) {
                        continue;
                    }
                    Long id = jsonObject.getLong("id");
                    String isShow = jsonObject.getString("isShow");
                    goodsShowMap.put(id, isShow);
                }
            }
        } catch (Exception e) {
            log.error("查询商品在架状态error", e);
        }
        stopWatch.stop();
        log.info("goodsShowMap:{}, 耗时:{}ms", goodsShowMap, stopWatch.getTime());
        return goodsShowMap;
    }

    @Override
    public List<GoodsESModelSimpleVo> querySimpleGoodsByGoodsIdAndCountry(RecommentGoodsQueryDTO recommentGoodsQueryDTO) {
        List<GoodsESModelSimpleVo> simpleGoodsList = new ArrayList<>();
        RecommentGoodsListESModelDTO recommentGoodsListESModelDTO = new RecommentGoodsListESModelDTO();
        recommentGoodsListESModelDTO.setGoodsId(recommentGoodsQueryDTO.getGoodsIdList());
        recommentGoodsListESModelDTO.setCountryName(recommentGoodsQueryDTO.getCountry());
        recommentGoodsListESModelDTO.setPageNow(1);
        recommentGoodsListESModelDTO.setPageSize(400);
        List<GoodsESModelVo> goodsESModelVos = queryGoodsByGoodsIdAndCountry(recommentGoodsListESModelDTO);
        if(CollectionUtils.isNotEmpty(goodsESModelVos)){
            CountryCurDTO countryCurDTO = getCountryCurrenyInfoByCache(recommentGoodsQueryDTO.getCountry());
            List<String> goodsNameList = goodsESModelVos.stream().map(GoodsESModelVo::getName).collect(Collectors.toList());
            Map<String, String> translateResultMap = languageTranslate(goodsNameList, recommentGoodsQueryDTO.getLanguage());
            goodsESModelVos.stream().forEach(item -> {
                GoodsESModelSimpleVo goodsESModelSimpleVo = new GoodsESModelSimpleVo();
                goodsESModelSimpleVo.setGoodsId(item.getId());
                goodsESModelSimpleVo.setMainImage(item.getMainImage());
                goodsESModelSimpleVo.setMinPrice(currencyCovert(item.getMinPrice(), countryCurDTO));
                goodsESModelSimpleVo.setMinMarketPrice(currencyCovert(item.getMinMarketPrice(), countryCurDTO));
                String translatedName = translateResultMap.get(item.getName());
                if(StringUtils.isNotBlank(translatedName)){
                    goodsESModelSimpleVo.setGoodsName(translatedName);
                }else {
                    goodsESModelSimpleVo.setGoodsName(item.getName());
                }
                goodsESModelSimpleVo.setSales(item.getSales());
                if(Objects.nonNull(item.getListGoodsCommentModel())){
                    goodsESModelSimpleVo.setCommentAverage(item.getListGoodsCommentModel().getCommentAverage());
                }else {
                    goodsESModelSimpleVo.setCommentAverage(0d);
                }
                if(Objects.nonNull(countryCurDTO)){
                    goodsESModelSimpleVo.setCurrencyMark(countryCurDTO.getCurrency());
                    goodsESModelSimpleVo.setPriceRuler(countryCurDTO.getPriceRuler());
                }
                simpleGoodsList.add(goodsESModelSimpleVo);
            });
        }
        log.info("querySimpleGoodsByGoodsIdAndCountry result:{}", JSON.toJSONString(simpleGoodsList));
        return simpleGoodsList;
    }

    /**
     * 获取国家对应的默认货币类型
     * @param country
     * @return
     */
    private CountryCurDTO getCountryCurrenyInfoByCache(String country) {
        CountryCurDTO currencyInfo;
        Map<Object, Object> countryCurrenyMap = redisApi.hmget(COUNTRY_CURRENCY_MAPPING);
        if (MapUtils.isNotEmpty(countryCurrenyMap)) {
            Object currenyCodeObj = countryCurrenyMap.get(country);
            if(Objects.nonNull(currenyCodeObj)){
                currencyInfo = JSON.parseObject(currenyCodeObj.toString(), CountryCurDTO.class);
            }else{
                currencyInfo = queryCountryCurrencyInfo(country);
            }
        }else{
            currencyInfo = queryCountryCurrencyInfo(country);
        }
        return currencyInfo;
    }

    /**
     * 获取国家货币信息
     * @param country
     * @return
     */
    private CountryCurDTO queryCountryCurrencyInfo(String country) {
        log.info("queryCountryCurrencyInfo country:{}", country);
        AppCountryCurLangVO appCountryCurLangVO = new AppCountryCurLangVO();
        appCountryCurLangVO.setCountryCode(country);
        List<CountryCurDTO> countryCurDTOS = countryCurLangClientFactory.appQueryCountryCurList(appCountryCurLangVO);
        if(CollectionUtils.isNotEmpty(countryCurDTOS)){
            Optional<CountryCurDTO> first = countryCurDTOS.stream().filter(item -> Objects.nonNull(item.getIsDefault())).filter(CountryCurDTO::getIsDefault).findFirst();
            if(first.isPresent()){
                CountryCurDTO countryCurDTO = first.get();
                redisApi.hset(COUNTRY_CURRENCY_MAPPING, country, JSON.toJSONString(countryCurDTO), 3600 * 24);
                return countryCurDTO;
            }
        }
        return null;
    }


    /**
     * 货币转换价格
     * @param orginalPrice
     * @param countryCurDTO
     * @return
     */
    private BigDecimal currencyCovert(BigDecimal orginalPrice, CountryCurDTO countryCurDTO) {
        log.info("querySimpleGoodsByGoodsIdAndCountry currencyCovert orginalPrice:{},CountryCurDTO:{}", orginalPrice,JSON.toJSONString(countryCurDTO));
        if(Objects.isNull(countryCurDTO)){
            return orginalPrice;
        }
        BigDecimal currencyRate = new BigDecimal(1);
        Object cacheRate = redisApi.get(SystemConstants.CURRENYTAG + countryCurDTO.getCurrencyCode());
        if (Objects.nonNull(cacheRate)) {
            currencyRate = new BigDecimal(cacheRate.toString()).setScale(6, RoundingMode.HALF_UP);
            ThreadContext.putContext(SystemConstants.CURRENYTAG + countryCurDTO.getCurrencyCode(), currencyRate);
        }
        //默认只支持欧元为标准
        if (null != orginalPrice && orginalPrice.compareTo(BigDecimal.ZERO) > 0
                && null != currencyRate && currencyRate.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal resultAmount = orginalPrice.multiply(currencyRate).setScale(2, RoundingMode.HALF_UP);
            log.info("原价格:{},当前汇率:{},转换后价格:{}", orginalPrice, currencyRate, resultAmount);
            return resultAmount;
        }
        return orginalPrice;
    }

    private Map<String, String> languageTranslate(List<String> contentList, String language) {
        if (CollectionUtils.isNotEmpty(contentList)) {
            log.info("languageTranslate contentList:{},language:{}", contentList, language);
            HashMap<String, String> translateResultMap = translateClientFactory.batchTranslate(contentList, AwsLang.EN, AwsLang.getLangByKey(language));
            log.info("languageTranslate result:{}", JSON.toJSONString(translateResultMap));
            return translateResultMap;
        }
        return new HashMap<>();
    }
}
