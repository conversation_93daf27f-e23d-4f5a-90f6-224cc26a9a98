package com.voghion.product.remote.impl;

import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.GoodsTagDTO;
import com.voghion.product.api.dto.ShopTagDTO;
import com.voghion.product.api.input.ShopTagInput;
import com.voghion.product.api.service.GoodsTagRemoteService;
import com.voghion.product.core.GoodsTagCoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * GoodsTagRemoteServiceImpl
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
@Service
@Slf4j
public class GoodsTagRemoteServiceImpl implements GoodsTagRemoteService {
    @Resource
    private GoodsTagCoreService goodsTagCoreService;

    @Override
    public Result<GoodsTagDTO> queryGoodsTagByTagId(Long tagId) {
        GoodsTagDTO goodsTagDTO = goodsTagCoreService.getGoodsTagInfoById(tagId);
        return Result.success(goodsTagDTO);
    }

    @Override
    public Result<List<GoodsTagDTO>> queryGoodsTagByTagIds(List<Long> tagIds) {
        List<GoodsTagDTO> goodsTagList = goodsTagCoreService.getGoodsTagInfoByIds(tagIds);
        return Result.success(goodsTagList);
    }

    @Override
    public Result<List<GoodsTagDTO>> queryGoodsTags() {
        List<GoodsTagDTO> goodsTagList = goodsTagCoreService.queryGoodsTags();
        return Result.success(goodsTagList);
    }

    @Override
    public Result<List<ShopTagDTO>> queryShopTagsInput(ShopTagInput shopTagInput) {
        List<ShopTagDTO> shopTagDTOS =  goodsTagCoreService.queryShopTagsByInput(shopTagInput);
        return Result.success(shopTagDTOS);
    }
}
