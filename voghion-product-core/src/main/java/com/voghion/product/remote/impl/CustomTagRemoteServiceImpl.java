package com.voghion.product.remote.impl;

import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.CustomTagInfoDTO;
import com.voghion.product.api.service.CustomTagRemoteService;
import com.voghion.product.core.CustomTagCoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
@Slf4j
public class CustomTagRemoteServiceImpl implements CustomTagRemoteService {

    @Resource
    private CustomTagCoreService customTagCoreService;

    @Override
    public Result<Boolean> refreshGoodsTagRelNum() {
        return Result.success(customTagCoreService.refreshGoodsTagRelNum());
    }

    @Override
    public Result<List<CustomTagInfoDTO>> listCustomTagInfo() {
        return Result.success(customTagCoreService.listCustomTagInfo());
    }

    @Override
    public Result<List<CustomTagInfoDTO>> queryCustomTagByGoodsId(Long goodsId) {
        return Result.success(customTagCoreService.queryCustomTagByGoodsId(goodsId));
    }
}
