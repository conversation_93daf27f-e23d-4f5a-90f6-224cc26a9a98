package com.voghion.product.remote.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.lang.exception.CustomException;
import com.colorlight.base.model.BaseResultCode;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.colorlight.base.model.constants.SystemConstants;
import com.colorlight.base.utils.CheckUtils;
import com.colorlight.base.utils.TransferUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.onlest.GoodsSyncModel;
import com.voghion.boot.common.enums.CurrencyCodeEnum;
import com.voghion.product.api.dto.*;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.api.input.*;
import com.voghion.product.api.output.*;
import com.voghion.product.api.output.GoodsFreightVO;
import com.voghion.product.api.service.GoodsRemoteService;
import com.voghion.product.core.*;
import com.voghion.product.helper.BeanConversionHelper;
import com.voghion.product.helper.PropertyHelper;
import com.voghion.product.listener.GoodsFreightImportVO;
import com.voghion.product.listener.GoodsImportVO;
import com.voghion.product.listener.GoodsSkuImportVO;
import com.voghion.product.listener.GoodsStockImportVO;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.dto.PropertyDTO;
import com.voghion.product.model.dto.PropertyValueDTO;
import com.voghion.product.model.enums.CustomResultCode;
import com.voghion.product.model.enums.ProductResultCode;
import com.voghion.product.model.po.*;
import com.voghion.product.model.po.goods.PropertyAssociationPo;
import com.voghion.product.model.vo.*;
import com.voghion.product.model.vo.GoodsInfoVO;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.util.AssertsUtils;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.util.DateUtils;
import com.voghion.product.util.LogUtils;
import com.voghion.product.utils.CommonConstants;
import com.voghion.product.utils.GoodsDiscountLabelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2021/5/17 15:31
 * @describe
 */
@Service
@Slf4j
public class GoodsRemoteServiceImpl implements GoodsRemoteService {

    @Resource
    private GoodsCoreService goodsCoreService;

    @Resource
    private NewAddGoodsCoreService newAddGoodsCoreService;

    @Resource
    private UpdateGoodsInfoCoreService updateGoodsInfoCoreService;

    @Resource
    private WarehouseMerchantsService warehouseMerchantsService;

    @Resource
    private CategoryTreeCoreService categoryTreeCoreService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private CategoryCoreService categoryCoreService;

    @Resource
    private GoodsFreightCoreService goodsFreightCoreService;

    @Resource
    private GoodsFreightService goodsFreightService;

    @Resource
    private GoodsService goodsService;

    @Autowired
    private GoodsBlacklistCoreService goodsBlacklistCoreService;

    @Resource
    private SensitiveWordsCoreService sensitiveWordsCoreService;

    @Resource
    private FaMerchantsApplyCoreService faMerchantsApplyCoreService;

    @Resource
    private MqSender mqSender;

    @Autowired
    private BuyOnePieceCoreService buyOnePieceCoreService;


    @Resource
    private ShopGoodsCoreService shopGoodsCoreService;

    @Resource
    private MerchantsBrandMarkService merchantsBrandMarkService;

    @Resource
    private FileSyncRecordService fileSyncRecordService;

    @Resource
    private FileSyncRecordDataService fileSyncRecordDataService;

    @Resource
    private CustomListCoreService customListCoreService;

    @Autowired
    private GoodsDraftCoreService goodsDraftCoreService;

    @Resource
    private SearchConfigService searchConfigService;

    @Resource
    private RedisApi redisApi;

    @Resource(name = "goodsInfoPool")
    private Executor execute;

    @Resource
    private WxBindShopCoreService wxBindShopCoreService;

    @Resource
    private HotGoodsMonitorCoreService hotGoodsMonitorCoreService;

    @Resource
    private GoodsItemService goodsItemService;

    @Resource
    private GoodsItemGradientService goodsItemGradientService;

    @Resource
    private ListingFollowGoodsService listingFollowGoodsService;

    @Resource
    private GoodsProhibitCoreService goodsProhibitCoreService;

    @Resource
    private GoodsExtDetailService goodsExtDetailService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private GoodsLogisticsCoreService goodsLogisticsCoreService;

    @Resource
    private ProductSaleExportCoreService productSaleExportCoreService;

    @Resource
    private PropertyHelper propertyHelper;

    /**
     * 根就商品ids查询所有商品
     *
     * @param ids
     * @return
     */
    @Override
    public Result<List<GoodsOutput>> queryGoodsByIds(List<Long> ids) {
        return Result.success(goodsCoreService.queryGoodsByIds(ids));
    }

    @Override
    public Result<List<GoodsOutput>> queryBackendGoodsByIds(List<Long> ids) {
        return Result.success(goodsCoreService.queryBackendGoodsByIds(ids));
    }

    /**
     * 根就商品ids查询所有商品
     *
     * @param ids
     * @param isValid true 有效的  过滤删除 有效状态  false 不过滤删除  有些状态  null 查询所有
     * @return
     */
    @Override
    public Result<List<GoodsOutput>> queryGoodsByIds(List<Long> ids, Boolean isValid) {
        return Result.success(goodsCoreService.queryGoodsByIds(ids, isValid));
    }

    /**
     * 增加销量
     *
     * @param goodsSales
     * @return
     */
    @Override
    public Result<Boolean> addGoodsSales(Map<Long, Integer> goodsSales) {
        return Result.success(goodsCoreService.updateGoodsSales(goodsSales));
    }

    @Override
    public Result<Long> addGoods(ProductInfoInput infoInput) {
        infoInput.setOperationLogType(getOperationLogType(infoInput.getOperationLogType()));

        infoInput.setBrandId(getBrandId(infoInput.getBrandId(), infoInput.getCategoryId(), infoInput.getShopId()));

        infoInput.setLogisticsProperty(getLogisticsProperty(infoInput.getOperationLogType(), infoInput.getLogisticsProperty(), infoInput.getCategoryId()));

        return Result.success(newAddGoodsCoreService.shopAddGoods(infoInput));
    }

    private Integer getLogisticsProperty(Integer operationLogType, Integer logisticsProperty, Long categoryId) {
        if (OperationLogTypeEnums.ERP_ADD_GOODS.getCode().equals(operationLogType) && logisticsProperty == null) {
            // erp物流属性兜底，不传物流属性时，取类目配置中一个
            List<LogisticsPropertyVo> logisticsPropertyVos = goodsLogisticsCoreService.queryByCategoryId(categoryId);
            if (CollectionUtils.isNotEmpty(logisticsPropertyVos)) {
                return logisticsPropertyVos.stream().findFirst().get().getCode();
            }
        }

        return logisticsProperty;
    }

    private Integer getOperationLogType(Integer operationLogType) {
        if (operationLogType != null) {
            return operationLogType;
        }

        return OperationLogTypeEnums.ERP_ADD_GOODS.getCode();
    }

    private Long getBrandId(Long brandId, Long categoryId, Long shopId) {
        // erp的brandId不填时先任取该店铺类目下的一个
        if (brandId != null && brandId != 0) {
            return brandId;
        }

        Long firstLevelCategory = getFirstLevelCategory(categoryId);
        List<Category> childCategories = categoryCoreService.queryAllByParentCategoryId(firstLevelCategory);
        if (CollectionUtils.isEmpty(childCategories)) {
            return null;
        }

        List<Long> childCategoryIds = childCategories.stream().map(Category::getId).collect(Collectors.toList());
        List<MerchantsBrandMark> merchantsBrandMarks = getMerchantsBrandMarks(shopId, childCategoryIds);
        if (CollectionUtils.isEmpty(merchantsBrandMarks)) {
            return null;
        }

        return merchantsBrandMarks.get(0).getMerchantsBrandStoreId();
    }

    private List<MerchantsBrandMark> getMerchantsBrandMarks(Long shopId, List<Long> childCategoryIds) {
        return merchantsBrandMarkService.lambdaQuery()
                .eq(MerchantsBrandMark::getShopId, shopId)
                .in(MerchantsBrandMark::getCategoryId, childCategoryIds)
                .eq(MerchantsBrandMark::getStatus, 10)
                .ge(MerchantsBrandMark::getExpireTime, new Date())
                .eq(MerchantsBrandMark::getIsDel, 0)
                .list();
    }

    private Long getFirstLevelCategory(Long categoryId) {
        Long firstLevelCategory = categoryId;
        Category category = categoryService.getById(categoryId);
        CheckUtils.notNull(category, ProductResultCode.CATEGORY_NOT_FIND);
        if (StringUtils.isNotBlank(category.getPids())) {
            firstLevelCategory = Long.parseLong(category.getPids().split(",")[0]);
        }

        return firstLevelCategory;
    }

    @Override
    public Result<Long> threePartiesAddGoodsInfo(ItemGoodsInfoRemoteInputDto dto) {
        dto.setOperationLogType(getOperationLogType(dto.getOperationLogType()));

        dto.setBrandId(getBrandId(dto.getBrandId(), dto.getCategoryId(), dto.getShopId()));

        dto.setLogisticsProperty(getLogisticsProperty(dto.getOperationLogType(), dto.getLogisticsProperty(), dto.getCategoryId()));

        ItemGoodsInfoInputDto transform = BeanConversionHelper.remoteInputDtoConversionItemGoodsInfo(dto);
        return Result.success(newAddGoodsCoreService.threePartiesAddGoodsInfo(transform));
    }

    @Override
    public Result<Long> queryGoodsCount(Long shopId) {
        return Result.success(goodsCoreService.queryGoodsCount(shopId));
    }

    @Override
    public Result<Boolean> updateGoods(ProductInfoInput infoInput) {
        if (infoInput.getOperationLogType() == null) {
            infoInput.setOperationLogType(OperationLogTypeEnums.ERP_UPDATE_GOODS.getCode());
        }

        fillProperties(infoInput);

        return Result.success(updateGoodsInfoCoreService.shopUpdateGoods(infoInput));
    }

    private void fillProperties(ProductInfoInput infoInput) {
        Map<String, Set<String>> map = Maps.newHashMap();
        infoInput.getSkuInfo().stream().filter(dto -> dto.getSkuId() != null).forEach(dto -> {
            dto.getSkuProperty().forEach((k, v) -> {
                Set<String> pv = map.get(k);
                if (CollectionUtils.isEmpty(pv)) {
                    pv = Sets.newHashSet();
                    map.put(k, pv);
                }

                pv.add(v);
            });
        });

        if (MapUtils.isEmpty(map)) {
            return;
        }

        Long goodsId = infoInput.getId();
        List<GoodsItem> goodsItems = goodsItemService.queryByGoodsId(goodsId);
        Map<String, Map<String, PropertyAssociationPo>> propertyAssociationMap = propertyHelper.getPropertyAssociationByGoodItem(goodsId, goodsItems);

        for (PropertyDTO property : infoInput.getProperties()) {
            Set<String> valueList = map.get(property.getName());
            if (CollectionUtils.isEmpty(valueList)) {
                continue;
            }

            Map<String, PropertyAssociationPo> associationPoMap = propertyAssociationMap.get(property.getName());
            if (MapUtils.isEmpty(associationPoMap)) {
                continue;
            }

            for (PropertyValueDTO value : property.getValues()) {
                if (!valueList.contains(value.getValue())) {
                    continue;
                }

                PropertyAssociationPo propertyAssociationPo = associationPoMap.get(value.getValue());
                if (propertyAssociationPo == null) {
                    continue;
                }

                value.setGoodsPropertyRelevantId(propertyAssociationPo.getPropertyRelevantId());
            }
        }
    }

    @Override
    public Result<Boolean> updateBkGoodsStatus(Long goodsId) {
        Goods goods = new Goods();
        goods.setIsBidPrice(1);
        goods.setId(goodsId);
        return Result.success(goodsService.updateById(goods));
    }

    @Override
    public Result<PageView<GoodsListVO>> queryList(QueryGoodsDTO queryGoodsDTO) {
        return Result.success(goodsCoreService.queryGoodsList(queryGoodsDTO));
    }

    @Override
    public Result<List<Long>> queryGoodsIdsByCategoryIdsPage(QueryGoodsDTO queryGoodsDTO) {
        return Result.success(goodsCoreService.queryGoodsIdsByCategoryIdsPage(queryGoodsDTO));
    }

    @Override
    public Result<GoodsDetailInfoVO> queryDetail(QueryGoodsDTO queryGoodsDTO) {
        return Result.success(goodsCoreService.queryGoodsDetailById(queryGoodsDTO.getGoodsId(), queryGoodsDTO.getShopId()));
    }

    @Override
    public Result<Boolean> updateLockBatchById(List<GoodsOutput> needLockList) {
        return Result.success(goodsCoreService.updateLockBatchById(needLockList));
    }

    @Override
    public Result<Boolean> updateTag(List<GoodsTagInfoDTO> goodsTagInfoDTOList) {
        return Result.success(goodsCoreService.updateTag(goodsTagInfoDTOList));
    }

    @Override
    public Result<Boolean> deleteTag(List<GoodsTagInfoDTO> goodsTagInfoDTOList) {
        return Result.success(goodsCoreService.deleteTag(goodsTagInfoDTOList));
    }

    @Override
    public Result<Boolean> addOrUpdateTagVO(GoodsTagUpdateInput goodsTagUpdateInput) {
        return Result.success(goodsCoreService.updateGoodsTag(goodsTagUpdateInput));
    }

    @Override
    public Result<Boolean> deleteTagVO(GoodsTagUpdateInput goodsTagUpdateInput) {
        return Result.success(goodsCoreService.deleteGoodsTag(goodsTagUpdateInput));
    }

    @Override
    public List<GoodsOutput> queryGoodsSevenByIds(List<Long> ids) {
        return goodsCoreService.queryGoodsByIds(ids);
    }

  /*  @Override
    public Boolean queryIsSevenDayGood(Long goodsId) {
        return  goodsCoreService.queryIsSevenDayGood(goodsId);
    }*/

    @Override
    public List<Long> queryGoodsIsSevenDay(List<Long> goodsIds) {
        return goodsCoreService.queryGoodsIsSevenDay(goodsIds);
    }

    @Override
    public Result<List<SpecialActGoodsDataDTO>> fillData(List<SpecialActGoodsDataDTO> datas) {
        List<Long> goodsIds = datas.stream().map(SpecialActGoodsDataDTO::getGoodsId).collect(Collectors.toList());
        List<GoodsOutput> goods = goodsCoreService.queryGoodsByIds(goodsIds, null);
        if (CollectionUtils.isEmpty(goods)) {
            return Result.success(datas);
        }
        List<Long> categoryIds = new ArrayList<>();
        List<Long> shopIds = new ArrayList<>();
        Map<Long, GoodsOutput> goodsMap = new HashMap<>();
        for (GoodsOutput gd : goods) {
            categoryIds.add(gd.getCategoryId());
            shopIds.add(gd.getShopId());
            goodsMap.put(gd.getId(), gd);
        }
        //查询完整类目路径
        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(categoryIds);
        Map<Long, WarehouseMerchants> map = new HashMap<>();
        WarehouseMerchantsVO vo = new WarehouseMerchantsVO();
        vo.setShopIds(shopIds);

        List<WarehouseMerchants> warehouseMerchants = warehouseMerchantsService.queryListByOptions(vo);
        if (CollectionUtils.isNotEmpty(warehouseMerchants)) {
            map = warehouseMerchants.stream().collect(Collectors.toMap(WarehouseMerchants::getShopId, item -> item, (v1, v2) -> v1));
        }

        List<GoodsFreight> goodsFreights = goodsFreightService.queryByGoodsIds(goodsIds);
        Map<Long, List<GoodsFreight>> freightMap = goodsFreights.stream().collect(Collectors.groupingBy(GoodsFreight::getGoodsId));

        for (SpecialActGoodsDataDTO dto : datas) {
            GoodsOutput goods1 = goodsMap.get(dto.getGoodsId());
            if (null == goods1) {
                continue;
            }
            Long shopId = goods1.getShopId();
            List<GoodsFreight> freights = freightMap.get(dto.getGoodsId());
            if (CollectionUtils.isNotEmpty(freights)) {
                freights.sort(Comparator.comparing(GoodsFreight::getCurrentFreight));
                dto.setFreightRange(freights.get(0).getCurrentFreight() + " ~ " + freights.get(freights.size() - 1).getCurrentFreight());
            }
            dto.setCategoryId(goods1.getCategoryId());

            dto.setGoodsName(goods1.getName());
            dto.setMainImg(goods1.getMainImage());
            dto.setCategoryTreeStr(categoryTreeMap.get(goods1.getCategoryId()));
            dto.setShopId(shopId);
            dto.setFakeDiscount(GoodsDiscountLabelUtil.calculateDiscount(goods1.getMinPrice(), goods1.getMinMarketPrice()));
            dto.setMinPrice(goods1.getMinPrice());
            dto.setMaxPrice(goods1.getMaxPrice());
            dto.setShopName(goods1.getShopName());
            dto.setMinStock(goods1.getMinStock());

            WarehouseMerchants warehouse = map.get(shopId);
            if (null != warehouse) {
                dto.setWarehouse(map.get(shopId).getWarehouseName());
            }
        }
        return Result.success(datas);
    }

    @Override
    public Result<List<SpecialActGoodsDataDTO>> fillDataBack(List<SpecialActGoodsDataDTO> datas) {
        List<Long> goodsIds = datas.stream().map(SpecialActGoodsDataDTO::getGoodsId).collect(Collectors.toList());
        List<GoodsOutput> goods = goodsCoreService.queryBackendGoodsByIds(goodsIds);
        if (CollectionUtils.isEmpty(goods)) {
            return Result.success(datas);
        }
        List<Long> categoryIds = new ArrayList<>();
        List<Long> shopIds = new ArrayList<>();
        Map<Long, GoodsOutput> goodsMap = new HashMap<>();
        for (GoodsOutput gd : goods) {
            categoryIds.add(gd.getCategoryId());
            shopIds.add(gd.getShopId());
            goodsMap.put(gd.getId(), gd);
        }
        //查询完整类目路径
        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(categoryIds);
        Map<Long, WarehouseMerchants> map = new HashMap<>();
        WarehouseMerchantsVO vo = new WarehouseMerchantsVO();
        vo.setShopIds(shopIds);

        List<WarehouseMerchants> warehouseMerchants = warehouseMerchantsService.queryListByOptions(vo);
        if (CollectionUtils.isNotEmpty(warehouseMerchants)) {
            map = warehouseMerchants.stream().collect(Collectors.toMap(WarehouseMerchants::getShopId, item -> item, (v1, v2) -> v1));
        }

        List<GoodsFreight> goodsFreights = goodsFreightService.queryByGoodsIds(goodsIds);
        Map<Long, List<GoodsFreight>> freightMap = goodsFreights.stream().collect(Collectors.groupingBy(GoodsFreight::getGoodsId));

        for (SpecialActGoodsDataDTO dto : datas) {
            GoodsOutput goods1 = goodsMap.get(dto.getGoodsId());
            if (null == goods1) {
                continue;
            }
            Long shopId = goods1.getShopId();
            List<GoodsFreight> freights = freightMap.get(dto.getGoodsId());
            if (CollectionUtils.isNotEmpty(freights)) {
                freights.sort(Comparator.comparing(GoodsFreight::getCurrentFreight));
                dto.setFreightRange(freights.get(0).getCurrentFreight() + " ~ " + freights.get(freights.size() - 1).getCurrentFreight());
            }
            dto.setCategoryId(goods1.getCategoryId());

            dto.setGoodsName(goods1.getName());
            dto.setMainImg(goods1.getMainImage());
            dto.setCategoryTreeStr(categoryTreeMap.get(goods1.getCategoryId()));
            dto.setShopId(shopId);
            dto.setShopName(goods1.getShopName());
            dto.setFakeDiscount(GoodsDiscountLabelUtil.calculateDiscount(goods1.getMinPrice(), goods1.getMinMarketPrice()));
            dto.setMinPrice(goods1.getMinPrice());
            dto.setMaxPrice(goods1.getMaxPrice());
            dto.setMinStock(goods1.getMinStock());
            dto.setShowStatus(Integer.parseInt(goods1.getIsShow()));

            WarehouseMerchants warehouse = map.get(shopId);
            if (null != warehouse) {
                dto.setWarehouse(map.get(shopId).getWarehouseName());
            }
        }
        return Result.success(datas);
    }

    @Override
    public Result<List<GoodsFieldVo>> queryGoodsFieldByIds(List<Long> goodsIds) {
        //todo 缓存机制
        List<GoodsOutput> goodsList = goodsCoreService.queryGoodsByIds(goodsIds, null);
        List<GoodsFieldVo> vos = goodsList.stream().map(GoodsFieldVo::new).collect(Collectors.toList());
        return Result.success(vos);
    }

    @Override
    public Result<List<GoodsOutput>> queryAllActivityGoodsInfoByGoodsId(List<Long> goodsIds) {
        log.info("queryAllActivityGoodsInfoByGoodsId goodsIds:{}", goodsIds);
        List<GoodsOutput> goodsOutputs = goodsCoreService.queryActivityGoodsByIds(goodsIds);
        return Result.success(goodsOutputs);
    }

    @Override
    public Result<Map<String, List<GoodsOutput>>> queryActivityGoodsInfoByGoodsId(StartingActivityGoodsQueryDto queryDto) {
        log.info("queryActivityGoodsInfoByGoodsId queryDto:{}", queryDto);
        HashMap<String, List<GoodsOutput>> map = new HashMap<>(2);
        if (CollectionUtils.isEmpty(queryDto.getStartingGoodsIds()) && CollectionUtils.isEmpty(queryDto.getUnStartedGoodsIds()) && CollectionUtils.isEmpty(queryDto.getRepeatSignGoodsIds())) {
            map.put("0", Collections.emptyList());
            map.put("1", Collections.emptyList());
            return Result.success(map);
        }

        if (CollectionUtils.isNotEmpty(queryDto.getStartingGoodsIds()) && queryDto.getStartingActivityId() != null) {
            //进行中活动商品
            List<GoodsOutput> goodsOutputs = goodsCoreService.queryActivityGoodsByIds(queryDto.getStartingGoodsIds());
            map.put("1", goodsOutputs);
        } else {
            map.put("1", Collections.emptyList());
        }

        if (CollectionUtils.isNotEmpty(queryDto.getUnStartedGoodsIds())) {
            //未开始或已结束的活动商品
            List<GoodsOutput> goodsOutputs = goodsCoreService.queryActivityGoodsByIds(queryDto.getUnStartedGoodsIds());
            map.put("0", goodsOutputs);
        } else {
            map.put("0", Lists.newArrayList());
        }

        if (CollectionUtils.isNotEmpty(queryDto.getRepeatSignGoodsIds()) && queryDto.getStartingActivityId() != null) {
            //当前非进行中活动但又同时参与了进行中活动的商品
            List<GoodsOutput> goodsOutputs = goodsCoreService.queryStartingActivityGoodsByIds(queryDto.getStartingActivityId(), queryDto.getRepeatSignGoodsIds());
            List<GoodsOutput> allUnStartingGoods = map.get("0");
            allUnStartingGoods.addAll(goodsOutputs);
            map.put("0", allUnStartingGoods);
        }
        return Result.success(map);
    }

//    @Override
//    public Result<GoodsItemDTO> queryItemPriceBySkuIdAndCountry(Long skuId, String country) {
//        if (Objects.isNull(skuId) && StringUtils.isBlank(country)) {
//            return Result.fail(ProductResultCode.PARAMETER_ERROR);
//        }
//        GoodsItem goodsItem = goodsCoreService.queryGoodsItemBySkuId(skuId);
//        if (Objects.isNull(goodsItem)) {
//            return Result.fail(ProductResultCode.GOODS_NOT_EXIST);
//        }
//        GoodsItemDTO dto = BeanCopyUtil.transform(goodsItem, GoodsItemDTO.class);
//        GoodsFreight goodsFreight = goodsFreightCoreService.queryFreightByGoodsIdAndCountry(goodsItem.getGoodsId(), country);
//        if (Objects.nonNull(goodsFreight)) {
//            dto.setPrice(dto.getOrginalPrice().add(goodsFreight.getCurrentFreight()));
//        }
//        dto.setBeforeVatPrice(dto.getPrice());
//        TaxVatDto taxVatDto = taxVatCoreService.queryVatByCountry(country);
//        if (Objects.nonNull(taxVatDto)) {
//            BigDecimal vatRate = BigDecimal.ONE.add(taxVatDto.getRate().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
//            dto.setPrice(dto.getPrice().multiply(vatRate));
//        }
//        return Result.success(dto);
//    }

//    @Override
//    public Result<Map<Long, GoodsItemDTO>> queryItemPriceBySkuIdsAndCountry(List<Long> skuIds, String country) {
//        if (CollectionUtils.isEmpty(skuIds) && StringUtils.isBlank(country)) {
//            return Result.fail(ProductResultCode.PARAMETER_ERROR);
//        }
//        List<GoodsItem> goodsItems = goodsCoreService.queryGoodsItemBySkuIds(skuIds);
//        if (CollectionUtils.isEmpty(goodsItems)) {
//            return Result.fail(ProductResultCode.GOODS_NOT_EXIST);
//        }
//        List<GoodsItemDTO> dtos = BeanCopyUtil.transformList(goodsItems, GoodsItemDTO.class);
//        Set<Long> goodsIds = dtos.stream().map(GoodsItemDTO::getGoodsId).collect(Collectors.toSet());
//        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIdsAndCountry(Lists.newArrayList(goodsIds), country);
//        Map<Long, BigDecimal> freightGroupGoodsIdMap = goodsFreights.stream().collect(Collectors.toMap(GoodsFreight::getGoodsId, GoodsFreight::getCurrentFreight, (a, b) -> b));
//        TaxVatDto taxVatDto = taxVatCoreService.queryVatByCountry(country);
//        BigDecimal vatRate = BigDecimal.ONE;
//        if (Objects.nonNull(taxVatDto)) {
//            vatRate = BigDecimal.ONE.add(taxVatDto.getRate().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP));
//        }
//        HashMap<Long, GoodsItemDTO> map = new HashMap<>(skuIds.size());
//        BigDecimal finalRate = vatRate;
//        for (GoodsItemDTO dto : dtos) {
//            BigDecimal goodsFreight = freightGroupGoodsIdMap.get(dto.getGoodsId());
//            if (Objects.nonNull(goodsFreight)) {
//                dto.setPrice(dto.getOrginalPrice().add(goodsFreight));
//                dto.setGrouponPrice(dto.getOriginalGrouponPrice().add(goodsFreight));
//            }
//            dto.setBeforeVatPrice(dto.getPrice());
//            dto.setBeforeVatGrouponPrice(dto.getGrouponPrice());
//            dto.setPrice(dto.getPrice().multiply(finalRate).setScale(2, BigDecimal.ROUND_HALF_UP));
//            if (dto.getGrouponPrice() != null) {
//                dto.setGrouponPrice(dto.getGrouponPrice().multiply(finalRate).setScale(2, BigDecimal.ROUND_HALF_UP));
//            }
//            map.put(dto.getSkuId(), dto);
//        }
//        return Result.success(map);
//    }

    /**
     * 根据shopId 上下架商品
     *
     * @param ids
     * @param shopId
     * @return
     */
    @Override
    public Result<Boolean> showGoods(List<Long> ids, Long shopId, String isShow) {
        Result<Boolean> result = new Result<>();
        try {
            GoodsIsShowInput showInput = new GoodsIsShowInput();
            showInput.setGoodsIds(ids);
            showInput.setShopId(shopId);
            showInput.setIsShow(isShow);
            showInput.setOperationLogType(OperationLogTypeEnums.ERP_SHOW);
            showInput.setOperator("erp");
            Boolean aBoolean = goodsCoreService.isShow(showInput);
//            Boolean aBoolean = goodsCoreService.showGoods(ids, shopId, isShow);
            return Result.success(aBoolean);
        } catch (CustomException e) {
            BaseResultCode businessResultCode = e.getBusinessResultCode();
            if (null != businessResultCode) {
                result.setRtn_code(businessResultCode.getCode());
                result.setRtn_msg(businessResultCode.getMsg());
                result.setSuccess(false);
                result.setData(false);
            } else {
                result.setRtn_code(ProductResultCode.GOODS_SHOW_ERROR.getCode());
                result.setRtn_msg(ProductResultCode.GOODS_SHOW_ERROR.getMsg());
                result.setSuccess(false);
                result.setData(false);
            }
        }
        return result;
    }

    @Override
    public List<GoodsFreightVO> queryGoodsFreightByGoodsIds(List<Long> goodsIds) {
        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(goodsIds);
        if (CollectionUtils.isEmpty(goodsFreights)) {
            return Collections.emptyList();
        }
        List<GoodsFreightVO> collect = goodsFreights.stream().map(goodsFreight -> {
            GoodsFreightVO freightVO = BeanCopyUtil.transform(goodsFreight, GoodsFreightVO.class);
            freightVO.setCountry(goodsFreight.getCode());
            return freightVO;
        }).collect(Collectors.toList());
        return BeanCopyUtil.transformList(collect, GoodsFreightVO.class);
    }

    @Override
    public PageView<GoodsOutput> queryGoodsByParam(GoodsQueryCondition vo) {
        if (Objects.isNull(vo.getPageNow())) {
            vo.setPageNow(1);
        }

        if (Objects.isNull(vo.getPageSize())) {
            vo.setPageSize(20);
        }

        QueryWrapper<Goods> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", 0);

        if (CollectionUtils.isNotEmpty(vo.getGoodsIds())) {
            queryWrapper.in("id", vo.getGoodsIds());
        }

        if (CollectionUtils.isNotEmpty(vo.getNoGoodsIds())) {
            queryWrapper.notIn("id", vo.getNoGoodsIds());
        }

        if (org.apache.commons.lang.StringUtils.isNotBlank(vo.getName())) {
            queryWrapper.like("name", vo.getName());
        }

        if (Objects.nonNull(vo.getShopId())) {
            queryWrapper.eq("shop_id", vo.getShopId());
        }

        if (Objects.nonNull(vo.getCategoryId())) {
            List<Long> allCategoryId = Lists.newArrayList(vo.getCategoryId());
            List<Category> categories = categoryService.queryChildCategoryByParentId(vo.getCategoryId());
            if (CollectionUtils.isEmpty(categories)) {
                allCategoryId.addAll(categories.stream().map(Category::getId).collect(Collectors.toSet()));
            }
            queryWrapper.in("category_id", allCategoryId);
        }

        if (Objects.nonNull(vo.getIsShow())) {
            queryWrapper.eq("is_show", vo.getIsShow());
        }

        if (Objects.nonNull(vo.getStartMinPrice())) {
            queryWrapper.ge("min_price", vo.getStartMinPrice());
        }

        if (Objects.nonNull(vo.getEndMaxPrice())) {
            queryWrapper.ge("max_price", vo.getEndMaxPrice());
        }
        PageView<GoodsOutput> pageView = new PageView<>(vo.getPageSize(), vo.getPageNow());
        Page<Goods> page = new Page<>(vo.getPageNow(), vo.getPageSize());
        IPage<Goods> goodsIPage = goodsService.page(page, queryWrapper);
        pageView.setRecords(BeanCopyUtil.transformList(goodsIPage.getRecords(), GoodsOutput.class));
        pageView.setRowCount(goodsIPage.getTotal());
        return pageView;
    }

    @Override
    public List<Long> blacklistedGoods(List<Long> goodsIds) {
        return goodsBlacklistCoreService.blacklistedGoods(goodsIds);
    }

    @Override
    public Result<Boolean> checkSensitiveWords(String text) {
        Set<String> sensitiveWords = sensitiveWordsCoreService.check(text);
        return Result.success(CollectionUtils.isEmpty(sensitiveWords));
    }

    @Override
    public List<String> checkSensitiveWords2(String text) {
        Set<String> sensitiveWords = sensitiveWordsCoreService.check(text);
        return Lists.newArrayList(sensitiveWords);
    }

    @Override
    public List<String> listAllSensitiveWords() {
        return sensitiveWordsCoreService.listAll();
    }

    @Override
    public Result<PageView<GoodsInfoVO>> goodsByPageListWithEs(QueryGoodsVO queryGoodsVO) {
        PageView<GoodsInfoVO> goodsInfoVOPageView = goodsCoreService.goodsByPageListWithEs(queryGoodsVO);
        return Result.success(goodsInfoVOPageView);
    }

    @Override
    public void calculateGoodsSort() {
        log.info("GoodsRemoteService.calculateAllGoodsSort starting!");
        execute.execute(() -> goodsCoreService.calculateAllGoodsSortV2());
    }


    @Override
    public void addGoodsOperationLog(GoodsOperationLogDto goodsOperationLogDto) {
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
    }

    @Override
    public Result<List<SystemBidGoodsImportVo>> queryGoods(List<Long> goodsIds) {
        List<SystemBidGoodsImportVo> goodsOutputs = goodsCoreService.queryGoods(goodsIds);
        return Result.success(goodsOutputs);
    }

    @Override
    public Result<List<FaMerchantsApply>> queryShopName(List<Long> shopIds) {
        return Result.success(faMerchantsApplyCoreService.queryByShopIds(shopIds));
    }

    @Override
    public Result<BottomGoodsVO> queryByGoodsId(Long goodsId) {
        Goods goods = goodsService.queryById(goodsId);
        if (goods == null) {
            return Result.success(null);
        }
        BottomGoodsVO bottomGoodsVO = buyOnePieceCoreService.queryByShopId(goods.getShopId());
        if (bottomGoodsVO == null) {
            bottomGoodsVO = buyOnePieceCoreService.queryBottomGoodsByCategoryId(goods.getCategoryId());
        }
        log.info("queryBottomGoodsByCategoryId:{}", JSONObject.toJSONString(bottomGoodsVO));
        if (bottomGoodsVO != null) {
            GoodsDetailInfoVO goodsDetailInfoVO = goodsCoreService.queryGoodsDetailById(bottomGoodsVO.getGoodsId(), null);
            log.info("queryGoodsById:{}", JSONObject.toJSONString(goodsDetailInfoVO));
            if (goodsDetailInfoVO == null) {
                return Result.success(null);
            }
            String mainImg = goodsDetailInfoVO.getMainImage();
            String skuImg = null;
            bottomGoodsVO.setShopName(goodsDetailInfoVO.getShopName());
            List<GoodsSkuVO> skuInfo = goodsDetailInfoVO.getSkuInfo();
            if (CollectionUtils.isNotEmpty(skuInfo)) {
                for (GoodsSkuVO goodsSkuVO : skuInfo) {
                    if (goodsSkuVO.getSkuId().equals(bottomGoodsVO.getSkuId())) {
                        skuImg = goodsSkuVO.getImageUrl();
                        break;
                    }
                }
            }
            skuImg = StringUtils.isNotEmpty(skuImg) ? skuImg : mainImg;
            bottomGoodsVO.setSkuImg(skuImg);
        }
        return Result.success(bottomGoodsVO);
    }

    @Override
    public Result<BottomGoodsVO> queryValidGoods(Long goodsId) {
        return Result.success(buyOnePieceCoreService.queryByGoodsId(goodsId));
    }


    @Override
    public boolean syncPassGoods(List<Long> goodsIds) {
        log.info("开始远程调用同步上架商品");
        goodsCoreService.syncPassGoodsForRemote(goodsIds);
        log.info("远程调用同步上架商品成功");
        return true;
    }

    @Override
    public boolean syncAdjustWithPriceGoods(List<Long> goodsIds) {
        log.info("开始远程调用同步待调整-价格相关商品");
        goodsCoreService.syncAdjustWithPriceGoods(goodsIds);
        log.info("远程调用同步待调整-价格商品成功");
        return true;
    }

    @Override
    public boolean syncAdjustNotPriceGoods(List<Long> goodsIds) {
        log.info("开始远程调用同步待调整-非价格相关商品");
        goodsCoreService.syncAdjustNotPriceGoods(goodsIds);
        log.info("远程调用同步待调整-非价格相关商品成功");
        return true;
    }

    @Override
    public boolean syncShopDisableGoods(List<Long> goodsIds) {
        log.info("开始远程调用同步禁售商品");
        goodsCoreService.syncDisableGoods(goodsIds, "精选店铺禁售", "PICKED_SHOP_OPERATOR");
        log.info("远程调用同步禁售商品成功");
        return true;
    }

    @Override
    public Result<PickedShopGoodsDTO> queryPickedShopGoodsInfo(PickedShopGoodsVO vo) {
        return Result.success(goodsCoreService.queryPickedShopGoodsInfo(vo));
    }

    @Override
    public List<String> batchImportGoods(Long fileSyncRecordId) {
        log.info("batchImportGoods fileSyncRecordId:{}", fileSyncRecordId);

        Object o = redisApi.get(CommonConstants.BATCH_IMPORT_GOODS_LOCK_ + fileSyncRecordId);
        if (o != null) {
            log.info("batchImportGoods 当前任务正在进行中，请勿重复触发 fileSyncRecordId:{}", fileSyncRecordId);
            return Collections.emptyList();
        }

        FileSyncRecord fileSyncRecord = fileSyncRecordService.getById(fileSyncRecordId);
        if (fileSyncRecord == null) {
            LogUtils.error(log, "batchImportGoods 找不到导入记录 fileSyncRecordId:{}", fileSyncRecordId);
            CheckUtils.check(true, ProductResultCode.FILE_SYNC_RECORD_NOT_FIND);
            return Collections.emptyList();
        }
        if (fileSyncRecord.getStatus() == 1) {
            LogUtils.error(log, "batchImportGoods 该导入任务已完成 fileSyncRecordId:{}", fileSyncRecordId);
            CheckUtils.check(true, ProductResultCode.FILE_SYNC_RECORD_FINISHED);
            return Collections.emptyList();
        }

        List<FileSyncRecordData> fileSyncRecordDataList = fileSyncRecordDataService.lambdaQuery()
                .eq(FileSyncRecordData::getStatus, 0)
                .eq(FileSyncRecordData::getFileSyncRecordId, fileSyncRecordId)
                .list();
        CheckUtils.isEmpty(fileSyncRecordDataList, ProductResultCode.FILE_SYNC_RECORD_DATA_ALL_FINISH);

        redisApi.set(CommonConstants.BATCH_IMPORT_GOODS_LOCK_ + fileSyncRecordId, "1", 15 * 60);

        List<GoodsImportVO> list = BeanCopyUtil.transformList(fileSyncRecordDataList, GoodsImportVO.class);
        LogUtils.info(log, "batchImportGoods 导入商品读取url并解析： list.size:{}", list.size());
        if (list.size() < 5) {
            LogUtils.info(log, "batchImportGoods 导入商品读取url并解析结果:{}", JSONObject.toJSONString(list));
        }


        Map<String, String> errorMsgMap = Maps.newHashMap();
        try {
            Map<String, Long> successGoodsIdMap = Maps.newHashMap();
            errorMsgMap = shopGoodsCoreService.batchImportGoodsNew(list, Long.parseLong(fileSyncRecord.getShopId()), fileSyncRecord.getShopName(), successGoodsIdMap);
            log.info("batchImportGoods 处理返回:{}", JSON.toJSONString(errorMsgMap));
            fileSyncRecord.setStatus(1);
            fileSyncRecord.setMsg(MapUtils.isEmpty(errorMsgMap) ? "导入成功" : MapUtils.isNotEmpty(successGoodsIdMap) ? "部分成功" : "导入失败");
            fileSyncRecord.setUpdateTime(LocalDateTime.now());
            if (MapUtils.isNotEmpty(successGoodsIdMap)) {
                LogUtils.info(log, "成功导入的商品id:{}", JSON.toJSONString(successGoodsIdMap));
                fileSyncRecord.setSuccessGoodsIds(StringUtils.join(successGoodsIdMap.values(), ","));
            }
            fileSyncRecord.setSuccessGoodsCount(successGoodsIdMap.size());
            fileSyncRecord.setFailedGoodsCount(errorMsgMap.size());
            fileSyncRecordService.updateById(fileSyncRecord);

            for (FileSyncRecordData fileSyncRecordData : fileSyncRecordDataList) {
                Long goodsId = successGoodsIdMap.get(fileSyncRecordData.getItemCode());
                if (goodsId != null) {
                    fileSyncRecordData.setStatus(1);
                    fileSyncRecordData.setSuccessGoodsId(goodsId);
                } else {
                    fileSyncRecordData.setStatus(-1);
                    fileSyncRecordData.setMsg(errorMsgMap.get(fileSyncRecordData.getItemCode()));
                }
            }
            fileSyncRecordDataService.updateBatchById(fileSyncRecordDataList);
        } catch (CustomException e) {
            LogUtils.error(log, "执行失败 msg:{}", e.getBusinessResultCode().getMsg(), e);
            fileSyncRecord.setStatus(1);
            fileSyncRecord.setMsg("执行失败:" + e.getBusinessResultCode().getMsg());
            fileSyncRecord.setUpdateTime(LocalDateTime.now());
            fileSyncRecordService.updateById(fileSyncRecord);

            for (FileSyncRecordData fileSyncRecordData : fileSyncRecordDataList) {
                fileSyncRecordData.setStatus(-1);
                fileSyncRecordData.setMsg(e.getBusinessResultCode().getMsg());
            }
            fileSyncRecordDataService.updateBatchById(fileSyncRecordDataList);
        } catch (Exception e) {
            LogUtils.error(log, "执行异常 msg:{}", e.getMessage(), e);
            fileSyncRecord.setStatus(1);
            fileSyncRecord.setMsg("执行异常:" + e.getMessage());
            fileSyncRecord.setUpdateTime(LocalDateTime.now());
            fileSyncRecordService.updateById(fileSyncRecord);
        } finally {
            redisApi.del(CommonConstants.BATCH_IMPORT_GOODS_LOCK_ + fileSyncRecordId);
        }
        return Lists.newArrayList(errorMsgMap.values());
    }

    public static String listToString(List<String> list) {
        if (list == null) {
            return "执行异常失败";
        }
        Set<String> stringSet = new HashSet<>(list);
        StringBuilder result = new StringBuilder();
        boolean first = true;

        //第一个前面不拼接","
        for (String string : stringSet) {
            if (first) {
                first = false;
            } else {
                result.append(",");
            }
            result.append(string);
        }
        return result.toString();
    }

    @Override
    public List<BrandVO> queryShopBrandList(Long shopId, Long categoryId) {
        List<BrandVO> res = new ArrayList<>();
        List<MerchantsBrandMark> merchantsBrandMarks = merchantsBrandMarkService.queryBrandListByShopId(shopId, categoryId);
        if (CollectionUtils.isNotEmpty(merchantsBrandMarks)) {
            merchantsBrandMarks.forEach(k -> {
                BrandVO vo = new BrandVO();
                vo.setBrandId(k.getMerchantsBrandStoreId());
                vo.setBrandName(k.getTitle());
                vo.setCategoryId(k.getCategoryId());
                res.add(vo);
            });
        }
        return res;
    }

    @Override
    public void deleteGoodsDraft() {
        log.info("开始删除7天前草稿商品");
        goodsDraftCoreService.deleteGoodsDraft();
        log.info("删除7天前草稿商品成功");
    }

    @Override
    public Map<Long, Integer> quereyShopDeliveryTypeMap(List<Long> shopIds) {
        return goodsCoreService.quereyShopDeliveryTypeMap(shopIds);
    }

    @Override
    public List<Long> hitAdvertGoodsByKeyword(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return Collections.emptyList();
        }
        List<SearchConfig> searchConfigList = searchConfigService.lambdaQuery()
                .eq(SearchConfig::getKeyWord, keyword)
                .eq(SearchConfig::getIsDel, 0)
                .list();
        if (CollectionUtils.isEmpty(searchConfigList)) {
            return Collections.emptyList();
        }
        List<Long> advertGoodsIds = Lists.newArrayList();
        for (SearchConfig searchConfig : searchConfigList) {
            if (StringUtils.isBlank(searchConfig.getGoodsIds())) {
                continue;
            }
            for (String goodsId : searchConfig.getGoodsIds().replace("，", ",").split(",")) {
                try {
                    advertGoodsIds.add(Long.parseLong(goodsId.trim()));
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }
        }
        return advertGoodsIds;
    }

    @Override
    public void createNewGoodsCustom() {
        log.info("开始重建新品专区");
        customListCoreService.createNewGoodsCustom();
        log.info("重建新品专区成功");
    }

    @Override
    public Result<List<FaMerchantsApplyDTO>> listFaMerchantsApplyListByPrinciple(String getPrincipal) {
        return Result.success(faMerchantsApplyCoreService.listFaMerchantsApplyListByPrinciple(getPrincipal));
    }

    @Override
    public Result<Boolean> systemGoodsIsShow(GoodsIsShowInput goodsIsShowInput) {
        return Result.success(goodsCoreService.systemIsShow(goodsIsShowInput));
    }

    @Override
    public Result<Boolean> insertNotice() {
        wxBindShopCoreService.insertNotice();
        return Result.success(true);
    }

    @Override
    public Result<Boolean> pushOrderInfo() {
        wxBindShopCoreService.pushOrderInfo();
        return Result.success(true);
    }

    @Override
    public Result<Boolean> backupRelatedHotGoods() {
        LogUtils.info(log, "backupRelatedHotGoods start");
        execute.execute(() -> hotGoodsMonitorCoreService.backupRelatedHotGoods());
        return Result.success(true);
    }

    @Override
    public Result<Map<String, BigDecimal>> calculateGoodsPrice(GoodsCalulatePriceInput input) {
        return Result.success(goodsCoreService.calculateGoodsPrice(input));
    }

    @Override
    public Result<Map<String, BigDecimal>> calculateGoodsPriceV2(GoodsCalulatePriceInput input) {
        return Result.success(goodsCoreService.calculateGoodsPriceV2(input));
    }

    @Override
    public Result<Map<Long, BigDecimal>> calculateGoodsPriceV3(GoodsCalulatePriceInput input) {
        return Result.success(goodsCoreService.calculateGoodsPriceV3(input));
    }

    @Override
    public Map<Long, BigDecimal> rmb2Eub(GoodsCalulatePriceInput input) {
        return goodsCoreService.rmb2Eub(input);
    }

    @Override
    public Map<Long, BigDecimal> eub2Rmb(GoodsCalulatePriceInput input) {
        return goodsCoreService.eub2Rmb(input);
    }

    @Override
    @SneakyThrows
    public Result<Boolean> updateGoodsPriceOrStock(GoodsItemUpdateInput input) {
        log.info("updateGoodsPriceOrStock input:{}", JSON.toJSONString(input));
        Long goodsId = input.getItemDTOS().get(0).getGoodsId();
        if (input != null && CollectionUtils.isNotEmpty(input.getItemDTOS())) {
            List<GoodsItem> goodsItems = goodsItemService.queryGoodsItemByGoodsId(goodsId);
            Map<String, Long> skuMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getName, GoodsItem::getSkuId, (v1, v2) -> v1));
            input.getItemDTOS().forEach(k -> {
                if (skuMap.containsKey(k.getSkuName())) {
                    k.setSkuId(skuMap.get(k.getSkuName()));
                }
            });
        }
        if (input.getUpdateScope().equals(1) || input.getUpdateScope().equals(3)) {
            List<GoodsSkuImportVO> goodsSkuImportVOS = TransferUtils.transferList(input.getItemDTOS(), GoodsSkuImportVO.class);

            goodsSkuImportVOS.forEach(k -> k.setId(goodsId));
            shopGoodsCoreService.batchImportGoodsSkuFee(goodsSkuImportVOS,true);
        }
        if (input.getUpdateScope().equals(2) || input.getUpdateScope().equals(3)) {
            List<GoodsStockImportVO> goodsStockImportVOS = TransferUtils.transferList(input.getItemDTOS(), GoodsStockImportVO.class);
            goodsCoreService.batchUpdateStock(goodsStockImportVOS,true);
        }
        return Result.success(true);
    }

    @Override
    public Result<List<Long>> queryListingGoodsById(Long goodsId) {
        LogUtils.info(log, "queryListingGoodsById goodsId:{}", goodsId);
        CheckUtils.notNull(goodsId, ProductResultCode.PARAMETER_ID_ERROR);

        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getGoodsId, goodsId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .one();
        if (listingFollowGoods == null) {
            return Result.success(Collections.emptyList());
        }

        List<ListingFollowGoods> list = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, listingFollowGoods.getListingId())
                .ne(ListingFollowGoods::getGoodsId, goodsId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return Result.success(Collections.emptyList());
        }

        list.sort((o1, o2) -> {
            o1.setSort(o1.getSort() == null ? 0 : o1.getSort());
            o2.setSort(o2.getSort() == null ? 0 : o2.getSort());
            o1.setIsTop(o1.getIsTop() == null ? 0 : o1.getIsTop());
            o2.setIsTop(o2.getIsTop() == null ? 0 : o2.getIsTop());
            int compareTo = o2.getIsTop().compareTo(o1.getIsTop());
            if (compareTo != 0) {
                return compareTo;
            }
            return o1.getSort().compareTo(o2.getSort());
        });
        List<Long> resultGoodsIds = list.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        return Result.success(resultGoodsIds);
    }

    @Override
    public Result<Boolean> updatePriceAndStock(UpdatePriceAndStockVo vo) {
        LogUtils.info(log, "进入dubbo - applyUpdatePriceAndStock...");
        vo.setChannel(1);
        updateGoodsInfoCoreService.updatePriceAndStock(vo);
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> updateProperty(UpdatePropertyVo vo) {
        LogUtils.info(log, "进入dubbo - updateProperty...");
        updateGoodsInfoCoreService.updateProperty(vo);
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> updateDetail(UpdateDetailVo vo) {
        LogUtils.info(log, "进入dubbo - updateDetail...");
        updateGoodsInfoCoreService.updateDetail(vo);
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> scanProhibitionGoods(Integer type) {
        String startTime = null;
        String endTime = null;
        if (type == null || type == 0) {
            startTime = DateUtils.formatDateTime(LocalDateTime.now().minusMinutes(30), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtils.formatDateTime(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss");
        } else {
            startTime = DateUtils.formatDateTime(LocalDateTime.now().minusDays(type), "yyyy-MM-dd HH:mm:ss");
            endTime = DateUtils.formatDateTime(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss");
        }
        goodsProhibitCoreService.scanProhibitionGoods(startTime, endTime);
        return Result.success(true);
    }

    @Override
    public Result<Boolean> phpGoodsIsShow(GoodsIsShowInput goodsIsShowInput) {
        LogUtils.info(log, "进入dubbo - phpGoodsIsShow...");
        return Result.success(goodsCoreService.phpIsShow(goodsIsShowInput));
    }

    @Override
    public Result<Boolean> updateGoodsShopInfo(UpdateShopInfoVo input) {
        //更新商品表的店铺id和名称
        Goods goods = goodsService.selectById(input.getGoodsId());
        CheckUtils.notNull(goods, CustomResultCode.fill(ProductResultCode.GOODS_NOT_EXIST_EXT, input.getGoodsId().toString()));

        goods.setShopId(input.getShopId());
        goods.setShopName(input.getShopName());
        goods.setUpdateTime(LocalDateTime.now());
        if(Objects.nonNull(input.getSizeChartTemplateId())){
            goods.setSizeChartTemplateId(input.getSizeChartTemplateId());
        }
        goodsService.updateById(goods);
        //更新商品扩展表中店铺信息
        GoodsExtDetail goodsExtDetail = goodsExtDetailService.lambdaQuery().eq(GoodsExtDetail::getGoodsId, input.getGoodsId()).one();
        CheckUtils.notNull(goodsExtDetail, CustomResultCode.fill(ProductResultCode.GOODS_EXT_DETAIL_NOT_EXIST, input.getGoodsId().toString()));
        goodsExtDetail.setShopUrl(input.getShopId().toString());
        goodsExtDetail.setShopName(input.getShopName());
        goodsExtDetailService.updateById(goodsExtDetail);

        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(input.getGoodsId());
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("更新商品表的店铺id和名称");
        goodsSyncModel.setSourceService("vp");
        mqSender.send("SYNC_GOODS_TOPIC", JSON.toJSONString(goodsSyncModel));

        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(input.getGoodsId())
                .type(OperationLogTypeEnums.SCM_UPDATE_GOODS)
                .newData(JSON.toJSONString(input))
                .content(OperationLogTypeEnums.SCM_UPDATE_GOODS.getDesc())
                .status(1)
                .user("SYSTEM");
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> batchUpdateFreight(List<BatchUpdateGoodsFreightVo> list) {
        CheckUtils.isEmpty(list, ProductResultCode.PARAMETER_ERROR);
        list.forEach(vo -> CheckUtils.check(vo.getGoodsId() == null || StringUtils.isEmpty(vo.getWeight()), ProductResultCode.PARAMETER_ERROR));

        List<GoodsFreightImportVO> importVOList = list.stream().map(vo -> {
            GoodsFreightImportVO importVO = BeanCopyUtil.transform(vo, GoodsFreightImportVO.class);
            importVO.setOperator("dubbo.api调用");
            return importVO;
        }).collect(Collectors.toList());

        goodsCoreService.batchUpdateFreight(importVOList, 2);
        return Result.success(Boolean.TRUE);
    }


    @Override
    public List<GoodsBriefInfoDTO> queryGoodsBriefInfo(QueryGoodsVO queryGoodsVO) {
        if (queryGoodsVO == null || CollectionUtils.isEmpty(queryGoodsVO.getGoodsIds())){
            CheckUtils.check(true, ProductResultCode.PARAMETER_ERROR);
        }

        List<GoodsBriefInfoDTO> goodsBriefInfoDTOS = goodsCoreService.queryGoodsBriefInfo(queryGoodsVO);
        if(StringUtils.isNotEmpty(queryGoodsVO.getCurrency()) && !CurrencyCodeEnum.EUR.getCode().equals(queryGoodsVO.getCurrency().toUpperCase())){
            CurrencyCodeEnum currenyEnums = CurrencyCodeEnum.of(queryGoodsVO.getCurrency().toUpperCase());
            Object cacheRate = redisApi.get(SystemConstants.CURRENYTAG + currenyEnums.getCode());
            if ( Objects.nonNull(cacheRate)){
                BigDecimal currenyRate =new BigDecimal(cacheRate.toString()).setScale(6, RoundingMode.HALF_UP);
                for (GoodsBriefInfoDTO goodsBriefInfoDTO : goodsBriefInfoDTOS) {
                    goodsBriefInfoDTO.setMinPrice(goodsBriefInfoDTO.getMinPrice().multiply(currenyRate).setScale(2, RoundingMode.HALF_UP));
                }
            }
        }

        return goodsBriefInfoDTOS;
    }

    @Override
    public List<Long> selectNegativeGoodsIds(List<Long> goodsIds) {
        return goodsExtConfigCoreService.selectNegativeGoodsIds(goodsIds);
    }

    @Override
    public Result<List<SkuGradientPriceDto>> queryGoodsPriceByGradient(Long goodsId, String country) {
        CheckUtils.notNull(goodsId, ProductResultCode.PARAMETER_ERROR);
        CheckUtils.notEmpty(country, ProductResultCode.PARAMETER_ERROR);

        List<GoodsItem> goodsItemList = goodsItemService.queryGoodsItemByGoodsId(goodsId);

        Map<Long, List<GoodsItemGradient>> goodsItemGradientGroupMap = goodsItemGradientService.lambdaQuery()
                .eq(GoodsItemGradient::getGoodsId, goodsId)
                .eq(GoodsItemGradient::getIsDel, 0).list()
                .stream()
                .collect(Collectors.groupingBy(GoodsItemGradient::getGoodsItemId));

        List<SkuGradientPriceDto> dtoList = Lists.newArrayList();
        for (GoodsItem goodsItem : goodsItemList) {
            SkuGradientPriceDto priceDto = new SkuGradientPriceDto(goodsId, goodsItem.getSkuId());
            List<SkuGradientPriceDto.GradientPriceDetail> priceMap = Lists.newArrayList();

            List<GoodsItemGradient> itemGradientList = goodsItemGradientGroupMap.get(goodsItem.getId());
            if (CollectionUtils.isEmpty(itemGradientList)) {
                LogUtils.info(log, "sku梯度价格信息为空 goodsId:{}, itemId:{}", goodsId, goodsItem.getId());
                continue;
            }
            for (GoodsItemGradient itemGradient : itemGradientList) {
                SkuGradientPriceDto.GradientPriceDetail priceDetail = new SkuGradientPriceDto.GradientPriceDetail();
                priceDetail.setPrice(itemGradient.getPrice());
                priceDetail.setMinLimit(itemGradient.getMinNum());
                priceDetail.setMaxLimit(itemGradient.getMaxNum());
                priceMap.add(priceDetail);
            }
            priceDto.setPriceMap(priceMap);
            dtoList.add(priceDto);
        }
        return Result.success(dtoList);
    }

    @Override
    public Result<Map<Long, SkuGradientPriceListDto>> querySkuPriceByGradient(QueryAllSkuPriceByGradientCondition condition) {
        CheckUtils.notEmpty(condition.getCountry(), ProductResultCode.PARAMETER_ERROR);
        CheckUtils.isEmpty(condition.getSkuInfos(), ProductResultCode.PARAMETER_ERROR);
        for (QueryAllSkuPriceByGradientCondition.SkuInfo skuInfo : condition.getSkuInfos()) {
            CheckUtils.notNull(skuInfo.getGoodsId(), ProductResultCode.PARAMETER_ERROR);
            CheckUtils.notNull(skuInfo.getSkuId(), ProductResultCode.PARAMETER_ERROR);
            CheckUtils.notNull(skuInfo.getNum(), ProductResultCode.PARAMETER_ERROR);
        }

        List<QueryAllSkuPriceByGradientCondition.SkuInfo> skuInfos = condition.getSkuInfos();
//        List<Long> goodsIds = skuInfos.stream().map(QueryAllSkuPriceByGradientCondition.SkuInfo::getGoodsId).distinct().collect(Collectors.toList());
        List<Long> skuIds = skuInfos.stream().map(QueryAllSkuPriceByGradientCondition.SkuInfo::getSkuId).collect(Collectors.toList());
        Map<Long, Integer> skuNumMap = skuInfos.stream().collect(Collectors.toMap(QueryAllSkuPriceByGradientCondition.SkuInfo::getSkuId, QueryAllSkuPriceByGradientCondition.SkuInfo::getNum, (v1, v2) -> v1));

        List<GoodsItem> goodsItemList = goodsItemService.queryListBySkuIds(skuIds);
        CheckUtils.isEmpty(goodsItemList,ProductResultCode.NOT_FIND_GOODS_ITEM_BY_SKU_ID);

        List<Long> itemIds = goodsItemList.stream().map(GoodsItem::getId).collect(Collectors.toList());
        Map<Long, List<GoodsItemGradient>> goodsItemGradientGroupMap = goodsItemGradientService.lambdaQuery()
                .in(GoodsItemGradient::getGoodsItemId, itemIds)
                .eq(GoodsItemGradient::getIsDel, 0).list()
                .stream()
                .collect(Collectors.groupingBy(GoodsItemGradient::getGoodsItemId));

//        Map<Long, List<GoodsFreight>> goodsFreightGroupMap = goodsFreightService.lambdaQuery()
//                .in(GoodsFreight::getGoodsId, goodsIds)
//                .eq(GoodsFreight::getCode, condition.getCountry())
//                .eq(GoodsFreight::getIsDel, 0)
//                .list()
//                .stream().collect(Collectors.groupingBy(GoodsFreight::getGoodsId));

        Map<Long, SkuGradientPriceListDto> resultMap = Maps.newHashMap();
        for (GoodsItem goodsItem : goodsItemList) {
            Integer num = skuNumMap.get(goodsItem.getSkuId());

            List<GoodsItemGradient> goodsItemGradientList = goodsItemGradientGroupMap.get(goodsItem.getId());
            if (CollectionUtils.isEmpty(goodsItemGradientList)) {
                LogUtils.warn(log, "sku梯度价格为空 goodsId:{}, skuId:{}", goodsItem.getGoodsId(), goodsItem.getSkuId());
                continue;
            }

            SkuGradientPriceListDto dto = new SkuGradientPriceListDto();
            dto.setGoodsId(goodsItem.getGoodsId());
            dto.setSkuId(goodsItem.getSkuId());
            for (GoodsItemGradient goodsItemGradient : goodsItemGradientList) {
                if (num <= goodsItemGradient.getMaxNum() && num >= goodsItemGradient.getMinNum()) {
                    dto.setCurrentPrice(goodsItemGradient.getPrice());
                }
                if (goodsItemGradient.getGradientNum() == 1) {
                    dto.setCommonPrice(goodsItemGradient.getPrice());
                }
            }
            List<SkuGradientPriceDto.GradientPriceDetail> priceDetails = goodsItemGradientList.stream()
                    .map(goodsItemGradient -> new SkuGradientPriceDto.GradientPriceDetail(goodsItemGradient.getMinNum(), goodsItemGradient.getMaxNum(), goodsItemGradient.getPrice()))
                    .collect(Collectors.toList());
            dto.setPriceMap(priceDetails);

            resultMap.put(goodsItem.getSkuId(), dto);
        }

        return Result.success(resultMap);
    }

    @Override
    public Result<BigDecimal> calculateWholesaleFreightAmount(List<CalculateWholesaleFreightDto> dtoList) {
        return goodsFreightCoreService.calculateWholesaleFreightAmount(dtoList);
    }

    @Override
    public Result<CalculateWholesaleFreightResultDto> calculateWholesaleFreightInfo(List<CalculateWholesaleFreightSkuDto> dtoList) {
        return goodsFreightCoreService.calculateWholesaleFreightInfo(dtoList);
    }

    @Override
    public Result<Boolean> batchUpdateCostPrice(List<UpdateCostPriceDto> dtoList) {
        goodsCoreService.batchUpdateCostPrice(dtoList, false);
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> refreshGoodsCountry(RefreshGoodsCountryVO refreshGoodsCountryVO) {
        try {
            updateGoodsInfoCoreService.refreshGoodsCountry(refreshGoodsCountryVO);
        }catch (Exception e){
            return Result.success(Boolean.FALSE);
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> syncPassGoodsNew(List<Long> goodsIds) {
        return Result.success(goodsCoreService.syncPassGoodsNew(goodsIds));
    }

    /**
     * 下架零库存sku商品：目前只对批发商品做校验
     */
    @Override
    public void takeOffZeroStockSkuGoods() {
        goodsCoreService.takeOffZeroStockSkuGoods();
    }

    @Override
    public Result<Map<Long, List<Long>>> queryTagByGoodsIds(List<Long> goodsIds) {
        Map<Long, List<Long>> goodsTagMap = goodsCoreService.queryTagByGoodsIds(goodsIds);
        return Result.success(goodsTagMap);
    }

    @Override
    public Result<Boolean> selfSupportGoodsChangeShop(ChangeShopInfoVo input) {
        goodsCoreService.importUpdateShopInfo(input.getUpdateShopInfoVOS(), input.getUpdateBy());
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<Boolean> refreshGoodsRealShotImg(RefreshGoodsRealShotImgDTO refreshGoodsRealShotImgDTO) {
        try {
            return Result.success(updateGoodsInfoCoreService.refreshGoodsRealShotImg(refreshGoodsRealShotImgDTO));
        }catch (Exception e){
            return Result.success(Boolean.FALSE);
        }
    }

    @Override
    public Result<Boolean> conversionGoodsIsShow(ConversionGoodsIsShowInput conversionGoodsIsShowInput) {
        return Result.success(goodsCoreService.conversionGoodsIsShow(conversionGoodsIsShowInput));
    }

    @Override
    public Result<Boolean> productSaleExportTask() {
        String traceId = MDC.get("traceId");
        execute.execute(() -> productSaleExportCoreService.handleTask(traceId));
        return Result.success(Boolean.TRUE);
    }

    @Override
    public Result<GoodsManualVO> queryGoodsManualByGoodsId(Long goodsId, String countryKey) {
        return Result.success(goodsCoreService.getGoodsManualByGoodsIdAndCountryType(goodsId, countryKey));
    }

    @Override
    public void negativeGoodsExitWholesale() {
        goodsCoreService.negativeGoodsExitWholesale();
    }


    @Override
    public Result<Map<Long, Integer>> queryShopRealGoodsCountByShopIds(List<Long> shopIds) {
        return goodsCoreService.queryShopRealGoodsCountByShopIds(shopIds);
    }

    @Override
    public Result<List<ShopInfoDto>> queryShopInfoByShopIds(List<Long> shopIds) {
        return goodsCoreService.queryShopInfoByShopIds(shopIds);
    }

}
