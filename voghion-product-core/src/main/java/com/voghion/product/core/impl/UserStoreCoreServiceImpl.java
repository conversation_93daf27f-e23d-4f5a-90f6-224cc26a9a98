package com.voghion.product.core.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.PageView;
import com.colorlight.base.utils.TransferUtils;
import com.voghion.merchant.api.output.ShopSearchPageOutput;
import com.voghion.product.api.dto.UserStoreCountVO;
import com.voghion.product.api.dto.UserStoreVO;
import com.voghion.product.api.output.QueryGoodsOrderDTO;
import com.voghion.product.client.MerchantsDecorateClientFactory;
import com.voghion.product.core.UserStoreCoreService;
import com.voghion.product.model.po.FaMerchantsApply;
import com.voghion.product.model.po.UserStore;
import com.voghion.product.model.vo.ShopCountVO;
import com.voghion.product.service.FaMerchantsApplyService;
import com.voghion.product.service.GoodsService;
import com.voghion.product.service.UserStoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
@Service
@Slf4j
public class UserStoreCoreServiceImpl implements UserStoreCoreService {


    @Resource
    private UserStoreService userStoreService;

    @Resource
    private RedisApi redisApi;

    @Resource
    private GoodsService goodsService;

    @Resource
    private FaMerchantsApplyService faMerchantsApplyService;

    @Resource
    private MerchantsDecorateClientFactory merchantsDecorateClientFactory;

    @Value("${voghion.dev}")
    private String env;

    @Override
    public PageView<UserStoreVO> queryPage(UserStoreVO queryVO) {

        PageView<UserStore> userStorePageView = userStoreService.queryPage(queryVO);
        if(userStorePageView == null || CollectionUtils.isEmpty(userStorePageView.getRecords())){
            return null;
        }
        log.info("userStore数据{}",userStorePageView.getRecords());
        List<Long> shopIds = userStorePageView.getRecords().stream().map(UserStore::getShopId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(shopIds)){
            return null;
        }
        String countKey = "CountShopId";
        String jsonData = redisApi.get(countKey);
        log.info("jsonData里的数据{}",jsonData);
        Map<String, String> maps = new HashMap<>();
        if(StringUtils.isBlank(jsonData)){
            List<ShopCountVO> shopCountVOS = goodsService.queryCountShopIds(shopIds);
            for(ShopCountVO vo : shopCountVOS){
                maps.put(String.valueOf(vo.getShopId()), String.valueOf(vo.getCount()));
            }
            redisApi.set(countKey, JSON.toJSONString(maps), 60 * 60 * 12);
            log.info("存进缓存的{}", JSONObject.toJSONString(maps));
        }else {
            maps = JSON.parseObject(jsonData, Map.class);
            log.info("maps里的数据{}", maps);
            List<Long> ids = new ArrayList<>();
            for (Long id : shopIds) {
                if (!maps.containsKey(String.valueOf(id))) {
                    ids.add(id);
                }
            }
            if(CollectionUtils.isNotEmpty(ids)) {

                List<ShopCountVO> shopCountVOS = goodsService.queryCountShopIds(ids);
                for (ShopCountVO vo : shopCountVOS) {
                    maps.put(String.valueOf(vo.getShopId()), String.valueOf(vo.getCount()));
                }
                redisApi.set(countKey, JSON.toJSONString(maps), 60 * 60 * 12);
                log.info("存进缓存的{}", JSONObject.toJSONString(maps));
            }
        }
        final Collection<FaMerchantsApply> faMerchantsApplies = faMerchantsApplyService.selectByIds(shopIds);
        Map<Long, Boolean> shopShowMap = merchantsDecorateClientFactory.querySearchPageByShopIds(shopIds)
                .stream()
                .filter(shopSearchPageOutput -> shopSearchPageOutput.getIsShow() != null)
                .collect(Collectors.toMap(ShopSearchPageOutput::getShopId, ShopSearchPageOutput::getIsShow));


        Map<Long, FaMerchantsApply> shopMap = null;
        if(CollectionUtils.isNotEmpty(faMerchantsApplies)){
            shopMap = faMerchantsApplies.stream().collect(Collectors.toMap(FaMerchantsApply::getId, Function.identity()));

        if (CollectionUtils.isNotEmpty(userStorePageView.getRecords())) {
            PageView<UserStoreVO> result = new PageView<>();
            List<UserStore> records = userStorePageView.getRecords();
            List<UserStoreVO> userStoreVOS = new ArrayList<>();
            for(UserStore userStore:records){
                UserStoreVO userStoreVO = new UserStoreVO() ;
                userStoreVO.setShopGoodsCounts(Integer.parseInt(maps.get(String.valueOf(userStore.getShopId()))==null?"314":maps.get(String.valueOf(userStore.getShopId()))));
                userStoreVO.setShopId(userStore.getShopId());
                userStoreVO.setUserId(userStore.getUserId());
                userStoreVO.setShopName(shopMap.get(userStore.getShopId()).getShopname());
                userStoreVO.setStatus(userStore.getStatus());
                userStoreVO.setShopIcon(shopMap.get(userStore.getShopId()).getShopIcon());
                userStoreVO.setId(userStore.getId());

                Boolean isShow = shopShowMap.get(userStore.getShopId());
                if (isShow != null && isShow) {
                    String routeUrl;
                    if (StringUtils.isNotBlank(env) && !StringUtils.equals(env, "prod")) {
                        routeUrl = "voghion://approuter/showWeb?openUrl=https%3A%2F%2Fstatic-" + env + ".voghion.com%2Fstatic%2Fmobile%2Findex.html%23%2Fshop%2F" + userStore.getShopId();
                    } else {
                        routeUrl = "voghion://approuter/showWeb?openUrl=https%3A%2F%2Fstatic.voghion.com%2Fstatic%2Fmobile%2Fv0%2Findex.html%23%2Fshop%2F" + userStore.getShopId();
                    }
                    userStoreVO.setRouteUrl(routeUrl);
                }
                userStoreVOS.add(userStoreVO);
            }
            result.setRecords(userStoreVOS);
            result.setPageNow(userStorePageView.getPageNow());
            result.setPageSize(userStorePageView.getPageSize());
            result.setPageCount(userStorePageView.getPageCount());
            result.setRowCount(userStorePageView.getRowCount());
            return result;
        }        }

        return null;
    }

    @Override
    public Integer addOrCancelUserStore(UserStoreVO userStoreVO) {
        List<UserStore> userStoreList = userStoreService.listUserStore(userStoreVO);

        if (CollectionUtils.isEmpty(userStoreList)) {
            UserStore userStore = new UserStore();
            userStore.setUserId(userStoreVO.getUserId());
            userStore.setShopId(userStoreVO.getShopId());
            //10 收藏， 20未收藏
            userStore.setStatus(10);
            userStore.setCreateTime(LocalDateTime.now());
            userStore.setShopName(userStoreVO.getShopName());
            userStore.setShopIcon(userStoreVO.getShopIcon());
            final boolean insert = userStoreService.insert(userStore);
            if(insert){
                return 10;
            }
            return null;
        }
        // 先查一遍，没有是新增，有是更新
        UserStore userStore = userStoreList.get(0);
        if (userStore.getId().equals(userStoreVO.getId()) && !userStore.getShopName().equals(userStoreVO.getShopName())) {
            userStore.setShopName(userStoreVO.getShopName());
        }
        if (userStore.getStatus().equals(10)) {
            userStore.setStatus(20);
        } else if (userStore.getStatus().equals(20)) {
            userStore.setStatus(10);
        }
        userStore.setUpdateTime(LocalDateTime.now());
        final boolean b = userStoreService.updateById(userStore);
        if(b){
            return userStore.getStatus();
        }
        return null;
    }

    @Override
    public UserStoreVO queryByOption(Long userId, QueryGoodsOrderDTO queryGoodsOrderDTO) {
        UserStoreVO result = new UserStoreVO()  ;
        UserStoreVO queryVO = new UserStoreVO()  ;
        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.selectById(queryGoodsOrderDTO.getShopId());

        queryVO.setUserId(userId);
        queryVO.setShopName(faMerchantsApply.getShopname());
        queryVO.setShopId(queryGoodsOrderDTO.getShopId());
//        Integer status = 20;
        List<UserStore> userStoreList = userStoreService.listUserStore(queryVO);
        if (CollectionUtils.isNotEmpty(userStoreList)) {
            TransferUtils.transferBean(userStoreList.get(0), result);
            result.setShopIcon(faMerchantsApply.getShopIcon());
        }
        return result;
    }

    @Override
    public UserStoreCountVO queryShopFollowersCount(UserStoreVO userStoreVO) {
        if (userStoreVO == null || userStoreVO.getShopId() == null) {
            return null;
        }
        UserStoreVO queryVO = new UserStoreVO();
        queryVO.setShopId(userStoreVO.getShopId());
        List<UserStore> userStoreList = userStoreService.listUserStore(queryVO);
        UserStoreCountVO res = new UserStoreCountVO();

        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.selectById(userStoreVO.getShopId());
        if (faMerchantsApply != null) {
            res.setShopIcon(faMerchantsApply.getShopIcon());
        }

        if (CollectionUtils.isEmpty(userStoreList)) {
            res.setFollowersCount(0L);
            return res;
        }
        long count = userStoreList.stream().filter(k -> {
            if (userStoreVO.getUserId() != null && k.getUserId().equals(userStoreVO.getUserId())) {
                res.setStatus(k.getStatus());
            }
            if (k.getStatus() != null && k.getStatus().equals(10)) {
                return true;
            } else {
                return false;
            }
        }).map(UserStore::getUserId).distinct().count();
        res.setFollowersCount(count);
        return res;
    }
}
