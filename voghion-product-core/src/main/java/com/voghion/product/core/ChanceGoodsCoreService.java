package com.voghion.product.core;

import com.colorlight.base.model.PageView;
import com.voghion.product.listener.BatchSaveChanceGoodsVo;
import com.voghion.product.listener.BatchUpdateStatusAndHotVo;
import com.voghion.product.model.vo.*;
import com.voghion.product.model.vo.condition.*;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/7 21:41
 * @describe
 */
public interface ChanceGoodsCoreService {

    PageView<ChanceGoodsTemplateVo> listTemplate(ChanceGoodsTemplateQueryCondition condition);

    List<ChanceGoodsTemplateExportVo> exportTemplateList(ChanceGoodsTemplateQueryCondition condition);

    Long addTemplate(ChanceGoodsTemplateSaveCondition condition);

    void updateTemplate(ChanceGoodsTemplateSaveCondition condition);

    /**
     * 开放/关闭
     * @param condition
     */
    void updateTemplateStatus(ChanceGoodsTemplateUpdateStatusCondition condition);


    ChanceGoodsTemplateDetailInfoVO queryTemplateDetailById(Long goodsId);

    /**
     * 修改热度
     * @param condition
     */
    void updateTemplateHot(ChanceGoodsTemplateUpdateHotCondition condition);

    void batchSaveTemplate(List<BatchSaveChanceGoodsVo> list);

    void batchUpdateStatusAndHot(List<BatchUpdateStatusAndHotVo> list);

    List<ChanceGoodsVo> listChanceGoodsByTemplateId(ChanceGoodsQueryCondition condition);

    List<ChanceGoodsExportVo> exportChanceGoods(ChanceGoodsQueryCondition condition);

    void selectChanceGoods(ChanceGoodsSelectCondition condition);

    /**
     * 机会商品报名(模板列表)【商家】
     */
    PageView<ChanceGoodsTemplateForSignUpVo> listChanceGoodsTemplateForSignUp(ChanceGoodsTemplateQueryForSignUpCondition condition);

    /**
     * 我有同款
     */
    void matchSameChanceGoods(ChanceGoodsMatchSameCondition condition);

    /**
     * 发布同款
     */
    void createSameChanceGoods(ChanceGoodsCreateSameCondition condition);

    PageView<ChanceGoodsForAuditVo> listChanceGoodsAudit(ChanceGoodsQueryForAuditCondition condition);

    List<ChanceGoodsForAuditExportVo> exportChanceGoodsAudit(ChanceGoodsQueryForAuditCondition condition);

    void chooseChanceGoods(ChanceGoodsChooseCondition condition);

    PageView<ChanceGoodsSelectedVo> listSelectedChanceGoods(ChanceGoodsSelectedQueryCondition condition);

    List<ChanceGoodsSelectedExportVo> exportSelectedChanceGoods(ChanceGoodsSelectedQueryCondition condition);

    /**
     * 每日凌晨跑脚本，先判断每个模板生成的商品的SKU(red-s)项是否和模板一致，并打标。
     * 同时计算每个商品的SKU均价(按德国价格计算即可)，打上价格最低是 的标签。方便运营导出后人工额外加流量
     */
    void checkChanceGoodsIsSkuConsistentAndIsMinSkuAvgPrice();

    /**
     * 当某个模板被5个商家开卖后，当天自动关闭，最后更新人记录为system
     * @param goodsIds 新上架的商品id集合
     */
    void checkChanceGoodsTemplateForClose(List<Long> goodsIds);

    /**
     * 次日0点起脚本，检查所有自动关闭的模板里的商品在架状态,和是否和模板一致，如果<5个和模板一致的且在架的商品，则把模板继续放开给商家复制，差几个可报名几个，报满后再继续自动关闭
     */
    void checkChanceGoodsTemplateForOpen();

    /**
     * 连续30天没有商家跟卖的机会商品自动关闭
     */
    void checkChanceGoodsTemplateForClose();

    /**
     * 随机选择部分机会商品列表
     * @param pageSize
     * @return
     */
    List<ChanceGoodsTemplateVo> randomChanceGoodsList(Integer pageSize);

    void updateCategory(ChanceGoodsUpdateCategoryCondition condition);

//    /**
//     * 根据goodsId查询所属同一机会商品模板且价格更低的goodsId列表
//     */
//    List<Long> queryGoodsIdsBySameTemplate(Long goodsId);
}
