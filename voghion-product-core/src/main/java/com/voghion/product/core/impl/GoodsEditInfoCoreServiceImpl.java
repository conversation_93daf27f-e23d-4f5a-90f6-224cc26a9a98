package com.voghion.product.core.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.enums.CountryEnums;
import com.colorlight.base.utils.CheckUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.onlest.GoodsSyncModel;
import com.voghion.product.api.dto.BindTagDTO;
import com.voghion.product.api.dto.GoodsOperationLogDto;
import com.voghion.product.api.dto.GoodsRelTagDTO;
import com.voghion.product.api.enums.GoodsEditTypeEnums;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.biz.CleaningGoodsItemBiz;
import com.voghion.product.client.ActivityRemoteClientFactory;
import com.voghion.product.core.*;
import com.voghion.product.enums.SizeTemplatePropAreaEnum;
import com.voghion.product.helper.GoodsSkuHelper;
import com.voghion.product.helper.PropertyHelper;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.enums.DeliveryTypeEnum;
import com.voghion.product.model.enums.GoodsLockLabelTypEnums;
import com.voghion.product.model.enums.ProductResultCode;
import com.voghion.product.model.po.*;
import com.voghion.product.model.po.goods.GoodsSkuPo;
import com.voghion.product.model.vo.*;
import com.voghion.product.model.vo.condition.*;
import com.voghion.product.mq.MqDelayLevel;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.util.*;
import com.voghion.product.utils.CommonConstants;
import com.voghion.product.utils.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoodsEditInfoCoreServiceImpl extends AbstractCommonServiceImpl implements GoodsEditInfoCoreService {

    @Resource
    private GoodsEditInfoService goodsEditInfoService;

    @Resource
    private GoodsEditInfoDetailService goodsEditInfoDetailService;

    @Resource
    private RecommendCoreService recommendCoreService;

    @Resource
    private UpdateGoodsInfoCoreService updateGoodsInfoCoreService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private GoodsItemService goodsItemService;

    @Resource
    private GoodsItemGradientService goodsItemGradientService;

    @Resource
    private GoodsExtDetailService goodsExtDetailService;

    @Resource
    private GoodsFreightService goodsFreightService;

    @Resource
    private GoodsFreightCoreService goodsFreightCoreService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private CategoryTreeCoreService categoryTreeCoreService;

    @Resource
    private FaMerchantsApplyService faMerchantsApplyService;

    @Resource
    private ListingFollowGoodsService listingFollowGoodsService;

    @Resource
    private ListingInfoService listingInfoService;

    @Resource
    private MerchantsBrandStoreService merchantsBrandStoreService;

    @Resource
    private SizeChartTemplateService sizeChartTemplateService;

    @Resource
    private SizeChartPropService sizeChartPropService;

    @Resource
    private GoodsDetailService goodsDetailService;

    @Resource
    private PropertyGoodsInfoCoreService propertyGoodsInfoCoreService;

    @Resource
    private ProductSkuService productSkuService;

    @Resource
    private NewAddGoodsCoreService newAddGoodsCoreService;

    @Resource
    private PropertyInfoService propertyInfoService;

    @Resource
    private PropertyValueRecordService propertyValueRecordService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private GoodsExtConfigService goodsExtConfigService;

    @Resource
    private GoodsLockCoreService goodsLockCoreService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private ActivityOriginalPriceService activityOriginalPriceService;

    @Resource
    private ActivityRemoteClientFactory activityRemoteClientFactory;

    @Resource
    private SearchTypeMatchConfigService searchTypeMatchConfigService;

    @Resource
    private AliGoodsCategoryPriceConfigService aliGoodsCategoryPriceConfigService;

    @Resource
    private CustomTagCoreService customTagCoreService;

    @Resource
    private GoodsSkuHelper goodsSkuHelper;

    @Resource
    private MqSender mqSender;

    @Value("${voghion.dev}")
    private String env;

    @NacosValue(value = "${listing.audit.user.ids:}", autoRefreshed = true)
    private List<Long> listingAuditUserIdList;

    @Resource
    private PropertyHelper propertyHelper;

    @Resource
    private GoodsSkuService goodsSkuService;

    @Resource
    private CleaningGoodsItemBiz cleaningGoodsItemBiz;

    @Override
    public PageView<GoodsEditListVo> pageList(GoodsEditInfoQueryCondition condition) {
        LogUtils.info(log, "查询商品修改申请列表 condition:{}", condition);

        Asserts.check(condition.getStartApplyTime() != null && condition.getEndApplyTime() != null, "申请时间范围必选");

        List<Long> shopIds = Lists.newArrayList();
        PageView<GoodsEditListVo> pageView = new PageView<>(condition.getPageSize(), condition.getPageNow());
        if (StringUtils.isNotBlank(condition.getPrincipal())) {
            List<Long> principalShopIds = faMerchantsApplyService.lambdaQuery()
                    .eq(FaMerchantsApply::getPrincipal, condition.getPrincipal())
                    .list()
                    .stream().map(FaMerchantsApply::getId).distinct().collect(Collectors.toList());
            // 小二没有商家
            if (CollectionUtils.isEmpty(principalShopIds)) {
                return pageView;
            }

            if (CollectionUtils.isNotEmpty(principalShopIds)) {
                // 没有交集
                if (condition.getShopId() != null && !principalShopIds.contains(condition.getShopId())) {
                    return pageView;
                }
            }

            shopIds.addAll(principalShopIds);
        }

        // 1. 有交集取交集 2. 没有小二直接用传递的商家shopId
        if (condition.getShopId() != null) {
            shopIds = Lists.newArrayList(condition.getShopId());
        }

        if ("flashdeals".equals(condition.getSpecialTag())) {
            condition.setSpecialTag("flashDeal");
        }

        List<String> specialTags = Lists.newArrayList();
        if (StringUtils.isNotBlank(condition.getSpecialTag())) {
            specialTags = searchTypeMatchConfigService.lambdaQuery()
                    .eq(SearchTypeMatchConfig::getType, 1)
                    .eq(SearchTypeMatchConfig::getSearchWord, condition.getSpecialTag())
                    .list()
                    .stream().map(SearchTypeMatchConfig::getMatchWord).collect(Collectors.toList());
        }

        List<Long> goodsIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(condition.getGoodsIdListStr())) {
            goodsIds = Arrays.stream(condition.getGoodsIdListStr().split("\n")).map(Long::parseLong).collect(Collectors.toList());
        }

        List<Long> categoryIds = Lists.newArrayList();
        if (condition.getCategoryId() != null) {
            categoryIds.add(condition.getCategoryId());
            List<Category> categoryList = categoryService.queryAllChildCategoryByParentId(condition.getCategoryId());
            categoryList.stream().map(Category::getId).forEach(categoryIds::add);
        }

        int searchTypes = 0;
//        List<Integer> possibleTypes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(condition.getTypes())) {
            searchTypes = condition.getTypes().stream().mapToInt(Integer::valueOf).sum();
//            possibleTypes = MathUtils.listAllCombinationSumList(GoodsEditTypeEnums.listAllTypeCodes(), condition.getTypes());
        }
//        LogUtils.info(log, "possibleTypes.size:{} ===> include:{}", possibleTypes.size(), possibleTypes);

        IPage<GoodsEditInfo> page = goodsEditInfoService.lambdaQuery().select(GoodsEditInfo::getGoodsId,
                GoodsEditInfo::getId, GoodsEditInfo::getShopId, GoodsEditInfo::getCategoryId, GoodsEditInfo::getIsDel,
                GoodsEditInfo::getShopName, GoodsEditInfo::getSpecialTag, GoodsEditInfo::getType, GoodsEditInfo::getAddPrice,
                GoodsEditInfo::getApplyReason, GoodsEditInfo::getApplyTime, GoodsEditInfo::getApplyUser, GoodsEditInfo::getAuditTime,
                GoodsEditInfo::getAuditUser, GoodsEditInfo::getRejectReason, GoodsEditInfo::getStatus, GoodsEditInfo::getUpdateTime)
                .eq(GoodsEditInfo::getIsDel, 0)
                .in(CollectionUtils.isNotEmpty(goodsIds), GoodsEditInfo::getGoodsId, goodsIds)
                .in(CollectionUtils.isNotEmpty(shopIds), GoodsEditInfo::getShopId, shopIds)
                .in(CollectionUtils.isNotEmpty(categoryIds), GoodsEditInfo::getCategoryId, categoryIds)
//                .eq(StringUtils.isNotBlank(condition.getShopName()), GoodsEditInfo::getShopName, condition.getShopName())
                .eq(Objects.nonNull(condition.getStatus()), GoodsEditInfo::getStatus, condition.getStatus())
                .eq(searchTypes > 0, GoodsEditInfo::getType, searchTypes)
//                .apply(Objects.nonNull(condition.getSpecialTag()), "FIND_IN_SET ('" + condition.getSpecialTag() + "',special_tag)")
                .in(CollectionUtils.isNotEmpty(specialTags), GoodsEditInfo::getSpecialTag, specialTags)
                .eq(StringUtils.isNotBlank(condition.getAuditUser()), GoodsEditInfo::getAuditUser, condition.getAuditUser())
                .ge(Objects.nonNull(condition.getStartApplyTime()), GoodsEditInfo::getApplyTime, condition.getStartApplyTime())
                .le(Objects.nonNull(condition.getEndApplyTime()), GoodsEditInfo::getApplyTime, condition.getEndApplyTime())
//                .ge(Objects.nonNull(condition.getStartAuditTime()), GoodsEditInfo::getAuditTime, condition.getStartAuditTime())
//                .le(Objects.nonNull(condition.getEndAuditTime()), GoodsEditInfo::getAuditTime, condition.getEndAuditTime())
                .select()
                .orderByDesc(GoodsEditInfo::getId)
                .page(new Page<>(condition.getPageNow(), condition.getPageSize()));

        List<GoodsEditInfo> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return pageView;
        }

        goodsIds = records.stream().map(GoodsEditInfo::getGoodsId).collect(Collectors.toList());
        LogUtils.info(log, "当前页goodsIds:{}", goodsIds);

        List<Goods> goodsList = goodsService.queryGoodsByAllIds(goodsIds);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

        shopIds = goodsList.stream().map(Goods::getShopId).collect(Collectors.toList());
        Map<Long, String> shopPrincipalMap = faMerchantsApplyService.listByIds(shopIds).stream()
                .filter(faMerchantsApply -> StringUtils.isNotBlank(faMerchantsApply.getPrincipal()))
                .collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getPrincipal, (v1, v2) -> v1));

        categoryIds = goodsList.stream().map(Goods::getCategoryId).collect(Collectors.toList());
        Map<Long, String> categoryPathMap = categoryTreeCoreService.getCategoryPathByIds(categoryIds);

        Map<Long, Long> goodsIdListingIdMap = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getGoodsId, goodsIds)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .select(ListingFollowGoods::getGoodsId, ListingFollowGoods::getListingId)
                .list()
                .stream().collect(Collectors.toMap(ListingFollowGoods::getGoodsId, ListingFollowGoods::getListingId, (v1, v2) -> v1));

        List<GoodsEditListVo> vos = Lists.newArrayList();
        for (GoodsEditInfo info : records) {
            GoodsEditListVo vo = new GoodsEditListVo();
            vo.setId(info.getId());
            vo.setShopName(info.getShopName());
            vo.setPrincipal(shopPrincipalMap.get(info.getShopId()));
            vo.setGoodsId(info.getGoodsId());

            Goods goods = goodsMap.get(info.getGoodsId());
            if (goods != null) {
                vo.setMainImage(goods.getMainImage());
                vo.setGoodsName(goods.getName());
                vo.setCategoryId(goods.getCategoryId());
                vo.setCategory(categoryPathMap.get(goods.getCategoryId()));
            }

            vo.setType(info.getType());
            vo.setTypeInfo(GoodsEditTypeEnums.getMsg(info.getType()));
            vo.setSpecialTags(info.getSpecialTag());
            if (StringUtils.isNotBlank(info.getSpecialTag())) {
                List<GoodsEditListVo.SpecialTagDto> specialTagDtoList = Lists.newArrayList();
                for (String s : info.getSpecialTag().split(",")) {
                    if (s.equals("listing") && goodsIdListingIdMap.containsKey(info.getGoodsId())) {
                        specialTagDtoList.add(new GoodsEditListVo.SpecialTagDto("listing", goodsIdListingIdMap.get(info.getGoodsId()).toString()));
                    } else {
                        specialTagDtoList.add(new GoodsEditListVo.SpecialTagDto("锁定", s));
                    }
                }
                vo.setSpecialTagDetailList(specialTagDtoList);
            }
            vo.setApplyReason(info.getApplyReason());
            vo.setStatus(info.getStatus());
            vo.setApplyTime(info.getApplyTime());
            vo.setAuditTime(info.getAuditTime());
            vo.setAuditUser(info.getAuditUser());
            vos.add(vo);
        }

        pageView.setRowCount(page.getTotal());
        pageView.setRecords(vos);
        return pageView;
    }

    @Override
    public List<GoodsEditHistoryVo> listHistory(GoodsIdCondition condition) {
        LogUtils.info(log, "修改审批历史 :{}", condition);
        CheckUtils.notNull(condition.getGoodsId(), ProductResultCode.PARAMETER_ID_ERROR);

        List<GoodsEditInfo> goodsEditInfoList = goodsEditInfoService.lambdaQuery()
                .eq(GoodsEditInfo::getGoodsId, condition.getGoodsId())
                .eq(GoodsEditInfo::getIsDel, 0)
                .select(GoodsEditInfo::getId, GoodsEditInfo::getType, GoodsEditInfo::getApplyReason, GoodsEditInfo::getStatus, GoodsEditInfo::getRejectReason, GoodsEditInfo::getApplyTime, GoodsEditInfo::getAuditTime)
                .orderByDesc(GoodsEditInfo::getId)
                .list();
        if (CollectionUtils.isEmpty(goodsEditInfoList)) {
            return Collections.emptyList();
        }

        List<GoodsEditHistoryVo> vos = BeanCopyUtil.transformList(goodsEditInfoList, GoodsEditHistoryVo.class);
        vos.forEach(vo -> vo.setTypeName(GoodsEditTypeEnums.getMsg(vo.getType())));
        return vos;
    }

    @Override
    public GoodsEditInfoDetailVo detail(Long id) {
        LogUtils.info(log, "修改记录详情 id:{}", id);
        CheckUtils.notNull(id, ProductResultCode.PARAMETER_ID_ERROR);

        GoodsEditInfo goodsEditInfo = goodsEditInfoService.getById(id);
        Integer type = goodsEditInfo.getType();
        List<Integer> typeList = MathUtils.splitBinary(type);
//        String content = goodsEditInfo.getContent();

        GoodsEditInfoDetail goodsEditInfoDetail = goodsEditInfoDetailService.getByEditId(goodsEditInfo.getId());
        CheckUtils.notNull(goodsEditInfoDetail, ProductResultCode.GOODS_EDIT_DETAIL_NOT_EXIST);
        String content = goodsEditInfoDetail.getContent();

        Goods goods = goodsService.getById(goodsEditInfo.getGoodsId());

        GoodsEditInfoDetailVo detailVo = new GoodsEditInfoDetailVo();
        detailVo.setGoodsId(goodsEditInfo.getGoodsId());
        detailVo.setName(goods.getName());
        detailVo.setMainImage(goods.getMainImage());
        detailVo.setApplyReason(goodsEditInfo.getApplyReason());
        detailVo.setTypeList(typeList);

        if (typeList.contains(GoodsEditTypeEnums.DETAIL.getCode())) {
            GoodsEditDetailVo goodsEditPropertyVo = JSON.parseObject(content, GoodsEditDetailVo.class);

            //categoryPath
            List<Long> categoryIds = Lists.newArrayList(goodsEditPropertyVo.getOldCategoryId(), goodsEditPropertyVo.getNewCategoryId())
                    .stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(categoryIds)) {
                Map<Long, String> categoryTree = categoryTreeCoreService.getCategoryTreeStrByCategoryId(categoryIds);
                goodsEditPropertyVo.setOldCategoryPath(categoryTree.get(goodsEditPropertyVo.getOldCategoryId()));
                goodsEditPropertyVo.setNewCategoryPath(categoryTree.get(goodsEditPropertyVo.getNewCategoryId()));
            }

            //brandName
            Map<Long, String> brandNameMap = Maps.newHashMap();
            List<Long> brandIds = Lists.newArrayList(goodsEditPropertyVo.getOldBrandId(), goodsEditPropertyVo.getNewBrandId()).stream().filter(aLong -> aLong != null && aLong != 0).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(brandIds)) {
                brandNameMap = merchantsBrandStoreService.listByIds(brandIds).stream().collect(Collectors.toMap(MerchantsBrandStore::getId, MerchantsBrandStore::getBrandName, (v1, v2) -> v1));
                goodsEditPropertyVo.setOldBrandName(goodsEditPropertyVo.getOldBrandId() == null || goodsEditPropertyVo.getOldBrandId() == 0 ? "noBrand" : brandNameMap.get(goodsEditPropertyVo.getOldBrandId()));
                goodsEditPropertyVo.setNewBrandName(goodsEditPropertyVo.getNewBrandId() == null || goodsEditPropertyVo.getNewBrandId() == 0 ? "noBrand" : brandNameMap.get(goodsEditPropertyVo.getNewBrandId()));
            }

            //sizeChartData
            Map<Long, SizeChartTemplateVo> sizeCharDataMap = Maps.newHashMap();
            List<Long> sizeChartTemplateIds = Lists.newArrayList(goodsEditPropertyVo.getOldSizeChartTemplateId(), goodsEditPropertyVo.getNewSizeChartTemplateId()).stream().filter(aLong -> aLong != null && aLong != 0).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sizeChartTemplateIds)) {
                List<SizeChartTemplateVo> sizeChartTemplateVos = buildSizeChartTemplateVo(sizeChartTemplateIds);
                if (CollectionUtils.isNotEmpty(sizeChartTemplateVos)) {
                    sizeCharDataMap = sizeChartTemplateVos.stream().collect(Collectors.toMap(SizeChartTemplateVo::getId, Function.identity(), (v1, v2) -> v1));
                }
            }
            goodsEditPropertyVo.setOldSizeChartTemplateData(goodsEditPropertyVo.getOldSizeChartTemplateId() == null ? null : sizeCharDataMap.get(goodsEditPropertyVo.getOldSizeChartTemplateId()));
            goodsEditPropertyVo.setNewSizeChartTemplateData(goodsEditPropertyVo.getNewSizeChartTemplateId() == null ? null : sizeCharDataMap.get(goodsEditPropertyVo.getNewSizeChartTemplateId()));
            detailVo.setGoodsEditDetailVo(goodsEditPropertyVo);

        } else if (typeList.contains(GoodsEditTypeEnums.PROPERTY.getCode())){
            GoodsEditPropertyDto editPropertyDto = JSON.parseObject(content, GoodsEditPropertyDto.class);
            List<GoodsEditPropertyVo.PropertySnap> oldProperties = editPropertyDto.getOldPropertyList().stream().map(propertyDTO -> {
                GoodsEditPropertyVo.PropertySnap propertySnap = new GoodsEditPropertyVo.PropertySnap(propertyDTO.getId(), propertyDTO.getName());
                List<GoodsEditPropertyVo.PropertyValueSnap> valueSnaps = propertyDTO.getValues().stream().map(valueDTO -> new GoodsEditPropertyVo.PropertyValueSnap(valueDTO.getId(), valueDTO.getValue(), valueDTO.getImgUrl())).collect(Collectors.toList());
                propertySnap.setValues(valueSnaps);
                return propertySnap;
            }).collect(Collectors.toList());
            List<GoodsEditPropertyVo.PropertySnap> newProperties = editPropertyDto.getNewPropertyList().stream().map(propertyDTO -> {
                GoodsEditPropertyVo.PropertySnap propertySnap = new GoodsEditPropertyVo.PropertySnap(propertyDTO.getId(), propertyDTO.getName());
                List<GoodsEditPropertyVo.PropertyValueSnap> valueSnaps = propertyDTO.getValues().stream().map(valueDTO -> new GoodsEditPropertyVo.PropertyValueSnap(valueDTO.getId(), valueDTO.getValue(), valueDTO.getImgUrl())).collect(Collectors.toList());
                propertySnap.setValues(valueSnaps);
                return propertySnap;
            }).collect(Collectors.toList());
            List<GoodsEditPropertyVo.SkuSnap> oldSkuList = editPropertyDto.getOldSkuList().stream().map(goodsItemDTO -> new GoodsEditPropertyVo.SkuSnap(goodsItemDTO.getId(), goodsItemDTO.getName(), goodsItemDTO.getOriginalSkuId(), goodsItemDTO.getOrginalPrice(), goodsItemDTO.getStock(), goodsItemDTO.getSkuImage())).collect(Collectors.toList());
            List<GoodsEditPropertyVo.SkuSnap> newSkuList = editPropertyDto.getNewSkuList().stream().map(goodsItemDTO -> new GoodsEditPropertyVo.SkuSnap(goodsItemDTO.getId(), goodsItemDTO.getName(), goodsItemDTO.getOriginalSkuId(), goodsItemDTO.getOrginalPrice(), goodsItemDTO.getStock(), goodsItemDTO.getImageUrl())).collect(Collectors.toList());
            GoodsEditPropertyVo goodsEditPropertyVo = new GoodsEditPropertyVo(oldProperties, newProperties, oldSkuList, newSkuList);


            detailVo.setGoodsEditPropertyVo(goodsEditPropertyVo);

        } else if (typeList.contains(GoodsEditTypeEnums.REAL_SHOT_IMG.getCode())){
            GoodsEditRealShotImgDTO goodsEditRealShotImgDTO = JSON.parseObject(content, GoodsEditRealShotImgDTO.class);
            GoodsEditRealShotImgVO goodsEditRealShotImgVO = new GoodsEditRealShotImgVO();
            if (goodsEditRealShotImgDTO != null){
                goodsEditRealShotImgVO.setOldRealShotImgs(goodsEditRealShotImgDTO.getOldRealShotImgs());
                goodsEditRealShotImgVO.setNewRealShotImgs(goodsEditRealShotImgDTO.getNewRealShotImgs());
            }
            detailVo.setGoodsEditRealShotImgVO(goodsEditRealShotImgVO);
        }else {
            List<GoodsItem> goodsItems = goodsItemService.queryGoodsItemByGoodsId(goodsEditInfo.getGoodsId());
            Map<Long, String> itemNameMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getId, GoodsItem::getName, (v1, v2) -> v1));
            GoodsEditPriceVo goodsEditPriceVo = JSON.parseObject(content, GoodsEditPriceVo.class);
            if (CollectionUtils.isNotEmpty(goodsEditPriceVo.getSkuChangeDtoList())) {
                goodsEditPriceVo.getSkuChangeDtoList().forEach(skuChangeVo -> skuChangeVo.setName(itemNameMap.get(skuChangeVo.getId())));
            }

            detailVo.setGoodsEditPriceVo(goodsEditPriceVo);
        }
        return detailVo;
    }

    private List<SizeChartTemplateVo> buildSizeChartTemplateVo(List<Long> sizeChartTemplateIds) {
        List<SizeChartTemplate> allShopSizeChartTemplateList = Lists.newArrayList(sizeChartTemplateService.listByIds(sizeChartTemplateIds));
        if (CollectionUtils.isEmpty(allShopSizeChartTemplateList)) {
            return Collections.emptyList();
        }

        List<Long> systemTemplateIds = allShopSizeChartTemplateList.stream().map(SizeChartTemplate::getRuleId).collect(Collectors.toList());
        Map<Long, List<SizeChartPropVO>> templatePropGroupMap = sizeChartPropService.lambdaQuery()
                .in(SizeChartProp::getTemplateId, systemTemplateIds)
                .list()
                .stream()
                .map(sizeChartProp -> BeanCopyUtil.transform(sizeChartProp, SizeChartPropVO.class))
                .collect(Collectors.groupingBy(SizeChartPropVO::getTemplateId));

        List<SizeChartTemplateVo> vos = BeanCopyUtil.transformList(allShopSizeChartTemplateList, SizeChartTemplateVo.class);
        for (SizeChartTemplateVo sizeChartTemplateVo : vos) {
            List<SizeChartPropVO> sizeChartPropVOS = templatePropGroupMap.get(sizeChartTemplateVo.getRuleId());
            if (CollectionUtils.isNotEmpty(sizeChartPropVOS)) {
                Map<Integer, List<SizeChartPropVO>> propMap = sizeChartPropVOS.stream().collect(Collectors.groupingBy(SizeChartPropVO::getType, Collectors.toList()));
                List<SizeChartPropVO> propList = propMap.get(SizeTemplatePropAreaEnum.PROP.getCode());
                if (CollectionUtils.isNotEmpty(propList)) {
                    sizeChartTemplateVo.setPropList(propList);
                    sizeChartTemplateVo.setProp(propList.stream().map(SizeChartPropVO::getName).collect(Collectors.joining(";")));
                }
                List<SizeChartPropVO> areaList = propMap.get(SizeTemplatePropAreaEnum.AREA.getCode());
                if (CollectionUtils.isNotEmpty(areaList)) {
                    sizeChartTemplateVo.setAreaList(areaList);
                    sizeChartTemplateVo.setArea(areaList.stream().map(SizeChartPropVO::getName).collect(Collectors.joining(";")));
                }
            }
        }
        return vos;
    }

    @Override
    public Boolean revoke(IdCondition condition) {
        LogUtils.info(log, "撤销审批:{}", condition);
        CheckUtils.notNull(condition.getId(), ProductResultCode.PARAMETER_ID_ERROR);

        GoodsEditInfo goodsEditInfo = goodsEditInfoService.getById(condition.getId());
        CheckUtils.notNull(goodsEditInfo, ProductResultCode.GOODS_EDIT_APPLY_NOT_EXIST);
        CheckUtils.check(goodsEditInfo.getStatus() != 0, ProductResultCode.GOODS_EDIT_APPLY_ALREADY_AUDIT);

        return goodsEditInfoService.deleteById(condition.getId());
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Boolean pass(GoodsEditAuditCondition condition) {
        LogUtils.info(log, "审批通过:{}", condition);
        CheckUtils.isEmpty(condition.getIds(), ProductResultCode.PARAMETER_ID_ERROR);

        List<GoodsEditInfo> goodsEditInfoList = Lists.newArrayList(goodsEditInfoService.selectByIds(condition.getIds()));
        if (condition.getType() == 1) {
            goodsEditInfoList.forEach(goodsEditInfo -> CheckUtils.check(goodsEditInfo.getStatus() != 0, ProductResultCode.GOODS_EDIT_APPLY_ALREADY_AUDIT));
        }

        List<GoodsEditInfoDetail> goodsEditInfoDetails = goodsEditInfoDetailService.getByEditIds(condition.getIds());
        Map<Long, String> editContentMap = goodsEditInfoDetails.stream().collect(Collectors.toMap(GoodsEditInfoDetail::getEditId, GoodsEditInfoDetail::getContent, (v1, v2) -> v1));

        String operator = StringUtils.isNotBlank(condition.getOperator()) ? condition.getOperator() : getUserName();
//        List<GoodsOperationLogDto> operationLogDtoList = Lists.newArrayList();
//        List<GoodsSyncModel> goodsSyncModelList = Lists.newArrayList();
        for (GoodsEditInfo goodsEditInfo : goodsEditInfoList) {
            Long goodsId = goodsEditInfo.getGoodsId();
            Integer type = goodsEditInfo.getType();
            List<Integer> typeList = MathUtils.splitBinary(type);
//            String content = goodsEditInfo.getContent();
            String content = editContentMap.get(goodsEditInfo.getId());
            LogUtils.info(log, "开始审批通过 goodsId:{}", goodsId);

            if (StringUtils.isNotBlank(goodsEditInfo.getSpecialTag()) && goodsEditInfo.getSpecialTag().contains("listing") && condition.getType() == 1) {
                Long userId = getUserId();
                //暂时写死 只有旭东与赵晗 zhaojiajun 有权限审核listing 商品
                LogUtils.info(log, "拥有listing审核权限的user:{}", listingAuditUserIdList);
                CheckUtils.check(!"test".equalsIgnoreCase(env) && (CollectionUtils.isEmpty(listingAuditUserIdList) || !listingAuditUserIdList.contains(userId)), ProductResultCode.GOODS_EDIT_LISTING_UNAUTHORIZED);
            }

            GoodsOperationLogDto goodsOperationLogDto;
            Boolean success;

            if (typeList.contains(GoodsEditTypeEnums.DETAIL.getCode())) {
                //商详修改
                success = transactionTemplate.execute(transactionStatus -> {
                    try {
                        passUpdateGoodsDetail(goodsId, goodsEditInfo, content);
                    } catch (Exception e) {
                        transactionStatus.setRollbackOnly();
                        log.info("pass修改审批1 transaction error goodsId:{}", goodsId, e);
                        return Boolean.FALSE;
                    }
                    return Boolean.TRUE;
                });

                goodsOperationLogDto = new GoodsOperationLogDto()
                        .goodsId(goodsId)
                        .type(OperationLogTypeEnums.UPDATE_GOODS_DETAIL)
                        .newData(content)
                        .content(OperationLogTypeEnums.UPDATE_GOODS_DETAIL.getDesc())
                        .status(1)
                        .user(goodsEditInfo.getApplyUser());
//                operationLogDtoList.add(goodsOperationLogDto);

            } else if (typeList.contains(GoodsEditTypeEnums.PROPERTY.getCode())){
                //规格修改
                success = transactionTemplate.execute(transactionStatus -> {
                    try {
                        passUpdateGoodsProperty(goodsId, goodsEditInfo, content);
                    } catch (Exception e) {
                        transactionStatus.setRollbackOnly();
                        log.info("pass修改审批2 transaction error goodsId:{}", goodsId, e);
                        return Boolean.FALSE;
                    }
                    return Boolean.TRUE;
                });

                goodsOperationLogDto = new GoodsOperationLogDto()
                        .goodsId(goodsId)
                        .type(OperationLogTypeEnums.UPDATE_GOODS_PROPERTY)
                        .newData(content)
                        .content(OperationLogTypeEnums.UPDATE_GOODS_PROPERTY.getDesc())
                        .status(1)
                        .user(goodsEditInfo.getApplyUser());
//                operationLogDtoList.add(goodsOperationLogDto);

            } else {
                //价格修改
                success = transactionTemplate.execute(transactionStatus -> {
                    try {
                        passUpdateGoodsPrice(goodsId, goodsEditInfo, content);
                    } catch (Exception e) {
                        transactionStatus.setRollbackOnly();
                        log.info("pass修改审批3 transaction error goodsId:{}", goodsId, e);
                        return Boolean.FALSE;
                    }
                    return Boolean.TRUE;
                });

                goodsOperationLogDto = new GoodsOperationLogDto()
                        .goodsId(goodsId)
                        .type(OperationLogTypeEnums.UPDATE_PRICE_AND_STOCK)
                        .newData(content)
                        .content(OperationLogTypeEnums.UPDATE_PRICE_AND_STOCK.getDesc())
                        .status(1)
                        .user(goodsEditInfo.getApplyUser());
//                operationLogDtoList.add(goodsOperationLogDto);
            }

            CheckUtils.check(!success, ProductResultCode.EXECUTE_FAIL);

            goodsEditInfo.setStatus(1);
            goodsEditInfo.setAuditTime(LocalDateTime.now());
            goodsEditInfo.setAuditUser(operator);
            goodsEditInfo.setUpdateTime(LocalDateTime.now());
            goodsEditInfoService.updateById(goodsEditInfo);

            // 更新推荐日志审核状态
            recommendCoreService.updateAuditStatus(goodsEditInfo);

            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(goodsId);
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("修改商品审批通过");
            goodsSyncModel.setSourceService("vp");
            mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.TEN_SEC);
//            goodsSyncModelList.add(goodsSyncModel);

            mqSender.sendDelay("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto, MqDelayLevel.TEN_SEC);

            LogUtils.info(log, "审批通过完成 goodsId:{}", goodsId);
        }
//        goodsSyncModelList.forEach(goodsSyncModel -> mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.TEN_SEC));
//        operationLogDtoList.forEach(goodsOperationLogDto -> mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto));

        return true;
    }

    private void passUpdateGoodsPrice(Long goodsId, GoodsEditInfo goodsEditInfo, String content) {
        LogUtils.info(log, "通过并修改商品价格 goodsId:{} start", goodsId);
//        String content = goodsEditInfo.getContent();
        GoodsEditPriceDto goodsEditPriceDto = JSON.parseObject(content, GoodsEditPriceDto.class);
        LogUtils.info(log, "通过并修改商品价格 goodsEditPriceDto :{}", goodsEditPriceDto);

        Goods goods = goodsService.getById(goodsId);
        boolean isWholeSale = goods.getType() == 3;
        List<GoodsFreight> goodsFreights = goodsFreightService.queryByGoodsId(goodsId);

        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(goodsEditInfo.getShopId());
        boolean isWarehouseOverSea = faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.DIRECT.getCode());

        Map<String, BigDecimal> newFreightUpdateMap = CollectionUtils.isEmpty(goodsEditPriceDto.getFreightChangeDtoList()) ? Maps.newHashMap() : goodsEditPriceDto.getFreightChangeDtoList().stream()
                .collect(Collectors.toMap(GoodsEditPriceDto.FreightChangeDto::getCountry, GoodsEditPriceDto.FreightChangeDto::getNewFreight, (v1, v2) -> v1));

        List<GoodsFreightVO> freightList = null;
        if (CollectionUtils.isNotEmpty(goodsEditPriceDto.getFreightChangeDtoList()) || MapUtils.isNotEmpty(goodsEditPriceDto.getFreightGradientDto())) {
            if (MapUtils.isNotEmpty(newFreightUpdateMap)) {
                    freightList = newFreightUpdateMap.entrySet().stream().map(entry -> {
                        GoodsFreightVO freightVO = new GoodsFreightVO();
                        freightVO.setCode(entry.getKey());
                        freightVO.setCurrentFreight(entry.getValue());
                        return freightVO;
                    }).collect(Collectors.toList());
            }

            ProductInfoInput goodsInfoInput = new ProductInfoInput();
            goodsInfoInput.setId(goodsId);
            goodsInfoInput.setType(goods.getType());
            goodsInfoInput.setFreightList(freightList);
            goodsInfoInput.setFreightGradientDto(goodsEditPriceDto.getFreightGradientDto());
            goodsFreightCoreService.saveOrUpdateFreight(goodsInfoInput);
        } else {
            freightList = goodsFreights.stream().map(goodsFreight -> {
                GoodsFreightVO freightVO = new GoodsFreightVO();
                freightVO.setCode(goodsFreight.getCode());
                freightVO.setCurrentFreight(goodsFreight.getCurrentFreight());
                return freightVO;
            }).collect(Collectors.toList());
        }


        BigDecimal defaultDelivery = BigDecimal.ZERO;
        //todo 批发商品 默认运费
        if (CollectionUtils.isNotEmpty(freightList) && goods.getType() != 3) {
            defaultDelivery = freightList.stream()
                    .filter(goodsFreight -> goodsFreight.getCode().equals(CountryEnums.DE.getCode()))
                    .map(GoodsFreight::getCurrentFreight)
                    .findAny().orElse(BigDecimal.ZERO);
        }

        Map<Long, GoodsEditPriceDto.SkuChangeDto> skuChangeDtoMap = Maps.newHashMap();
        List<GoodsEditPriceDto.SkuChangeDto> skuChangeDtoList = goodsEditPriceDto.getSkuChangeDtoList();
        if (CollectionUtils.isNotEmpty(skuChangeDtoList)) {
            skuChangeDtoMap = skuChangeDtoList.stream().collect(Collectors.toMap(GoodsEditPriceDto.SkuChangeDto::getId, Function.identity(), (v1, v2) -> v1));
        }

        List<GoodsItemGradient> goodsItemGradientList = Lists.newArrayList();
        List<GoodsItem> goodsItems = goodsItemService.queryGoodsItemByGoodsId(goodsId);

        List<GoodsSkuPo> goodsSkuList = goodsSkuService.findListByGoodsId(goodsId);
        if (CollectionUtils.isEmpty(goodsSkuList)) {
            goodsSkuList = cleaningGoodsItemBiz.cleaningByGoodsItem(goodsId, goodsItems);
        }

        Map<Long, GoodsSkuPo> goodsSkuMap = goodsSkuList.stream().collect(Collectors.toMap(GoodsSkuPo::getId, Function.identity()));
        AliGoodsCategoryPriceConfig aliGoodsCategoryPriceConfig = null;
        if (isWholeSale) {
//            Category category = categoryService.selectById(goods.getCategoryId());
//            List<Long> categoryIds = Lists.newArrayList();
//            if (org.apache.commons.lang.StringUtils.isNotBlank(category.getPids())) {
//                Arrays.stream(category.getPids().split(",")).forEach(s -> categoryIds.add(Long.parseLong(s)));
//            }
//            categoryIds.add(goods.getCategoryId());

            double avgPrice = skuChangeDtoList.stream().mapToDouble(skuUpdateDto -> skuUpdateDto.getNewCostPrice().doubleValue()).average().orElse(0d);
            LogUtils.info(log, "updatePriceAndStock批发商品 avgPrice:{}", avgPrice);
            aliGoodsCategoryPriceConfig = aliGoodsCategoryPriceConfigService.lambdaQuery()
                    .eq(AliGoodsCategoryPriceConfig::getLastCategoryId, goods.getCategoryId())
                    .le(AliGoodsCategoryPriceConfig::getStartPrice, avgPrice)
                    .gt(AliGoodsCategoryPriceConfig::getEndPrice, avgPrice)
                    .one();
            LogUtils.info(log, "updatePriceAndStock批发商品 aliGoodsCategoryPriceConfig.1:{}", aliGoodsCategoryPriceConfig);
            if (aliGoodsCategoryPriceConfig == null) {
                aliGoodsCategoryPriceConfig = aliGoodsCategoryPriceConfigService.lambdaQuery()
                        .eq(AliGoodsCategoryPriceConfig::getLastCategoryId, 0)
                        .le(AliGoodsCategoryPriceConfig::getStartPrice, avgPrice)
                        .gt(AliGoodsCategoryPriceConfig::getEndPrice, avgPrice)
                        .one();
                LogUtils.info(log, "updatePriceAndStock批发商品 aliGoodsCategoryPriceConfig.2:{}", aliGoodsCategoryPriceConfig);
            }
            CheckUtils.notNull(aliGoodsCategoryPriceConfig, ProductResultCode.SPIDER_1688_NOT_HIT_PRICE_MODEL);
        }

        for (GoodsItem goodsItem : goodsItems) {
            GoodsEditPriceDto.SkuChangeDto skuChangeDto = skuChangeDtoMap.get(goodsItem.getId());
            if (skuChangeDto != null) {
                goodsItem.setOrginalPrice(skuChangeDto.getNewPrice());
                goodsItem.setCostPrice(skuChangeDto.getNewCostPrice());
                goodsItem.setStock(skuChangeDto.getNewStock());
                goodsItem.setSkuStatus(skuChangeDto.getNewSkuStatus());
            }
            if (isWarehouseOverSea && skuChangeDto != null && skuChangeDto.getNewDefaultDelivery() != null) {
                goodsItem.setDefaultDelivery(skuChangeDto.getNewDefaultDelivery());
            } else if (!isWarehouseOverSea) {
                goodsItem.setDefaultDelivery(defaultDelivery);
            }
            goodsItem.setPrice(goodsItem.getOrginalPrice().add(goodsItem.getDefaultDelivery()));
            goodsItem.setWeight(goodsEditPriceDto.getNewWeight() == null ? goodsItem.getWeight() : GoodsExtDetailUtils.getWeight(goodsEditPriceDto.getNewWeight().toString()));
            goodsItem.setUpdateTime(new Date());

            if (MapUtils.isNotEmpty(goodsSkuMap)) {
                GoodsSkuPo goodsSku = goodsSkuMap.get(goodsItem.getSkuId());
                if (goodsSku != null) {
                    goodsSku.setOriginalPrice(goodsItem.getOrginalPrice());
                    goodsSku.setCostPrice(goodsItem.getCostPrice());
                    goodsSku.setStock(goodsItem.getStock());
                    goodsSku.setSkuStatus(goodsItem.getSkuStatus());
                    goodsSku.setDefaultDelivery(goodsItem.getDefaultDelivery());
                    goodsSku.setPrice(goodsItem.getPrice());
                    goodsSku.setWeight(goodsItem.getWeight());
                }
            }

            if (isWholeSale) {
                BigDecimal price2 = goodsItem.getCostPrice()
                        .divide(new BigDecimal("7.5"), 4, RoundingMode.HALF_UP)
                        .add(defaultDelivery)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal("100").subtract(aliGoodsCategoryPriceConfig.getProfitRate().subtract(new BigDecimal("5"))).subtract(aliGoodsCategoryPriceConfig.getRefundRate()), 2, RoundingMode.HALF_UP)
                        .add(aliGoodsCategoryPriceConfig.getFreight())
                        .add(aliGoodsCategoryPriceConfig.getProfit())
                        .subtract(defaultDelivery)
                        .setScale(2, RoundingMode.HALF_UP);

                BigDecimal price3 = goodsItem.getCostPrice()
                        .divide(new BigDecimal("7.5"), 4, RoundingMode.HALF_UP)
                        .add(defaultDelivery)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal("100").subtract(aliGoodsCategoryPriceConfig.getProfitRate().subtract(new BigDecimal("10"))).subtract(aliGoodsCategoryPriceConfig.getRefundRate()), 2, RoundingMode.HALF_UP)
                        .add(aliGoodsCategoryPriceConfig.getFreight())
                        .add(aliGoodsCategoryPriceConfig.getProfit())
                        .subtract(defaultDelivery)
                        .setScale(2, RoundingMode.HALF_UP);

                BigDecimal price4 = goodsItem.getCostPrice()
                        .divide(new BigDecimal("7.5"), 4, RoundingMode.HALF_UP)
                        .add(defaultDelivery)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal("100").subtract(aliGoodsCategoryPriceConfig.getProfitRate().subtract(new BigDecimal("15"))).subtract(aliGoodsCategoryPriceConfig.getRefundRate()), 2, RoundingMode.HALF_UP)
                        .add(aliGoodsCategoryPriceConfig.getFreight())
                        .add(aliGoodsCategoryPriceConfig.getProfit())
                        .subtract(defaultDelivery)
                        .setScale(2, RoundingMode.HALF_UP);
                goodsItemGradientList.add(new GoodsItemGradient(goodsId, goodsItem.getId(), goodsItem.getSkuId(), 1, goodsItem.getPrice(), 1, 2));
                goodsItemGradientList.add(new GoodsItemGradient(goodsId, goodsItem.getId(), goodsItem.getSkuId(), 2, price2, 3, 5));
                goodsItemGradientList.add(new GoodsItemGradient(goodsId, goodsItem.getId(), goodsItem.getSkuId(), 3, price3, 6, 17));
                goodsItemGradientList.add(new GoodsItemGradient(goodsId, goodsItem.getId(), goodsItem.getSkuId(), 4, price4, 18, 999));
            }
        }
        goodsItemService.updateBatchById(goodsItems);
        if (CollectionUtils.isNotEmpty(goodsSkuList)) {
            goodsSkuService.updateBatchByIdAndGoodsId(goodsSkuList);
        }

        if (isWholeSale) {
            List<GoodsItemGradient> oldGoodsItemGradientList = goodsItemGradientService.lambdaQuery()
                    .eq(GoodsItemGradient::getGoodsId, goodsId)
                    .eq(GoodsItemGradient::getIsDel, 0)
                    .list();
            if (CollectionUtils.isNotEmpty(oldGoodsItemGradientList)) {
                oldGoodsItemGradientList.forEach(goodsItemGradient -> {
                    goodsItemGradient.setIsDel(1);
                    goodsItemGradient.setUpdateTime(LocalDateTime.now());
                });
                goodsItemGradientList.addAll(oldGoodsItemGradientList);
            }

            if (CollectionUtils.isNotEmpty(goodsItemGradientList)) {
                goodsItemGradientService.saveOrUpdateBatch(goodsItemGradientList);
            }
        }

        goods.setLogisticsProperty(goodsEditPriceDto.getNewLogisticProperty());
        goods.setCountry(goodsEditPriceDto.getNewCountry());
        goods.setUpdateTime(LocalDateTime.now());
        if (goodsEditPriceDto.getNewIsShow() != null) {
            goods.setIsShow(String.valueOf(goodsEditPriceDto.getNewIsShow()));
        }

        List<BigDecimal> priceList = goodsItems.stream().map(GoodsItem::getPrice).collect(Collectors.toList());
        priceList.stream().max(BigDecimal::compareTo).ifPresent(goods::setMaxPrice);
        priceList.stream().min(BigDecimal::compareTo).ifPresent(goods::setMinPrice);

        List<BigDecimal> costPriceList = goodsItems.stream().map(GoodsItem::getCostPrice).filter(Objects::nonNull).collect(Collectors.toList());
        costPriceList.stream().max(BigDecimal::compareTo).ifPresent(goods::setMaxCostPrice);
        costPriceList.stream().min(BigDecimal::compareTo).ifPresent(goods::setMinCostPrice);
        goodsService.updateById(goods);

        if (goodsEditPriceDto.getOldWeight() != null && goodsEditPriceDto.getNewWeight() != null && !goodsEditPriceDto.getOldWeight().equals(goodsEditPriceDto.getNewWeight())) {
            GoodsExtDetail goodsExtDetail = goodsExtDetailService.lambdaQuery().eq(GoodsExtDetail::getGoodsId, goodsId).one();
            goodsExtDetail.setWeight(GoodsExtDetailUtils.getWeight(goodsEditPriceDto.getNewWeight().toString()));
            goodsExtDetailService.updateById(goodsExtDetail);
        }

        List<GoodsExtConfig> goodsExtConfigList = goodsExtConfigService.selectGoodsTagConfig(Collections.singletonList(goodsId), CommonConstants.FLASH_DEAL_TAG_ID);
        List<Integer> typeList = MathUtils.splitBinary(goodsEditInfo.getType());
        if (CollectionUtils.isNotEmpty(goodsExtConfigList) && typeList.contains(GoodsEditTypeEnums.PRICE.getCode())) {
            //改价 --> 自动退出flashDeal
            LogUtils.info(log, "通过并修改商品价格 --> 自动退出flashDeal goodsId:{}", goodsId);

            //解除锁定标签
            UpdateLockLabelCondition condition = new UpdateLockLabelCondition(Collections.singletonList(goodsId), Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()),"自动解标签(改价自动退出flashDeal)", OperationLogTypeEnums.UNLOCK_AUTO.getCode());
            goodsLockCoreService.removeLockByApi(condition);
            LogUtils.info(log, "通过并修改商品价格 --> 自动退出flashDeal,解除锁定标签 goodsId:{}", goodsId);

            //删除活动标签
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setGoodsIds(Collections.singletonList(goodsId));
            bindTagDTO.setTagId(CommonConstants.FLASH_DEAL_TAG_ID);
            goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
            LogUtils.info(log, "通过并修改商品价格 --> 自动退出flashDeal,删除活动标签 goodsId:{}", goodsId);

            //退出活动
            Long activityId = activityRemoteClientFactory.exitFlashDeal(goodsId);
            LogUtils.info(log, "通过并修改商品价格 --> 自动退出flashDeal,退出活动 goodsId:{}, activityId:{}", goodsId, activityId);

            //删除活动价格备份信息
            if (activityId != null) {
                List<ActivityOriginalPrice> activityOriginalPriceList = activityOriginalPriceService.queryPriceByActivityIdAndGoodsId(activityId, goodsId);
                if (CollectionUtils.isNotEmpty(activityOriginalPriceList)) {
                    activityOriginalPriceService.deleteByIds(activityOriginalPriceList.stream().map(ActivityOriginalPrice::getId).collect(Collectors.toList()));
                    LogUtils.info(log, "通过并修改商品价格 --> 自动退出flashDeal,删除活动价格备份信息 goodsId:{}, size:{}", goodsId, activityOriginalPriceList.size());
                }
            }
        }
        LogUtils.info(log, "通过并修改商品价格 goodsId:{} end", goodsId);
    }

    private void passUpdateGoodsProperty(Long goodsId, GoodsEditInfo goodsEditInfo, String content) {
        LogUtils.info(log, "通过并修改商品规格 goodsId:{} start", goodsId);
        GoodsEditPropertyDto goodsEditPropertyDto = JSON.parseObject(content, GoodsEditPropertyDto.class);

        Goods goods = goodsService.getById(goodsId);
        GoodsExtDetail goodsExtDetail = goodsExtDetailService.lambdaQuery().eq(GoodsExtDetail::getGoodsId, goodsId).one();

        List<Long> pathCategoryIds = Lists.newArrayList(goods.getCategoryId());
        Category category = categoryService.getById(goods.getCategoryId());
        if (StringUtils.isNotBlank(category.getPids())) {
            pathCategoryIds.addAll(Arrays.stream(category.getPids().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }
        BigDecimal virtualDiscount = newAddGoodsCoreService.getVirtualDiscount(pathCategoryIds, goods.getShopId());

        ProductInfoInput goodsInfoInput = new ProductInfoInput();
        goodsInfoInput.setId(goodsId);
        goodsInfoInput.setProductId(goods.getProductId());
        goodsInfoInput.setIsShow(goods.getIsShow());
        goodsInfoInput.setPackageSize(GoodsExtDetailUtils.getPackageSize(goodsExtDetail.getPackageSize()));
        goodsInfoInput.setWeight(GoodsExtDetailUtils.getWeightNumber(goodsExtDetail.getWeight()));
        goodsInfoInput.setDiscount(virtualDiscount);
        goodsInfoInput.setProperties(goodsEditPropertyDto.getNewPropertyList());
        goodsInfoInput.setSkuInfo(goodsEditPropertyDto.getNewSkuList());
        GoodsInfoUtils.isPropertyValues(goodsInfoInput);

        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(goods.getShopId());
        GoodsInfoUtils.extractedShopInfo(goodsInfoInput, faMerchantsApply);
//        if (DeliveryTypeEnum.DIRECT.getCode().equals(faMerchantsApply.getDeliveryType())) {
//            //海外仓重量跟随sku重量
//            String weight = goodsInfoInput.getSkuInfo().stream().map(GoodsItem::getWeight).filter(Objects::nonNull).findFirst().orElse(null);
//            if (weight != null) {
//                goodsExtDetail.setWeight(GoodsExtDetailUtils.getWeight(weight));
//                goodsExtDetailService.updateById(goodsExtDetail);
//            }
//        }

        Map<Long, List<Long>> propValueMap = new LinkedHashMap<>();
        Map<Long, String> propValueNameMap = new LinkedHashMap<>();
        for (PropertyDTO propertyBO : goodsInfoInput.getProperties()) {
            propertyBO.setCategoryId(goods.getCategoryId());
            propertyInfoService.insertProperty(propertyBO);
            propertyInfoService.insertPropertyValue(goodsInfoInput, propertyBO, propValueMap, propValueNameMap);
        }

        Set<String> propertyValueStrList = Sets.newHashSet();
        for (PropertyDTO property : goodsInfoInput.getProperties()) {
            for (PropertyValueDTO value : property.getValues()) {
                propertyValueStrList.add(value.getValue());
            }
        }

        // 后续可以拿掉
        List<PropertyValueRecord> propertyValueRecordList = propertyValueRecordService.lambdaQuery()
                .in(PropertyValueRecord::getName, propertyValueStrList)
                .list();
        Map<String, Long> propertyValueRecordMap = propertyValueRecordList.stream().collect(Collectors.toMap(PropertyValueRecord::getName, PropertyValueRecord::getId, (v1, v2) -> v1));

        List<PropertyValueRecord> saveList = Lists.newArrayList();
        for (String propertyValue : propertyValueStrList) {
            Long propertyValueRecordId = propertyValueRecordMap.get(propertyValue);
            if (propertyValueRecordId == null) {
                saveList.add(new PropertyValueRecord(propertyValue));
            }
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(saveList)) {
            propertyValueRecordService.saveBatch(saveList);
            propertyValueRecordList.addAll(saveList);
            propertyValueRecordMap = propertyValueRecordList.stream().collect(Collectors.toMap(PropertyValueRecord::getName, PropertyValueRecord::getId, (v1, v2) -> v1));
        }
        goodsInfoInput.setPropertyValueRecordMap(propertyValueRecordMap);

        List<ProductSku> originalProductSkuList = productSkuService.queryProductSkuByProductId(goodsInfoInput.getProductId());
        List<GoodsItem> originalGoodsItemList = goodsItemService.queryGoodsItemByGoodsId(goodsInfoInput.getId());

        Map<String, Set<String>> originalPropertyMap = Maps.newHashMap();
        for (GoodsItem originalGoodsItem : originalGoodsItemList) {
            for (String propertyKV : originalGoodsItem.getPvalueStr().split(";")) {
                String[] propertyKvArr = propertyKV.split(":");
                Set<String> originalPropertyValueIds = originalPropertyMap.get(propertyKvArr[0]);
                if (CollectionUtils.isEmpty(originalPropertyValueIds)) {
                    originalPropertyValueIds = Sets.newHashSet();
                }
                originalPropertyValueIds.add(propertyKvArr[1]);
                originalPropertyMap.put(propertyKvArr[0], originalPropertyValueIds);
            }
        }

        List<PropertyDTO> originalPropertyList = propertyInfoService.queryOriginalProperty(originalPropertyMap);
        List<String> originalPropertyNameList = originalPropertyList.stream().map(Property::getName).collect(Collectors.toList());
        List<String> newPropertyNameList = goodsInfoInput.getProperties().stream().map(Property::getName).collect(Collectors.toList());
        Collection disjunction = CollectionUtils.disjunction(originalPropertyNameList, newPropertyNameList);

        if (CollectionUtils.isNotEmpty(disjunction)) {
            LogUtils.info(log,"===>存在规格大类的变更");

            originalProductSkuList.forEach(productSku -> productSku.setIsDel(1));
            productSkuService.updateBatchById(originalProductSkuList);
            goodsItemService.deleteByIds(originalGoodsItemList.stream().map(GoodsItem::getId).collect(Collectors.toList()));

            // 规格大类的变更要确定下，因为新逻辑会有别名的概念
            List<GoodsSkuPo> goodsSkuPos = goodsSkuHelper.changePropertyAndReturnSku(goodsId, goodsInfoInput);

            Map<String, Long> skuNameIdMap = goodsSkuPos.stream().collect(Collectors.toMap(GoodsSkuPo::getName, GoodsSkuPo::getId, (v1, v2) -> v2));

            List<ProductSku> newProductSkuList = ProductSkuGenerator.generateSkuData(goodsInfoInput.getProductId(), goodsInfoInput.getSkuMap(), goodsInfoInput.getProperties());
            CheckUtils.isEmpty(newProductSkuList, ProductResultCode.CREATE_SKU_ERROR);

            productSkuService.saveBatch(newProductSkuList);
            Map<String, ProductSku> newProductSkuIdMap = newProductSkuList.stream().collect(Collectors.toMap(ProductSku::getName, Function.identity(), (v1, v2) -> v1));

            List<GoodsItem> newGoodsItemList = goodsInfoInput.getSkuMap().values().stream()
                    .map(goodsItemDTO -> getGoodsItem(goodsInfoInput.getId(), newProductSkuIdMap.get(goodsItemDTO.getName()), goodsItemDTO, goodsInfoInput, skuNameIdMap.get(goodsItemDTO.getName())))
                    .collect(Collectors.toList());
            goodsItemService.saveBatch(newGoodsItemList);
            LogUtils.info(log,"===>规格大类变更-更新成功，return");
        }else {
            LogUtils.info(log,"===>规格大类不变");
            Map<String, GoodsItemDTO> skuMap = goodsInfoInput.getSkuMap();
            List<ProductSku> newProductSkuList = ProductSkuGenerator.generateSkuData(goods.getProductId(), skuMap, goodsInfoInput.getProperties());
            CheckUtils.isEmpty(newProductSkuList, ProductResultCode.CREATE_SKU_ERROR);

            if (CollectionUtils.isNotEmpty(originalProductSkuList)) {
                LogUtils.info(log,"passUpdateGoodsProperty ===> originalProductSkuList is not empty");
                Map<String, ProductSku> oldPValueMap = originalProductSkuList.stream().collect(Collectors.toMap(ProductSku::getPvalueDesc, item -> item, (v1, v2) -> v1));
                Map<String, ProductSku> newPValueMap = newProductSkuList.stream().collect(Collectors.toMap(ProductSku::getPvalueDesc, item -> item, (v1, v2) -> v1));

                Map<String, String> reversePvalueDescMap = Maps.newHashMap();
                for (ProductSku productSku : originalProductSkuList) {
                    List<String> pvalueList = Lists.newArrayList(productSku.getPvalueDesc().split("&gt;"));
                    Collections.reverse(pvalueList);
                    String reversePvalueDesc = StringUtils.join(pvalueList, "&gt;");
                    reversePvalueDescMap.put(productSku.getPvalueDesc(), reversePvalueDesc);
                    reversePvalueDescMap.put(reversePvalueDesc, productSku.getPvalueDesc());
                }

                LogUtils.info(log, "old pvalueStr list:{}", originalProductSkuList.stream().map(ProductSku::getPvalueStr).collect(Collectors.toList()));
                LogUtils.info(log, "new pvalueStr list:{}", newProductSkuList.stream().map(ProductSku::getPvalueStr).collect(Collectors.toList()));

                List<ProductSku> addSkuList = new ArrayList<>();
                List<ProductSku> updateSkuList = new ArrayList<>();
                List<Long> deleteSkuIds = new ArrayList<>();
                for (ProductSku newProductSku : newProductSkuList) {
                    String newPValueDesc = newProductSku.getPvalueDesc();
                    ProductSku oldProductSku = oldPValueMap.get(newPValueDesc);

                    if (null == oldProductSku) {
                        String reversePvalueDesc = reversePvalueDescMap.get(newPValueDesc);
                        oldProductSku = oldPValueMap.get(reversePvalueDesc);
                    }

                    if (null != oldProductSku) {
                        newProductSku.setId(oldProductSku.getId());
                        updateSkuList.add(newProductSku);
                    } else {
                        addSkuList.add(newProductSku);
                    }
                }

                List<GoodsSkuPo> goodsSkuPos = goodsSkuHelper.addOrDelPropertyValueAndReturnSku(goodsId, goodsInfoInput);

                Map<String, Long> skuNameIdMap = goodsSkuPos.stream().collect(Collectors.toMap(GoodsSkuPo::getName, GoodsSkuPo::getId, (v1, v2) -> v2));

                Map<String, GoodsItem> originalGoodsItemPvalueDescMap = originalGoodsItemList.stream().filter(goodsItemDTO -> org.apache.commons.lang.StringUtils.isNotBlank(goodsItemDTO.getPvalueStr()))
                        .collect(Collectors.toMap(GoodsItem::getPvalueDesc, Function.identity(), (v1, v2) -> v1));

                // goodsItem上的SkuId
                List<Long> delSkuIdList = new ArrayList<>();
                for (ProductSku oldSku : originalProductSkuList) {
                    String pvalueDesc = oldSku.getPvalueDesc();
                    ProductSku productSku = newPValueMap.get(pvalueDesc);

                    if (null == productSku) {
                        String reversePvalueDesc = reversePvalueDescMap.get(pvalueDesc);
                        productSku = newPValueMap.get(reversePvalueDesc);

                        if (null == productSku) {
                            deleteSkuIds.add(oldSku.getId());
                            GoodsItem goodsItem = originalGoodsItemPvalueDescMap.get(pvalueDesc);
                            if (goodsItem != null) {
                                delSkuIdList.add(goodsItem.getSkuId());
                            }
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(deleteSkuIds)) {
                    ProductSkuDTO skuBO = new ProductSkuDTO();
                    skuBO.setIds(deleteSkuIds);
                    productSkuService.deleteProductSkuByOption(skuBO);
                }


                if (CollectionUtils.isNotEmpty(updateSkuList)) {
                    boolean b = productSkuService.updateBatchById(updateSkuList);
                    CheckUtils.check(!b, ProductResultCode.UPDATE_ERROR);
                }


                if (CollectionUtils.isNotEmpty(addSkuList)) {
                    boolean i = productSkuService.insertBatch(addSkuList);
                    CheckUtils.check(!i, ProductResultCode.UPDATE_ERROR);
                }


                if (CollectionUtils.isNotEmpty(originalGoodsItemList)) {
                    if (CollectionUtils.isNotEmpty(delSkuIdList)) {
                        GoodsItemDTO goodsItemInput = new GoodsItemDTO();
                        goodsItemInput.setSkuIds(delSkuIdList);
                        goodsItemInput.setGoodsId(goodsInfoInput.getId());
                        goodsItemService.deleteGoodsItemByOption(goodsItemInput);
                    }

                    Map<String, GoodsItemDTO> pValueGoodsItemMap = skuMap.values().stream()
                            .filter(goodsItemDTO -> StringUtils.isNotBlank(goodsItemDTO.getPvalueStr()))
                            .collect(Collectors.toMap(GoodsItem::getPvalueDesc, Function.identity(), (v1, v2) -> v1));
                    List<GoodsItem> updateGoodsItemList = new ArrayList<>();
                    for (GoodsItem originalGoodsItem : originalGoodsItemList) {
                        String pvalueDesc = originalGoodsItem.getPvalueDesc();
                        ProductSku newProductSku = newPValueMap.get(pvalueDesc);
                        GoodsItemDTO newGoodsItemDTO = pValueGoodsItemMap.get(pvalueDesc);

                        if (newProductSku == null) {
                            String reversePvalueDesc = reversePvalueDescMap.get(pvalueDesc);
                            newProductSku = newPValueMap.get(reversePvalueDesc);
                        }

                        if (newGoodsItemDTO == null) {
                            String reversePvalueDesc = reversePvalueDescMap.get(pvalueDesc);
                            newGoodsItemDTO = pValueGoodsItemMap.get(reversePvalueDesc);
                        }

                        if (null != newGoodsItemDTO) {
                            GoodsItem updateGoodsItem = new GoodsItem();
                            updateGoodsItem.setSkuId(skuNameIdMap.get(newGoodsItemDTO.getName()));
                            updateGoodsItem.setId(originalGoodsItem.getId());
                            updateGoodsItem.setPrice(newGoodsItemDTO.getPrice());
                            updateGoodsItem.setGrouponPrice(newGoodsItemDTO.getGrouponPrice());
                            updateGoodsItem.setName(newGoodsItemDTO.getName());

                            updateGoodsItem.setOrginalPrice(newGoodsItemDTO.getOrginalPrice() != null ? newGoodsItemDTO.getOrginalPrice() : newGoodsItemDTO.getPrice());
                            updateGoodsItem.setMarketPrice(countGoodsVirtualPrice(updateGoodsItem.getPrice(), goodsInfoInput.getDiscount()));
                            updateGoodsItem.setOrginalMarketPrice(newGoodsItemDTO.getOrginalMarketPrice() != null ? newGoodsItemDTO.getOrginalMarketPrice() : new BigDecimal(0));
                            updateGoodsItem.setOriginalGrouponPrice(newGoodsItemDTO.getOriginalGrouponPrice());
                            updateGoodsItem.setCostPrice(newGoodsItemDTO.getCostPrice());
                            updateGoodsItem.setOriginalProductId(StringUtils.isBlank(newGoodsItemDTO.getOriginalProductId()) ? null : newGoodsItemDTO.getOriginalProductId());
                            updateGoodsItem.setOriginalSkuId(newGoodsItemDTO.getOriginalSkuId());
                            updateGoodsItem.setStock(newGoodsItemDTO.getStock());
                            updateGoodsItem.setSold(newGoodsItemDTO.getSold());
                            updateGoodsItem.setPackageSize(newGoodsItemDTO.getPackageSize());
                            updateGoodsItem.setWeight(newGoodsItemDTO.getWeight());
                            updateGoodsItem.setDefaultDelivery(newGoodsItemDTO.getDefaultDelivery());
                            updateGoodsItem.setMinPurchaseQuantity(newGoodsItemDTO.getMinPurchaseQuantity() != null ? newGoodsItemDTO.getMinPurchaseQuantity() : 1);
                            updateGoodsItem.setUpdateTime(new Date());
                            updateGoodsItem.setSkuStatus(originalGoodsItem.getSkuStatus() == null ? 1 : originalGoodsItem.getSkuStatus());
                            if (null == newGoodsItemDTO.getNum() || newGoodsItemDTO.getNum() <= 0) {
                                updateGoodsItem.setNum(1);
                            } else {
                                updateGoodsItem.setNum(newGoodsItemDTO.getNum());
                            }

                            if (null != newProductSku) {
                                updateGoodsItem.setName(newProductSku.getName());
                                updateGoodsItem.setPvalueStr(newProductSku.getPvalueStr());
                                updateGoodsItem.setPvalueDesc(newProductSku.getPvalueDesc());

                                if (MapUtils.isNotEmpty(propertyValueRecordMap)) {
                                    String propertyValueRecordSnap = goodsId + "-" + Arrays.stream(newProductSku.getPvalueDesc().split("&gt;"))
                                            .map(propertyValueRecordMap::get)
                                            .filter(Objects::nonNull)
                                            .sorted(Long::compareTo)
                                            .map(Object::toString)
                                            .collect(Collectors.joining("-"));
                                    updateGoodsItem.setPropertyValueRecordSnap(propertyValueRecordSnap);
                                }
                            }

                            Map<Long, String> propertyValueImgUrlMap = goodsInfoInput.getPropertyValueImgUrlMap();
                            updateGoodsItem.setSkuImage(StringUtils.isNotBlank(newGoodsItemDTO.getImageUrl()) ? newGoodsItemDTO.getImageUrl() : "");
                            if (StringUtils.isBlank(updateGoodsItem.getSkuImage()) && propertyValueImgUrlMap != null && StringUtils.isNotBlank(updateGoodsItem.getPvalueStr())) {
                                for (String propertyStr : updateGoodsItem.getPvalueStr().split(";")) {
                                    String propertyValueId = propertyStr.split(":")[1];
                                    String imgUrl = propertyValueImgUrlMap.get(Long.parseLong(propertyValueId));
                                    if (StringUtils.isNotBlank(imgUrl)) {
                                        LogUtils.info(log, "item图片替换为规格图片 itemId:{}, imgUrl:{}", originalGoodsItem.getId(), imgUrl);
                                        updateGoodsItem.setSkuImage(imgUrl);
                                        break;
                                    }
                                }
                            }
                            updateGoodsItemList.add(updateGoodsItem);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(updateGoodsItemList)) {
                        boolean b = goodsItemService.updateBatchById(updateGoodsItemList);
                        CheckUtils.check(!b, ProductResultCode.PRODUCT_SKU_ERROR);
                    }
                }

                //在判断有没有需要添加的sku数据，有就在重新添加进去
                if (CollectionUtils.isNotEmpty(addSkuList)) {
                    List<GoodsItem> goodsItemList = new ArrayList<>();
                    addSkuList.forEach(productSku -> {
                        String name = productSku.getName();
                        GoodsItemDTO goodsItemDTO = skuMap.get(name);
                        if (null != goodsItemDTO) {
                            GoodsItem goodsItem = getGoodsItem(goodsId, productSku, goodsItemDTO, goodsInfoInput, skuNameIdMap.get(goodsItemDTO.getName()));
                            goodsItemList.add(goodsItem);
                        }

                    });
                    if (CollectionUtils.isNotEmpty(goodsItemList)) {
                        boolean b = goodsItemService.insertBatch(goodsItemList);
                        CheckUtils.check(!b, ProductResultCode.ADD_ERROR);
                    }
                }
            }
        }

        // 全局规范同步es
        propertyHelper.propertyValueRecordSyncEs(PropertyHelper.specificationsDtoFunction.apply(goodsInfoInput.getProperties()));

        LogUtils.info(log, "检测图片变更");
        Set<String> newImages = Sets.newHashSet();
        Set<String> oldImages = Sets.newHashSet();
        for (PropertyDTO propertyDTO : goodsEditPropertyDto.getNewPropertyList()) {
            for (PropertyValueDTO value : propertyDTO.getValues()) {
                if (StringUtils.isNotBlank(value.getImgUrl())) {
                    newImages.add(value.getImgUrl());
                }
            }
        }
        for (PropertyDTO propertyDTO : goodsEditPropertyDto.getOldPropertyList()) {
            for (PropertyValueDTO value : propertyDTO.getValues()) {
                if (StringUtils.isNotBlank(value.getImgUrl())) {
                    oldImages.add(value.getImgUrl());
                }
            }
        }
        goodsEditPropertyDto.getNewSkuList().stream().filter(goodsItemDTO -> StringUtils.isNotBlank(goodsItemDTO.getImageUrl())).forEach(goodsItemDTO -> newImages.add(goodsItemDTO.getImageUrl()));
        goodsEditPropertyDto.getOldSkuList().stream().filter(goodsItemDTO -> StringUtils.isNotBlank(goodsItemDTO.getSkuImage())).forEach(goodsItemDTO -> oldImages.add(goodsItemDTO.getSkuImage()));
        newImages.removeAll(oldImages);
        Integer isWhite = 0;
        if (faMerchantsApply != null && faMerchantsApply.getTongDunWhiteList() != null) {
            isWhite = faMerchantsApply.getTongDunWhiteList();
        }
        if (CollectionUtils.isNotEmpty(newImages) && isWhite != 1) {
            TongDunGoodsImagesVO tongDunGoodsImagesVO = new TongDunGoodsImagesVO();
            tongDunGoodsImagesVO.setGoodsId(goodsId);
            tongDunGoodsImagesVO.setShopId(goods.getShopId());
            tongDunGoodsImagesVO.setGoodsName(goods.getName());
            tongDunGoodsImagesVO.setCategoryId(goods.getCategoryId());
            tongDunGoodsImagesVO.setGoodsImage(JSON.toJSONString(Lists.newArrayList(newImages)));
            tongDunGoodsImagesVO.setIsType(0L);
            tongDunGoodsImagesVO.setTdType(1);
            mqSender.send("ADD_TONG_DUN_GOODS", JSON.toJSONString(tongDunGoodsImagesVO));
            LogUtils.info(log, "图片变更，添加同盾");
        }

        LogUtils.info(log, "通过并修改商品规格 goodsId:{} end", goodsId);
    }

    private void passUpdateGoodsDetail(Long goodsId, GoodsEditInfo goodsEditInfo, String content) {
        LogUtils.info(log, "通过并修改商品详情 goodsId:{} start", goodsId);
//        String content = goodsEditInfo.getContent();
        GoodsEditDetailDto goodsEditDetailDto = JSON.parseObject(content, GoodsEditDetailDto.class);

        processDbOfUpdateDetail(goodsId, goodsEditDetailDto, true);
        LogUtils.info(log, "通过并修改商品详情 goodsId:{} end", goodsId);


        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery().eq(ListingFollowGoods::getGoodsId, goodsId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .one();
        if (listingFollowGoods != null) {
            syncListingGoodsDetail(listingFollowGoods.getListingId(), goodsEditDetailDto, goodsEditInfo, goodsId);
        }
    }

    private void syncListingGoodsDetail(Long listingId, GoodsEditDetailDto goodsEditDetailDto, GoodsEditInfo goodsEditInfo,Long auditGoodsId) {
        LogUtils.info(log, "通过并修改商品详情->同步listing id:{}", listingId);
        ListingInfo listingInfo = listingInfoService.getById(listingId);
        if (listingInfo.getIsDel() == 1) {
            LogUtils.info(log, "通过并修改商品详情->该listing id:{} 模板已删除", listingId);
            return;
        }
        processDbOfUpdateDetail(listingInfo.getGoodsId(), goodsEditDetailDto, false);

        goodsEditDetailDto.listingClear();

        GoodsEditInfo listingGoodsEditInfo = BeanCopyUtil.transform(goodsEditInfo, GoodsEditInfo.class);
        listingGoodsEditInfo.setContent(JSON.toJSONString(goodsEditDetailDto));
        listingGoodsEditInfo.setId(null);
        listingGoodsEditInfo.setGoodsId(listingInfo.getGoodsId());
        listingGoodsEditInfo.setStatus(1);
        listingGoodsEditInfo.setShopId(CommonConstants.LISTING_GOODS_SHOP_ID);
        listingGoodsEditInfo.setShopName(CommonConstants.LISTING_GOODS_SHOP_NAME);
        listingGoodsEditInfo.setApplyUser("system");
        listingGoodsEditInfo.setApplyTime(LocalDateTime.now());
        listingGoodsEditInfo.setApplyReason(auditGoodsId + ":商品修改申请审核通过，自动同步所属listing模板及其他跟卖商品信息");
        listingGoodsEditInfo.setAuditUser("system");
        listingGoodsEditInfo.setAuditTime(LocalDateTime.now());
        listingGoodsEditInfo.setUpdateTime(LocalDateTime.now());
        goodsEditInfoService.save(listingGoodsEditInfo);

        GoodsEditInfoDetail listingGoodsEditInfoDetail = new GoodsEditInfoDetail();
        listingGoodsEditInfoDetail.setEditId(listingGoodsEditInfo.getId());
        listingGoodsEditInfoDetail.setGoodsId(listingGoodsEditInfo.getGoodsId());
        listingGoodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditDetailDto));
        goodsEditInfoDetailService.save(listingGoodsEditInfoDetail);

        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(listingInfo.getGoodsId())
                .type(OperationLogTypeEnums.UPDATE_DETAIL_FROM_SYNC_LISTING_FOLLOW_GOODS)
                .newData(JSON.toJSONString(goodsEditDetailDto))
                .content(OperationLogTypeEnums.UPDATE_DETAIL_FROM_SYNC_LISTING_FOLLOW_GOODS.getDesc())
                .status(1)
                .user(goodsEditInfo.getApplyUser());
        mqSender.sendDelay("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto, MqDelayLevel.THIRTY_SEC);

        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(listingInfo.getGoodsId());
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("修改商品审批通过(同步所属listing)");
        goodsSyncModel.setSourceService("vp");
        mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.THIRTY_SEC);

        List<ListingFollowGoods> listingFollowGoods = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, listingId)
                .ne(ListingFollowGoods::getGoodsId, goodsEditInfo.getGoodsId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .list();
        if (CollectionUtils.isNotEmpty(listingFollowGoods)) {
            for (ListingFollowGoods listingFollowGood : listingFollowGoods) {
                processDbOfUpdateDetail(listingFollowGood.getGoodsId(), goodsEditDetailDto, false);

                GoodsEditInfo listingFollowGoodsEditInfo = BeanCopyUtil.transform(goodsEditInfo, GoodsEditInfo.class);
                listingFollowGoodsEditInfo.setContent(JSON.toJSONString(goodsEditDetailDto));
                listingFollowGoodsEditInfo.setId(null);
                listingFollowGoodsEditInfo.setGoodsId(listingFollowGood.getGoodsId());
                listingFollowGoodsEditInfo.setStatus(1);
                listingFollowGoodsEditInfo.setShopId(listingFollowGood.getShopId());
                listingFollowGoodsEditInfo.setApplyReason(auditGoodsId + ":商品修改申请审核通过，自动同步所属listing模板及其他跟卖商品信息");
                listingFollowGoodsEditInfo.setApplyUser("system");
                listingFollowGoodsEditInfo.setApplyTime(LocalDateTime.now());
                listingFollowGoodsEditInfo.setAuditUser("system");
                listingFollowGoodsEditInfo.setAuditTime(LocalDateTime.now());
                listingFollowGoodsEditInfo.setUpdateTime(LocalDateTime.now());
                goodsEditInfoService.save(listingFollowGoodsEditInfo);

                GoodsEditInfoDetail listingFollowGoodsEditInfoDetail = new GoodsEditInfoDetail();
                listingFollowGoodsEditInfoDetail.setEditId(listingFollowGoodsEditInfo.getId());
                listingFollowGoodsEditInfoDetail.setGoodsId(listingFollowGoodsEditInfo.getGoodsId());
                listingFollowGoodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditDetailDto));
                goodsEditInfoDetailService.save(listingFollowGoodsEditInfoDetail);

                GoodsOperationLogDto ListingFollowGoodsOperationLogDto = new GoodsOperationLogDto()
                        .goodsId(listingFollowGood.getGoodsId())
                        .type(OperationLogTypeEnums.UPDATE_DETAIL_FROM_SYNC_LISTING_FOLLOW_GOODS)
                        .newData(JSON.toJSONString(goodsEditDetailDto))
                        .content(OperationLogTypeEnums.UPDATE_DETAIL_FROM_SYNC_LISTING_FOLLOW_GOODS.getDesc())
                        .status(1)
                        .user(goodsEditInfo.getApplyUser());
                mqSender.sendDelay("SYNC_GOODS_OPERATION_LOG", ListingFollowGoodsOperationLogDto, MqDelayLevel.THIRTY_SEC);

                GoodsSyncModel goodsSyncModel2 = new GoodsSyncModel();
                goodsSyncModel2.setGoodsId(listingFollowGood.getGoodsId());
                goodsSyncModel2.setSyncTime(System.currentTimeMillis());
                goodsSyncModel2.setBusiness("修改商品审批通过(同步所属listing)");
                goodsSyncModel2.setSourceService("vp");
                mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel2), MqDelayLevel.THIRTY_SEC);
            }
        }
    }

    private void processDbOfUpdateDetail(Long goodsId, GoodsEditDetailDto goodsEditDetailDto, boolean exclusive) {
        Goods goods = goodsService.getById(goodsId);
        goodsService.lambdaUpdate()
                .set(goodsEditDetailDto.getNewName() != null, Goods::getName, GoodsInfoUtils.formatGoodsName(goodsEditDetailDto.getNewName()))
                .set(goodsEditDetailDto.getNewCategoryId() != null, Goods::getCategoryId, goodsEditDetailDto.getNewCategoryId())
                .set(CollectionUtils.isNotEmpty(goodsEditDetailDto.getNewGoodsImages()), Goods::getMainImage, goodsEditDetailDto.getNewGoodsImages().get(0))
                .set(exclusive, Goods::getSizeChartTemplateId, goodsEditDetailDto.getNewSizeChartTemplateId())
                .set(Goods::getUpdateTime, LocalDateTime.now())
                .eq(Goods::getId, goodsId)
                .update();

        GoodsExtDetail goodsExtDetail = goodsExtDetailService.lambdaQuery().eq(GoodsExtDetail::getGoodsId, goodsId).one();
        goodsExtDetail.setItemCode(goodsEditDetailDto.getNewItemCode());
        goodsExtDetail.setBrandId(exclusive ? goodsEditDetailDto.getNewBrandId() : null);
        goodsExtDetailService.updateById(goodsExtDetail);
        if (exclusive) {
            //新增处理逻辑，商品绑定的品牌如果关联了自定义标签，该商品也应关联对应的自定义标签
            GoodsRelTagDTO relTagDTO = new GoodsRelTagDTO();
            relTagDTO.setBrandId(goodsExtDetail.getBrandId());
            relTagDTO.setGoodsIds(Lists.newArrayList(goodsExtDetail.getGoodsId()));
            mqSender.send(CommonConstants.REL_TAG_TOPIC, relTagDTO);
        }

        GoodsDetail goodsDetail = goodsDetailService.queryGoodsDetailByGoodsId(goodsId);
        goodsDetail.setDescription(goodsEditDetailDto.getNewDescription());
        goodsDetail.setSizeImage(exclusive ? goodsEditDetailDto.getNewSizeImage() : null);
        goodsDetailService.updateById(goodsDetail);

        ProductInfoInput productInfoInput = new ProductInfoInput();
        productInfoInput.setId(goodsId);
        productInfoInput.setGoodsImages(goodsEditDetailDto.getNewGoodsImages());
        productInfoInput.setGoodsVideos(goodsEditDetailDto.getNewGoodsVideos());
        productInfoInput.setDetailsImgs(goodsEditDetailDto.getNewDetailsImgs());
        updateGoodsInfoCoreService.updateGoodsImgs(productInfoInput);
        updateGoodsInfoCoreService.updateDetailImgs(productInfoInput);

        if (exclusive) {
            if (CollectionUtils.isNotEmpty(goodsEditDetailDto.getNewPropertyGoodsInfoVOS())) {
                List<PropertyGoodsInfoVO> newPropertyGoodsInfoVos = goodsEditDetailDto.getNewPropertyGoodsInfoVOS().stream()
                        .filter(k -> StringUtils.isNotBlank(k.getInputValue()))
                        .peek(v -> v.setGoodsId(goodsId))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(newPropertyGoodsInfoVos)) {
                    propertyGoodsInfoCoreService.saveOrUpdatePropertyGoods(newPropertyGoodsInfoVos);
                }
            }

            if (goodsEditDetailDto.getNewGoodsExtra() != null && StringUtils.isNotBlank(goodsEditDetailDto.getNewGoodsExtra().getPropertyJson())) {
                GoodsExtraPropertyVO goodsExtraPropertyVO = JSON.parseObject(goodsEditDetailDto.getNewGoodsExtra().getPropertyJson(), GoodsExtraPropertyVO.class);
                updateGoodsInfoCoreService.insertGoodsExtraInfo(Collections.singletonList(goodsExtraPropertyVO), goodsId);
            }
        }

        Set<String> tongDunImages = Sets.newHashSet(goodsEditDetailDto.getNewGoodsImages());
        tongDunImages.addAll(goodsEditDetailDto.getNewDetailsImgs());
        tongDunImages.addAll(goodsEditDetailDto.getNewGoodsVideos());
        tongDunImages.removeAll(goodsEditDetailDto.getOldGoodsImages());
        tongDunImages.removeAll(goodsEditDetailDto.getOldDetailsImgs());
        tongDunImages.removeAll(goodsEditDetailDto.getOldGoodsVideos());
        Integer isWhite = 0;
        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(goods.getShopId());
        if (faMerchantsApply != null && faMerchantsApply.getTongDunWhiteList() != null) {
            isWhite = faMerchantsApply.getTongDunWhiteList();
        }
        if (CollectionUtils.isNotEmpty(tongDunImages) && isWhite != 1) {
            TongDunGoodsImagesVO tongDunGoodsImagesVO = new TongDunGoodsImagesVO();
            tongDunGoodsImagesVO.setGoodsId(goodsId);
            tongDunGoodsImagesVO.setShopId(goods.getShopId());
            tongDunGoodsImagesVO.setGoodsName(goods.getName());
            tongDunGoodsImagesVO.setCategoryId(goods.getCategoryId());
            tongDunGoodsImagesVO.setGoodsImage(JSON.toJSONString(Lists.newArrayList(tongDunImages)));
            tongDunGoodsImagesVO.setIsType(0L);
            tongDunGoodsImagesVO.setTdType(1);
            mqSender.send("ADD_TONG_DUN_GOODS", JSON.toJSONString(tongDunGoodsImagesVO));
            LogUtils.info(log, "图片变更，添加同盾");
        }
    }

    private GoodsItem getGoodsItem(Long goodsId, ProductSku productSku, GoodsItemDTO goodsItemDTO, ProductInfoInput goodsInfoInput, Long skuId) {
        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setCreateTime(LocalDateTime.now());
        goodsItem.setUpdateTime(new Date());
        goodsItem.setSkuId(skuId);
        goodsItem.setGoodsId(goodsId);
        goodsItem.setPrice(goodsItemDTO.getPrice());
        goodsItem.setOrginalPrice(goodsItemDTO.getOrginalPrice());
        goodsItem.setMarketPrice(countGoodsVirtualPrice(goodsItem.getPrice(), goodsInfoInput.getDiscount()));
        goodsItem.setOrginalMarketPrice(goodsItem.getMarketPrice());
        goodsItem.setOriginalGrouponPrice(goodsItem.getOrginalPrice().multiply(new BigDecimal("0.8")).setScale(2, BigDecimal.ROUND_HALF_UP));
        goodsItem.setGrouponPrice(goodsItem.getOriginalGrouponPrice().add(goodsItemDTO.getDefaultDelivery()));

        goodsItem.setName(goodsItemDTO.getName());
        goodsItem.setCostPrice(goodsItemDTO.getCostPrice());
        goodsItem.setPvalueDesc(productSku.getPvalueDesc());
        goodsItem.setPvalueStr(productSku.getPvalueStr());
        goodsItem.setStock(goodsItemDTO.getStock());
        goodsItem.setSold(goodsItemDTO.getSold());
        goodsItem.setOriginalSkuId(goodsItemDTO.getOriginalSkuId());
        goodsItem.setDefaultDelivery(goodsItemDTO.getDefaultDelivery());
        goodsItem.setPackageSize(goodsItemDTO.getPackageSize());
        goodsItem.setWeight(goodsItemDTO.getWeight());
//        goodsItem.setChannel(goodsInfoInput.getChannel());
        goodsItem.setMinPurchaseQuantity(goodsItemDTO.getMinPurchaseQuantity());
        goodsItem.setRatio(60);
        goodsItem.setNum(1);
        goodsItem.setMinPurchaseQuantity(goodsItemDTO.getMinPurchaseQuantity() != null ? goodsItemDTO.getMinPurchaseQuantity() : 1);
        goodsItem.setSkuStatus(goodsItemDTO.getSkuStatus() == null ? 1 : goodsItemDTO.getSkuStatus());

        goodsItem.setSkuImage(StringUtils.isNotBlank(goodsItemDTO.getImageUrl()) ? goodsItemDTO.getImageUrl() : "");
        Map<Long, String> propertyValueImgUrlMap = goodsInfoInput.getPropertyValueImgUrlMap();
        if (StringUtils.isBlank(goodsItem.getSkuImage()) && propertyValueImgUrlMap != null) {
            for (String propertyStr : goodsItem.getPvalueStr().split(";")) {
                String propertyValueId = propertyStr.split(":")[1];
                String imgUrl = propertyValueImgUrlMap.get(Long.parseLong(propertyValueId));
                if (StringUtils.isNotBlank(imgUrl)) {
                    goodsItem.setSkuImage(imgUrl);
                    break;
                }
            }
        }

        Map<String, Long> propertyValueRecordMap = goodsInfoInput.getPropertyValueRecordMap();
        if (MapUtils.isNotEmpty(propertyValueRecordMap)) {
            String propertyValueRecordSnap = goodsId + "-" + Arrays.stream(productSku.getPvalueDesc().split("&gt;"))
                    .map(propertyValueRecordMap::get)
                    .filter(Objects::nonNull)
                    .sorted(Long::compareTo)
                    .map(Object::toString)
                    .collect(Collectors.joining("-"));
            goodsItem.setPropertyValueRecordSnap(propertyValueRecordSnap);
        }
        return goodsItem;
    }

    private BigDecimal countGoodsVirtualPrice(BigDecimal price, BigDecimal virtualDiscount) {
//        log.info("计算划线价price {}, virtualDiscount{}", price, virtualDiscount);
        return price.divide(virtualDiscount, 2, BigDecimal.ROUND_HALF_UP);
    }

    @Override
    public Boolean reject(GoodsEditAuditCondition condition) {
        LogUtils.info(log, "审批驳回:{}", condition);
        CheckUtils.isEmpty(condition.getIds(), ProductResultCode.PARAMETER_ID_ERROR);

        List<GoodsEditInfo> goodsEditInfoList = Lists.newArrayList(goodsEditInfoService.selectByIds(condition.getIds()));
        goodsEditInfoList.forEach(goodsEditInfo -> CheckUtils.check(goodsEditInfo.getStatus() != 0, ProductResultCode.GOODS_EDIT_APPLY_ALREADY_AUDIT));

        for (GoodsEditInfo goodsEditInfo : goodsEditInfoList) {
            goodsEditInfo.setStatus(-1);
            goodsEditInfo.setAuditTime(LocalDateTime.now());
            goodsEditInfo.setAuditUser(getUserName());
            goodsEditInfo.setRejectReason(condition.getReason());
            goodsEditInfo.setUpdateTime(LocalDateTime.now());
        }

        boolean result = goodsEditInfoService.updateBatchById(goodsEditInfoList);

        // 更新推荐日志审核状态
        recommendCoreService.batchUpdateAuditStatus(condition.getIds(), -1);

        return result;
    }
}
