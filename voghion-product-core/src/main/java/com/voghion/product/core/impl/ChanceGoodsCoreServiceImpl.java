package com.voghion.product.core.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.lang.exception.CustomException;
import com.colorlight.base.model.GoodsSyncModel;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.ThreadContext;
import com.colorlight.base.model.constants.SystemConstants;
import com.colorlight.base.model.dto.ClientInfoDTO;
import com.colorlight.base.utils.CheckUtils;
import com.google.common.collect.Lists;
import com.voghion.es.dto.GoodsPriceDTO;
import com.voghion.es.service.GoodsEsService;
import com.voghion.product.api.dto.GoodsOperationLogDto;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.core.*;
import com.voghion.product.enums.CustomizedGoodsSyncEnums;
import com.voghion.product.listener.BatchSaveChanceGoodsVo;
import com.voghion.product.listener.BatchUpdateStatusAndHotVo;
import com.voghion.product.model.dto.GoodsCopyDto;
import com.voghion.product.model.enums.*;
import com.voghion.product.model.po.*;
import com.voghion.product.model.vo.*;
import com.voghion.product.model.vo.condition.*;
import com.voghion.product.mq.MqSender;
import com.voghion.product.mq.model.CustomizedGoodsSyncModel;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.support.ClientInfoSupportService;
import com.voghion.product.support.ElasticsearchHandler;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.util.LogUtils;
import com.voghion.product.utils.CommonConstants;
import com.voghion.product.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.index.query.TermQueryBuilder;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @date: 2022/12/16 上午10:31
 * @author: jashley
 */
@Service
@Slf4j
public class ChanceGoodsCoreServiceImpl extends AbstractCommonServiceImpl implements ChanceGoodsCoreService {

    @Resource
    private ChanceGoodsTemplateService chanceGoodsTemplateService;

    @Resource
    private ChanceGoodsService chanceGoodsService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private CategoryTreeCoreService categoryTreeCoreService;

    @Resource
    private GoodsFreightCoreService goodsFreightCoreService;

    @Resource
    private NewAddGoodsCoreService newAddGoodsCoreService;

    @Resource
    private UpdateGoodsInfoCoreService updateGoodsInfoCoreService;

    @Resource
    private GoodsLockCoreService goodsLockCoreService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private GoodsItemService goodsItemService;

    @Resource
    private GoodsCoreService goodsCoreService;

    @Resource
    private FaMerchantsApplyCoreService faMerchantsApplyCoreService;

    @Resource
    private CategoryMainService categoryMainService;

    @Resource
    private ClientInfoSupportService clientInfoSupportService;

    @Resource
    private GoodsEsService goodsEsService;

    @Resource
    private ElasticsearchHandler elasticsearchHandler;

    @Resource
    private MqSender mqSender;

    @Resource
    private RedisApi redisApi;

    private static long changeGoodsPageStart = 1;


    @Override
    public PageView<ChanceGoodsTemplateVo> listTemplate(ChanceGoodsTemplateQueryCondition condition) {
        LogUtils.info(log, "listTemplate:{}", condition);
        List<Long> categoryIds = Lists.newArrayList();
        if (condition.getCategoryId() != null) {
            List<Category> categories = categoryService.queryAllChildCategoryByParentId(condition.getCategoryId());
            categoryIds.add(condition.getCategoryId());
            categoryIds.addAll(categories.stream().map(Category::getId).collect(Collectors.toList()));
        }

        List<Long> idList = Lists.newArrayList();
        if (StringUtils.isNotBlank(condition.getIds())) {
            idList = Arrays.stream(condition.getIds().split("\n")).filter(s -> !"".equals(s)).map(Long::parseLong).collect(Collectors.toList());
        }

        IPage<ChanceGoodsTemplate> page = chanceGoodsTemplateService.lambdaQuery()
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .eq(condition.getSourceType() != null, ChanceGoodsTemplate::getSourceType, condition.getSourceType())
                .in(CollectionUtils.isNotEmpty(idList), ChanceGoodsTemplate::getId, idList)
                .eq(condition.getStatus() != null, ChanceGoodsTemplate::getStatus, condition.getStatus())
                .like(StringUtils.isNotBlank(condition.getGoodsName()), ChanceGoodsTemplate::getGoodsName, condition.getGoodsName())
                .in(CollectionUtils.isNotEmpty(categoryIds), ChanceGoodsTemplate::getCategoryId, categoryIds)
                .eq(StringUtils.isNotBlank(condition.getCreateUser()), ChanceGoodsTemplate::getCreateUser, condition.getCreateUser())
                .eq(StringUtils.isNotBlank(condition.getUpdateUser()), ChanceGoodsTemplate::getUpdateUser, condition.getUpdateUser())
                .ge(condition.getStartCreateTime() != null, ChanceGoodsTemplate::getCreateTime, condition.getStartCreateTime())
                .le(condition.getEndCreateTime() != null, ChanceGoodsTemplate::getCreateTime, condition.getEndCreateTime())
                .ge(condition.getStartUpdateTime() != null, ChanceGoodsTemplate::getUpdateTime, condition.getStartUpdateTime())
                .le(condition.getEndUpdateTime() != null, ChanceGoodsTemplate::getUpdateTime, condition.getEndUpdateTime())
                .ge(condition.getMinHot() != null, ChanceGoodsTemplate::getHot, condition.getMinHot())
                .le(condition.getMaxHot() != null, ChanceGoodsTemplate::getHot, condition.getMaxHot())
                .ge(condition.getMinSales() != null, ChanceGoodsTemplate::getSevenSales, condition.getMinSales())
                .le(condition.getMaxSales() != null, ChanceGoodsTemplate::getSevenSales, condition.getMaxSales())
                .orderByDesc(ChanceGoodsTemplate::getUpdateTime)
                .page(new Page<>(condition.getPageNow(), condition.getPageSize()));

        List<ChanceGoodsTemplateVo> vos = BeanCopyUtil.transformList(page.getRecords(), ChanceGoodsTemplateVo.class);
        List<Long> existCategoryIds = vos.stream().map(ChanceGoodsTemplateVo::getCategoryId).collect(Collectors.toList());
        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(existCategoryIds);
        vos.forEach(vo -> vo.setCategoryPath(categoryTreeMap.get(vo.getCategoryId())));

        PageView<ChanceGoodsTemplateVo> pageView = new PageView<>(condition.getPageSize(), condition.getPageNow());
        pageView.setRecords(vos);
        pageView.setRowCount(page.getTotal());
        return pageView;
    }

    @Override
    public List<ChanceGoodsTemplateExportVo> exportTemplateList(ChanceGoodsTemplateQueryCondition condition) {
//        List<ChanceGoodsTemplateExportVo> results = Lists.newArrayList();
//        int pageNow = 1;
//        boolean flag = true;
//        while (flag) {
            condition.setPageNow(1);
            condition.setPageSize(20000);
            PageView<ChanceGoodsTemplateVo> chanceGoodsTemplateVoPageView = listTemplate(condition);
            List<ChanceGoodsTemplateVo> records = chanceGoodsTemplateVoPageView.getRecords();
            List<ChanceGoodsTemplateExportVo> vos = BeanCopyUtil.transformList(records, ChanceGoodsTemplateExportVo.class);
            for (ChanceGoodsTemplateExportVo vo : vos) {
                vo.setSourceTypeStr(ChanceGoodsSourceTypeEnums.getNameByCode(vo.getSourceType()));
                vo.setStatusStr(ChanceGoodsTemplateStatusEnums.getNameByCode(vo.getStatus()));
            }
//            results.addAll(vos);

//            if (pageNow == chanceGoodsTemplateVoPageView.getPageCount()) {
//                flag = false;
//            }
//            pageNow++;
//        }
        return vos;
    }

    @Override
    public Long addTemplate(ChanceGoodsTemplateSaveCondition condition) {
        LogUtils.info(log, "新增模板商品:{}", condition);
        CheckUtils.notNull(condition.getSourceType(), ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(2 == condition.getSourceType() && StringUtils.isBlank(condition.getSourceDetail()), ProductResultCode.CHANCE_GOODS_CLONE_NEED_GOODS_ID);

        if (2 == condition.getSourceType()) {
            Integer count = chanceGoodsTemplateService.lambdaQuery()
                    .eq(ChanceGoodsTemplate::getSourceType, 2)
                    .eq(ChanceGoodsTemplate::getSourceDetail, condition.getSourceDetail())
                    .count();
            CheckUtils.check(count > 0, ProductResultCode.CHANCE_GOODS_TEMPLATE_ALREADY_CLONG_THE_GOODS);

            count = chanceGoodsService.lambdaQuery()
                    .eq(ChanceGoods::getGoodsId, Long.parseLong(condition.getSourceDetail()))
                    .count();
            CheckUtils.check(count > 0, ProductResultCode.CHANCE_GOODS_CANNOT_CLONG_TO_TEMPLATE);
        }

        ProductInfoInput infoInput = BeanCopyUtil.transform(condition, ProductInfoInput.class);
        infoInput.setShopId(CommonConstants.CHANCE_GOODS_SHOP_ID);
        infoInput.setShopName(CommonConstants.CHANCE_GOODS_SHOP_NAME);
        infoInput.setStoreName(CommonConstants.CHANCE_GOODS_SHOP_NAME);
        infoInput.setIsShow(GoodsIsShowEnums.TEMPLATE.getType().toString());
        infoInput.setTag("");
        infoInput.setOperationLogType(OperationLogTypeEnums.CREATE_CHANCE_GOODS_TEMPLATE.getCode());
        infoInput.setChannel(ChannelEnums.CREATE_CHANCE_GOODS_TEMPLATE.getCode());
        infoInput.setNeedCheckShop(false);
        if (infoInput.getBrandId() == null) {
            infoInput.setBrandId(0L);
        }
        Long goodsId = newAddGoodsCoreService.shopAddGoods(infoInput);
        CheckUtils.notNull(goodsId, ProductResultCode.ADD_ERROR);

        String operator = condition.getOperator();
        if (StringUtils.isBlank(operator)) {
            operator = getUserName();
        }
        Date now = new Date();
        Goods goods = goodsService.getById(goodsId);


        ChanceGoodsTemplate chanceGoodsTemplate = new ChanceGoodsTemplate();
        chanceGoodsTemplate.setSourceProId(condition.getSourceProId());
        chanceGoodsTemplate.setSourceType(condition.getSourceType());
        chanceGoodsTemplate.setSourceDetail(condition.getSourceDetail());
        chanceGoodsTemplate.setMainImage(goods.getMainImage());
        chanceGoodsTemplate.setGoodsName(goods.getName());
        chanceGoodsTemplate.setCategoryId(goods.getCategoryId());
        chanceGoodsTemplate.setMinPrice(goods.getMinPrice());
        chanceGoodsTemplate.setMaxPrice(goods.getMaxPrice());
        chanceGoodsTemplate.setHot(condition.getHot() == null ? 0 : condition.getHot());
        chanceGoodsTemplate.setStatus(0 == condition.getFromType() ? ChanceGoodsTemplateStatusEnums.DRAFT.getCode() : ChanceGoodsTemplateStatusEnums.OPEN.getCode());
        chanceGoodsTemplate.setGenerateGoodsCount(0);
        chanceGoodsTemplate.setGoodsId(goodsId);
        chanceGoodsTemplate.setIsDel(0);
        chanceGoodsTemplate.setCreateTime(now);
        chanceGoodsTemplate.setUpdateTime(now);
        chanceGoodsTemplate.setCreateUser(operator);
        chanceGoodsTemplate.setUpdateUser(operator);
        chanceGoodsTemplate.setSevenSales(condition.getSevenSales() != null ? condition.getSevenSales() : 0);
        chanceGoodsTemplate.setHot(condition.getHot() != null ? condition.getHot() : 0);
        chanceGoodsTemplateService.save(chanceGoodsTemplate);
        return chanceGoodsTemplate.getId();
    }

    @Override
    public void updateTemplate(ChanceGoodsTemplateSaveCondition condition) {
        LogUtils.info(log, "更新模板商品:{}", condition);
        Long goodsId = condition.getId();
        ChanceGoodsTemplate chanceGoodsTemplate = chanceGoodsTemplateService.lambdaQuery().eq(ChanceGoodsTemplate::getIsDel, 0).eq(ChanceGoodsTemplate::getGoodsId, goodsId).one();
        CheckUtils.notNull(chanceGoodsTemplate, ProductResultCode.GOODS_NOT_EXIST);
//        CheckUtils.check(!ChanceGoodsTemplateStatusEnums.DRAFT.getCode().equals(chanceGoodsTemplate.getStatus()), ProductResultCode.CHANCE_GOODS_TEMPLATE_STATUS_FORBID_UPDATE);

        ProductInfoInput infoInput = BeanCopyUtil.transform(condition, ProductInfoInput.class);
        infoInput.setShopId(CommonConstants.CHANCE_GOODS_SHOP_ID);
        infoInput.setShopName(CommonConstants.CHANCE_GOODS_SHOP_NAME);
        if (infoInput.getBrandId() == null) {
            infoInput.setBrandId(0L);
        }
        Boolean success = updateGoodsInfoCoreService.shopUpdateGoods(infoInput);

        if (success) {
            Goods goods = goodsService.getById(goodsId);
            chanceGoodsTemplate.setMainImage(goods.getMainImage());
            chanceGoodsTemplate.setGoodsName(goods.getName());
            chanceGoodsTemplate.setCategoryId(goods.getCategoryId());
            chanceGoodsTemplate.setMinPrice(goods.getMinPrice());
            chanceGoodsTemplate.setMaxPrice(goods.getMaxPrice());
            chanceGoodsTemplate.setUpdateUser(getUserName());
            chanceGoodsTemplate.setUpdateTime(new Date());
            if (condition.getSevenSales() != null) {
                chanceGoodsTemplate.setSevenSales(condition.getSevenSales());
            }
            chanceGoodsTemplateService.updateById(chanceGoodsTemplate);
        }
    }

    @Override
    public void updateTemplateStatus(ChanceGoodsTemplateUpdateStatusCondition condition) {
        LogUtils.info(log, "机会商品更新状态:{}", condition);
        CheckUtils.check(condition.getId() == null || condition.getStatus() == null, ProductResultCode.PARAMETER_ERROR);

        ChanceGoodsTemplate chanceGoodsTemplate = chanceGoodsTemplateService.getById(condition.getId());
        CheckUtils.notNull(chanceGoodsTemplate, ProductResultCode.CHANCE_GOODS_TEMPLATE_NOT_EXIST);

        chanceGoodsTemplate.setStatus(ChanceGoodsTemplateStatusEnums.OPEN.getCode().equals(condition.getStatus()) ?
                ChanceGoodsTemplateStatusEnums.OPEN.getCode() : ChanceGoodsTemplateStatusEnums.MANUAL_CLOSE.getCode());
        chanceGoodsTemplate.setUpdateUser(getUserName());
        chanceGoodsTemplate.setUpdateTime(new Date());
        chanceGoodsTemplateService.updateById(chanceGoodsTemplate);
    }

    @Override
    public ChanceGoodsTemplateDetailInfoVO queryTemplateDetailById(Long goodsId) {
        CheckUtils.notNull(goodsId, ProductResultCode.PARAMETER_ID_ERROR);
        GoodsDetailInfoVO detailInfoVO = goodsCoreService.queryGoodsDetailById(goodsId, null);
        ChanceGoodsTemplateDetailInfoVO vo = BeanCopyUtil.transform(detailInfoVO, ChanceGoodsTemplateDetailInfoVO.class);

        ChanceGoodsTemplate template = chanceGoodsTemplateService.lambdaQuery()
                .eq(ChanceGoodsTemplate::getGoodsId, goodsId)
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .one();
//        CheckUtils.notNull(template, ProductResultCode.CHANCE_GOODS_TEMPLATE_NOT_EXIST);
        if (template != null) {
            vo.setSourceType(template.getSourceType());
        }
        return vo;
    }

    @Override
    public void updateTemplateHot(ChanceGoodsTemplateUpdateHotCondition condition) {
        LogUtils.info(log, "机会商品更新热度:{}", condition);
        CheckUtils.check(condition.getId() == null || condition.getHot() == null, ProductResultCode.PARAMETER_ERROR);

        ChanceGoodsTemplate chanceGoodsTemplate = chanceGoodsTemplateService.getById(condition.getId());
        CheckUtils.notNull(chanceGoodsTemplate, ProductResultCode.CHANCE_GOODS_TEMPLATE_NOT_EXIST);

        chanceGoodsTemplate.setHot(condition.getHot());
        chanceGoodsTemplate.setUpdateUser(getUserName());
        chanceGoodsTemplate.setUpdateTime(new Date());
        chanceGoodsTemplateService.updateById(chanceGoodsTemplate);
    }

    @Override
    public void batchSaveTemplate(List<BatchSaveChanceGoodsVo> list) {
        LogUtils.info(log, "batchSaveTemplate:{}", list);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(vo -> CheckUtils.check(vo.getType() == null || StringUtils.isBlank(vo.getValue()), ProductResultCode.PARAMETER_ERROR));

        Map<Integer, List<BatchSaveChanceGoodsVo>> diffSourceTypeSaveMap = list.stream().collect(Collectors.groupingBy(BatchSaveChanceGoodsVo::getType));

        String operator = getUserName();
        Date now = new Date();
        List<ChanceGoodsTemplate> addList = Lists.newArrayList();
        for (Map.Entry<Integer, List<BatchSaveChanceGoodsVo>> entry : diffSourceTypeSaveMap.entrySet()) {
            Integer type = entry.getKey();
            //1复制商品2速卖通爬虫3shein爬虫
            if (type == 1) {
                List<BatchSaveChanceGoodsVo> batchSaveChanceGoodsVoList = entry.getValue();
                List<Long> goodsIds = batchSaveChanceGoodsVoList.stream().map(vo -> Long.parseLong(vo.getValue())).distinct().collect(Collectors.toList());
                Map<Long, Integer> goodsSevenSalesMap = batchSaveChanceGoodsVoList.stream()
                        .collect(Collectors.toMap(vo -> Long.parseLong(vo.getValue()), BatchSaveChanceGoodsVo::getSevenSales, (v1, v2) -> v1));
                for (Long goodsId : goodsIds) {
                    Goods goods;
                    try {
                        GoodsCopyDto copyDto = new GoodsCopyDto();
                        copyDto.setGoodsId(goodsId);
                        copyDto.setType(1);
                        copyDto.setShopId(CommonConstants.CHANCE_GOODS_SHOP_ID);
                        copyDto.setShopName(CommonConstants.CHANCE_GOODS_SHOP_NAME);
                        copyDto.setIsShow(GoodsIsShowEnums.TEMPLATE);
                        copyDto.setIsLock(0);
//                        copyDto.setGoodsLockLabelTyp(GoodsLockLabelTypEnums.CHANCE_GOODS);
                        goods = newAddGoodsCoreService.copyAddGoods(copyDto);
                    } catch (Exception e) {
                        LogUtils.error(log, "batchSaveTemplate copy失败 goodsId:{}", goodsId, e);
                        continue;
                    }
                    ChanceGoodsTemplate chanceGoodsTemplate = new ChanceGoodsTemplate();
                    chanceGoodsTemplate.setSourceType(2);
                    chanceGoodsTemplate.setSourceDetail(goodsId.toString());
                    chanceGoodsTemplate.setMainImage(goods.getMainImage());
                    chanceGoodsTemplate.setGoodsName(goods.getName());
                    chanceGoodsTemplate.setCategoryId(goods.getCategoryId());
                    chanceGoodsTemplate.setHot(0);
                    chanceGoodsTemplate.setStatus(ChanceGoodsTemplateStatusEnums.OPEN.getCode());
                    chanceGoodsTemplate.setGenerateGoodsCount(0);
                    chanceGoodsTemplate.setSevenSales(goodsSevenSalesMap.getOrDefault(goodsId, 0));
                    chanceGoodsTemplate.setGoodsId(goods.getId());
                    chanceGoodsTemplate.setMinPrice(goods.getMinPrice());
                    chanceGoodsTemplate.setMaxPrice(goods.getMaxPrice());
                    chanceGoodsTemplate.setIsDel(0);
                    chanceGoodsTemplate.setCreateTime(now);
                    chanceGoodsTemplate.setUpdateTime(now);
                    chanceGoodsTemplate.setCreateUser(operator);
                    chanceGoodsTemplate.setUpdateUser(operator);
                    addList.add(chanceGoodsTemplate);
                }
            } else if (type == 2) {
                //todo
            } else {

            }
        }
        chanceGoodsTemplateService.saveBatch(addList);
    }


    @Override
    public void batchUpdateStatusAndHot(List<BatchUpdateStatusAndHotVo> list) {
        LogUtils.info(log, "batchUpdateStatusAndHot:{}", list);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(vo -> CheckUtils.check(vo.getId() == null, ProductResultCode.PARAMETER_ID_ERROR));

        Map<Long, BatchUpdateStatusAndHotVo> updateStatusAndHotVoMap = list.stream().collect(Collectors.toMap(BatchUpdateStatusAndHotVo::getId, Function.identity(), (a, b) -> a));
        List<Long> templateIds = Lists.newArrayList(updateStatusAndHotVoMap.keySet());
        List<ChanceGoodsTemplate> templateList = chanceGoodsTemplateService.lambdaQuery().in(ChanceGoodsTemplate::getId, templateIds).eq(ChanceGoodsTemplate::getIsDel, 0).list();

        if (templateList.size() < list.size()) {
            List<Long> existTemplateIds = templateList.stream().map(ChanceGoodsTemplate::getId).collect(Collectors.toList());
            templateIds.removeAll(existTemplateIds);
            CheckUtils.check(true, CustomResultCode.fill(ProductResultCode.CHANCE_GOODS_TEMPLATE_NOT_EXIST_EXT, StringUtils.join(templateIds, ",")));
        }

        Date now = new Date();
        String operator = getUserName();
        for (ChanceGoodsTemplate goodsTemplate : templateList) {
            BatchUpdateStatusAndHotVo updateStatusAndHotVo = updateStatusAndHotVoMap.get(goodsTemplate.getId());
            goodsTemplate.setStatus(updateStatusAndHotVo.getStatus());
            goodsTemplate.setHot(updateStatusAndHotVo.getHot());
            goodsTemplate.setSevenSales(updateStatusAndHotVo.getSevenSales());
            goodsTemplate.setUpdateTime(now);
            goodsTemplate.setUpdateUser(operator);
        }
        chanceGoodsTemplateService.updateBatchById(templateList);
    }

    @Override
    public List<ChanceGoodsVo> listChanceGoodsByTemplateId(ChanceGoodsQueryCondition condition) {
        IPage<ChanceGoods> page = chanceGoodsService.lambdaQuery()
                .eq(ChanceGoods::getIsDel, 0)
                .eq(condition.getTemplateId() != null, ChanceGoods::getTemplateId, condition.getTemplateId())
                .page(new Page<>(1, 20000));
        List<ChanceGoods> chanceGoodsList = page.getRecords();

        if (CollectionUtils.isEmpty(chanceGoodsList)) {
            return Collections.emptyList();
        }

        List<Long> goodsIds = chanceGoodsList.stream().map(ChanceGoods::getGoodsId).collect(Collectors.toList());
        List<Goods> goodsList = goodsService.queryAllGoodsByIds(goodsIds);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));

        List<Long> categoryIds = goodsList.stream().map(Goods::getCategoryId).collect(Collectors.toList());

        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(categoryIds);

        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(goodsIds);
        List<GoodsFreightResult> goodsFreightResultList = BeanCopyUtil.transformList(goodsFreights, GoodsFreightResult.class);
        Map<Long, List<GoodsFreightResult>> goodsFreightMap = goodsFreightResultList.stream().collect(Collectors.groupingBy(GoodsFreightResult::getGoodsId));

        return chanceGoodsList.stream().map(chanceGoods -> {
            ChanceGoodsVo vo = BeanCopyUtil.transform(chanceGoods, ChanceGoodsVo.class);
            Goods goods = goodsMap.get(vo.getGoodsId());
            if (goods != null) {
                vo.setCategoryId(goods.getCategoryId());
                vo.setCategoryPath(categoryTreeMap.get(goods.getCategoryId()));
                vo.setName(goods.getName());
                vo.setMainImage(goods.getMainImage());
                vo.setMinPrice(goods.getMinPrice());
                vo.setMaxPrice(goods.getMaxPrice());
                vo.setMinGrouponPrice(goods.getMinGrouponPrice());
                vo.setMaxGrouponPrice(goods.getMaxGrouponPrice());
                vo.setIsShow(Integer.parseInt(goods.getIsShow()));
                vo.setShopId(goods.getShopId());
                vo.setShopName(goods.getShopName());
            }
            List<GoodsFreightResult> goodsFreightResults = goodsFreightMap.get(vo.getGoodsId());
            vo.setGoodsFreightList(goodsFreightResults);
            vo.setExistGoodsFreight(CollectionUtils.isNotEmpty(goodsFreightResults) ? "有" : "无");
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ChanceGoodsExportVo> exportChanceGoods(ChanceGoodsQueryCondition condition) {
        List<ChanceGoodsVo> chanceGoodsVos = listChanceGoodsByTemplateId(condition);
        List<ChanceGoodsExportVo> vos = BeanCopyUtil.transformList(chanceGoodsVos, ChanceGoodsExportVo.class);
        for (ChanceGoodsExportVo vo : vos) {
            vo.setGrouponPrice(vo.getMinGrouponPrice() + " ~ " + vo.getMaxGrouponPrice());
            vo.setPrice(vo.getMinPrice() + " ~ " + vo.getMaxPrice());
            vo.setSourceTypeStr(vo.getSourceType() == 1 ? "机会商品" : "我有同款");
        }
        return vos;
    }

    @Override
    public void selectChanceGoods(ChanceGoodsSelectCondition condition) {
        LogUtils.info(log, "精选机会商品:{}", condition);
        CheckUtils.notNull(condition.getId(), ProductResultCode.PARAMETER_ID_ERROR);

        ChanceGoods chanceGoods = chanceGoodsService.getById(condition.getId());
        CheckUtils.notNull(chanceGoods, ProductResultCode.CHANCE_GOODS_NOT_EXIST);

        chanceGoods.setIsSelect(condition.getIsSelect());
        chanceGoods.setSelectUser(getUserName());
        chanceGoods.setSelectTime(new Date());
        chanceGoodsService.updateById(chanceGoods);
    }

    @Override
    public PageView<ChanceGoodsTemplateForSignUpVo> listChanceGoodsTemplateForSignUp(ChanceGoodsTemplateQueryForSignUpCondition condition) {
        LogUtils.info(log, "listChanceGoodsTemplateForSignUp:{}", condition);
        PageView<ChanceGoodsTemplateForSignUpVo> pageView = new PageView<>(condition.getPageSize(), condition.getPageNow());

        List<Long> categoryIds = Lists.newArrayList();
        if (condition.getCategoryId() != null) {
            List<Category> categories = categoryService.queryAllChildCategoryByParentId(condition.getCategoryId());
            categoryIds.add(condition.getCategoryId());
            categoryIds.addAll(categories.stream().map(Category::getId).collect(Collectors.toList()));
        }

        Long shopId = getShopId();
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);

        FaMerchantsApply faMerchantsApply = faMerchantsApplyCoreService.queryById(shopId);
        if (faMerchantsApply.getCategoryMainId() != null) {
            CategoryMain categoryMain = categoryMainService.getById(faMerchantsApply.getCategoryMainId());
            CheckUtils.notNull(categoryMain, ProductResultCode.CATEGORY_MAIN_NOT_EXIST);

            List<Long> mainCategoryIds = Lists.newArrayList();
            if (StringUtils.isNotBlank(categoryMain.getLeafCategoryIds())) {
                Arrays.stream(categoryMain.getLeafCategoryIds().split(",")).map(Long::parseLong).forEach(mainCategoryIds::add);
            }
            if (StringUtils.isNotBlank(categoryMain.getLeafSecondCategoryIds())) {
                Arrays.stream(categoryMain.getLeafSecondCategoryIds().split(",")).map(Long::parseLong).forEach(mainCategoryIds::add);
            }
            if (CollectionUtils.isNotEmpty(categoryIds)) {
                categoryIds.retainAll(mainCategoryIds);
            } else {
                categoryIds = Lists.newArrayList(mainCategoryIds);
            }
            if (CollectionUtils.isEmpty(categoryIds)) {
                return pageView;
            }
        }

        List<ChanceGoods> shopExistChanceGoodsList = chanceGoodsService.lambdaQuery()
                .eq(ChanceGoods::getShopId, shopId)
                .eq(ChanceGoods::getIsDel, 0)
                .select(ChanceGoods::getTemplateId)
                .list();
        List<Long> existEnterTemplateIds = shopExistChanceGoodsList.stream().map(ChanceGoods::getTemplateId).collect(Collectors.toList());

        //转换来源
        List<Integer> sourceTypes = new ArrayList<>();
        if (condition.getSourceType() != null){
            if ( 99 == condition.getSourceType()){
                sourceTypes.add(1);
                sourceTypes.add(2);
            }else {
                sourceTypes.add(condition.getSourceType());
            }
        }
        // 默认按照create time desc 排序
        if (condition.getSortType() == null){
            condition.setSortType(3);
        }
        IPage<ChanceGoodsTemplate> page = chanceGoodsTemplateService.lambdaQuery()
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .eq(ChanceGoodsTemplate::getStatus, ChanceGoodsTemplateStatusEnums.OPEN.getCode())
                .eq(condition.getTemplateId() != null, ChanceGoodsTemplate::getId, condition.getTemplateId())
                .notIn(CollectionUtils.isNotEmpty(existEnterTemplateIds), ChanceGoodsTemplate::getId, existEnterTemplateIds)
                .in(CollectionUtils.isNotEmpty(categoryIds), ChanceGoodsTemplate::getCategoryId, categoryIds)
                .in(CollectionUtils.isNotEmpty(sourceTypes), ChanceGoodsTemplate::getSourceType, sourceTypes)
                .ge(condition.getMinPrice() != null, ChanceGoodsTemplate::getMinPrice, condition.getMinPrice())
                .le(condition.getMaxPrice() != null, ChanceGoodsTemplate::getMaxPrice, condition.getMaxPrice())
                .ge(condition.getMinHot() != null, ChanceGoodsTemplate::getGenerateGoodsCount, condition.getMinHot())
                .le(condition.getMaxHot() != null, ChanceGoodsTemplate::getGenerateGoodsCount, condition.getMaxHot())
                .ge(condition.getStartCreateTime() != null, ChanceGoodsTemplate::getCreateTime, condition.getStartCreateTime())
                .le(condition.getEndCreateTime() != null, ChanceGoodsTemplate::getCreateTime, condition.getEndCreateTime())
                .ge(condition.getMinSales() != null, ChanceGoodsTemplate::getSevenSales, condition.getMinSales())
                .le(condition.getMaxSales() != null, ChanceGoodsTemplate::getSevenSales, condition.getMaxSales())
                .orderByDesc(condition.getSortType() != null && condition.getSortType() == 1, ChanceGoodsTemplate::getSevenSales)
                .orderByDesc(condition.getSortType() != null && condition.getSortType() == 2, ChanceGoodsTemplate::getHot)
                .orderByDesc(condition.getSortType() != null && condition.getSortType() == 3, ChanceGoodsTemplate::getCreateTime)
//                .orderByDesc(ChanceGoodsTemplate::getId)
                .page(new Page<>(condition.getPageNow(), condition.getPageSize()));
        List<ChanceGoodsTemplateForSignUpVo> vos = BeanCopyUtil.transformList(page.getRecords(), ChanceGoodsTemplateForSignUpVo.class);
        List<Long> existCategoryIds = vos.stream().map(ChanceGoodsTemplateForSignUpVo::getCategoryId).collect(Collectors.toList());
        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(existCategoryIds);
        vos.forEach(vo -> {
            vo.setCategoryPath(categoryTreeMap.get(vo.getCategoryId()));
            vo.setHot(vo.getGenerateGoodsCount());
            if (vo.getSourceType() != null && (1 ==vo.getSourceType() || 2 ==vo.getSourceType())){
                vo.setSourceType(99);
            }
        });

        pageView.setRecords(vos);
        pageView.setRowCount(page.getTotal());
        return pageView;
    }

    @Override
    public void matchSameChanceGoods(ChanceGoodsMatchSameCondition condition) {
        LogUtils.info(log, "我有同款:{}", condition);
        CheckUtils.check(condition.getGoodsId() == null || condition.getTemplateId() == null, ProductResultCode.PARAMETER_ERROR);

        Long shopId = getShopId();
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);

        ChanceGoodsTemplate chanceGoodsTemplate = chanceGoodsTemplateService.getById(condition.getTemplateId());
        CheckUtils.notNull(chanceGoodsTemplate, ProductResultCode.CHANCE_GOODS_TEMPLATE_NOT_EXIST);
        CheckUtils.check(!ChanceGoodsTemplateStatusEnums.OPEN.getCode().equals(chanceGoodsTemplate.getStatus()), ProductResultCode.CHANCE_GOODS_TEMPLATE_STATUS_FORBID_OPERATE);

        Goods goods = goodsService.getById(condition.getGoodsId());
        CheckUtils.notNull(goods, ProductResultCode.GOODS_NOT_EXIST);
        CheckUtils.check(!shopId.equals(goods.getShopId()), ProductResultCode.EXIST_GOODS_NOT_BELONG_SHOP_EXT);

        ChanceGoods chanceGoods = new ChanceGoods();
        chanceGoods.setTemplateId(condition.getTemplateId());
        chanceGoods.setGoodsId(condition.getGoodsId());
        chanceGoods.setShopId(goods.getShopId());
        chanceGoods.setCategoryId(goods.getCategoryId());
        chanceGoods.setMinPrice(goods.getMinPrice());
        chanceGoods.setMaxPrice(goods.getMaxPrice());
        chanceGoods.setSourceType(2);
        chanceGoods.setIsSelect(0);
        chanceGoods.setAuditStatus(1);
        chanceGoods.setAuditUser("system");
        chanceGoods.setAuditTime(new Date());
        chanceGoods.setIsDel(0);
        chanceGoods.setCreateTime(new Date());
        chanceGoods.setCreateUser(getUserName());
        chanceGoodsService.save(chanceGoods);

        chanceGoodsTemplate.setGenerateGoodsCount(chanceGoodsTemplate.getGenerateGoodsCount() + 1);
        chanceGoodsTemplateService.updateById(chanceGoodsTemplate);

//        UpdateLockLabelCondition updateLockLabelCondition = new UpdateLockLabelCondition(Collections.singletonList(condition.getGoodsId()),
//                Collections.singletonList(GoodsLockLabelTypEnums.CHANCE_GOODS.getCode()));
//        goodsLockCoreService.addLockByApi(updateLockLabelCondition);

//        checkChanceGoodsTemplateForClose(Collections.singletonList(condition.getGoodsId()));
    }

    @Override
    public void createSameChanceGoods(ChanceGoodsCreateSameCondition condition) {
        LogUtils.info(log, "发布同款机会商品:{}", condition);
        CheckUtils.notNull(condition.getChanceGoodsTemplateId(), ProductResultCode.PARAMETER_ERROR);

        ChanceGoodsTemplate chanceGoodsTemplate = chanceGoodsTemplateService.getById(condition.getChanceGoodsTemplateId());
        CheckUtils.notNull(chanceGoodsTemplate, ProductResultCode.CHANCE_GOODS_TEMPLATE_NOT_EXIST);
        CheckUtils.check(!ChanceGoodsTemplateStatusEnums.OPEN.getCode().equals(chanceGoodsTemplate.getStatus()), ProductResultCode.CHANCE_GOODS_TEMPLATE_STATUS_FORBID_OPERATE);

        BaseTokenUserInfo userInfo = getUserInfo();
        Long shopId = userInfo.getShopId();
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);
        String shopName = userInfo.getShopName();
        String operator = userInfo.getUserName();
        Date now = new Date();

        ProductInfoInput infoInput = BeanCopyUtil.transform(condition, ProductInfoInput.class);
        infoInput.setShopId(shopId);
        infoInput.setShopName(shopName);
        infoInput.setStoreName(shopName);
        infoInput.setIsShow(null);
        infoInput.setTag("");
        infoInput.setOperationLogType(OperationLogTypeEnums.CREATE_SAME_CHANCE_GOODS_FROM_TEMPLATE.getCode());
        infoInput.setChannel(ChannelEnums.CREATE_SAME_CHANCE_GOODS_FROM_TEMPLATE.getCode());
        Long goodsId = newAddGoodsCoreService.shopAddGoods(infoInput);
        CheckUtils.notNull(goodsId, ProductResultCode.ADD_ERROR);

        Goods goods = goodsService.getById(goodsId);
        ChanceGoods chanceGoods = new ChanceGoods();
        chanceGoods.setTemplateId(condition.getChanceGoodsTemplateId());
        chanceGoods.setGoodsId(goods.getId());
        chanceGoods.setShopId(goods.getShopId());
        chanceGoods.setCategoryId(goods.getCategoryId());
        chanceGoods.setMinPrice(goods.getMinPrice());
        chanceGoods.setMaxPrice(goods.getMaxPrice());
        chanceGoods.setSourceType(1);
        chanceGoods.setIsSelect(0);
        chanceGoods.setAuditStatus(1);
        chanceGoods.setAuditUser("system");
        chanceGoods.setAuditTime(new Date());
        chanceGoods.setIsDel(0);
        chanceGoods.setCreateTime(now);
        chanceGoods.setCreateUser(operator);
        chanceGoodsService.save(chanceGoods);

        chanceGoodsTemplate.setGenerateGoodsCount(chanceGoodsTemplate.getGenerateGoodsCount() + 1);
        chanceGoodsTemplateService.updateById(chanceGoodsTemplate);

//        goods.setIsLock(1);
//        goodsService.updateById(goods);

//        UpdateLockLabelCondition updateLockLabelCondition = new UpdateLockLabelCondition(Collections.singletonList(goodsId),
//                Collections.singletonList(GoodsLockLabelTypEnums.CHANCE_GOODS.getCode()));
//        goodsLockCoreService.addLockByApi(updateLockLabelCondition);

//        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
//        goodsSyncModel.setGoodsId(goodsId);
//        goodsSyncModel.setSyncTime(System.currentTimeMillis());
//        mqSender.send("SYNC_GOODS_TOPIC_UPDATE", goodsSyncModel);
    }

    @Override
    public PageView<ChanceGoodsForAuditVo> listChanceGoodsAudit(ChanceGoodsQueryForAuditCondition condition) {
        LogUtils.info(log, "listChanceGoodsAudit:{}", condition);
        List<Long> searchCategoryIds = Lists.newArrayList();
        if (condition.getCategoryId() != null) {
            List<Category> categories = categoryService.queryAllChildCategoryByParentId(condition.getCategoryId());
            searchCategoryIds.add(condition.getCategoryId());
            searchCategoryIds.addAll(categories.stream().map(Category::getId).collect(Collectors.toList()));
        }

        List<Long> searchGoodsIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(condition.getGoodsIdListStr())) {
            searchGoodsIds = Arrays.stream(condition.getGoodsIdListStr().trim().split("\n"))
                    .filter(s -> StringUtils.isNotBlank(s) && !" ".equals(s))
                    .map(s -> Long.parseLong(s.trim()))
                    .distinct()
                    .collect(Collectors.toList());
        }

        IPage<ChanceGoods> page = chanceGoodsService.lambdaQuery()
                .eq(condition.getTemplateId() != null, ChanceGoods::getTemplateId, condition.getTemplateId())
                .eq(condition.getShopId() != null, ChanceGoods::getShopId, condition.getShopId())
                .eq(condition.getAuditStatus() != null, ChanceGoods::getAuditStatus, condition.getAuditStatus())
                .in(CollectionUtils.isNotEmpty(searchCategoryIds), ChanceGoods::getCategoryId, searchCategoryIds)
                .ge(condition.getMinPrice() != null, ChanceGoods::getMinPrice, condition.getMinPrice())
                .ge(condition.getMaxPrice() != null, ChanceGoods::getMaxPrice, condition.getMaxPrice())
                .in(CollectionUtils.isNotEmpty(searchGoodsIds), ChanceGoods::getGoodsId, searchGoodsIds)
                .ge(condition.getStartCreateTime() != null, ChanceGoods::getCreateTime, condition.getStartCreateTime())
                .le(condition.getEndCreateTime() != null, ChanceGoods::getCreateTime, condition.getEndCreateTime())
                .ge(condition.getStartAuditTime() != null, ChanceGoods::getAuditTime, condition.getStartAuditTime())
                .le(condition.getEndAuditTime() != null, ChanceGoods::getAuditTime, condition.getEndAuditTime())
                .eq(StringUtils.isNotBlank(condition.getCreateUser()), ChanceGoods::getCreateUser, condition.getCreateUser())
                .eq(StringUtils.isNotBlank(condition.getAuditUser()), ChanceGoods::getAuditUser, condition.getAuditUser())
                .eq(ChanceGoods::getIsDel, 0)
                .page(new Page<>(condition.getPageNow(), condition.getPageSize()));

        PageView<ChanceGoodsForAuditVo> pageView = new PageView<>(condition.getPageSize(), condition.getPageNow());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return pageView;
        }

        List<ChanceGoods> records = page.getRecords();
        List<Long> goodsIds = records.stream().map(ChanceGoods::getGoodsId).collect(Collectors.toList());
        List<Goods> goodsList = goodsService.queryAllGoodsByIds(goodsIds);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));

        List<Long> chanceGoodsTemplateIds = records.stream().map(ChanceGoods::getTemplateId).distinct().collect(Collectors.toList());
        List<ChanceGoodsTemplate> chanceGoodsTemplates = Lists.newArrayList(chanceGoodsTemplateService.selectByIds(chanceGoodsTemplateIds));
        Map<Long, ChanceGoodsTemplate> goodsTemplateMap = chanceGoodsTemplates.stream().collect(Collectors.toMap(ChanceGoodsTemplate::getId, Function.identity(), (a, b) -> a));

        List<Long> categoryIds = goodsList.stream().map(Goods::getCategoryId).collect(Collectors.toList());

        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(categoryIds);

        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(goodsIds);
        List<GoodsFreightResult> goodsFreightResultList = BeanCopyUtil.transformList(goodsFreights, GoodsFreightResult.class);
        Map<Long, List<GoodsFreightResult>> goodsFreightMap = goodsFreightResultList.stream().collect(Collectors.groupingBy(GoodsFreightResult::getGoodsId));


        List<ChanceGoodsForAuditVo> vos = records.stream().map(chanceGoods -> {
            ChanceGoodsForAuditVo vo = BeanCopyUtil.transform(chanceGoods, ChanceGoodsForAuditVo.class);
            ChanceGoodsTemplate chanceGoodsTemplate = goodsTemplateMap.get(vo.getTemplateId());
            if (chanceGoodsTemplate != null) {
                vo.setTemplateGoodsId(chanceGoodsTemplate.getGoodsId());
                vo.setTemplateGoodsName(chanceGoodsTemplate.getGoodsName());
                vo.setTemplateMainImage(chanceGoodsTemplate.getMainImage());
                vo.setTemplatePrice(chanceGoodsTemplate.getMinPrice() + " ~ " + chanceGoodsTemplate.getMaxPrice());
            }
            Goods goods = goodsMap.get(vo.getGoodsId());
            if (goods != null) {
                vo.setCategoryId(goods.getCategoryId());
                vo.setCategoryPath(categoryTreeMap.get(goods.getCategoryId()));
                vo.setName(goods.getName());
                vo.setMainImage(goods.getMainImage());
                vo.setPrice(goods.getMinPrice() + " ~ " + goods.getMaxPrice());
                vo.setIsShow(Integer.parseInt(goods.getIsShow()));
                vo.setShopName(goods.getShopName());
            }
            List<GoodsFreightResult> goodsFreightResults = goodsFreightMap.get(vo.getGoodsId());
            vo.setGoodsFreightList(goodsFreightResults);
            vo.setExistGoodsFreight(CollectionUtils.isNotEmpty(goodsFreightResults) ? "有" : "无");
            return vo;
        }).collect(Collectors.toList());
        pageView.setRecords(vos);
        pageView.setRowCount(page.getTotal());
        return pageView;
    }

    @Override
    public List<ChanceGoodsForAuditExportVo> exportChanceGoodsAudit(ChanceGoodsQueryForAuditCondition condition) {
        condition.setPageSize(1000);
        PageView<ChanceGoodsForAuditVo> pageView = listChanceGoodsAudit(condition);
        List<ChanceGoodsForAuditVo> records = pageView.getRecords();

        List<ChanceGoodsForAuditExportVo> vos = BeanCopyUtil.transformList(records, ChanceGoodsForAuditExportVo.class);
        for (ChanceGoodsForAuditExportVo vo : vos) {
            vo.setAuditStatusStr(vo.getAuditStatus() == 0 ? "待审核" : vo.getAuditStatus() == 1 ? "入选" : "落选");
        }
        return vos;
    }

    @Override
    public void chooseChanceGoods(ChanceGoodsChooseCondition condition) {
        LogUtils.info(log, "审核机会商品:{}", condition);
        CheckUtils.check(CollectionUtils.isEmpty(condition.getIds()) || condition.getAuditStatus() == null, ProductResultCode.PARAMETER_ID_ERROR);

        List<Long> chanceGoodsIds = condition.getIds();
        List<ChanceGoods> chanceGoodsList = Lists.newArrayList(chanceGoodsService.selectByIds(chanceGoodsIds));
        if (chanceGoodsList.size() < chanceGoodsIds.size()) {
            List<Long> existChanceGoodsIds = chanceGoodsList.stream().map(ChanceGoods::getId).collect(Collectors.toList());
            chanceGoodsIds.removeAll(existChanceGoodsIds);
            CheckUtils.check(true, CustomResultCode.fill(ProductResultCode.CHANCE_GOODS_NOT_EXIST_EXT, StringUtils.join(chanceGoodsIds, ",")));
        }

        List<Long> goodsIds = chanceGoodsList.stream().map(ChanceGoods::getGoodsId).distinct().collect(Collectors.toList());
        List<Goods> goodsList = goodsService.lambdaQuery().in(Goods::getId, goodsIds).eq(Goods::getIsDel, 0).list();
        if (goodsList.size() < goodsIds.size()) {
            List<Long> existGoodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());
            goodsIds.removeAll(existGoodsIds);
            CheckUtils.check(true, CustomResultCode.fill(ProductResultCode.GOODS_NOT_EXIST_EXT, StringUtils.join(goodsIds, ",")));
        }

        String auditor = getUserName();
        Date now = new Date();
        for (ChanceGoods chanceGoods : chanceGoodsList) {
            chanceGoods.setAuditStatus(condition.getAuditStatus());
            chanceGoods.setAuditUser(auditor);
            chanceGoods.setAuditTime(now);
            chanceGoods.setAuditRemark(condition.getAuditRemark());
        }
        chanceGoodsService.updateBatchById(chanceGoodsList);

        //审核通过 -> 商品上架, 校验是否需要关闭模板
        if (condition.getAuditStatus() == 1) {
            List<Goods> updateGoodsList = Lists.newArrayList();
            for (Goods goods : goodsList) {
                String originalShowStatus = goods.getIsShow();
                if ("0".equals(originalShowStatus)) {
                    goods.setIsShow("1");
                    goods.setUpdateTime(LocalDateTime.now());
                    updateGoodsList.add(goods);

                    GoodsOperationLogDto dto = new GoodsOperationLogDto()
                            .goodsId(goods.getId())
                            .type(OperationLogTypeEnums.SHOW_BY_CHANCE_GOODS)
                            .content(OperationLogTypeEnums.SHOW_BY_CHANCE_GOODS.getDesc())
                            .oldData(originalShowStatus)
                            .newData("1")
                            .status(1)
                            .user(auditor);
                    mqSender.send("SYNC_GOODS_OPERATION_LOG", dto);
                }
            }

            if (CollectionUtils.isNotEmpty(updateGoodsList)) {
                goodsService.updateBatchById(updateGoodsList);
            }

            //同步上架状态到es
//            Map<Long, Integer> dataMap = goodsIds.stream().collect(Collectors.toMap(Function.identity(), a -> 1, (a, b) -> b));
//            elasticsearchHandler.syncGoodsEsField(1, dataMap, MDC.get("traceId"));

            CustomizedGoodsSyncModel goodsSyncModel = new CustomizedGoodsSyncModel();
            goodsSyncModel.setGoodsIds(goodsIds);
            goodsSyncModel.setValues(Collections.singletonList(CustomizedGoodsSyncEnums.GOODS_STATUS.getVal()));
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("审核机会商品");
            goodsSyncModel.setSourceService("vp");
            mqSender.send("CUSTOMIZED_SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel));

            //校验是否需要关闭模板
//            checkChanceGoodsTemplateForClose(goodsIds);
        }
    }

    @Override
    public PageView<ChanceGoodsSelectedVo> listSelectedChanceGoods(ChanceGoodsSelectedQueryCondition condition) {
        LogUtils.info(log, "listSelectedChanceGoods:{}", condition);
        List<Long> searchCategoryIds = Lists.newArrayList();
        if (condition.getCategoryId() != null) {
            List<Category> categories = categoryService.queryAllChildCategoryByParentId(condition.getCategoryId());
            searchCategoryIds.add(condition.getCategoryId());
            searchCategoryIds.addAll(categories.stream().map(Category::getId).collect(Collectors.toList()));
        }

        List<Long> searchGoodsIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(condition.getGoodsIdListStr())) {
            searchGoodsIds = Arrays.stream(condition.getGoodsIdListStr().trim().split("\n"))
                    .filter(s -> StringUtils.isNotBlank(s) && !" ".equals(s))
                    .map(s -> Long.parseLong(s.trim()))
                    .distinct()
                    .collect(Collectors.toList());
        }

        List<Long> templateIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(condition.getTemplateId())) {
            templateIds = Arrays.stream(condition.getTemplateId().trim().split("\n"))
                    .filter(s -> StringUtils.isNotBlank(s) && !" ".equals(s))
                    .map(s -> Long.parseLong(s.trim()))
                    .distinct()
                    .collect(Collectors.toList());
        }


        IPage<ChanceGoods> page = chanceGoodsService.lambdaQuery()
                .eq(ChanceGoods::getAuditStatus, 1)
                .eq(ChanceGoods::getIsSelect, 1)
                .in(CollectionUtils.isNotEmpty(templateIds), ChanceGoods::getTemplateId, templateIds)
                .eq(condition.getShopId() != null, ChanceGoods::getShopId, condition.getShopId())
                .in(CollectionUtils.isNotEmpty(searchCategoryIds), ChanceGoods::getCategoryId, searchCategoryIds)
                .ge(condition.getMinPrice() != null, ChanceGoods::getMinPrice, condition.getMinPrice())
                .le(condition.getMaxPrice() != null, ChanceGoods::getMaxPrice, condition.getMaxPrice())
                .in(CollectionUtils.isNotEmpty(searchGoodsIds), ChanceGoods::getGoodsId, searchGoodsIds)
                .ge(condition.getStartSelectTime() != null, ChanceGoods::getSelectTime, DateUtil.getDayBegin(condition.getStartSelectTime()))
                .le(condition.getEndSelectTime() != null, ChanceGoods::getSelectTime, DateUtil.getDayEnd(condition.getEndSelectTime()))
                .eq(ChanceGoods::getIsDel, 0)
                .page(new Page<>(condition.getPageNow(), condition.getPageSize()));

        PageView<ChanceGoodsSelectedVo> pageView = new PageView<>(condition.getPageSize(), condition.getPageNow());
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return pageView;
        }

        List<ChanceGoods> records = page.getRecords();
        List<Long> goodsIds = records.stream().map(ChanceGoods::getGoodsId).collect(Collectors.toList());
        List<Goods> goodsList = goodsService.queryAllGoodsByIds(goodsIds);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));

        List<Long> chanceGoodsTemplateIds = records.stream().map(ChanceGoods::getTemplateId).distinct().collect(Collectors.toList());
        List<ChanceGoodsTemplate> chanceGoodsTemplates = Lists.newArrayList(chanceGoodsTemplateService.selectByIds(chanceGoodsTemplateIds));
        Map<Long, ChanceGoodsTemplate> goodsTemplateMap = chanceGoodsTemplates.stream().collect(Collectors.toMap(ChanceGoodsTemplate::getId, Function.identity(), (a, b) -> a));

        List<Long> categoryIds = goodsList.stream().map(Goods::getCategoryId).collect(Collectors.toList());

        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(categoryIds);

        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(goodsIds);
        List<GoodsFreightResult> goodsFreightResultList = BeanCopyUtil.transformList(goodsFreights, GoodsFreightResult.class);
        Map<Long, List<GoodsFreightResult>> goodsFreightMap = goodsFreightResultList.stream().collect(Collectors.groupingBy(GoodsFreightResult::getGoodsId));


        List<ChanceGoodsSelectedVo> vos = records.stream().map(chanceGoods -> {
            ChanceGoodsSelectedVo vo = BeanCopyUtil.transform(chanceGoods, ChanceGoodsSelectedVo.class);
            ChanceGoodsTemplate chanceGoodsTemplate = goodsTemplateMap.get(vo.getTemplateId());
            if (chanceGoodsTemplate != null) {
                vo.setTemplateGoodsId(chanceGoodsTemplate.getGoodsId());
                vo.setTemplateGoodsName(chanceGoodsTemplate.getGoodsName());
                vo.setTemplateMainImage(chanceGoodsTemplate.getMainImage());
            }
            Goods goods = goodsMap.get(vo.getGoodsId());
            if (goods != null) {
                vo.setCategoryId(goods.getCategoryId());
                vo.setCategoryPath(categoryTreeMap.get(goods.getCategoryId()));
                vo.setName(goods.getName());
                vo.setMainImage(goods.getMainImage());
                vo.setPrice(goods.getMinPrice() + " ~ " + goods.getMaxPrice());
            }
            List<GoodsFreightResult> goodsFreightResults = goodsFreightMap.get(vo.getGoodsId());
            vo.setGoodsFreightList(goodsFreightResults);
            vo.setExistGoodsFreight(CollectionUtils.isNotEmpty(goodsFreightResults) ? "有" : "无");
            return vo;
        }).collect(Collectors.toList());
        pageView.setRecords(vos);
        pageView.setRowCount(page.getTotal());
        return pageView;
    }

    @Override
    public List<ChanceGoodsSelectedExportVo> exportSelectedChanceGoods(ChanceGoodsSelectedQueryCondition condition) {
        condition.setPageSize(1000);
        PageView<ChanceGoodsSelectedVo> pageView = listSelectedChanceGoods(condition);
        List<ChanceGoodsSelectedVo> records = pageView.getRecords();

        List<ChanceGoodsSelectedExportVo> vos = BeanCopyUtil.transformList(records, ChanceGoodsSelectedExportVo.class);
        for (ChanceGoodsSelectedExportVo vo : vos) {
            vo.setSourceTypeStr(vo.getSourceType() == 1 ? "机会商品" : "我有同款");
        }
        return vos;
    }

    @Override
    public void checkChanceGoodsIsSkuConsistentAndIsMinSkuAvgPrice() {
        LogUtils.info(log, "checkChanceGoodsIsSkuConsistentAndIsMinSkuAvgPrice ===> 开始每日校验机会商品是否sku一致和是否sku均价最低");
        //每日凌晨跑脚本，先判断每个模板生成的商品的SKU(red-s)项是否和模板一致，并打标。
        //同时计算每个商品的SKU均价(按德国价格计算即可)，打上价格最低是 的标签。方便运营导出后人工额外加流量
        long startTemplateId = 0;
        IPage<ChanceGoodsTemplate> page = chanceGoodsTemplateService.lambdaQuery()
                .gt(ChanceGoodsTemplate::getId, startTemplateId)
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .page(new Page<>(1, 20));
        while (CollectionUtils.isNotEmpty(page.getRecords())) {
            LogUtils.info(log, "checkChanceGoodsIsSkuConsistentAndIsMinSkuAvgPrice ===> startTemplateId:{}, templateSize:{}", startTemplateId, page.getRecords().size());
            List<ChanceGoodsTemplate> chanceGoodsTemplates = page.getRecords();
            List<Long> templateGoodsIds = chanceGoodsTemplates.stream().map(ChanceGoodsTemplate::getGoodsId).collect(Collectors.toList());
            List<GoodsItem> allTemplateGoodsItems = goodsItemService.queryGoodsIdsList(templateGoodsIds);
            Map<Long, List<GoodsItem>> templateGoodsItemMap = allTemplateGoodsItems.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

            List<Long> templateIds = chanceGoodsTemplates.stream().map(ChanceGoodsTemplate::getId).collect(Collectors.toList());
            List<ChanceGoods> chanceGoodsList = chanceGoodsService.lambdaQuery()
                    .in(ChanceGoods::getTemplateId, templateIds)
                    .eq(ChanceGoods::getIsDel, 0)
                    .list();
            List<Long> goodsIds = chanceGoodsList.stream().map(ChanceGoods::getGoodsId).collect(Collectors.toList());
            Map<Long, List<ChanceGoods>> templateChanceGoodsMap = chanceGoodsList.stream().collect(Collectors.groupingBy(ChanceGoods::getTemplateId));

            List<GoodsItem> allChanceGoodsItems = goodsItemService.queryGoodsIdsList(goodsIds);
            Map<Long, List<GoodsItem>> chanceGoodsItemMap = allChanceGoodsItems.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

            for (ChanceGoodsTemplate template : chanceGoodsTemplates) {
                List<GoodsItem> templateGoodsItems = templateGoodsItemMap.get(template.getGoodsId());
                List<String> allPropertyList = templateGoodsItems.stream().map(GoodsItem::getPvalueStr).collect(Collectors.toList());

                List<ChanceGoods> belongChanceGoodsList = templateChanceGoodsMap.get(template.getId());
                if (CollectionUtils.isNotEmpty(belongChanceGoodsList)) {
                    for (ChanceGoods chanceGoods : belongChanceGoodsList) {
                        List<GoodsItem> chanceGoodsItems = chanceGoodsItemMap.get(chanceGoods.getGoodsId());
                        List<String> propertyList = chanceGoodsItems.stream().map(GoodsItem::getPvalueStr).collect(Collectors.toList());
                        propertyList.removeAll(allPropertyList);
                        chanceGoods.setIsSkuConsistent(CollectionUtils.isEmpty(propertyList) ? 1 : 0);

                        BigDecimal sum = chanceGoodsItems.stream().map(GoodsItem::getPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        BigDecimal avgPrice = sum.divide(BigDecimal.valueOf(chanceGoodsItems.size()), 2, BigDecimal.ROUND_HALF_UP);
                        chanceGoods.setAvgPrice(avgPrice);
                    }
                    belongChanceGoodsList.sort(Comparator.comparing(ChanceGoods::getAvgPrice));
                    BigDecimal minAvgPrice = belongChanceGoodsList.get(0).getAvgPrice();
                    for (ChanceGoods chanceGoods : belongChanceGoodsList) {
                        chanceGoods.setIsMinSkuAvgPrice(chanceGoods.getAvgPrice().compareTo(minAvgPrice) == 0 ? 1 : 0);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(chanceGoodsList)) {
                chanceGoodsService.updateBatchById(chanceGoodsList);
            }


            startTemplateId = chanceGoodsTemplates.get(chanceGoodsTemplates.size() - 1).getId();
            page = chanceGoodsTemplateService.lambdaQuery()
                    .gt(ChanceGoodsTemplate::getId, startTemplateId)
                    .eq(ChanceGoodsTemplate::getIsDel, 0)
                    .page(new Page<>(1, 100));
        }
    }

    @Override
    public void checkChanceGoodsTemplateForClose(List<Long> goodsIds) {
        LogUtils.info(log, "校验商品是否有关联机会商品模板需要关闭 goodsIds:{}", goodsIds);
        if (CollectionUtils.isEmpty(goodsIds)) {
            return;
        }

        List<ChanceGoods> chanceGoodsList = chanceGoodsService.lambdaQuery()
                .in(ChanceGoods::getGoodsId, goodsIds)
                .eq(ChanceGoods::getAuditStatus, 1)
                .eq(ChanceGoods::getIsDel, 0)
                .list();
        if (CollectionUtils.isEmpty(chanceGoodsList)) {
            LogUtils.info(log, "无相关机会商品绑定信息，return");
            return;
        }

        List<Long> templateIds = chanceGoodsList.stream().map(ChanceGoods::getTemplateId).collect(Collectors.toList());
        List<ChanceGoodsTemplate> templateList = chanceGoodsTemplateService.lambdaQuery()
                .in(ChanceGoodsTemplate::getId, templateIds)
                .eq(ChanceGoodsTemplate::getStatus, ChanceGoodsTemplateStatusEnums.OPEN.getCode())
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .list();
        if (CollectionUtils.isEmpty(templateList)) {
            LogUtils.info(log, "查不到开放中机会商品模板，templateIds:{}", templateIds);
            return;
        }

        templateIds = templateList.stream().map(ChanceGoodsTemplate::getId).distinct().collect(Collectors.toList());
        List<ChanceGoods> allChanceGoodsList = chanceGoodsService.lambdaQuery()
                .in(ChanceGoods::getTemplateId, templateIds)
                .eq(ChanceGoods::getAuditStatus, 1)
                .eq(ChanceGoods::getIsDel, 0)
                .list();

        Map<Long, List<ChanceGoods>> templateChangeGoodsMap = allChanceGoodsList.stream().collect(Collectors.groupingBy(ChanceGoods::getTemplateId));

        List<Long> allGoodsIds = allChanceGoodsList.stream().map(ChanceGoods::getGoodsId).distinct().collect(Collectors.toList());

        //上架状态的商品id
        List<Long> showGoodsId = goodsService.lambdaQuery()
                .in(Goods::getId, allGoodsIds)
                .eq(Goods::getIsDel, 0)
                .eq(Goods::getIsShow, "1")
                .select(Goods::getId)
                .list()
                .stream()
                .map(Goods::getId)
                .collect(Collectors.toList());

        List<ChanceGoodsTemplate> needUpdateList = Lists.newArrayList();
        Date now = new Date();
        for (ChanceGoodsTemplate template : templateList) {
            List<ChanceGoods> belongChanceGoodsList = templateChangeGoodsMap.get(template.getId());
            long count = belongChanceGoodsList.stream().filter(chanceGoods -> showGoodsId.contains(chanceGoods.getGoodsId())).count();
            if (count > 5) {
                template.setStatus(ChanceGoodsTemplateStatusEnums.AUTO_CLOSE.getCode());
                template.setUpdateUser("system");
                template.setUpdateTime(now);
                needUpdateList.add(template);
            }
        }
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            chanceGoodsTemplateService.updateBatchById(needUpdateList);
        }
    }

    @Override
    public void checkChanceGoodsTemplateForOpen() {
        LogUtils.info(log, "checkChanceGoodsTemplateForOpen ===> 开始每日校验机会商品模板报名商品的在架状态");
        //次日0点起脚本，检查所有自动关闭的模板里的商品在架状态,和是否和模板一致，
        //如果<5个和模板一致的且在架的商品，则把模板继续放开给商家复制，差几个可报名几个，报满后再继续自动关闭

        long pageNow = 1;
        long totalPages = 99999;


        while (pageNow < totalPages) {
            IPage<ChanceGoodsTemplate> page = chanceGoodsTemplateService.lambdaQuery()
                    .eq(ChanceGoodsTemplate::getIsDel, 0)
                    .eq(ChanceGoodsTemplate::getStatus, ChanceGoodsTemplateStatusEnums.AUTO_CLOSE.getCode())
                    .page(new Page<>(pageNow, 20));

            handler(page.getRecords());

            totalPages = page.getPages();
            pageNow++;
        }
    }

    @Override
    public List<ChanceGoodsTemplateVo> randomChanceGoodsList(Integer pageSize) {
        List<Long> shopCategoryIdList = getShopCategoryIdList();
        IPage<ChanceGoodsTemplate> page = chanceGoodsTemplateService.lambdaQuery()
                .eq(ChanceGoodsTemplate::getStatus, 1)
                .in(CollectionUtils.isNotEmpty(shopCategoryIdList), ChanceGoodsTemplate::getCategoryId, shopCategoryIdList)
                .orderByDesc(ChanceGoodsTemplate::getSevenSales)
                .page(new Page<>(changeGoodsPageStart++, pageSize));
        if(CollectionUtils.isEmpty(page.getRecords())){
            changeGoodsPageStart = 1L;
            page = chanceGoodsTemplateService.lambdaQuery()
                    .eq(ChanceGoodsTemplate::getStatus, 1)
                    .in(CollectionUtils.isNotEmpty(shopCategoryIdList), ChanceGoodsTemplate::getCategoryId, shopCategoryIdList)
                    .orderByDesc(ChanceGoodsTemplate::getSevenSales)
                    .page(new Page<>(changeGoodsPageStart++, pageSize));
        }
        List<ChanceGoodsTemplate> records = page.getRecords();
        if(CollectionUtils.isEmpty(page.getRecords())){
            return new ArrayList<>();
        }
        List<Long> goodsIdList = records.stream().map(ChanceGoodsTemplate::getGoodsId).collect(Collectors.toList());
        Collection<Goods> goodsList = goodsService.listByIds(goodsIdList);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity()));
        List<ChanceGoodsTemplateVo> collect = page.getRecords().stream()
                .map(chanceGoods -> {
                    ChanceGoodsTemplateVo goodsVo = new ChanceGoodsTemplateVo();
                    BeanCopyUtil.copyProperties(chanceGoods, goodsVo);
                    goodsVo.setSales(chanceGoods.getSevenSales());
                    Goods goods = goodsMap.get(chanceGoods.getGoodsId());
                    if(Objects.nonNull(goods)){
                        goodsVo.setMainImage(goods.getMainImage());
                        goodsVo.setMaxPrice(goods.getMaxPrice());
                        goodsVo.setMinPrice(goods.getMinPrice());
                    }
                    return goodsVo;
                }).collect(Collectors.toList());
        return collect;
    }

    private List<Long> getShopCategoryIdList(){
        Long shopId = clientInfoSupportService.getShopInfo().getShopId();
        if(Objects.isNull(shopId)){
            log.info("getShopCategoryIdList shopId is null");
            return null;
        }
        FaMerchantsApply faMerchantsApply = faMerchantsApplyCoreService.queryById(shopId);
        Long categoryMainId = faMerchantsApply.getCategoryMainId();
        if(Objects.isNull(categoryMainId)){
            log.info("getShopCategoryIdList categoryMainId is null");
            return null;
        }
        CategoryMain categoryMain = categoryMainService.getById(categoryMainId);
        List<Long> mainCategoryIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(categoryMain.getLeafCategoryIds())) {
            Arrays.stream(categoryMain.getLeafCategoryIds().split(",")).map(Long::parseLong).forEach(mainCategoryIds::add);
        }
        if (StringUtils.isNotBlank(categoryMain.getLeafSecondCategoryIds())) {
            Arrays.stream(categoryMain.getLeafSecondCategoryIds().split(",")).map(Long::parseLong).forEach(mainCategoryIds::add);
        }
        return mainCategoryIds;
    }

    private void handler(List<ChanceGoodsTemplate> needCheckTemplateList) {
        if (CollectionUtils.isEmpty(needCheckTemplateList)) {
            LogUtils.info(log, "checkChanceGoodsTemplateForOpen ===> 暂无状态为自动关闭的机会商品模板");
            return;
        }

        List<Long> needCheckTemplateIds = needCheckTemplateList.stream().map(ChanceGoodsTemplate::getId).collect(Collectors.toList());
        List<ChanceGoods> chanceGoodsList = chanceGoodsService.lambdaQuery()
                .in(ChanceGoods::getTemplateId, needCheckTemplateIds)
                .eq(ChanceGoods::getAuditStatus, 1)
                .eq(ChanceGoods::getIsDel, 0)
                .list();
        Map<Long, List<ChanceGoods>> templateChanceGoodsMap = chanceGoodsList.stream().collect(Collectors.groupingBy(ChanceGoods::getTemplateId));

        List<Long> showStatusGoodsIds = Lists.newArrayList();
        List<Long> needCheckShowStatusGoodsIds = Lists.newArrayList();
        for (Long needCheckTemplateId : needCheckTemplateIds) {
            //查询该模板已入选的商品
            List<ChanceGoods> needCheckShowStatusChanceGoodsList = templateChanceGoodsMap.get(needCheckTemplateId);
            if (CollectionUtils.isNotEmpty(needCheckShowStatusChanceGoodsList) && needCheckShowStatusChanceGoodsList.size() >= 5) {
                needCheckShowStatusChanceGoodsList.stream().map(ChanceGoods::getGoodsId).forEach(needCheckShowStatusGoodsIds::add);
            }
        }

        LogUtils.info(log, "checkChanceGoodsTemplateForOpen ===> 需要校验在架状态的商品id:{}", needCheckShowStatusGoodsIds);
        if (CollectionUtils.isNotEmpty(needCheckShowStatusGoodsIds)) {
            showStatusGoodsIds = goodsService.lambdaQuery()
                    .in(Goods::getId, needCheckShowStatusGoodsIds)
                    .eq(Goods::getIsDel, 0)
                    .eq(Goods::getIsShow, "1")
                    .select(Goods::getId)
                    .list()
                    .stream()
                    .map(Goods::getId)
                    .collect(Collectors.toList());
        }

        List<ChanceGoodsTemplate> needUpdateTemplateList = Lists.newArrayList();
        for (ChanceGoodsTemplate template : needCheckTemplateList) {
            List<ChanceGoods> belongChanceGoodsList = templateChanceGoodsMap.get(template.getId());
            if (belongChanceGoodsList.size() < 5) {
                LogUtils.info(log, "checkChanceGoodsTemplateForOpen ===> 入选总商品数<5，重新放开 templateId:{}", template.getId());
                template.setStatus(ChanceGoodsTemplateStatusEnums.OPEN.getCode());
                needUpdateTemplateList.add(template);
            } else {
                List<Long> finalShowStatusGoodsIds = showStatusGoodsIds;
                long count = belongChanceGoodsList.stream().filter(chanceGoods -> finalShowStatusGoodsIds.contains(chanceGoods.getGoodsId())).count();
                if (count < 5) {
                    LogUtils.info(log, "checkChanceGoodsTemplateForOpen ===> 入选&&在架商品数<5，重新放开 templateId:{}", template.getId());
                    template.setStatus(ChanceGoodsTemplateStatusEnums.OPEN.getCode());
                    needUpdateTemplateList.add(template);
                }
            }
        }

        LogUtils.info(log, "checkChanceGoodsTemplateForOpen ===> 重新放开的模板id： needUpdateTemplateList:{}", needUpdateTemplateList.stream().map(ChanceGoodsTemplate::getId).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(needUpdateTemplateList)) {
            chanceGoodsTemplateService.updateBatchById(needUpdateTemplateList);
        }
    }


    @Override
    public void updateCategory(ChanceGoodsUpdateCategoryCondition condition) {
        LogUtils.info(log, "修改机会商品模板类目 condition:{}", condition);
        CheckUtils.check(condition.getId() == null || condition.getCategoryId() == null, ProductResultCode.PARAMETER_ERROR);

        ChanceGoodsTemplate chanceGoodsTemplate = chanceGoodsTemplateService.getById(condition.getId());
        CheckUtils.notNull(chanceGoodsTemplate, ProductResultCode.CHANCE_GOODS_TEMPLATE_NOT_EXIST);

        Category category = categoryService.getById(condition.getCategoryId());
        CheckUtils.check(category == null || category.getIsDel() == 1, ProductResultCode.CATEGORY_NOT_FIND);
        if (chanceGoodsTemplate.getCategoryId().equals(condition.getCategoryId())) {
            return;
        }

        Goods goods = goodsService.getById(chanceGoodsTemplate.getGoodsId());
        goods.setCategoryId(condition.getCategoryId());
        goods.setUpdateTime(LocalDateTime.now());
        goodsService.updateById(goods);

        chanceGoodsTemplate.setCategoryId(condition.getCategoryId());
        chanceGoodsTemplate.setUpdateTime(new Date());
        chanceGoodsTemplateService.updateById(chanceGoodsTemplate);

        TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("id", chanceGoodsTemplate.getGoodsId());
        Map<String, Object> map = new HashMap<>();
        map.put("categoryId", condition.getCategoryId());
        goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), termQueryBuilder, map);
    }

    @Override
    public void checkChanceGoodsTemplateForClose() {
        LogUtils.info(log, "checkChanceGoodsTemplateForClose ===> 连续30天没有商家跟卖的机会商品自动关闭");

        long pageNow = 1;
        long totalPages = 99999;

        while (pageNow < totalPages) {
            IPage<ChanceGoodsTemplate> page = chanceGoodsTemplateService.lambdaQuery()
                    .eq(ChanceGoodsTemplate::getIsDel, 0)
                    .eq(ChanceGoodsTemplate::getStatus, ChanceGoodsTemplateStatusEnums.OPEN.getCode())
                    .page(new Page<>(pageNow, 20));

            List<ChanceGoodsTemplate> templateList = page.getRecords();
            if (CollectionUtils.isNotEmpty(templateList)) {
                List<Long> needCheckTemplateIds = templateList.stream().map(ChanceGoodsTemplate::getId).collect(Collectors.toList());
                List<ChanceGoods> allChanceGoodsList = chanceGoodsService.lambdaQuery()
                        .in(ChanceGoods::getTemplateId, needCheckTemplateIds)
                        .eq(ChanceGoods::getAuditStatus, 1)
                        .eq(ChanceGoods::getIsDel, 0)
                        .ge(ChanceGoods::getCreateTime, com.colorlight.base.utils.DateUtil.addDays(new Date(), -30L))
                        .list();
                Map<Long, List<ChanceGoods>> templateChanceGoodsMap = allChanceGoodsList.stream().collect(Collectors.groupingBy(ChanceGoods::getTemplateId));

                List<ChanceGoodsTemplate> updateTemplateList = Lists.newArrayList();
                for (ChanceGoodsTemplate template : templateList) {
                    List<ChanceGoods> chanceGoodsList = templateChanceGoodsMap.get(template.getId());
                    if (CollectionUtils.isEmpty(chanceGoodsList)) {
                        template.setStatus(ChanceGoodsTemplateStatusEnums.AUTO_CLOSE.getCode());
                        updateTemplateList.add(template);
                    }
                }

                if (CollectionUtils.isNotEmpty(updateTemplateList)) {
                    chanceGoodsTemplateService.updateBatchById(updateTemplateList);
                }
            }


            totalPages = page.getPages();
            pageNow++;
        }
    }

//    @Override
//    public List<Long> queryGoodsIdsBySameTemplate(Long goodsId) {
//        CheckUtils.notNull(goodsId, ProductResultCode.PARAMETER_ID_ERROR);
//
//        Goods goods = goodsService.getById(goodsId);
//        CheckUtils.notNull(goods, ProductResultCode.GOODS_NOT_EXIST);
//
//        List<Long> sameTemplateGoodsIds = Lists.newArrayList();
//        ChanceGoods chanceGoods = chanceGoodsService.lambdaQuery().eq(ChanceGoods::getGoodsId, goodsId).one();
//        if (chanceGoods != null) {
//            //目标作为机会商品
//            sameTemplateGoodsIds = chanceGoodsService.lambdaQuery()
//                    .eq(ChanceGoods::getTemplateId, chanceGoods.getTemplateId())
//                    .ne(ChanceGoods::getGoodsId, goodsId)
//                    .list()
//                    .stream().map(ChanceGoods::getGoodsId).collect(Collectors.toList());
//            LogUtils.info(log, "queryGoodsIdsBySameTemplate goodsId:{}, 目标作为机会商品 sameTemplateGoodsIds:{}", goodsId, sameTemplateGoodsIds);
//
//            ChanceGoodsTemplate chanceGoodsTemplate = chanceGoodsTemplateService.getById(chanceGoods.getTemplateId());
//            if (ChanceGoodsSourceTypeEnums.COPY.getCode().equals(chanceGoodsTemplate.getSourceType())) {
//                sameTemplateGoodsIds.add(Long.parseLong(chanceGoodsTemplate.getSourceDetail()));
//                LogUtils.info(log, "queryGoodsIdsBySameTemplate goodsId:{}, 添加克隆源商品:{}", goodsId, chanceGoodsTemplate.getSourceDetail());
//            }
//        } else {
//            //目标作为模板来源
//            List<Long> templateIds = chanceGoodsTemplateService.lambdaQuery()
//                    .eq(ChanceGoodsTemplate::getSourceType, ChanceGoodsSourceTypeEnums.COPY.getCode())
//                    .eq(ChanceGoodsTemplate::getSourceDetail, goodsId.toString())
//                    .list()
//                    .stream().map(ChanceGoodsTemplate::getId).collect(Collectors.toList());
//            LogUtils.info(log, "queryGoodsIdsBySameTemplate goodsId:{}, 目标作为模板来源 templateIds:{}", goodsId, templateIds);
//
//            if (CollectionUtils.isEmpty(templateIds)) {
//                return Collections.emptyList();
//            }
//
//            sameTemplateGoodsIds = chanceGoodsService.lambdaQuery()
//                    .in(ChanceGoods::getTemplateId, templateIds)
//                    .list().stream().map(ChanceGoods::getGoodsId).collect(Collectors.toList());
//        }
//
//        if (CollectionUtils.isEmpty(sameTemplateGoodsIds)) {
//            return Collections.emptyList();
//        }
//
//        List<String> goodsIds = goodsService.lambdaQuery()
//                .in(Goods::getId, sameTemplateGoodsIds)
//                .eq(Goods::getIsDel, 0)
//                .eq(Goods::getIsShow, GoodsIsShowEnums.SHELF.getType().toString())
//                .select(Goods::getId)
//                .list()
//                .stream()
//                .map(goods1 -> goods1.getId().toString())
//                .collect(Collectors.toList());
//        goodsIds.add(goodsId.toString());
//
//        //国家价格比价
//        String country = null;
//        ClientInfoDTO clientInfoDTO = ThreadContext.getContext(SystemConstants.CLIENTINFO);
//        if (null != clientInfoDTO && StringUtils.isNotEmpty(clientInfoDTO.getCountry())) {
//            country = clientInfoDTO.getCountry();
//        }
//
//        Map<String, BigDecimal> goodsPriceMap = goodsEsService.queryPriceByGoodsIdAndCountry(goodsIds, country);
//        BigDecimal goodsPrice = goodsPriceMap.get(goodsId.toString());
//
//
//        return goodsPriceMap.entrySet().stream()
//                .filter(entry -> entry.getValue().compareTo(goodsPrice) < 0)
//                .map(entry -> Long.parseLong(entry.getKey()))
//                .collect(Collectors.toList());
//    }
}
