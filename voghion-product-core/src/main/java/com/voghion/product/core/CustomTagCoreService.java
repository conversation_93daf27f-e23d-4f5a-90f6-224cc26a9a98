package com.voghion.product.core;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.CustomTagInfoDTO;
import com.voghion.product.model.vo.GoodsInfoVO;
import com.voghion.product.model.vo.GoodsRelCustomTagImportVO;
import com.voghion.product.model.vo.GoodsRelCustomTagOutput;
import com.voghion.product.model.vo.condition.CustomTagCondition;
import com.voghion.product.model.vo.condition.CustomTagRelCondition;
import com.voghion.product.model.vo.customTag.CustomTagRelGoodsExportVO;
import com.voghion.product.model.vo.customTag.CustomTagRelGoodsReqVO;
import com.voghion.product.model.vo.customTag.CustomTagReqVO;
import com.voghion.product.model.vo.customTag.CustomTagVO;

import java.util.List;
import java.util.Map;

/**
 * CustomTagCoreService
 *
 * <AUTHOR>
 * @date 2023/12/25
 */
public interface CustomTagCoreService {
    /**
     * 自定义标签保存或更新
     *
     * @param customTagReqVO 入参
     */
    void saveCustomTag(CustomTagReqVO customTagReqVO);


    /**
     * 自定义标签删除
     * @param tagId
     * @return
     */
    Boolean deleteCustomTag(Long tagId);

    /**
     * 分页查询
     *
     * @param customTagCondition
     * @return
     */
    PageView<CustomTagVO> listCustomTags(CustomTagCondition customTagCondition);

    /**
     * 根据标签id获取标签关联商品信息
     *
     * @param customTagRelCondition
     * @return
     */
    PageView<GoodsInfoVO> getCustomTagRelGoods(CustomTagRelCondition customTagRelCondition);

    /**
     * 根据标签id批量关联/解除商品列表
     * @param requestVO
     * @return
     */
    Result<Boolean> customTagRelGoods(CustomTagRelGoodsReqVO requestVO);

    /**
     * 根据标签id查询关联的商品id集合
     * @param tagId
     * @return
     */
    List<CustomTagRelGoodsExportVO> getCustomTagRelGoodsIdList(Long tagId);

    /**
     * 根据筛选条件查询关联的商品id集合
     * @param customTagRelCondition
     * @return
     */
    List<CustomTagRelGoodsExportVO> getCustomTagRelGoodsIdList(CustomTagRelCondition customTagRelCondition);

    
    List<CustomTagVO> listAll(CustomTagCondition customTagCondition);

    /**
     * 批量导入商品关联自定义标签
     * @param list
     */
    GoodsRelCustomTagOutput batchImportGoodsRelCustomTag(Long tagId, List<GoodsRelCustomTagImportVO> list);

    /**
     * 刷新标签关联商品数量
     * @return
     */
    Boolean refreshGoodsTagRelNum();

    List<CustomTagInfoDTO> listCustomTagInfo();

    Map<Long, List<CustomTagVO>> queryGoodsCustomTag(CustomTagCondition customTagCondition);

    void noticeDingDing();

    List<CustomTagInfoDTO> queryCustomTagByGoodsId(Long goodsId);

    Result<Boolean> customTagRelGoodsBatch(CustomTagRelGoodsReqVO requestVO);

    void saveCombo(CustomTagReqVO customTagReqVO);

    void syncGoodsTagRel(List<Long> goodsIds);
}
