package com.voghion.product.core.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.lang.exception.CustomException;
import com.colorlight.base.model.PageView;
import com.colorlight.base.utils.DateUtil;
import com.colorlight.base.utils.TransferUtils;
import com.voghion.es.impl.BaseEsQueryServiceImpl;
import com.voghion.es.vo.GoodsESVo;
import com.voghion.product.api.dto.BindTagDTO;
import com.voghion.product.api.dto.CustomTopGoodsDTO;
import com.voghion.product.api.input.FrontCategoryInputVO;
import com.voghion.product.api.output.FrontCategoryVO;
import com.voghion.product.bq.OutDbGoodsEveryDayEsService;
import com.voghion.product.bq.vo.OutDbGoodsEveryDayQueryParam;
import com.voghion.product.bq.vo.OutDbGoodsEveryDayVO;
import com.voghion.product.core.*;
import com.voghion.product.enums.GoodsNegativeEnums;
import com.voghion.product.listener.ImportCustmGoodsVO;
import com.voghion.product.model.dto.GoodsListPermission;
import com.voghion.product.model.enums.CustomResultCode;
import com.voghion.product.model.enums.EsEnums;
import com.voghion.product.model.enums.GoodsIsShowEnums;
import com.voghion.product.model.enums.ProductResultCode;
import com.voghion.product.model.po.*;
import com.voghion.product.model.vo.*;
import com.voghion.product.mq.MqMessage;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.support.ClientInfoSupportService;
import com.voghion.user.remove.dto.UserDTO;
import com.voghion.util.GoogleStorageUtil;
import com.voghion.util.ObjectStorePathEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.common.unit.TimeValue;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.SortOrder;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-08-26)
 */
@Component
@Slf4j
public class CustomListCoreServiceImpl extends AbstractCommonServiceImpl implements CustomListCoreService {

    @Resource
    private CustomListService customListService;

    @Resource
    private CustomListItemsService customListItemsService;

    @Resource
    private FrontCategoryCoreService frontCategoryCoreService;

    @Resource
    private FrontCategoryExtService frontCategoryExtService;

//    @Resource
//    private CustomGoodsItemsService customGoodsItemsService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private CategoryCoreService categoryCoreService;

    @Resource
    private GoodsBlacklistService goodsBlacklistService;

    @Resource
    private FaMerchantsApplyCoreService faMerchantsApplyCoreService;

    @Resource
    private CategoryTreeCoreService categoryTreeCoreService;

    @Resource
    private OutDbGoodsEveryDayEsService outDbGoodsEveryDayEsService;


    @Resource
    private ComplianceLabelTagConfigService complianceLabelTagConfigService;

    @Resource
    private GoodsExtConfigService goodsExtConfigService;

    @Resource
    private MqSender mqSender;

    @Resource
    private RedisApi redisApi;

    @Resource
    private ClientInfoSupportService clientInfoSupportService;

    @Resource(name = "goodsInfoPool")
    private Executor executor;

    @Resource
    private BaseEsQueryServiceImpl baseEsQueryServiceImpl;

    @Resource
    private RedisTemplate<String, String> redisTemplate;


    public static final String FILE_DIRECTORY = "/data/customExport";


    /**
     * 获取首页自定义列表Lis带分页
     *
     * @param customListQueryVO
     * @return
     */
    @Override
    public PageView<CustomList> queryPageByOption(CustomListQueryVO customListQueryVO) {
        PageView<CustomList> pageView = new PageView<>();
        Page<CustomList> page = new Page<>();
        page.setCurrent(customListQueryVO.getPageNow());
        page.setSize(customListQueryVO.getPageSize());


        this.setUserDeptDataPermission(customListQueryVO);
        //权限为空 直接返回空数据
        if (CollectionUtils.isEmpty(customListQueryVO.getDepartmentTypeList())) {
            return pageView;
        }
        if (StringUtils.isNotEmpty(customListQueryVO.getCustomIdStr())){
            List<Long> customIds = customListQueryVO.getCustomListIds();
            if (customIds == null){
                customIds = new ArrayList<>();
            }
            String[] s = customListQueryVO.getCustomIdStr().split("\n");
            for (String id : s) {
                if (StringUtils.isNotBlank(id)) {
                    customIds.add(Long.valueOf(id.replaceAll(" ", "")));
                }
            }
            if (CollectionUtils.isNotEmpty(customIds)) {
                customListQueryVO.setCustomListIds(customIds);
            }
        }


        IPage<CustomList> customListIPage = customListService.PageCustomList(page,customListQueryVO);
        List<CustomList> customLists = customListIPage.getRecords();
        pageView.setRecords(customLists);
        pageView.setRowCount(customListIPage.getTotal());
        pageView.setPageNow((int)customListIPage.getCurrent());
        pageView.setPageSize((int)customListIPage.getSize());
        return pageView;
    }

    /**
     * 补充用户部门数据权限
     */
    private void setUserDeptDataPermission(CustomListQueryVO vo) {
        if (Objects.nonNull(vo.getDepartmentType())) {
            vo.setDepartmentTypeList(Collections.singletonList(vo.getDepartmentType()));
            return;
        }
        //获取用户权限按钮信息 /商品信息管理/虚拟商品列表/  四个按钮
        String token = getToken();
        GoodsListPermission goodsListPermission = initCustomListGoodsPermissionByToken(token);
        vo.setDepartmentTypeList(goodsListPermission.getDepartmentTypeList());
    }

    @Override
    public Boolean addCustomList(CustomListQueryVO customListQueryVO) {
        CustomList customList = new CustomList();
        customList.setTitle(customListQueryVO.getTitle());
        customList.setImgUrl(customListQueryVO.getImgUrl());
        customList.setType(customListQueryVO.getType());
        customList.setCycleImg(customListQueryVO.getCycleImg());
        customList.setIsShow("0");
        customList.setTagId(customListQueryVO.getTagId());
        customList.setDetailImg(customListQueryVO.getDetailImg());
        if (CollectionUtils.isNotEmpty(customListQueryVO.getCountryCodeList())){
            customList.setCountryCode(StringUtils.join(customListQueryVO.getCountryCodeList(), ","));
        }
        customList.setDepartmentType(customListQueryVO.getDepartmentType());
        customList.setPlacementType(customListQueryVO.getPlacementType());
        customList.setInsertCustomId(customListQueryVO.getInsertCustomId());
        customList.setUseAlgorithm(customListQueryVO.getUseAlgorithm());
        customList.setInsertQuantity(customListQueryVO.getInsertQuantity()==null?0:customListQueryVO.getInsertQuantity());

        String userName = getUserName();
        customList.setCreateUser(userName);
        customList.setUpdateUser(userName);
        customList.setUpdateTime(LocalDateTime.now());
        customList.setCreateTime(LocalDateTime.now());
        customListService.insert(customList);
        if (customListQueryVO.getPlacementType() > 0) {
            mqSender.send("SYNC_CUSTOM_TOP_GOODS", customList.getId());
        }
        return true;
    }

    @Override
    public Boolean addGoods(CustomListGoodsVO customListGoodsVO) {

        if (customListGoodsVO != null && StringUtils.isNotEmpty(customListGoodsVO.getGoodsIdListStr())){
            List<Long> goodsIds = customListGoodsVO.getGoodsIds();
            if (goodsIds == null){
                goodsIds = new ArrayList<>();
            }
            String[] s = customListGoodsVO.getGoodsIdListStr().split("\n");
            for (String id : s) {
                    if (StringUtils.isNotBlank(id)) {
                        goodsIds.add(Long.valueOf(id.replaceAll(" ", "")));
                    }
                }
            customListGoodsVO.setGoodsIds(goodsIds);
        }
        log.debug("虚拟列表新增{}",customListGoodsVO.getGoodsIds());
        if(CollectionUtils.isEmpty(customListGoodsVO.getGoodsIds())){
            //数据出错
            return false;
        }
        String operator = customListGoodsVO.getOperator();
        if(StringUtils.isBlank(operator)){
            UserDTO operationUser = clientInfoSupportService.getOperationUser();
            if(Objects.nonNull(operationUser)){
                customListGoodsVO.setOperator(operationUser.getName());
            }else{
                customListGoodsVO.setOperator("MQ任务");
            }
        }
        Long customId = customListGoodsVO.getCustomId();
        Map<Long, Integer> showAreaMap = Optional.ofNullable(customListGoodsVO.getShowAreaMap()).orElse(Collections.emptyMap());
        List<CustomListItems> customListItemsList1 = customListItemsService.selectCustomListItems(customId, customListGoodsVO.getGoodsIds());
        if (CollectionUtils.isNotEmpty(customListItemsList1)) {
            List<Long> goodsIdList = customListItemsList1.stream().map(CustomListItems::getGoodsId).collect(Collectors.toList());
            if (showAreaMap.isEmpty()) {
                //更新createTime
                customListItemsService.updateByGoodsIds(goodsIdList, customId);
//                customGoodsItemsService.updateByGoodsIds(goodsIdList, customId);
            } else {
                for (CustomListItems customListItems : customListItemsList1) {
                    customListItems.setShowArea(Optional.ofNullable(showAreaMap.get(customListItems.getGoodsId())).orElse(0));
                    customListItems.setCreateTime(LocalDateTime.now());
                }
                customListItemsService.updateBatchById(customListItemsList1);

//                List<CustomGoodsItems> customListGoodsList1 = customGoodsItemsService.selectCustomGoodsItems(customId, customListGoodsVO.getGoodsIds());
//                if (CollectionUtils.isNotEmpty(customListGoodsList1)) {
//                    for (CustomGoodsItems customGoodsItems : customListGoodsList1) {
//                        customGoodsItems.setShowArea(Optional.ofNullable(showAreaMap.get(customGoodsItems.getGoodsId())).orElse(0));
//                        customGoodsItems.setCreateTime(LocalDateTime.now());
//                    }
//                    customGoodsItemsService.updateBatchById(customListGoodsList1);
//                }
            }
            customListGoodsVO.getGoodsIds().removeAll(goodsIdList);
        }
        List<Long> odlGoodsIds = new ArrayList<>(customListGoodsVO.getGoodsIds());
        if(CollectionUtils.isEmpty(odlGoodsIds)){
            log.debug("addGoods odlGoodsIds is empty");
            return true;
        }
        List<Goods> goodsList =  goodsService.lambdaQuery().in(Goods::getId, odlGoodsIds).list();
        if(customListGoodsVO.needCheckShow){
            log.debug("addGoods after before size = {} {} ", customListGoodsVO.getCustomId(), goodsList.size());
            goodsList = org.apache.commons.collections4.CollectionUtils.emptyIfNull(goodsList).stream().filter(goods -> Objects.equals(goods.getIsShow(), "1")).collect(Collectors.toList());
            log.debug("addGoods after filter size = {} {} ", customListGoodsVO.getCustomId(), goodsList.size());
        }
        if (CollectionUtils.isEmpty(goodsList)){
            log.debug("addGoods goodsList is empty {}", goodsList.size());
            return false;
        }
        log.debug("custom_remove_negative|goodsList:{}",goodsList.size());
        //负向业务剔除虚拟商品列表
//        Object obj = redisApi.get("custom_remove_negative");
//        if (obj != null) {
//            log.info("custom_remove_negative| value:{}",obj);
//            boolean match = Arrays.stream(String.valueOf(obj).split(";")).map(Long::valueOf).anyMatch(customId::equals);
//            if (match) {
//                //剔除负向业务的商品
//                List<Long> goodsIds = goodsExtConfigCoreService.selectNegativeGoodsIds(customListGoodsVO.getGoodsIds());
//                if(CollectionUtils.isNotEmpty(goodsIds)){
//                    log.info("custom_remove_negative|包含的负向业务商品:{}",goodsIds);
//                    goodsList.removeIf(goods -> goodsIds.contains(goods.getId()));
//                    log.info("custom_remove_negative|goodsList剔除后:{}",goodsList.size());
//                }
//            }
//        }
        List<Long> goodsIds = goodsExtConfigCoreService.selectNegativeGoodsIds(customListGoodsVO.getGoodsIds());

        if (CollectionUtils.isEmpty(goodsList)){
            return false;
        }
        CustomList customList = customListService.selectById(customId);
        List<CustomListItems> customListItemsList = new ArrayList<>();
        List<CustomGoodsItems> customGoodsItemsList = new ArrayList<>();

        for(Goods goods:goodsList){
            String country = goods.getCountry();
            CustomListItems customListItems = new CustomListItems();
            customListItems.setIsShow(goods.getIsShow());
            customListItems.setGoodsId(goods.getId());
            customListItems.setCustomId(customId);
            customListItems.setShowArea(Optional.ofNullable(showAreaMap.get(goods.getId())).orElse(0));
            customListItems.setCreateTime(LocalDateTime.now());
            customListItems.setCountry(country);
            customListItems.setAppChannel(goods.getAppChannel());
            customListItems.setCategoryId(goods.getCategoryId());
            customListItems.setPrice(goods.getMinPrice());
            customListItems.setSortValue(goods.getSortValue());
            customListItems.setIsNegative(goodsIds.contains(goods.getId()) ? GoodsNegativeEnums.YES.getCode() : GoodsNegativeEnums.NO.getCode());
            customListItems.setOperator(customListGoodsVO.getOperator());
            customListItemsList.add(customListItems);

           if(StringUtils.isNotBlank(country)) {
               if(org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(country,"SYSTEM","default","ALL")){
                   CustomGoodsItems customGoodsItems = getCustomGoodsItems(goods, customId, "SYSTEM");
                   customGoodsItems.setShowArea(Optional.ofNullable(showAreaMap.get(goods.getId())).orElse(0));
                   customGoodsItems.setIsNegative(customListItems.getIsNegative());
                   customGoodsItemsList.add(customGoodsItems);
               }else {
                   Arrays.stream(country.split(",")).distinct().map(cou -> getCustomGoodsItems(goods, customId, cou)).forEach(customGoodsItems -> {
                       customGoodsItems.setShowArea(Optional.ofNullable(showAreaMap.get(goods.getId())).orElse(0));
                       customGoodsItems.setIsNegative(customListItems.getIsNegative());
                       customGoodsItemsList.add(customGoodsItems);
                   });
               }
           }
        }
        //增加标签
        if (customList != null && StringUtils.isNotEmpty(customList.getTagId())){
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setTagId(Long.valueOf(customList.getTagId()));
            bindTagDTO.setGoodsIds(customListGoodsVO.getGoodsIds());
            bindTagDTO.setActivityId(Long.valueOf(customList.getTagId()));
            goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
        }
//        customGoodsItemsService.deleteByCustomIdAndGoodsIds(customId, odlGoodsIds);
//        if (CollectionUtils.isNotEmpty(customGoodsItemsList)) {
//            customGoodsItemsService.insertBatch(customGoodsItemsList);
//        }
        CustomList updateCustomList = new CustomList();
        updateCustomList.setId(customId);
        updateCustomList.setUpdateUser(getUserName());
        updateCustomList.setUpdateTime(LocalDateTime.now());
        if(customList!=null) {
            updateCustomList.setPlacementType(customList.getPlacementType());
            updateCustomList.setUseAlgorithm(customList.getUseAlgorithm());
            updateCustomList.setInsertQuantity(customList.getInsertQuantity());
            updateCustomList.setInsertCustomId(customList.getInsertCustomId());
            updateCustomList.setDepartmentType(customList.getDepartmentType());
        }
        customListService.updateById(updateCustomList);

        return customListItemsService.insertBatch(customListItemsList);

    }

    private CustomGoodsItems getCustomGoodsItems(Goods goods, Long customId, String country) {
        CustomGoodsItems customGoodsItems = new CustomGoodsItems();
        customGoodsItems.setGoodsId(goods.getId());
        customGoodsItems.setCustomId(customId);
        customGoodsItems.setIsShow(goods.getIsShow());
        customGoodsItems.setCreateTime(LocalDateTime.now());
        customGoodsItems.setCategoryId(goods.getCategoryId());
        customGoodsItems.setPrice(goods.getMinPrice());
        customGoodsItems.setSortValue(goods.getSortValue());
        customGoodsItems.setCountry(country);
        return customGoodsItems;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void coverCustomGoods(CustomListGoodsVO customListGoodsVO) {
        if (customListGoodsVO == null || customListGoodsVO.getCustomId() == null) {
            return;
        }
        Long customId = customListGoodsVO.getCustomId();
        CustomList customList = customListService.selectById(customId);
        if (customList == null) {
            return;
        }
        String tagId = customList.getTagId();
        if (customListGoodsVO.getDeleteAll() == 1) {
            List<CustomListItems> customListItemsList = customListItemsService.queryByCustomIds(Lists.newArrayList(customId));
            if (CollectionUtils.isNotEmpty(customListItemsList)) {
                //删除虚拟商品列表商品
                customListItemsService.deleteByCustomIds(Lists.newArrayList(customId));
//                customGoodsItemsService.deleteByCustomIds(Lists.newArrayList(customId));
                List<Long> goodsIds = customListItemsList.stream().map(CustomListItems::getGoodsId).distinct().collect(Collectors.toList());
                //判断删除标签
                if (CollectionUtils.isNotEmpty(goodsIds) && StringUtils.isNotEmpty(tagId)) {
                    BindTagDTO bindTagDTO = new BindTagDTO();
                    bindTagDTO.setTagId(Long.valueOf(tagId));
                    bindTagDTO.setGoodsIds(goodsIds);
                    goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
                }
            }
        }
        //要查询的商品id
        List<Long> goodsIds = customListGoodsVO.getGoodsIds();
        if(CollectionUtils.isEmpty(goodsIds)){
           return;
        }
        List<Goods> goodsList =  goodsService.queryGoodsByIds(goodsIds);
        if (CollectionUtils.isEmpty(goodsList)){
            return;
        }
//        List<CustomGoodsItems> customGoodsItemsList = new ArrayList<>();
        List<CustomListItems> addItems = goodsList.stream().map(goods -> {
            CustomListItems customListItems = new CustomListItems();
            customListItems.setIsShow(goods.getIsShow());
            customListItems.setGoodsId(goods.getId());
            customListItems.setCreateTime(LocalDateTime.now());
            customListItems.setCustomId(customId);
            customListItems.setShowArea(0);
            customListItems.setCountry(goods.getCountry());
            customListItems.setAppChannel(goods.getAppChannel());
            customListItems.setCategoryId(goods.getCategoryId());
            customListItems.setPrice(goods.getMinPrice());
            customListItems.setSortValue(goods.getSortValue());

//            String country = goods.getCountry();
//            if(StringUtils.isNotBlank(country)) {
//                if(org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(country,"SYSTEM","default","ALL")){
//                    CustomGoodsItems customGoodsItems = getCustomGoodsItems(goods, customId, "SYSTEM");
//                    customGoodsItems.setShowArea(0);
//                    customGoodsItemsList.add(customGoodsItems);
//                }else {
//                    String[] split = country.split(",");
//                    for (String cou : split) {
//                        CustomGoodsItems customGoodsItems = getCustomGoodsItems(goods, customId, cou);
//                        customGoodsItems.setShowArea(0);
//                        customGoodsItemsList.add(customGoodsItems);
//                    }
//                }
//            }

            return customListItems;
        }).collect(Collectors.toList());
        //增加标签
        if (StringUtils.isNotEmpty(tagId)) {
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setTagId(Long.valueOf(tagId));
            bindTagDTO.setGoodsIds(goodsIds);
            bindTagDTO.setActivityId(Long.valueOf(tagId));
            goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
        }
//        if (CollectionUtils.isNotEmpty(customGoodsItemsList)) {
//            customGoodsItemsService.insertBatch(customGoodsItemsList);
//        }
        customListItemsService.insertBatch(addItems);
    }

    @Override
    public void customCustomGoods(Long goodsId) {
        Goods goods = goodsService.selectById(goodsId);
        if(goods == null) {
            return;
        }
        customListItemsService.lambdaUpdate().set(CustomListItems::getIsShow, goods.getIsShow()).eq(CustomListItems::getGoodsId, goodsId).update();
//        customGoodsItemsService.lambdaUpdate().set(CustomGoodsItems::getIsShow, goods.getIsShow()).eq(CustomGoodsItems::getGoodsId, goodsId).update();
    }

    @Override
    public void clearCustomGoods(Long customId) {
        if (Objects.isNull(customId)) return;
        List<CustomListItems> customListItemsList;
        long id = 0;
        do {
            customListItemsList = customListItemsService.lambdaQuery()
                    .eq(CustomListItems::getCustomId, customId)
                    .gt(CustomListItems::getId, id)
                    .orderByAsc(CustomListItems::getId)
                    .last("limit 200")
                    .list();
            if (CollectionUtils.isEmpty(customListItemsList)) {
                log.info("clearCustomGoods end {} {}", customId, id);
                break;
            }
            id = customListItemsList.get(customListItemsList.size() - 1).getId();
            CustomListGoodsVO customListGoodsVO = new CustomListGoodsVO();
            customListGoodsVO.setCustomId(customId);
            customListGoodsVO.setGoodsIds(customListItemsList.stream().map(CustomListItems::getGoodsId)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            deleteGoodsById(customListGoodsVO);
        } while (true);
    }

    @Override
    public Boolean update(CustomListQueryVO customListQueryVO) {
        CustomList customList = new CustomList();
        if (customListQueryVO.getCustomId() == null || customListQueryVO.getCustomId() <= 0){
            return false;
        }
//        customList.setCreateTime(LocalDateTime.now());
        customList.setImgUrl(customListQueryVO.getImgUrl());
        customList.setId(customListQueryVO.getCustomId());
        customList.setCycleImg(customListQueryVO.getCycleImg());
        customList.setDetailImg(customListQueryVO.getDetailImg());
        customList.setTitle( customListQueryVO.getTitle());
        customList.setIsShow(customListQueryVO.getIsShow());
        customList.setType(customListQueryVO.getType());
        customList.setTagId(customListQueryVO.getTagId());
        if (CollectionUtils.isNotEmpty(customListQueryVO.getCountryCodeList())){
            customList.setCountryCode(StringUtils.join(customListQueryVO.getCountryCodeList(), ","));
        }
        customList.setDepartmentType(customListQueryVO.getDepartmentType());
        customList.setPlacementType(customListQueryVO.getPlacementType());
        customList.setInsertCustomId(customListQueryVO.getInsertCustomId());
        customList.setUseAlgorithm(customListQueryVO.getUseAlgorithm());
        customList.setInsertQuantity(customListQueryVO.getInsertQuantity()==null?0:customListQueryVO.getInsertQuantity());
        String userName = getUserName();
        customList.setUpdateUser(userName);
        customList.setUpdateTime(LocalDateTime.now());

//        if(customListService.isExist(customList)) return false;
        boolean success = customListService.updateById(customList);
        if (customListQueryVO.getPlacementType() > 0) {
            mqSender.send("SYNC_CUSTOM_TOP_GOODS", customList.getId());
        }
        return success;
    }

    @Override
    public CustomListVO queryDetailById(String customId) {
        if(customId==null) return null;
        CustomList customList = new CustomList();
        CustomListVO customListVO = new CustomListVO();
        customList = customListService.selectById(Long.valueOf(customId));
        TransferUtils.transferBean(customList,customListVO);
        return customListVO;
    }

    @SuppressWarnings(value = "unchecked")
    @Override
    public PageView<CustomListItemsVO> queryGoodsByCustomList(CustomListQueryVO customListQueryVO) {

        PageView<CustomListItemsVO> pageView = new PageView();
        Page<CustomListItems> page = new Page<>();
        page.setCurrent(customListQueryVO.getPageNow());
        page.setSize(customListQueryVO.getPageSize());
        List<CustomListItemsVO> list = new ArrayList<>();
        List<Long> goodsIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(customListQueryVO.getGoodsIdListStr())){
            String[] s = customListQueryVO.getGoodsIdListStr().split("\n");
            for (String id : s) {
                if (StringUtils.isNotBlank(id)) {
                    goodsIdList.add(Long.valueOf(id.replaceAll(" ", "")));
                }
            }
        }
        // 拿到虚拟商品列表的商品ids
        List<Long> categoryIds = null;
        if(Objects.nonNull(customListQueryVO.getCategoryId())){
            categoryIds = categoryTreeCoreService.getAllLeafCategoryByCategoryId(customListQueryVO.getCategoryId());
        }
//        List<String>  isShowList = new ArrayList<>();
//        String isShow = customListQueryVO.getIsShow();
//        if(StringUtils.isNotBlank(isShow)){
//            isShowList.add(isShow);
//            if("100".equals(isShow)){
//                isShowList = Lists.newArrayList("100", "101", "102", "103", "104", "105", "106");
//            }
//            if("200".equals(isShow)){
//                isShowList = Lists.newArrayList("200", "201", "202", "203", "204", "205", "206");
//            }
//        }


        Set<String> isShowSet = new LinkedHashSet<>();
        String isShow = customListQueryVO.getIsShow();
        if (StringUtils.isNotBlank(isShow)) {
            isShowSet.add(isShow);
        }
        isShowSet.addAll(Optional.ofNullable(customListQueryVO.getIsShowList()).orElse(Lists.newArrayList()));
        Set<String> additionalElements = new LinkedHashSet<>();
        for (String i : isShowSet) {
            if ("100".equals(i)) {
                additionalElements.addAll(Lists.newArrayList("100", "101", "102", "103", "104", "105", "106"));
            }
            if ("200".equals(i)) {
                additionalElements.addAll(Lists.newArrayList("200", "201", "202", "203", "204", "205", "206"));
            }
        }
        isShowSet.addAll(additionalElements);
        customListQueryVO.setIsShowList(new ArrayList<>(isShowSet));

        IPage<CustomListItems> customListIPage = customListItemsService.lambdaQuery()
                .eq(CustomListItems::getCustomId, customListQueryVO.getCustomId())
                .in(CollectionUtils.isNotEmpty(goodsIdList), CustomListItems::getGoodsId, goodsIdList)
                .in(CollectionUtils.isNotEmpty(isShowSet), CustomListItems::getIsShow, isShowSet)
                .eq(customListQueryVO.getIsNegative() != null, CustomListItems::getIsNegative, customListQueryVO.getIsNegative())
                .in(CollectionUtils.isNotEmpty(categoryIds), CustomListItems::getCategoryId, categoryIds)
                .orderByDesc(CustomListItems::getShowArea, CustomListItems::getSortValue)
                .orderByAsc(CustomListItems::getPrice, CustomListItems::getId)
                .page(page);

        List<CustomListItems> customListItemsList = customListIPage.getRecords();
        if(CollectionUtils.isEmpty(customListItemsList)) {
            pageView.setRecords(list);
            return pageView;
        }
        List<Long> goodsIds = new ArrayList<>();
        HashMap<Long, CustomListItems> map = new HashMap<>();
        for(CustomListItems customListItems:customListItemsList){
            Long goodsId = customListItems.getGoodsId();
            goodsIds.add(goodsId);
            map.put(goodsId,customListItems);
        }
        List<Goods> goodsList = customListItemsService.selectCustomGoods(goodsIds);

        List<Long> categoryList = customListItemsList.stream().map(CustomListItems::getCategoryId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(goodsList)) return null;
        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(categoryList);

        OutDbGoodsEveryDayQueryParam queryParam = new OutDbGoodsEveryDayQueryParam();
        queryParam.setGoodsIdList(goodsIds);
        queryParam.setRunDays(7);
        Date yesterday = DateUtil.addDays(new Date(), -1);
        String date = DateUtil.format(yesterday, DateUtil.webFormat);
        queryParam.setCreateDay(date);
        queryParam.setPageSize(goodsIds.size());
        queryParam.setOrderItem("dealPayMoney");
        queryParam.setPageNow(1);
        queryParam.setIgnoreShow(1);
        queryParam.setListingId(customListQueryVO.getListingId());

        List<OutDbGoodsEveryDayVO> outDbGoodsEveryDayVOS = outDbGoodsEveryDayEsService.pageOutDbGoodsEveryDay(queryParam, OutDbGoodsEveryDayVO.class);
        Map<Long, OutDbGoodsEveryDayVO> outMap = outDbGoodsEveryDayVOS.stream().collect(Collectors.toMap(OutDbGoodsEveryDayVO::getGoodsId, Function.identity()));
        List<Long> configTagIdList = complianceLabelTagConfigService.lambdaQuery()
                .eq(ComplianceLabelTagConfig::getIsDel, 0)
                .list()
                .stream().map(complianceLabelTagConfig -> complianceLabelTagConfig.getTagId().longValue()).distinct().collect(Collectors.toList());

        List<GoodsExtConfig> goodsExtConfigs = goodsExtConfigService.selectByGoodsIds(goodsIds);
        Map<Long, List<Long>> goodsTagMap = goodsExtConfigs.stream().collect(Collectors.groupingBy(GoodsExtConfig::getGoodsId, Collectors.mapping(GoodsExtConfig::getTagId, Collectors.toList())));

        for(Goods goods:goodsList){
            CustomListItemsVO customListItemsVO = new CustomListItemsVO();
            CustomListItems customListItems = map.get(goods.getId());
            customListItemsVO.setCategoryTreeStr(categoryTreeMap.get(goods.getCategoryId()));
            customListItemsVO.setId(customListItems.getId());
            customListItemsVO.setShowArea(customListItems.getShowArea());
            customListItemsVO.setCustomId(customListQueryVO.getCustomId());
            customListItemsVO.setAutoSelectStatus(customListItems.getAutoSelectStatus());
            customListItemsVO.setAutoSelectFailReason(customListItems.getAutoSelectFailReason());
            customListItemsVO.setAutoSelectTime(customListItems.getAutoSelectTime());
            customListItemsVO.setOperator(customListItems.getOperator());
            Long goodsId = goods.getId();
            customListItemsVO.setGoodsId(goodsId);
            customListItemsVO.setPrice(goods.getMinPrice() + "~" + goods.getMaxPrice());
            // 待确认
            customListItemsVO.setGoodsUrl("https://www.kfbuy.com/goodsDetail?goodsId=" + goodsId);
            customListItemsVO.setImgUrl(goods.getMainImage());
            customListItemsVO.setIsShow(goods.getIsShow());
            customListItemsVO.setName(goods.getName());
            customListItemsVO.setIsDel(goods.getIsDel());
            customListItemsVO.setIsLock(goods.getIsLock());
            customListItemsVO.setCountry(goods.getCountry());
            customListItemsVO.setShopName(goods.getShopName());

            OutDbGoodsEveryDayVO outDbGoodsEveryDayVO = outMap.get(goods.getId());
            if (Objects.nonNull(outDbGoodsEveryDayVO)) {
                customListItemsVO.setEcpm(outDbGoodsEveryDayVO.getEcpm());
                customListItemsVO.setDealCnt(outDbGoodsEveryDayVO.getDealCnt());
                customListItemsVO.setDealPayMoney(outDbGoodsEveryDayVO.getDealPayMoney());

                customListItemsVO.setShowUv(outDbGoodsEveryDayVO.getShowUv());
                customListItemsVO.setClickUv(outDbGoodsEveryDayVO.getClickUv());
                customListItemsVO.setDealUv(outDbGoodsEveryDayVO.getDealUv());
                customListItemsVO.setClickRateUv(Optional.ofNullable(outDbGoodsEveryDayVO.getClickRateUv()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP));
                customListItemsVO.setAddRateUv(Optional.ofNullable(outDbGoodsEveryDayVO.getAddRateUv()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP));
                customListItemsVO.setDealRateUv(Optional.ofNullable(outDbGoodsEveryDayVO.getDealRateUv()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP));
                customListItemsVO.setAddUv(outDbGoodsEveryDayVO.getAddUv());
                customListItemsVO.setComplianceAuditStatus(1);
                List<Long> tagIds = Optional.ofNullable(goodsTagMap.get(goods.getId())).orElse(Lists.newArrayList());
                if (tagIds.contains(99L)) {
                    customListItemsVO.setComplianceAuditStatus(2);
                } else if (CollectionUtils.isNotEmpty(configTagIdList) && CollectionUtils.isNotEmpty(CollectionUtils.intersection(tagIds, configTagIdList))) {
                    customListItemsVO.setComplianceAuditStatus(3);
                }
            }

            list.add(customListItemsVO);
        }
        List<CustomListItemsVO> collect = list.stream()
                .sorted(Comparator.comparingInt(item -> goodsIds.indexOf(item.getGoodsId())))
                .collect(Collectors.toList());
        pageView.setRecords(collect);
        pageView.setPageSize((int) customListQueryVO.getPageSize());
        pageView.setRowCount(customListIPage.getTotal());
        pageView.setPageNow((int)customListIPage.getCurrent());
        pageView.setPageSize((int)customListIPage.getSize());
        return pageView;

    }



    /**
     * 自定义商品列表的商品 移动位置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateGoods(CustomListItemsVO customListItemsVO) {
        if(customListItemsVO == null || null == customListItemsVO.getId()) return false;
        if(customListItemsVO.getShowArea() ==null) customListItemsVO.setShowArea(0);
        CustomListItems customListItems = new CustomListItems();
        TransferUtils.transferBean(customListItemsVO,customListItems);

        CustomListItems ct = customListItemsService.selectById(customListItemsVO.getId());

//        if (ct != null) {
//            customGoodsItemsService.lambdaUpdate()
//                    .set(CustomGoodsItems::getShowArea, customListItemsVO.getShowArea())
//                    .eq(CustomGoodsItems::getGoodsId, ct.getGoodsId())
//                    .eq(CustomGoodsItems::getCustomId, ct.getCustomId())
//                    .update();
//        }

        CustomList updateCustomList = new CustomList();
        updateCustomList.setId(ct.getCustomId());
        updateCustomList.setUpdateUser(getUserName());
        updateCustomList.setUpdateTime(LocalDateTime.now());
        CustomList customList = customListService.selectById(ct.getCustomId());
        if(customList!=null) {
            updateCustomList.setPlacementType(customList.getPlacementType());
            updateCustomList.setUseAlgorithm(customList.getUseAlgorithm());
            updateCustomList.setInsertQuantity(customList.getInsertQuantity());
            updateCustomList.setInsertCustomId(customList.getInsertCustomId());
            updateCustomList.setDepartmentType(customList.getDepartmentType());
        }
        customListService.updateById(updateCustomList);

        return customListItemsService.saveOrUpdate(customListItems);
    }


    @Override
    public Boolean setShowArea(CustomShowAreaVO customShowAreaList) {
        Long customId = customShowAreaList.getCustomId();
        List<CustomShowAreaVO> items = customShowAreaList.getItems();
        if (customId == null || CollectionUtils.isEmpty(items)) {
            return false;
        }
        Map<Long, Integer> map = items.stream().collect(Collectors.toMap(CustomShowAreaVO::getGoodsId, CustomShowAreaVO::getShowArea, (k1, k2) -> k2));
        CustomListGoodsVO customListGoodsVO = new CustomListGoodsVO();
        customListGoodsVO.setCustomId(customId);
        customListGoodsVO.setGoodsIds(new ArrayList<>(map.keySet()));
        customListGoodsVO.setNeedCheckShow(true);
        customListGoodsVO.setShowAreaMap(map);
        Boolean success = addGoods(customListGoodsVO);
        mqSender.send("SYNC_CUSTOM_TOP_GOODS", customId);
        return success;
    }

    @Override
    public Integer getGoodsCount(CustomListQueryVO customListQueryVO) {
        return customListItemsService.lambdaQuery()
                .eq(CustomListItems::getCustomId, customListQueryVO.getCustomId())
                .count();
    }

    @Override
    public void customFrontCateList(Long customId) {
        Long maxId = 0L;
        Integer size = 1000;
        Map<Long, Integer> categoryCountMap = new HashMap<>(16);
        while (true) {
            List<CustomListItems> itemList = customListItemsService.lambdaQuery()
                    .gt(CustomListItems::getId, maxId)
                    .eq(CustomListItems::getCustomId, customId)
                    .eq(CustomListItems::getIsShow, 1)
                    .select(CustomListItems::getId, CustomListItems::getCategoryId)
                    .last("limit " + size)
                    .orderByAsc(CustomListItems::getId)
                    .list();
            if (CollectionUtils.isEmpty(itemList)) {
                break;
            }
            maxId = itemList.get(itemList.size() - 1).getId();
            for (CustomListItems customListItems : itemList) {
                Long categoryId = customListItems.getCategoryId();
                if (categoryCountMap.containsKey(categoryId)) {
                    categoryCountMap.put(categoryId, categoryCountMap.get(categoryId) + 1);
                } else {
                    categoryCountMap.put(categoryId, 1);
                }
            }
        }

//        List<Long> allCategoryIds = customListItemsService.lambdaQuery()
//                .eq(CustomListItems::getCustomId, customId)
//                .eq(CustomListItems::getIsShow, 1)
//                .select(CustomListItems::getCategoryId)
//                .groupBy(CustomListItems::getCategoryId)
//                .list().stream().map(CustomListItems::getCategoryId).collect(Collectors.toList());
        if (categoryCountMap.isEmpty()) {
            return;
        }
        List<Long> allCategoryIds = new ArrayList<>(categoryCountMap.keySet());
        log.info("customFrontCateList|customId:{} 包含的后台类目总数量:{}", customId, allCategoryIds.size());
//        if (CollectionUtils.isEmpty(allCategoryIds)) {
//            return;
//        }
        FrontCategoryInputVO inputVO = new FrontCategoryInputVO();
        inputVO.setCategoryIds(allCategoryIds);
        List<FrontCategoryVO> frontCategoryList = frontCategoryCoreService.queryByCategory(inputVO);
        if (CollectionUtils.isEmpty(frontCategoryList)) {
            log.info("customFrontCateList|后台类目:{} 查无前台类", allCategoryIds);
            return;
        }
        Map<Long, Integer> frontGoodsMap = new HashMap<>();
        Set<String> uniqueCategoryName = new HashSet<>();
        for (FrontCategoryVO f : frontCategoryList) {
            if (f.getLevel() == 1 && !uniqueCategoryName.contains(f.getName())) {
                Long frontCateId = f.getId();
                uniqueCategoryName.add(f.getName());
                List<FrontCategoryExt> frontCategoryExtsList = frontCategoryExtService.queryCategoryIdByFrontCategoryId(frontCateId);
                if (CollectionUtils.isEmpty(frontCategoryExtsList)) {
                    continue;
                }
                List<Long> backCateIds = frontCategoryExtsList.stream().map(FrontCategoryExt::getCategoryId).collect(Collectors.toList());
//                Integer count = customListItemsService.lambdaQuery()
//                        .eq(CustomListItems::getCustomId, customId)
//                        .in(CustomListItems::getCategoryId, backCateIds)
//                        .count();
                Integer count = 0;
                for (Long backCateId : backCateIds) {
                    Integer backCount = Optional.ofNullable(categoryCountMap.get(backCateId)).orElse(0);
                    count += backCount;
                }

                log.info("customFrontCateList|前台类目:{} 对应虚拟列表的数量:{}", frontCateId, count);
                if (count >= 10) {
                    frontGoodsMap.put(frontCateId, count);
                }
            }
        }
        if(!frontGoodsMap.isEmpty()){
            List<Long> frontCategoryIdList = frontGoodsMap.keySet().stream().distinct().sorted((o1, o2) -> frontGoodsMap.get(o2).compareTo(frontGoodsMap.get(o1))).collect(Collectors.toList());
            frontCategoryIdList = frontCategoryIdList.size() > 10 ? frontCategoryIdList.subList(0, 10) : frontCategoryIdList;
            log.info("customFrontCateList|虚拟列表:{} 存入缓存的前台类目映射:{}", customId, frontCategoryIdList);
            redisApi.set("CUSTOM_FRONT_CATE_LIST_" + customId, JSON.toJSONString(frontCategoryIdList), 3L * 60 * 60 + RandomUtils.nextInt(10, 200));
        }
    }


    @Override
    public void customFrontCateListUS(Long customId) {
        Long maxId = 0L;
        Integer size = 1000;
        Map<Long, Integer> categoryCountMap = new HashMap<>(16);
        while (true) {
            List<CustomListItems> itemList = customListItemsService.lambdaQuery()
                    .gt(CustomListItems::getId, maxId)
                    .eq(CustomListItems::getCustomId, customId)
                    .eq(CustomListItems::getIsShow, 1)
                    .like(CustomListItems::getCountry, "US")
                    .select(CustomListItems::getId, CustomListItems::getCategoryId)
                    .last("limit " + size)
                    .orderByAsc(CustomListItems::getId)
                    .list();
            if (CollectionUtils.isEmpty(itemList)) {
                break;
            }
            maxId = itemList.get(itemList.size() - 1).getId();
            for (CustomListItems customListItems : itemList) {
                Long categoryId = customListItems.getCategoryId();
                if (categoryCountMap.containsKey(categoryId)) {
                    categoryCountMap.put(categoryId, categoryCountMap.get(categoryId) + 1);
                } else {
                    categoryCountMap.put(categoryId, 1);
                }
            }
        }
        if (categoryCountMap.isEmpty()) {
            return;
        }
        List<Long> allCategoryIds = new ArrayList<>(categoryCountMap.keySet());
        log.info("customFrontCateListUS|customId:{} 包含的后台类目总数量:{}", customId, allCategoryIds.size());
        FrontCategoryInputVO inputVO = new FrontCategoryInputVO();
        inputVO.setCategoryIds(allCategoryIds);
        List<FrontCategoryVO> frontCategoryList = frontCategoryCoreService.queryByCategory(inputVO);
        if (CollectionUtils.isEmpty(frontCategoryList)) {
            log.info("customFrontCateListUS|后台类目:{} 查无前台类", allCategoryIds);
            return;
        }
        Map<Long, Integer> frontGoodsMap = new HashMap<>();
        Set<String> uniqueCategoryName = new HashSet<>();
        for (FrontCategoryVO f : frontCategoryList) {
            if (f.getLevel() == 1 && !uniqueCategoryName.contains(f.getName())) {
                Long frontCateId = f.getId();
                uniqueCategoryName.add(f.getName());
                List<FrontCategoryExt> frontCategoryExtsList = frontCategoryExtService.queryCategoryIdByFrontCategoryId(frontCateId);
                if (CollectionUtils.isEmpty(frontCategoryExtsList)) {
                    continue;
                }
                List<Long> backCateIds = frontCategoryExtsList.stream().map(FrontCategoryExt::getCategoryId).collect(Collectors.toList());
                Integer count = 0;
                for (Long backCateId : backCateIds) {
                    Integer backCount = Optional.ofNullable(categoryCountMap.get(backCateId)).orElse(0);
                    count += backCount;
                }

                log.info("customFrontCateListUS|前台类目:{} 对应虚拟列表的数量:{}", frontCateId, count);
                if (count >= 10) {
                    frontGoodsMap.put(frontCateId, count);
                }
            }
        }
        if(!frontGoodsMap.isEmpty()){
            List<Long> frontCategoryIdList = frontGoodsMap.keySet().stream().distinct().sorted((o1, o2) -> frontGoodsMap.get(o2).compareTo(frontGoodsMap.get(o1))).collect(Collectors.toList());
            frontCategoryIdList = frontCategoryIdList.size() > 10 ? frontCategoryIdList.subList(0, 10) : frontCategoryIdList;
            log.info("customFrontCateListUS|虚拟列表:{} 存入缓存的前台类目映射:{}", customId, frontCategoryIdList);
            redisApi.set("CUSTOM_FRONT_CATE_LIST_US_" + customId, JSON.toJSONString(frontCategoryIdList), 3L * 60 * 60 + RandomUtils.nextInt(10, 200));
        }
    }

    @Override
    public void syncOverseasWarehouse(Long shopId) {
        final Long customId = 3461L;
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.timeout(new TimeValue(10, TimeUnit.SECONDS));
        SearchRequest searchRequest = new SearchRequest(EsEnums.GOODS_ES.getIndex());
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();

        List<Category> categoryList = categoryCoreService.queryAllByParentCategoryId(22567L);
        List<Long> categoryIdList = categoryList.stream().map(Category::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(categoryIdList)) {
            boolBuilder.mustNot(QueryBuilders.termsQuery("categoryId", categoryIdList));
        }

        boolBuilder.must(QueryBuilders.termsQuery("goodsExtConfigModel.tagIds", Collections.singletonList(4L)));
        boolBuilder.must(QueryBuilders.termQuery("shopId", shopId));
        sourceBuilder.size(500);
        sourceBuilder.query(boolBuilder);
        sourceBuilder.sort("id", SortOrder.DESC);
        sourceBuilder.fetchSource(new String[]{"id", "isShow", "isDel"}, null);
        searchRequest.source(sourceBuilder);
        log.info("syncOverseasWarehouse|sourceBuilder:{}", sourceBuilder);
        try {
            Pair<List<GoodsESVo>, String> pair = baseEsQueryServiceImpl.searchBatchData(searchRequest, GoodsESVo.class, null);
            while (CollectionUtils.isNotEmpty(pair.getLeft())) {
                List<GoodsESVo> left = pair.getLeft();
                List<Long> delGoodsIds = new ArrayList<>();
                List<Long> addGoodsIds = new ArrayList<>();
                for (GoodsESVo goodsESVo : left) {
                    if (goodsESVo.getIsDel() == 0 && "1".equals(goodsESVo.getIsShow())) {
                        addGoodsIds.add(goodsESVo.getId());
                    } else {
                        delGoodsIds.add(goodsESVo.getId());
                    }
                }
                if (CollectionUtils.isNotEmpty(delGoodsIds)) {
                    customListItemsService.deleteByCustomIdAndGoodsIds(customId, delGoodsIds);
                }
                if (CollectionUtils.isNotEmpty(addGoodsIds)) {
                    CustomListGoodsVO customListGoodsVO = new CustomListGoodsVO();
                    customListGoodsVO.setCustomId(customId);
                    customListGoodsVO.setGoodsIds(addGoodsIds);
                    this.addGoods(customListGoodsVO);
                }
                pair = baseEsQueryServiceImpl.searchBatchData(searchRequest, GoodsESVo.class, pair.getRight());
            }
        } catch (Exception e) {
            log.error("syncOverseasWarehouse|店铺:{} 同步海外仓商品异常:{}", shopId, e.getMessage());
        }
    }

    @Override
    public void syncCustomTopList() {
        log.info("syncCustomTopList|开始同步置顶商品");
        Long maxId = 0L;
        Integer size = 500;
        while (true) {
            List<CustomList> customIdLists = customListService.lambdaQuery()
                    .gt(CustomList::getId, maxId)
                    .ne(CustomList::getPlacementType, 0)
                    .eq(CustomList::getIsDel, 0)
//                    .eq(CustomList::getIsShow, 1)
                    .select(CustomList::getId)
                    .last("limit " + size)
                    .orderByAsc(CustomList::getId)
                    .list();
            if (CollectionUtils.isEmpty(customIdLists)) {
                break;
            }
            maxId = customIdLists.get(customIdLists.size() - 1).getId();
            List<Message<Object>> messages = customIdLists.stream().map(customIdList -> MqMessage.builder().body(customIdList.getId()).build()).collect(Collectors.toList());
            mqSender.sendBatch("SYNC_CUSTOM_TOP_GOODS", messages);
        }
    }

    @Override
    public void syncCustomTopListById(Long customId) {
        try {
            log.info("syncCustomTopList |同步虚拟商品:{} 置顶商品", customId);
            CustomList customList = customListService.selectById(customId);
            if (customList == null || customList.getPlacementType() == 0) {
                log.info("syncCustomTopList |虚拟列表:{} 没有设置置顶", customId);
                return;
            }

            List<CustomListItems> customListItems = customListItemsService.lambdaQuery()
                    .eq(CustomListItems::getCustomId, customId)
                    .eq(CustomListItems::getIsShow, 1)
                    .gt(CustomListItems::getShowArea, 0)
                    .select(CustomListItems::getGoodsId,
                            CustomListItems::getCategoryId,
                            CustomListItems::getIsNegative,
                            CustomListItems::getSortValue,
                            CustomListItems::getShowArea,
                            CustomListItems::getCountry)
//                    .orderByDesc(CustomListItems::getShowArea)
//                    .orderByDesc(CustomListItems::getSortValue)
                    .orderByDesc(CustomListItems::getId)
                    .last("limit 2000")
                    .list();

            if (CollectionUtils.isEmpty(customListItems)) {
                log.info("syncCustomTopList |虚拟商品列表:{} 无置顶商品", customId);
                return;
            }
            String redisKey = "SYNC_CUSTOM_TOP_GOODS_" + customId;
            log.info("syncCustomTopList |虚拟商品列表:{} redisKey:{}", customId, redisKey);
            List<CustomTopGoodsDTO> customTopGoodsList = customListItems.stream()
                    .sorted(Comparator.comparing(CustomListItems::getShowArea).thenComparing(CustomListItems::getSortValue).reversed())
                    .map(item -> {
                        CustomTopGoodsDTO customTopGoodsDTO = new CustomTopGoodsDTO();
                        customTopGoodsDTO.setGoodsId(item.getGoodsId());
                        customTopGoodsDTO.setCategoryId(item.getCategoryId());
                        customTopGoodsDTO.setIsNegative(item.getIsNegative());
                        customTopGoodsDTO.setCountry(item.getCountry());
                        return customTopGoodsDTO;
                    }).collect(Collectors.toList());
            redisApi.set(redisKey, JSON.toJSONString(customTopGoodsList), 2L * 60 * 60 + RandomUtils.nextInt(100, 200));
        } catch (Exception e) {
            log.error("syncCustomTopList  方法出现异常", e);
        }
    }

    /**
     * 根据自定义商品列表Ids批量删除（软删除，只是修改isDel字段）
     */
    @Override
    public Boolean deleteCustomListByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) return false;
        Collection<CustomList> customLists = customListService.selectByIds(ids);
        for (CustomList customList : customLists) {
            customList.setIsDel(1);
            customList.setUpdateUser(getUserName());
            customList.setUpdateTime(LocalDateTime.now());
        }

        return customListService.updateBatchById(customLists);
    }


    @Override
    public Boolean deleteGoodsById(CustomListGoodsVO customListGoodsVO) {
        CustomList customList = customListService.selectById(customListGoodsVO.getCustomId());
        //增加标签
        if (customList != null && StringUtils.isNotEmpty(customList.getTagId())){
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setTagId(Long.valueOf(customList.getTagId()));
            bindTagDTO.setGoodsIds(customListGoodsVO.getGoodsIds());
            goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
        }


        CustomList updateCustomList = new CustomList();
        updateCustomList.setId(customListGoodsVO.getCustomId());
        updateCustomList.setUpdateUser(getUserName());
        updateCustomList.setUpdateTime(LocalDateTime.now());
        if(customList!=null) {
            updateCustomList.setPlacementType(customList.getPlacementType());
            updateCustomList.setUseAlgorithm(customList.getUseAlgorithm());
            updateCustomList.setInsertQuantity(customList.getInsertQuantity());
            updateCustomList.setDepartmentType(customList.getDepartmentType());
        }
        customListService.updateById(updateCustomList);

//        customGoodsItemsService.deleteByCustomIdAndGoodsIds(customListGoodsVO.getCustomId(),customListGoodsVO.getGoodsIds());
        return customListItemsService.deleteByCustomIdAndGoodsIds(customListGoodsVO.getCustomId(),customListGoodsVO.getGoodsIds());
    }

    @Override
    public Boolean show(CustomListQueryVO input) {
        Collection<CustomList> customLists = customListService.selectByIds(input.getCustomListIds());
        for (CustomList customList: customLists){
            customList.setIsShow(input.getIsShow());
            String userName = getUserName();
            customList.setUpdateUser(userName);
            customList.setUpdateTime(LocalDateTime.now());
        }


        return customListService.updateBatchById(customLists);

    }

    @Override
    public List<CustomImportVO> getList(ImportCustmGoodsVO importCustmGoodsVO) {
        List<CustomImportVO> list = new ArrayList<>();
        if (importCustmGoodsVO.getCustomId() != null && importCustmGoodsVO.getCustomId() > 0){
            List<CustomListItems> customListItemsList = customListItemsService.queryByCustomIds(Lists.newArrayList(importCustmGoodsVO.getCustomId()));
            if (CollectionUtils.isNotEmpty(customListItemsList)){
                List<Category> categoryList = categoryCoreService.queryAllCategoriesFromCache();
                Map<Long, Category> categoryMap = new HashMap<>(16);
                if (CollectionUtils.isNotEmpty(categoryList)) {
                    categoryMap = categoryList.stream().collect(Collectors.toMap(Category::getId, Function.identity(), (oldVal, newVal) -> newVal));
                }

                List<Long> goodsIds = customListItemsList.stream().map(CustomListItems::getGoodsId).collect(Collectors.toList());
                OutDbGoodsEveryDayQueryParam queryParam = new OutDbGoodsEveryDayQueryParam();
                queryParam.setGoodsIdList(goodsIds);
                queryParam.setRunDays(7);
                Date yesterday = DateUtil.addDays(new Date(), -1);
                String date = DateUtil.format(yesterday, DateUtil.webFormat);
                queryParam.setCreateDay(date);
                queryParam.setPageSize(goodsIds.size());
                queryParam.setOrderItem("dealPayMoney");
                queryParam.setPageNow(1);
                queryParam.setIgnoreShow(1);
                List<OutDbGoodsEveryDayVO> outDbGoodsEveryDayVOS = outDbGoodsEveryDayEsService.pageOutDbGoodsEveryDay(queryParam, OutDbGoodsEveryDayVO.class);
                Map<Long, OutDbGoodsEveryDayVO> outMap = outDbGoodsEveryDayVOS.stream().collect(Collectors.toMap(OutDbGoodsEveryDayVO::getGoodsId, Function.identity()));
                List<Long> configTagIdList = complianceLabelTagConfigService.lambdaQuery()
                        .eq(ComplianceLabelTagConfig::getIsDel, 0)
                        .list()
                        .stream().map(complianceLabelTagConfig -> complianceLabelTagConfig.getTagId().longValue()).distinct().collect(Collectors.toList());

                List<GoodsExtConfig> goodsExtConfigs = goodsExtConfigService.selectByGoodsIds(goodsIds);
                Map<Long, List<Long>> goodsTagMap = goodsExtConfigs.stream().collect(Collectors.groupingBy(GoodsExtConfig::getGoodsId, Collectors.mapping(GoodsExtConfig::getTagId, Collectors.toList())));

                List<Goods> goodsList = customListItemsService.selectCustomGoods(goodsIds);
                Map<Long, Goods> goodsMap = new HashMap<>(16);
                if (CollectionUtils.isNotEmpty(goodsList)) {
                    goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (oldVal, newVal) -> newVal));
                }

                List<Long> shopIds = goodsList.stream().map(Goods::getShopId).collect(Collectors.toList());
                Map<Long, FaMerchantsApply> shopInfoMap = faMerchantsApplyCoreService.queryByShopIds(shopIds)
                        .stream()
                        .collect(Collectors.toMap(FaMerchantsApply::getId, Function.identity()));

                for(CustomListItems customListItems : customListItemsList){
                    CustomImportVO customListCategoryImportVO = new CustomImportVO();
                    customListCategoryImportVO.setCustomId(customListItems.getCustomId());
                    Goods goods = goodsMap.get(customListItems.getGoodsId());
                    if (goods != null) {
                        customListCategoryImportVO.setGoodsImg(goods.getMainImage());
                        FaMerchantsApply faMerchantsApply = shopInfoMap.get(goods.getShopId());
                        if (faMerchantsApply != null) {
                            customListCategoryImportVO.setPrincipal(faMerchantsApply.getPrincipal());
                        }
                        customListCategoryImportVO.setMinPrice(goods.getMinPrice());
                        customListCategoryImportVO.setMaxPrice(goods.getMaxPrice());
                    }

                    customListCategoryImportVO.setGoodsId(customListItems.getGoodsId());
                    customListCategoryImportVO.setShowArea(customListItems.getShowArea());
                    String isShowDesc = GoodsIsShowEnums.getDescByType(goods.getIsShow());
                    customListCategoryImportVO.setIsShow(StringUtils.isNotBlank(isShowDesc) ? isShowDesc : goods.getIsShow());
                    Long categoryId = customListItems.getCategoryId();
                    customListCategoryImportVO.setCategoryId(categoryId);
                    try {
                        if (categoryId != null) {
                            Category category = categoryMap.get(categoryId);
                            if (category != null) {
                                String pids = category.getPids();
                                if (StringUtils.isNotBlank(pids)) {
                                    String[] split = pids.split(",");
                                    for (int i = 0; i < split.length; i++) {
                                        String catId = split[i];
                                        if (StringUtils.isNotBlank(catId) && NumberUtils.isDigits(catId)) {
                                            Category parentCat = categoryMap.get(Long.parseLong(catId));
                                            if (parentCat != null) {
                                                if (i == 0) {
                                                    customListCategoryImportVO.setFirstCategoryId(parentCat.getId());
                                                    customListCategoryImportVO.setFirstCategoryName(parentCat.getName());
                                                } else if (i == 1) {
                                                    customListCategoryImportVO.setSecondCategoryId(parentCat.getId());
                                                    customListCategoryImportVO.setSecondCategoryName(parentCat.getName());
                                                } else {
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }catch (Exception e){
                        log.error("获取后台一二级类目信息失败:{}",e.getMessage(),e);
                    }

                    OutDbGoodsEveryDayVO outDbGoodsEveryDayVO = outMap.get(customListItems.getGoodsId());
                    if (Objects.nonNull(outDbGoodsEveryDayVO)) {
                        customListCategoryImportVO.setEcpm(outDbGoodsEveryDayVO.getEcpm());
                        customListCategoryImportVO.setDealCnt(outDbGoodsEveryDayVO.getDealCnt());
                        customListCategoryImportVO.setDealPayMoney(outDbGoodsEveryDayVO.getDealPayMoney());

                        customListCategoryImportVO.setShowUv(outDbGoodsEveryDayVO.getShowUv());
                        customListCategoryImportVO.setClickUv(outDbGoodsEveryDayVO.getClickUv());
                        customListCategoryImportVO.setDealUv(outDbGoodsEveryDayVO.getDealUv());
                        customListCategoryImportVO.setClickRateUv(Optional.ofNullable(outDbGoodsEveryDayVO.getClickRateUv()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP));
                        customListCategoryImportVO.setAddRateUv(Optional.ofNullable(outDbGoodsEveryDayVO.getAddRateUv()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP));
                        customListCategoryImportVO.setDealRateUv(Optional.ofNullable(outDbGoodsEveryDayVO.getDealRateUv()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP));
                        customListCategoryImportVO.setAddUv(outDbGoodsEveryDayVO.getAddUv());
                    }
                    //1待审核 2通过 3驳回
                    customListCategoryImportVO.setComplianceAuditStatus("待审核");
                    List<Long> tagIds = Optional.ofNullable(goodsTagMap.get(customListItems.getId())).orElse(Lists.newArrayList());
                    if (tagIds.contains(99L)) {
                        customListCategoryImportVO.setComplianceAuditStatus("通过");
                    } else if (CollectionUtils.isNotEmpty(configTagIdList) && CollectionUtils.isNotEmpty(CollectionUtils.intersection(tagIds, configTagIdList))) {
                        customListCategoryImportVO.setComplianceAuditStatus("驳回");
                    }
                    list.add(customListCategoryImportVO);
                }
            }
        }
        return list;
    }

    @Override
    public Long exportByCustomId(ImportCustmGoodsVO importCustmGoodsVO) throws IOException {
        Long customId = importCustmGoodsVO.getCustomId();
        String key = "CUSTOM_EXPORT_" + customId;
        if (redisApi.hasKey(key)) {
            throw new CustomException(CustomResultCode.fill(ProductResultCode.CUSTOM_EXPORT_ING, String.valueOf(customId)));
        }
        redisApi.set(key, 1, 60L * 30);
        Path directory = Paths.get(FILE_DIRECTORY);
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }
        File tempFile = Files.createTempFile(directory, customId + "_" + DateUtil.getDateString(new Date()), ".xlsx").toFile();
        Long taskId = taskInit(tempFile.getName(), getUserId());
        executor.execute(() -> {
            List<Category> categoryList = categoryCoreService.queryAllCategoriesFromCache();
            Map<Long, Category> categoryMap = new HashMap<>(16);
            if (CollectionUtils.isNotEmpty(categoryList)) {
                categoryMap = categoryList.stream().collect(Collectors.toMap(Category::getId, Function.identity(), (oldVal, newVal) -> newVal));
            }

            int size = 1000;
            Long maxId = 0L;

            try (ExcelWriter excelWriter = EasyExcel.write(tempFile, CustomImportVO.class).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                while (true) {
                    List<CustomListItems> customListItemsList = customListItemsService.lambdaQuery()
                            .gt(CustomListItems::getId, maxId)
                            .eq(CustomListItems::getCustomId, customId)
                            .last("limit " + size)
                            .orderByAsc(CustomListItems::getId)
                            .list();
                    if (CollectionUtils.isEmpty(customListItemsList)) {
                        break;
                    }
                    maxId = customListItemsList.get(customListItemsList.size() - 1).getId();
                    List<Long> goodsIds = customListItemsList.stream().map(CustomListItems::getGoodsId).collect(Collectors.toList());
                    OutDbGoodsEveryDayQueryParam queryParam = new OutDbGoodsEveryDayQueryParam();
                    queryParam.setGoodsIdList(goodsIds);
                    queryParam.setRunDays(7);
                    Date yesterday = DateUtil.addDays(new Date(), -1);
                    String date = DateUtil.format(yesterday, DateUtil.webFormat);
                    queryParam.setCreateDay(date);
                    queryParam.setPageSize(goodsIds.size());
                    queryParam.setOrderItem("dealPayMoney");
                    queryParam.setPageNow(1);
                    queryParam.setIgnoreShow(1);
                    List<OutDbGoodsEveryDayVO> outDbGoodsEveryDayVOS = outDbGoodsEveryDayEsService.pageOutDbGoodsEveryDay(queryParam, OutDbGoodsEveryDayVO.class);
                    Map<Long, OutDbGoodsEveryDayVO> outMap = outDbGoodsEveryDayVOS.stream().collect(Collectors.toMap(OutDbGoodsEveryDayVO::getGoodsId, Function.identity()));
                    List<Long> configTagIdList = complianceLabelTagConfigService.lambdaQuery()
                            .eq(ComplianceLabelTagConfig::getIsDel, 0)
                            .list()
                            .stream().map(complianceLabelTagConfig -> complianceLabelTagConfig.getTagId().longValue()).distinct().collect(Collectors.toList());

                    List<GoodsExtConfig> goodsExtConfigs = goodsExtConfigService.selectByGoodsIds(goodsIds);
                    Map<Long, List<Long>> goodsTagMap = goodsExtConfigs.stream().collect(Collectors.groupingBy(GoodsExtConfig::getGoodsId, Collectors.mapping(GoodsExtConfig::getTagId, Collectors.toList())));

                    List<Goods> goodsList = customListItemsService.selectCustomGoods(goodsIds);
                    Map<Long, Goods> goodsMap = new HashMap<>(16);
                    if (CollectionUtils.isNotEmpty(goodsList)) {
                        goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (oldVal, newVal) -> newVal));
                    }

                    List<Long> shopIds = goodsList.stream().map(Goods::getShopId).collect(Collectors.toList());
                    Map<Long, FaMerchantsApply> shopInfoMap = faMerchantsApplyCoreService.queryByShopIds(shopIds)
                            .stream()
                            .collect(Collectors.toMap(FaMerchantsApply::getId, Function.identity()));
                    List<CustomImportVO> exportList = new ArrayList<>();
                    for (CustomListItems customListItems : customListItemsList) {
                        CustomImportVO customListCategoryImportVO = new CustomImportVO();
                        customListCategoryImportVO.setCustomId(customListItems.getCustomId());
                        Goods goods = goodsMap.get(customListItems.getGoodsId());
                        if (goods != null) {
                            customListCategoryImportVO.setGoodsImg(goods.getMainImage());
                            FaMerchantsApply faMerchantsApply = shopInfoMap.get(goods.getShopId());
                            if (faMerchantsApply != null) {
                                customListCategoryImportVO.setPrincipal(faMerchantsApply.getPrincipal());
                            }
                            customListCategoryImportVO.setMinPrice(goods.getMinPrice());
                            customListCategoryImportVO.setMaxPrice(goods.getMaxPrice());
                        }

                        customListCategoryImportVO.setGoodsId(customListItems.getGoodsId());
                        customListCategoryImportVO.setShowArea(customListItems.getShowArea());
                        String isShowDesc = GoodsIsShowEnums.getDescByType(goods.getIsShow());
                        customListCategoryImportVO.setIsShow(StringUtils.isNotBlank(isShowDesc) ? isShowDesc : goods.getIsShow());
                        Long categoryId = customListItems.getCategoryId();
                        customListCategoryImportVO.setCategoryId(categoryId);
                        try {
                            if (categoryId != null) {
                                Category category = categoryMap.get(categoryId);
                                if (category != null) {
                                    String pids = category.getPids();
                                    if (StringUtils.isNotBlank(pids)) {
                                        String[] split = pids.split(",");
                                        for (int i = 0; i < split.length; i++) {
                                            String catId = split[i];
                                            if (StringUtils.isNotBlank(catId) && NumberUtils.isDigits(catId)) {
                                                Category parentCat = categoryMap.get(Long.parseLong(catId));
                                                if (parentCat != null) {
                                                    if (i == 0) {
                                                        customListCategoryImportVO.setFirstCategoryId(parentCat.getId());
                                                        customListCategoryImportVO.setFirstCategoryName(parentCat.getName());
                                                    } else if (i == 1) {
                                                        customListCategoryImportVO.setSecondCategoryId(parentCat.getId());
                                                        customListCategoryImportVO.setSecondCategoryName(parentCat.getName());
                                                    } else {
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("获取后台一二级类目信息失败:{}", e.getMessage(), e);
                        }

                        OutDbGoodsEveryDayVO outDbGoodsEveryDayVO = outMap.get(customListItems.getGoodsId());
                        if (Objects.nonNull(outDbGoodsEveryDayVO)) {
                            customListCategoryImportVO.setEcpm(outDbGoodsEveryDayVO.getEcpm());
                            customListCategoryImportVO.setDealCnt(outDbGoodsEveryDayVO.getDealCnt());
                            customListCategoryImportVO.setDealPayMoney(outDbGoodsEveryDayVO.getDealPayMoney());

                            customListCategoryImportVO.setShowUv(outDbGoodsEveryDayVO.getShowUv());
                            customListCategoryImportVO.setClickUv(outDbGoodsEveryDayVO.getClickUv());
                            customListCategoryImportVO.setDealUv(outDbGoodsEveryDayVO.getDealUv());
                            customListCategoryImportVO.setClickRateUv(Optional.ofNullable(outDbGoodsEveryDayVO.getClickRateUv()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP));
                            customListCategoryImportVO.setAddRateUv(Optional.ofNullable(outDbGoodsEveryDayVO.getAddRateUv()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP));
                            customListCategoryImportVO.setDealRateUv(Optional.ofNullable(outDbGoodsEveryDayVO.getDealRateUv()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)).setScale(3, RoundingMode.HALF_UP));
                            customListCategoryImportVO.setAddUv(outDbGoodsEveryDayVO.getAddUv());
                        }
                        //1待审核 2通过 3驳回
                        customListCategoryImportVO.setComplianceAuditStatus("待审核");
                        List<Long> tagIds = Optional.ofNullable(goodsTagMap.get(customListItems.getId())).orElse(Lists.newArrayList());
                        if (tagIds.contains(99L)) {
                            customListCategoryImportVO.setComplianceAuditStatus("通过");
                        } else if (CollectionUtils.isNotEmpty(configTagIdList) && CollectionUtils.isNotEmpty(CollectionUtils.intersection(tagIds, configTagIdList))) {
                            customListCategoryImportVO.setComplianceAuditStatus("驳回");
                        }
                        exportList.add(customListCategoryImportVO);
                    }
                    if (CollectionUtils.isNotEmpty(exportList)) {
                        excelWriter.write(exportList, writeSheet);
                    }
                }

                excelWriter.finish();
                String name = tempFile.getName();
                String contentType = name.substring(name.indexOf(".") + 1);
                String url = GoogleStorageUtil.putObjectAndReturnUrl(Files.newInputStream(tempFile.toPath()), ObjectStorePathEnum.IMPORT_EXCEL, contentType);
                taskFinish(taskId, url);
            } catch (Exception e) {
                log.error("虚拟列表:{} 导出失败:{}", customId, e.getMessage(), e);
                taskFailed(taskId, String.format("虚拟列表%s导出异常:%s", customId, e.getMessage()));
            } finally {
                tempFile.delete();
                redisApi.del(key);
            }
        });
        return taskId;
    }

    @Override
    public Boolean deleteCustomGoodsTagId(CustomListGoodsVO customListGoodsVO) {
        CustomList customList = customListService.selectById(customListGoodsVO.getCustomId());
        //增加标签
        if (customList != null && StringUtils.isNotEmpty(customList.getTagId())){
            List<CustomListItems> customListItemsList = customListItemsService.queryByCustomIds(Lists.newArrayList(customListGoodsVO.getCustomId()));
            if (CollectionUtils.isNotEmpty(customListItemsList)){
                List<Long> collect = customListItemsList.stream().map(CustomListItems::getGoodsId).collect(Collectors.toList());
                BindTagDTO bindTagDTO = new BindTagDTO();
                bindTagDTO.setTagId(Long.valueOf(customList.getTagId()));
                bindTagDTO.setGoodsIds(collect);
                goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
            }
            customList.setTagId("");
            String userName = getUserName();
            customList.setUpdateUser(userName);
            customList.setUpdateTime(LocalDateTime.now());
            customListService.updateById(customList);
        }

        return true;
    }

    @Override
    public Boolean addCustomGoodsTagId(CustomListGoodsVO customListGoodsVO) {
        CustomList customList = customListService.selectById(customListGoodsVO.getCustomId());
        //增加标签
        if (customList != null && StringUtils.isEmpty(customList.getTagId())){
            String userName = getUserName();
            customList.setUpdateUser(userName);
            customList.setUpdateTime(LocalDateTime.now());
            customList.setTagId(customListGoodsVO.getTagId());
            customListService.updateById(customList);

            executor.execute(() -> {
                List<CustomListItems> customListItemsList = customListItemsService.queryByCustomIds(Lists.newArrayList(customListGoodsVO.getCustomId()));
                if (CollectionUtils.isNotEmpty(customListItemsList)) {
                    List<Long> collect = customListItemsList.stream().map(CustomListItems::getGoodsId).collect(Collectors.toList());
                    BindTagDTO bindTagDTO = new BindTagDTO();
                    bindTagDTO.setTagId(Long.valueOf(customListGoodsVO.getTagId()));
                    bindTagDTO.setGoodsIds(collect);
                    bindTagDTO.setActivityId(Long.valueOf(customListGoodsVO.getTagId()));
                    goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
                }
            });
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async("userInfoPool")
    public void createNewGoodsCustom() {

        Collection<CustomList> customLists = customListService.selectByIds(Lists.newArrayList(1446L, 3840L));
        if (CollectionUtils.isEmpty(customLists)) {
            return;
        }

        List<Long> categoryList = new ArrayList<>();
        Object cateStr = redisApi.get("NEW_CATEGORY_GOODS_LIST");
        log.info("NEW_CATEGORY_GOODS_LIST|cateStr:{}",cateStr);
        if (cateStr != null) {
            List<Long> collect = Arrays.stream(((String) cateStr).split(",")).map(Long::valueOf).collect(Collectors.toList());
            Map<Long, List<Long>> longListMap = categoryCoreService.queryAllChildCategoryIdByParentIds(collect);
            if (longListMap != null && !longListMap.isEmpty()) {
                for (Map.Entry<Long, List<Long>> entry : longListMap.entrySet()) {
                    List<Long> childCategory = entry.getValue();
                    if (CollectionUtils.isNotEmpty(childCategory)) {
                        categoryList.addAll(childCategory);
                    }
                }
            }
        }
        log.info("NEW_CATEGORY_GOODS_LIST|categoryList:{}", categoryList);

        Map<Long, CustomList> cuMap = customLists.stream().collect(Collectors.toMap(CustomList::getId, Function.identity(), (k1, k2) -> k2));
        log.info("NEW_CATEGORY_GOODS_LIST|cuMap:{}", JSON.toJSONString(cuMap));
        //先删除新品专区虚拟商品列表的所有商品
        CustomList customList = cuMap.get(1446L);
        //指定新品类目
        CustomList specCustomList = cuMap.get(3840L);
        if (customList != null && StringUtils.isNotEmpty(customList.getTagId())){
            List<CustomListItems> customListItemsList = customListItemsService.queryByCustomIds(Lists.newArrayList(1446L));
            if (CollectionUtils.isNotEmpty(customListItemsList)){
                List<Long> goodsIds = new ArrayList<>();
                List<Long> spcGoodsIds = new ArrayList<>();
                for (CustomListItems customListItems : customListItemsList) {
                    Long goodsId = customListItems.getGoodsId();
                    goodsIds.add(goodsId);
                    if (categoryList.contains(goodsId)) {
                        spcGoodsIds.add(goodsId);
                    }
                }
                BindTagDTO bindTagDTO = new BindTagDTO();
                if (StringUtils.isNotEmpty(customList.getTagId())){
                    bindTagDTO.setTagId(Long.valueOf(customList.getTagId()));
                    bindTagDTO.setGoodsIds(goodsIds);
                    goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
                }
                if (StringUtils.isNotEmpty(specCustomList.getTagId())) {
                    bindTagDTO.setTagId(Long.valueOf(specCustomList.getTagId()));
                    bindTagDTO.setGoodsIds(spcGoodsIds);
                    goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
                }
            }
        }
        customListItemsService.deleteByCustomIds(Lists.newArrayList(1446L, 3840L));
//        customGoodsItemsService.deleteByCustomIds(Lists.newArrayList(1446L, 3840L));

         //查询48小时内创建的商品
        LocalDateTime now = LocalDateTime.now().minusDays(7);
        int pageSize = 500;
        int pageNow = 1;
        Boolean flag = Boolean.TRUE;
        while (flag){
            IPage<Goods> goodsIPage = goodsService.pageQueryNewGoods(now, pageNow, pageSize);
            List<CustomListItems> customListItemsList = new ArrayList<>();
            List<CustomListItems> specCustomListItemsList = new ArrayList<>();
            if (goodsIPage != null && CollectionUtils.isNotEmpty(goodsIPage.getRecords())){
                pageNow++;
                Set<Long> collect = goodsIPage.getRecords().stream().map(Goods::getShopId).collect(Collectors.toSet());
                List<Long> goldShopIds = faMerchantsApplyCoreService.queryGoldShopId(collect);
                if (CollectionUtils.isEmpty(goldShopIds)){
                    continue;
                }
                //判断是否在黑名单如果在黑名单剔除
                List<GoodsBlacklist> goodsBlacklists = goodsBlacklistService.queryByGoodsId(goodsIPage.getRecords().stream().map(Goods::getId).collect(Collectors.toList()));
                List<Long> blackGoodsList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(goodsBlacklists)){
                    blackGoodsList = goodsBlacklists.stream().map(GoodsBlacklist::getGoodsId).collect(Collectors.toList());
                }

                List<Long> tagGoodsIds = new ArrayList<>();
                List<Long> specGoodsIds = new ArrayList<>();

//                List<CustomGoodsItems> customGoodsItemsList = new ArrayList<>();
//                List<CustomGoodsItems> specCustomGoodsItemsList = new ArrayList<>();

                //开始新增上新虚拟商品列表
                for (Goods vo : goodsIPage.getRecords()) {
                    if (blackGoodsList.contains(vo.getId()) || !goldShopIds.contains(vo.getShopId())){
                        continue;
                    }

                    if (vo.getCreateTime().isAfter(LocalDateTime.now().minusDays(3))) {
                        CustomListItems customListItems = getCustomListItems(vo, 1446L);
                        tagGoodsIds.add(vo.getId());
                        customListItemsList.add(customListItems);
                    }

                    if(categoryList.contains(vo.getCategoryId())){
                        CustomListItems specCustomListItems = getCustomListItems(vo, 3840L);
                        specGoodsIds.add(vo.getId());
                        specCustomListItemsList.add(specCustomListItems);
                    }


//                    String country = vo.getCountry();
//                    if(StringUtils.isNotBlank(country)) {
//                        if(org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(country,"SYSTEM","default","ALL")){
//                            if (vo.getCreateTime().isAfter(LocalDateTime.now().minusDays(3))) {
//                                CustomGoodsItems customGoodsItems = getCustomGoodsItems(vo, 1446L, "SYSTEM");
//                                customGoodsItems.setShowArea(0);
//                                customGoodsItemsList.add(customGoodsItems);
//                            }
//
//                            if(categoryList.contains(vo.getCategoryId())) {
//                                CustomGoodsItems spcCustomGoodsItems = getCustomGoodsItems(vo, 3840L, "SYSTEM");
//                                spcCustomGoodsItems.setShowArea(0);
//                                specCustomGoodsItemsList.add(spcCustomGoodsItems);
//                            }
//                        }else {
//                            String[] split = country.split(",");
//                            for (String cou : split) {
//                                if (vo.getCreateTime().isAfter(LocalDateTime.now().minusDays(3))) {
//                                    CustomGoodsItems customGoodsItems = getCustomGoodsItems(vo, 1446L, cou);
//                                    customGoodsItems.setShowArea(0);
//                                    customGoodsItemsList.add(customGoodsItems);
//                                }
//
//                                if(categoryList.contains(vo.getCategoryId())) {
//                                    CustomGoodsItems specCustomGoodsItems = getCustomGoodsItems(vo, 3840L, cou);
//                                    specCustomGoodsItems.setShowArea(0);
//                                    specCustomGoodsItemsList.add(specCustomGoodsItems);
//                                }
//                            }
//                        }
//                    }
                }

                if (StringUtils.isNotEmpty(customList.getTagId()) && CollectionUtils.isNotEmpty(tagGoodsIds)) {
                    BindTagDTO bindTagDTO = new BindTagDTO();
                    bindTagDTO.setTagId(Long.valueOf(customList.getTagId()));
                    bindTagDTO.setGoodsIds(tagGoodsIds);
                    goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
                }

                if (StringUtils.isNotEmpty(specCustomList.getTagId()) && CollectionUtils.isNotEmpty(specGoodsIds)) {
                    BindTagDTO bindTagDTO = new BindTagDTO();
                    bindTagDTO.setTagId(Long.valueOf(specCustomList.getTagId()));
                    bindTagDTO.setGoodsIds(specGoodsIds);
                    goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
                }

//                if (CollectionUtils.isNotEmpty(customGoodsItemsList) || CollectionUtils.isNotEmpty(specCustomGoodsItemsList)) {
//                    log.info("NEW_CATEGORY_GOODS_LIST|customGoodsItemsList.size:{} specCustomGoodsItemsList.size:{}", customGoodsItemsList.size(), specCustomGoodsItemsList.size());
//                    customGoodsItemsList.addAll(specCustomGoodsItemsList);
//                    customGoodsItemsService.insertBatch(customGoodsItemsList);
//                }

                if (CollectionUtils.isNotEmpty(customListItemsList) || CollectionUtils.isNotEmpty(specCustomListItemsList)) {
                    log.info("NEW_CATEGORY_GOODS_LIST|customListItemsList.size:{} specCustomListItemsList.size:{}", customListItemsList.size(), specCustomListItemsList.size());
                    customListItemsList.addAll(specCustomListItemsList);
                    customListItemsService.insertBatch(customListItemsList);
                }
                if (goodsIPage.getRecords().size()< 500){
                    flag = Boolean.FALSE;
                }
            }else {
                flag = Boolean.FALSE;
            }

        }
    }

    private static CustomListItems getCustomListItems(Goods vo,Long customId) {
        CustomListItems customListItems = new CustomListItems();
        customListItems.setCustomId(customId);
        customListItems.setGoodsId(vo.getId());
        customListItems.setIsShow(vo.getIsShow());
        customListItems.setShowArea(0);
        customListItems.setCreateTime(LocalDateTime.now());
        customListItems.setCountry(vo.getCountry());
        customListItems.setPrice(vo.getMinPrice());
        customListItems.setAppChannel(vo.getAppChannel());
        customListItems.setCategoryId(vo.getCategoryId());
        return customListItems;
    }

    @Override
    @Async("userInfoPool")
    public void deletePaidGoodsCustomListItem() {
        try {
            //查询需要删除的商品
            List<CustomListItems> customListItemsList = customListItemsService.queryByPaidCustomIds();
            if (CollectionUtils.isNotEmpty(customListItemsList)){
                Map<Long, List<CustomListItems>> collect = customListItemsList.stream().collect(Collectors.groupingBy(CustomListItems::getCustomId));
                //开始删除
                collect.forEach((customId, customListItems) -> {
                    if (CollectionUtils.isEmpty(customListItems)) {
                        return;
                    }
                    List<Long> goodsIds = customListItems.stream().map(CustomListItems::getGoodsId).collect(Collectors.toList());
                    CustomListGoodsVO customListGoodsVO = new CustomListGoodsVO();
                    customListGoodsVO.setCustomId(customId);
                    customListGoodsVO.setGoodsIds(goodsIds);
                    deleteGoodsById(customListGoodsVO);
                });
            }else {
                log.info("没有需要删除的最近支付虚拟商品列表");
            }
            mqSender.send("WARING_ERROR_TOPIC", "删除新品专区成功");
        }catch (Exception e){
            mqSender.send("WARING_ERROR_TOPIC", "删除新品专区异常" + e.getMessage());
            throw e;
        }

    }

    @Override
    @Async("userInfoPool")
    public Boolean customListItemAutoUpdateJob(CustomListGoodsVO customListGoodsVO) {
        log.info("customListItemAutoUpdateJob 填充虚拟商品列表：入参{}",customListGoodsVO);
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            if (customListGoodsVO == null || null == customListGoodsVO.getCustomId() || CollectionUtils.isEmpty(customListGoodsVO.getShopIds())) {
                log.info("店铺虚拟商品列表参数错误{}", customListGoodsVO);
                return false;
            }
            List<CustomListItems> customListItemsList = customListItemsService.selectCustomListItems(customListGoodsVO.getCustomId(), customListGoodsVO.getGoodsIds());
            List<Long> customGoodsIds = Lists.newArrayList();
            if (customListItemsList != null) {
                customGoodsIds = customListItemsList.stream().map(CustomListItems::getGoodsId).collect(Collectors.toList());
            }
            List<Goods> goodsList = goodsService.queryByShopIds(customListGoodsVO.getShopIds());
            if (CollectionUtils.isEmpty(goodsList)) {
                log.info("店铺内没有商品信息{}", customListGoodsVO);
                return false;
            }
            Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));
            List<Long> deleteGoodsIds = new ArrayList<>();
            deleteGoodsIds.addAll(customGoodsIds);
            deleteGoodsIds.removeAll(goodsMap.keySet());
            //删除已下架的商品
            customListItemsService.deleteByCustomIdAndGoodsIds(customListGoodsVO.getCustomId(), deleteGoodsIds);
//            customGoodsItemsService.deleteByCustomIdAndGoodsIds(customListGoodsVO.getCustomId(), deleteGoodsIds);
            goodsMap.keySet().removeAll(customGoodsIds);
            List<CustomListItems> addCustomListItems = new ArrayList<>();
//            List<CustomGoodsItems> customGoodsItemsList = new ArrayList<>();

            for (Long goodsId : goodsMap.keySet()) {
                Goods goods = goodsMap.get(goodsId);
                String country = goods.getCountry();
                CustomListItems customListItems = new CustomListItems();
                Long customId = customListGoodsVO.getCustomId();
                customListItems.setCustomId(customId);
                customListItems.setGoodsId(goods.getId());
                customListItems.setIsShow(goods.getIsShow());
                customListItems.setShowArea(Integer.valueOf(goods.getSortValue().toString()));
                customListItems.setCreateTime(LocalDateTime.now());
                customListItems.setCountry(goods.getCountry());
                customListItems.setPrice(goods.getMinPrice());
                customListItems.setAppChannel(goods.getAppChannel());
                customListItems.setCategoryId(goods.getCategoryId());
                addCustomListItems.add(customListItems);

//                if(StringUtils.isNotBlank(country)) {
//                    if(org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase(country,"SYSTEM","default","ALL")){
//                        CustomGoodsItems customGoodsItems = getCustomGoodsItems(goods, customId, "SYSTEM");
//                        customGoodsItems.setShowArea(Integer.valueOf(goods.getSortValue().toString()));
//                        customGoodsItemsList.add(customGoodsItems);
//                    }else {
//                        String[] split = country.split(",");
//                        for (String cou : split) {
//                            CustomGoodsItems customGoodsItems = getCustomGoodsItems(goods, customId, cou);
//                            customGoodsItems.setShowArea(Integer.valueOf(goods.getSortValue().toString()));
//                            customGoodsItemsList.add(customGoodsItems);
//                        }
//                    }
//                }

            }
            customListItemsService.insertBatch(addCustomListItems);

//            if (CollectionUtils.isNotEmpty(customGoodsItemsList)) {
//                customGoodsItemsService.insertBatch(customGoodsItemsList);
//            }
            stopWatch.stop();
            log.info("customListItemAutoUpdateJob任务执行成功,执行时间:" + stopWatch.getTime() + "ms");
            mqSender.send("WARING_ERROR_TOPIC", "店铺虚拟商品列表执行成功");
        }catch (Exception e){
            mqSender.send("WARING_ERROR_TOPIC", "店铺虚拟商品列表执行失败" + customListGoodsVO.getShopIds() + e.getMessage());
            throw e;
        }
        return true;
    }


}
