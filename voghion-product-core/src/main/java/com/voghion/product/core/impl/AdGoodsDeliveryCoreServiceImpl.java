package com.voghion.product.core.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.R;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.PageView;
import com.colorlight.base.utils.CheckUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.opencsv.CSVWriter;
import com.voghion.es.service.GoodsEsService;
import com.voghion.product.api.dto.QueryGoodsVO;
import com.voghion.product.api.output.FrontCategoryDTO;
import com.voghion.product.api.output.FrontCategoryInfoVO;
import com.voghion.product.bigquery.dto.OutDbGoodsEveryDayDTO;
import com.voghion.product.bigquery.param.OutDbGoodsEveryDayQueryVO;
import com.voghion.product.bigquery.service.OutDbGoodsEveryDayService;
import com.voghion.product.core.*;
import com.voghion.product.enums.AdGoodsDeliveryResultCode;
import com.voghion.product.listener.AdDpaDataVO;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.po.*;
import com.voghion.product.model.vo.AdGoodsDeliveryReportVO;
import com.voghion.product.model.vo.AdGoodsDeliveryVO;
import com.voghion.product.model.vo.GoodsNewOutStockExportVo;
import com.voghion.product.model.vo.condition.AdGoodsDeliveryCondition;
import com.voghion.product.model.vo.customTag.CustomTagRelGoodsReqVO;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.utils.CommonConstants;
import com.voghion.util.GoogleStorageUtil;
import com.voghion.util.ObjectStorePathEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.channels.SeekableByteChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <p>
 * AdGoodsDeliveryCoreServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Service
@Slf4j
public class AdGoodsDeliveryCoreServiceImpl extends AbstractCommonServiceImpl implements AdGoodsDeliveryCoreService {
    public static final String FILE_DIRECTORY = "/data/fbFeedData";
    @Resource
    private AdGoodsDeliveryService adGoodsDeliveryService;

    @Resource
    private CategoryCoreService categoryCoreService;

    @Resource
    private FrontCategoryExtService frontCategoryExtService;

    @Resource
    private FrontCategoryCoreService frontCategoryCoreService;
    @Resource
    private FrontCategoryService frontCategoryService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private FaMerchantsApplyCoreService faMerchantsApplyCoreService;

    @Resource
    private CategoryTreeCoreService categoryTreeCoreService;

    @Resource
    private GoodsImageService goodsImageService;

    @Resource
    private GoodsExtDetailImgService goodsExtDetailImgService;

    @Resource
    private GoodsItemService goodsItemService;

    @Resource
    private GoodsTagRelService goodsTagRelService;

    @Resource
    private GoodsTagService goodsTagService;

    @Resource
    private GoodsEsService goodsEsService;

    @Resource
    private GoodsExtConfigService goodsExtConfigService;

    @Resource
    private AdDeliveryLinkService adDeliveryLinkService;

    @Resource
    private AdDeliveryReportService adDeliveryReportService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private OutDbGoodsEveryDayService outDbGoodsEveryDayService;

    @Resource
    private AdImportConfigService adImportConfigService;

    @Resource
    private AdCustomLabelService adCustomLabelService;

    @Resource
    private GoodsTagCustomService goodsTagCustomService;

    @Resource
    private GoodsAuditTaskCoreService goodsAuditTaskCoreService;

    @Resource
    private GoodsCoreService goodsCoreService;

    @Resource
    private CustomTagCoreService customTagCoreService;

    @Resource
    private RedisApi redisApi;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @Value("${buss.common.service.host}")
    private String bussCommonHost;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER_MIN = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    public static final String AUTO_DELIVERY_GOODS_SALE = "AUTO_DELIVERY_GOODS_SALE_";
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024;

    @Value("${voghion.dev}")
    private String env;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncDeliveryGoods() {
        String lockRedisKey = "LOCK_SYNC_DELIVERY_GOODS_" + LocalDateTime.now().format(DATE_TIME_FORMATTER);
        String batchDateStr = LocalDateTime.now().format(DATE_TIME_FORMATTER_MIN);
        log.info("syncDeliveryGoods|lockRedisKey:{},batchDateStr:{}", lockRedisKey, batchDateStr);
        if (!env.equals("test") && !env.equals("dev")) {
            Object o = redisApi.get(lockRedisKey);
            if (o != null) {
                log.info("syncDeliveryGoods|半小时内已经执行过");
                return;
            }
            redisApi.set(lockRedisKey, 1, 60L * 30);
        }
        try {
            AdImportConfig adImportConfig = adImportConfigService.lambdaQuery().eq(AdImportConfig::getIsEffective, 1).one();
            log.info("syncDeliveryGoods|adImportConfig:{}", adImportConfig);
            if (adImportConfig == null) {
                return;
            }
            adImportConfig.setExecuteBatchDate(batchDateStr);
            adImportConfigService.updateById(adImportConfig);

            AdGoodsDelivery one = adGoodsDeliveryService.lambdaQuery().eq(AdGoodsDelivery::getIsLatestBatch, 1).last("limit 1").one();
            log.info("syncDeliveryGoods|AdGoodsDelivery:{}", one);
            if (one != null) {
                String batchDate = one.getBatchDate();
                if (StringUtils.isNotBlank(batchDate)) {
                    boolean isLatest = batchDate.startsWith(LocalDateTime.now().format(DATE_TIME_FORMATTER));
                    if (!isLatest) {
                        adGoodsDeliveryService.lambdaUpdate().set(AdGoodsDelivery::getIsLatestBatch, 0).eq(AdGoodsDelivery::getIsLatestBatch, 1).update();
                    }
                }
            }

            OutDbGoodsEveryDayQueryVO queryVO = new OutDbGoodsEveryDayQueryVO();
            queryVO.setRunsDay(7);
            if (StringUtils.isNotBlank(adImportConfig.getExcludeLeafCategory())) {
                List<Long> backCategoryIds = Arrays.stream(adImportConfig.getExcludeLeafCategory().split(",")).map(Long::valueOf).collect(Collectors.toList());
                queryVO.setExcludeCategoryIds(backCategoryIds);
            }
            queryVO.setDealCnt(adImportConfig.getMinDeal());
            queryVO.setPageSize(500);
            Integer pageNow = 1;

            while (true) {
                queryVO.setPageNow(pageNow);
                List<OutDbGoodsEveryDayDTO> deliveryGoods = new ArrayList<>();
                if (env.equals("test") || env.equals("dev")) {
                    IPage<Goods> goodsIPage = goodsService.lambdaQuery().page(new Page<>(pageNow, 1000));
                    if (goodsIPage != null && CollectionUtils.isNotEmpty(goodsIPage.getRecords())) {
                        deliveryGoods = goodsIPage.getRecords().stream().map(goods -> {
                            OutDbGoodsEveryDayDTO outDbGoodsEveryDayDTO = new OutDbGoodsEveryDayDTO();
                            outDbGoodsEveryDayDTO.setGoodsId(goods.getId());
                            outDbGoodsEveryDayDTO.setDealCnt(RandomUtils.nextInt(1, 7));
                            outDbGoodsEveryDayDTO.setCreateTime(LocalDateTime.now().minusDays(RandomUtils.nextInt(1, 7)));
                            return outDbGoodsEveryDayDTO;
                        }).collect(Collectors.toList());
                    }
                } else {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    log.info("syncDeliveryGoods|queryVO:{}", queryVO);
                    deliveryGoods = outDbGoodsEveryDayService.getDeliveryGoods(queryVO, OutDbGoodsEveryDayDTO.class);
                }
                if (CollectionUtils.isEmpty(deliveryGoods)) {
                    return;
                }
                pageNow++;
                Map<Long, OutDbGoodsEveryDayDTO> everyDayGoodsMap = deliveryGoods.stream().collect(Collectors.toMap(OutDbGoodsEveryDayDTO::getGoodsId, Function.identity(), (k1, k2) -> k2));
                List<Long> goodsIds = new ArrayList<>(everyDayGoodsMap.keySet());
                //过滤已存的商品
                List<Long> existGoodsIds = adGoodsDeliveryService.lambdaQuery()
                        .in(AdGoodsDelivery::getGoodsId, goodsIds)
                        .select(AdGoodsDelivery::getGoodsId)
                        .list()
                        .stream().map(AdGoodsDelivery::getGoodsId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(existGoodsIds)) {
                    goodsIds.removeAll(existGoodsIds);
                    if (CollectionUtils.isEmpty(goodsIds)) {
                        continue;
                    }
                }

                List<Goods> goodsList = goodsService.lambdaQuery().in(Goods::getId, goodsIds).list();
                if (CollectionUtils.isEmpty(goodsList)) {
                    continue;
                }

                List<Long> excludeTagIds = goodsTagService.lambdaQuery()
                        .in(GoodsTag::getType, Lists.newArrayList(13, 14, 16))
                        .select(GoodsTag::getId)
                        .list()
                        .stream().map(GoodsTag::getId).collect(Collectors.toList());

                List<Long> negativeGoodsIds = goodsExtConfigService.lambdaQuery()
                        .in(GoodsExtConfig::getGoodsId, goodsIds)
                        .in(GoodsExtConfig::getTagId, CollectionUtils.isEmpty(excludeTagIds) ? Lists.newArrayList(40, 45, 46, 50, 51) : excludeTagIds)
                        .eq(GoodsExtConfig::getIsDel, 0)
                        .select(GoodsExtConfig::getGoodsId)
                        .list()
                        .stream().map(GoodsExtConfig::getGoodsId).collect(Collectors.toList());

                List<Long> customNegativeGoodsIds = goodsTagRelService.lambdaQuery()
                        .in(GoodsTagRel::getGoodsId, goodsIds)
                        .in(GoodsTagRel::getTagId, Lists.newArrayList(100102, 100105, 100104))
                        .select(GoodsTagRel::getGoodsId)
                        .list()
                        .stream().map(GoodsTagRel::getGoodsId).collect(Collectors.toList());

                negativeGoodsIds.addAll(customNegativeGoodsIds);

                List<Long> taskGoodsIds = new ArrayList<>();
                List<Long> rejectGoodIds = new ArrayList<>();
                List<AdGoodsDelivery> adGoodsDeliveryList = goodsList.stream().map(item -> {
                    Long goodsId = item.getId();
                    OutDbGoodsEveryDayDTO outDbGoodsEveryDayDTO = everyDayGoodsMap.get(goodsId);
                    if (outDbGoodsEveryDayDTO == null) {
                        return null;
                    }
                    AdGoodsDelivery adGoodsDelivery = new AdGoodsDelivery();
                    adGoodsDelivery.setGoodsId(goodsId);
                    adGoodsDelivery.setShopId(item.getShopId());
                    adGoodsDelivery.setCategoryId(item.getCategoryId());
                    adGoodsDelivery.setCreateTime(LocalDateTime.now());
                    adGoodsDelivery.setCreateUser("TF-SYSTEM");
                    adGoodsDelivery.setIsLatestBatch(1);
                    adGoodsDelivery.setBatchDate(batchDateStr);
                    if (negativeGoodsIds.contains(goodsId) || !(item.getIsDel() == 0 && "1".equals(item.getIsShow()))) {
                        adGoodsDelivery.setStatus(3);
                        adGoodsDelivery.setAuditUser("SYSTEM-TF");
                        adGoodsDelivery.setAuditTime(LocalDateTime.now());
                        rejectGoodIds.add(goodsId);
                    } else {
                        adGoodsDelivery.setStatus(0);
                        taskGoodsIds.add(goodsId);
                    }
                    Integer dealCnt = outDbGoodsEveryDayDTO.getDealCnt();
                    boolean popular = dealCnt != null && dealCnt >= adImportConfig.getMinDeal();
                    boolean newGoods = outDbGoodsEveryDayDTO.getCreateTime().isAfter(LocalDateTime.now().minusDays(7));
                    if (popular && newGoods) {
                        adGoodsDelivery.setType(3);
                    } else if (popular) {
                        adGoodsDelivery.setType(2);
                    } else if (newGoods) {
                        adGoodsDelivery.setType(1);
                    }else {
                        adGoodsDelivery.setType(2);
                    }
                    return adGoodsDelivery;
                }).filter(Objects::nonNull).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(taskGoodsIds)) {
                    log.info("syncDeliveryGoods|taskGoodsIds.size:{}", taskGoodsIds.size());
                    List<TaskDistributeDto> list = taskGoodsIds.stream().map(goodsId -> new TaskDistributeDto(goodsId, 0, null)).collect(Collectors.toList());
                    goodsAuditTaskCoreService.batchSaveTask(list, 2, "TF-SYSTEM");
                }

                if (CollectionUtils.isNotEmpty(adGoodsDeliveryList)) {
                    log.info("syncDeliveryGoods|adGoodsDeliveryList.size:{}", adGoodsDeliveryList.size());
                    adGoodsDeliveryService.saveBatch(adGoodsDeliveryList);
                }

                if (CollectionUtils.isNotEmpty(rejectGoodIds)) {
                    log.info("syncDeliveryGoods|rejectGoodIds.size:{}", rejectGoodIds.size());
//                    BindTagDTO bindTagDTO = new BindTagDTO();
//                    bindTagDTO.setGoodsIds(rejectGoodIds);
//                    bindTagDTO.setTagId(100102L);
//                    bindTagDTO.setActivityId(100102L);
//                    goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
                }
                deliveryGoods.clear();
                everyDayGoodsMap.clear();
                adGoodsDeliveryList.clear();
            }
        } catch (Exception e) {
            log.error("syncDeliveryGoods|同步投放自动化商品失败:{}", e.getMessage(), e);
            redisApi.del(lockRedisKey);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleAdDeliveryGoods(AdImportConfigDTO adImportConfigDTO) {
        Long newId = adImportConfigDTO.getNewId();
        AdImportConfig newConfig = adImportConfigService.lambdaQuery().eq(AdImportConfig::getId, newId).eq(AdImportConfig::getIsEffective, 1).one();
        log.info("handleAdDeliveryGoods|newConfig:{}", newConfig);
        if (newConfig == null) {
            return;
        }
        Set<Long> newCategoryList = Optional.ofNullable(newConfig.getExcludeLeafCategory())
                .map(newStr -> Arrays.stream(newStr.split(",")).map(Long::valueOf).collect(Collectors.toSet()))
                .orElse(new HashSet<>());
        Long oldId = adImportConfigDTO.getOldId();
        Set<Long> oldCategoryList = new HashSet<>();
        if (oldId != null) {
            AdImportConfig oldConfig = adImportConfigService.lambdaQuery().eq(AdImportConfig::getId, oldId).eq(AdImportConfig::getIsEffective, 0).one();
            oldCategoryList = Optional.ofNullable(oldConfig.getExcludeLeafCategory())
                    .map(odlStr -> Arrays.stream(odlStr.split(",")).map(Long::valueOf).collect(Collectors.toSet()))
                    .orElse(new HashSet<>());
        }

        Set<Long> deleteList = new HashSet<>(oldCategoryList);
        deleteList.removeAll(newCategoryList);
        //删除的类目
        if (CollectionUtils.isNotEmpty(deleteList)) {
            List<Long> taskGoodsIds = adGoodsDeliveryService.lambdaQuery()
                    .in(AdGoodsDelivery::getCategoryId, deleteList)
                    .eq(AdGoodsDelivery::getStatus, 3)
                    .list()
                    .stream().map(AdGoodsDelivery::getGoodsId).collect(Collectors.toList());

            List<Long> delGoodsIds = goodsTagRelService.lambdaQuery()
                    .in(GoodsTagRel::getGoodsId, taskGoodsIds)
                    .in(GoodsTagRel::getTagId, Lists.newArrayList(100101, 100102))
                    .select(GoodsTagRel::getGoodsId)
                    .list()
                    .stream().map(GoodsTagRel::getGoodsId).collect(Collectors.toList());
            taskGoodsIds.removeAll(delGoodsIds);
            if (CollectionUtils.isNotEmpty(taskGoodsIds)) {
                List<TaskDistributeDto> list = taskGoodsIds.stream().map(goodsId -> new TaskDistributeDto(goodsId, 0, null)).collect(Collectors.toList());
                goodsAuditTaskCoreService.batchSaveTask(list, 2, "TF-SYSTEM");
            }
        }

        Set<Long> addList = new HashSet<>(newCategoryList);
        addList.removeAll(oldCategoryList);
        if (CollectionUtils.isNotEmpty(addList)) {
            //TODO 因为历史通过的都是人工打过标的 所以不处理  除非要删标
        }
    }


    @Override
    public void createFbLink(Long adDeliveryLinkId) {
        AdDeliveryLink adDeliveryLink = adDeliveryLinkService.lambdaQuery()
                .eq(AdDeliveryLink::getId, adDeliveryLinkId)
                .eq(AdDeliveryLink::getStatus, 1).one();
        log.info("createFbLink|adDeliveryLink:{}", adDeliveryLink);
        if (adDeliveryLink == null) {
            return;
        }

        try {
            AdGoodsExportsCondition adGoodsExportsCondition = null;
            String conditions = adDeliveryLink.getConditions();
            if (StringUtils.isNotBlank(conditions)){
                adGoodsExportsCondition = JSON.parseObject(conditions, AdGoodsExportsCondition.class);
                log.info("AdGoodsExportsCondition body 体为：{}",JSON.toJSONString(adGoodsExportsCondition));
            }
            String filePrefix = "data_" + LocalDateTime.now().format(DATE_TIME_FORMATTER_MIN);
            // 步骤1：处理数据并生成Excel文件
            Pair<List<Path>, Path> csvPair = generateCsvFileBytes(filePrefix,adGoodsExportsCondition);
            List<Path> csvPaths = csvPair.getLeft();
            Path errorPath = csvPair.getRight();
            if (CollectionUtils.isEmpty(csvPaths)) {
                adDeliveryLink.setStatus(3);
                adDeliveryLink.setExecuteNote("生成的字节数组为空");
                adDeliveryLinkService.updateById(adDeliveryLink);
                return;
            }
            // 步骤2：将CSV文件压缩成ZIP文件
            Pair<List<Path>, Path> zipBytes = compressToZip(csvPaths, errorPath, filePrefix);
/*
            // 步骤3：上传压缩文件到服务器
            String fileUrl = uploadCompressedFile(zipBytes, "fbDataFeed.zip");
            // 打印文件URL
            log.info("createFbLink|文件URL:{} ", fileUrl);
*/
            Path errorZipPath = zipBytes.getRight();
            if (Objects.nonNull(errorZipPath)) {
                String name = errorZipPath.toFile().getName();
                String contentType = name.substring(name.indexOf(".") + 1);
                String errorFileUrl = GoogleStorageUtil.putObjectAndReturnUrl(Files.newInputStream(errorZipPath), ObjectStorePathEnum.IMPORT_ZIP, contentType);
                adDeliveryLink.setExecuteNote("执行失败:custom_label 冲突: " + errorFileUrl);
                adDeliveryLink.setStatus(3);
                adDeliveryLinkService.updateById(adDeliveryLink);
            } else {
                List<Path> zipResPathList = zipBytes.getLeft();
                StringBuilder note = new StringBuilder("执行成功,压缩文件链接: \n");
                for (Path path : zipResPathList) {
                    String name = path.toFile().getName();
                    String contentType = name.substring(name.indexOf(".") + 1);
                    String zipFileUrl = GoogleStorageUtil.putObjectAndReturnUrl(Files.newInputStream(path), ObjectStorePathEnum.IMPORT_ZIP, contentType);
                    note.append(zipFileUrl).append("\n");
                }
                adDeliveryLink.setExecuteNote(note.toString());
                adDeliveryLink.setStatus(2);
                adDeliveryLinkService.updateById(adDeliveryLink);
            }

        } catch (Exception e) {
            log.error("createFbLink|生成FB文件失败:{} ", e.getMessage(), e);
            adDeliveryLink.setStatus(3);
            adDeliveryLink.setExecuteNote("执行失败:" + e.getMessage());
            adDeliveryLinkService.updateById(adDeliveryLink);
        }

    }

    @Override
    public PageView<AdGoodsDeliveryVO> pageList(AdGoodsDeliveryCondition condition) {

        List<Long> filterGoodsIds = StringUtils.isNotBlank(condition.getGoodsIdListStr()) ? Arrays.stream(condition.getGoodsIdListStr()
                        .split("\n"))
                .filter(StringUtils::isNotBlank)
                .map(s -> Long.parseLong(s.trim()))
                .collect(Collectors.toList()) : Collections.emptyList();

        List<Long> filterCategoryIds = Lists.newArrayList();
        if (condition.getCategoryId() != null) {
            List<Category> categoryList = categoryCoreService.queryAllByParentCategoryId(condition.getCategoryId());

            if (CollectionUtils.isNotEmpty(categoryList)) {
                categoryList.stream().map(Category::getId).forEach(filterCategoryIds::add);
            }
            filterCategoryIds.add(condition.getCategoryId());
        }

        PageView<AdGoodsDeliveryVO> pageView = new PageView<>(condition.getPageSize(), condition.getPageNow());


        IPage<AdGoodsDelivery> page = adGoodsDeliveryService.lambdaQuery()
                .eq(condition.getShopId() != null, AdGoodsDelivery::getShopId, condition.getShopId())
                .eq(StringUtils.isNotBlank(condition.getAuditUser()), AdGoodsDelivery::getAuditUser, condition.getAuditUser())
                .eq(StringUtils.isNotBlank(condition.getCreateUser()), AdGoodsDelivery::getCreateUser, condition.getCreateUser())
                .in(CollectionUtils.isNotEmpty(filterGoodsIds), AdGoodsDelivery::getGoodsId, filterGoodsIds)
                .eq(condition.getStatus() != null, AdGoodsDelivery::getStatus, condition.getStatus())
                .in(CollectionUtils.isNotEmpty(filterCategoryIds), AdGoodsDelivery::getCategoryId, filterCategoryIds)
                .ge(condition.getStartCreateTime() != null, AdGoodsDelivery::getCreateTime, condition.getStartCreateTime())
                .le(condition.getEndCreateTime() != null, AdGoodsDelivery::getCreateTime, condition.getEndCreateTime())
                .ge(condition.getStartAuditTime() != null, AdGoodsDelivery::getAuditTime, condition.getStartAuditTime())
                .le(condition.getEndAuditTime() != null, AdGoodsDelivery::getAuditTime, condition.getEndAuditTime())
                .orderByDesc(AdGoodsDelivery::getId)
                .page(new Page<>(condition.getPageNow(), condition.getPageSize()));

        if (CollectionUtils.isEmpty(page.getRecords())) {
            return pageView;
        }

        List<AdGoodsDelivery> records = page.getRecords();

        List<Long> goodsIds = records.stream().map(AdGoodsDelivery::getGoodsId).distinct().collect(Collectors.toList());
        List<Goods> goodsList = goodsService.queryGoodsByIds(goodsIds);
        if (CollectionUtils.isEmpty(goodsList)) {
            return pageView;
        }
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> shopIds = records.stream().map(AdGoodsDelivery::getShopId).distinct().collect(Collectors.toList());
        Map<Long, Integer> shopDeliveryTypeMap = faMerchantsApplyCoreService.queryByShopIds(shopIds).stream().collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getDeliveryType, (v1, v2) -> v1));

        List<Long> categoryIds = records.stream().map(AdGoodsDelivery::getCategoryId).distinct().collect(Collectors.toList());
        Map<Long, String> categoryPathMap = categoryTreeCoreService.getCategoryPathByIds(categoryIds);

        //主图、详情图、sku图片
        Map<Long, List<GoodsImage>> goodsImageGroupMap = goodsImageService.queryListByGoodsIds(goodsIds).stream().collect(Collectors.groupingBy(GoodsImage::getGoodsId));

        Map<Long, List<GoodsExtDetailImg>> goodsExtDetailImgGroupMap = goodsExtDetailImgService.queryListByGoodsIds(goodsIds).stream().collect(Collectors.groupingBy(GoodsExtDetailImg::getGoodsId));

        Map<Long, List<GoodsItem>> goodsItemGroupMap = goodsItemService.queryGoodsIdsList(goodsIds).stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

        List<AdGoodsDeliveryVO> vos = records.stream().map(task -> {
            AdGoodsDeliveryVO vo = BeanCopyUtil.transform(task, AdGoodsDeliveryVO.class);
            Goods goods = goodsMap.get(task.getGoodsId());
            if (goods != null) {
                vo.setGoodsName(goods.getName());
                vo.setMainImage(goods.getMainImage());
                vo.setMinPrice(goods.getMinPrice());
                vo.setMaxPrice(goods.getMaxPrice());
                vo.setIsDel(goods.getIsDel());
                vo.setIsLock(goods.getIsLock());
                vo.setIsShow(goods.getIsShow());
            }
            vo.setDeliveryType(shopDeliveryTypeMap.get(task.getShopId()));
            vo.setCategoryName(categoryPathMap.get(task.getCategoryId()));

            List<String> goodsImageList = goodsImageGroupMap.getOrDefault(task.getGoodsId(), Lists.newArrayList()).stream().map(GoodsImage::getUrl).collect(Collectors.toList());
            vo.setGoodsImageList(goodsImageList);

            List<String> goodsExtDetailImgList = goodsExtDetailImgGroupMap.getOrDefault(task.getGoodsId(), Lists.newArrayList()).stream().map(GoodsExtDetailImg::getImgUrl).collect(Collectors.toList());
            vo.setGoodsExtDetailImgList(goodsExtDetailImgList);

            List<AdGoodsDeliveryVO.SkuImage> skuImageList = goodsItemGroupMap.getOrDefault(task.getGoodsId(), Lists.newArrayList())
                    .stream()
                    .filter(goodsItem -> StringUtils.isNotBlank(goodsItem.getSkuImage()))
                    .map(goodsItem -> new AdGoodsDeliveryVO.SkuImage(goodsItem.getName(), goodsItem.getSkuImage()))
                    .collect(Collectors.toList());
            vo.setSkuImageList(skuImageList);

            return vo;
        }).collect(Collectors.toList());

        pageView.setRowCount(page.getTotal());
        pageView.setRecords(vos);
        return pageView;
    }

    @Override
    public void collectInStockData() {
        if (env.equals("test") || env.equals("dev")) {
            String redisKey = CommonConstants.AD_IN_STOCK_GOODS_IDS + getFormattedWeekOfMonth(LocalDate.now().minusWeeks(1));
            List<Long> testGoodsIds = adGoodsDeliveryService.lambdaQuery()
                    .select(AdGoodsDelivery::getGoodsId)
                    .list()
                    .stream().map(AdGoodsDelivery::getGoodsId).collect(Collectors.toList());
            redisApi.del(redisKey);
            redisApi.lSet(redisKey, new ArrayList<>(testGoodsIds), 60L * 60 * 24 * 14);
            return;
        }
        int batchSize = 1000;
        Long maxId = 0L;
        String nowRedisKey = CommonConstants.AD_IN_STOCK_GOODS_IDS + getFormattedWeekOfMonth(LocalDate.now());
        String lockKey = "LOCK_" + nowRedisKey;
        if (redisApi.hasKey(lockKey) || redisApi.hasKey(nowRedisKey)) {
            return;
        }
        redisApi.set(lockKey, 1, 30L * 60);
        while (true) {
            List<GoodsTagRel> passGoods = goodsTagRelService.lambdaQuery()
                    .gt(GoodsTagRel::getId, maxId)
                    .eq(GoodsTagRel::getTagId, 100101L)
                    .last("limit " + batchSize)
                    .orderByAsc(GoodsTagRel::getId)
                    .list();

            if (CollectionUtils.isEmpty(passGoods)) {
                break;
            }

            maxId = passGoods.get(passGoods.size() - 1).getId();

            List<Long> passGoodsIds = passGoods.stream().map(GoodsTagRel::getGoodsId).distinct().collect(Collectors.toList());

            List<Long> rejectGoodsIds = goodsTagRelService.lambdaQuery()
                    .in(GoodsTagRel::getGoodsId, passGoodsIds)
                    .in(GoodsTagRel::getTagId, Lists.newArrayList(100104L, 100105L, 100102L))
                    .select(GoodsTagRel::getGoodsId)
                    .list()
                    .stream().map(GoodsTagRel::getGoodsId).distinct().collect(Collectors.toList());

            passGoodsIds = passGoodsIds.stream().filter(goodsId -> !rejectGoodsIds.contains(goodsId)).distinct().collect(Collectors.toList());

            List<Long> inStockGoodsIds = goodsService.lambdaQuery()
                    .in(Goods::getId, passGoodsIds)
                    .eq(Goods::getIsDel, 0)
                    .eq(Goods::getIsShow, "1")
                    .select(Goods::getId)
                    .list().stream().map(Goods::getId).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(inStockGoodsIds)) {
                continue;
            }
            redisApi.lSet(nowRedisKey, new ArrayList<>(inStockGoodsIds), 60L * 60 * 24 * 14);
        }
        redisApi.del(lockKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(AdGoodsDeliveryCondition condition) {
        List<Long> ids = condition.getIds();
        CheckUtils.isEmpty(ids, AdGoodsDeliveryResultCode.ID_NULL_ERROR);

        Integer status = condition.getStatus();
        CheckUtils.notNull(status, AdGoodsDeliveryResultCode.STATUS_NULL_ERROR);
        CheckUtils.check(status != 2 && status != 3, AdGoodsDeliveryResultCode.STATUS_CHANGE_ERROR);

        List<Long> goodsIds = new ArrayList<>();
        List<AdGoodsDelivery> adGoodsDeliveryList = adGoodsDeliveryService.lambdaQuery().in(AdGoodsDelivery::getId, ids).list();
        CheckUtils.isEmpty(adGoodsDeliveryList, AdGoodsDeliveryResultCode.LIST_NULL_ERROR);
        for (AdGoodsDelivery adGoodsDelivery : adGoodsDeliveryList) {
            adGoodsDelivery.setStatus(status);
            adGoodsDelivery.setNote(Optional.ofNullable(condition.getNote()).orElse(" "));
            adGoodsDelivery.setAuditUser(getUserName());
            adGoodsDelivery.setAuditUserId(getUserId());
            adGoodsDelivery.setAuditTime(LocalDateTime.now());
            goodsIds.add(adGoodsDelivery.getGoodsId());
        }

        if (status == 2) {
            CustomTagRelGoodsReqVO removeCustomId = new CustomTagRelGoodsReqVO();
            removeCustomId.setId(100102L);
            removeCustomId.setRelType(0);
            removeCustomId.setGoodsIdList(goodsIds);
            customTagCoreService.customTagRelGoods(removeCustomId);

            CustomTagRelGoodsReqVO addCustomId = new CustomTagRelGoodsReqVO();
            addCustomId.setId(100101L);
            addCustomId.setRelType(1);
            addCustomId.setGoodsIdList(goodsIds);
            customTagCoreService.customTagRelGoods(addCustomId);
        } else if (status == 3) {
            CustomTagRelGoodsReqVO removeCustomId = new CustomTagRelGoodsReqVO();
            removeCustomId.setId(100101L);
            removeCustomId.setRelType(0);
            removeCustomId.setGoodsIdList(goodsIds);
            customTagCoreService.customTagRelGoods(removeCustomId);

            CustomTagRelGoodsReqVO addCustomId = new CustomTagRelGoodsReqVO();
            addCustomId.setId(100102L);
            addCustomId.setRelType(1);
            addCustomId.setGoodsIdList(goodsIds);
            customTagCoreService.customTagRelGoods(addCustomId);
        }
        adGoodsDeliveryService.updateBatchById(adGoodsDeliveryList);
    }

    @Override
    public void createReport(Long reportId) {
        AdDeliveryReport adDeliveryReport = adDeliveryReportService.lambdaQuery()
                .eq(AdDeliveryReport::getId, reportId)
                .eq(AdDeliveryReport::getStatus, 1).one();
        log.info("createReport|adDeliveryReport:{}", adDeliveryReport);
        if (adDeliveryReport == null) {
            return;
        }

        try {
            int batchSize = 2000;
            Long maxId = 0L;

            Map<String, Long> newReportMap = new HashMap<>();
            Map<String, Long> popularReportMap = new HashMap<>();

            String batchDate = "";
            while (true) {
                List<AdGoodsDelivery> adGoodsDeliveryList = adGoodsDeliveryService.lambdaQuery()
                        .gt(AdGoodsDelivery::getId, maxId)
                        .eq(AdGoodsDelivery::getStatus, 2)
                        .eq(AdGoodsDelivery::getIsLatestBatch, 1)
                        .last("limit " + batchSize)
                        .orderByAsc(AdGoodsDelivery::getId)
                        .list();
                if (CollectionUtils.isEmpty(adGoodsDeliveryList)) {
                    break;
                }

                maxId = adGoodsDeliveryList.get(adGoodsDeliveryList.size() - 1).getId();
                if (StringUtils.isBlank(batchDate)) {
                    batchDate = adGoodsDeliveryList.get(adGoodsDeliveryList.size() - 1).getBatchDate();
                }
                List<Long> categoryIds = adGoodsDeliveryList.stream().map(AdGoodsDelivery::getCategoryId).distinct().collect(Collectors.toList());
                Map<Long, FirstCategoryDto> firstCategoryMap = categoryCoreService.queryFirstCategoryInfo(categoryIds);
                for (AdGoodsDelivery adGoodsDelivery : adGoodsDeliveryList) {
                    Long categoryId = adGoodsDelivery.getCategoryId();
                    Integer type = adGoodsDelivery.getType();
                    String firstCateName = Optional.ofNullable(firstCategoryMap.get(categoryId)).map(FirstCategoryDto::getFirstCategoryName).orElse("unknown");
                    if (type == 1 || type == 3) {
                        if (newReportMap.containsKey(firstCateName)) {
                            newReportMap.put(firstCateName, newReportMap.get(firstCateName) + 1);
                        } else {
                            newReportMap.put(firstCateName, 1L);
                        }
                    }

                    if (type == 2 || type == 3) {
                        if (popularReportMap.containsKey(firstCateName)) {
                            popularReportMap.put(firstCateName, popularReportMap.get(firstCateName) + 1);
                        } else {
                            popularReportMap.put(firstCateName, 1L);
                        }
                    }
                }
            }

            AdGoodsDeliveryReportVO newReportVO = getAdReport(newReportMap, batchDate, "DPA新品上架");
            AdGoodsDeliveryReportVO popularReportVO = getAdReport(popularReportMap, batchDate, "DPA动销上架");
            AdGoodsDeliveryReportVO newOutStockReportVO = getAdReport(scanNewOutStockGoods(), batchDate, "DPA新品下架");
            adDeliveryReport.setNewInStock(JSON.toJSONString(newReportVO));
            adDeliveryReport.setPopularInStock(JSON.toJSONString(popularReportVO));
            adDeliveryReport.setAddOutStock(JSON.toJSONString(newOutStockReportVO));
            adDeliveryReport.setStatus(2);
            adDeliveryReport.setExecuteNote("生成报告成功。");
            adDeliveryReportService.updateById(adDeliveryReport);
        } catch (Exception e) {
            log.error("createReport|生成报告失败:{} ", e.getMessage(), e);
            adDeliveryReport.setStatus(3);
            adDeliveryReport.setExecuteNote("执行失败:" + e.getMessage());
            adDeliveryReportService.updateById(adDeliveryReport);
        }
    }

    @Override
    public void exportNewOutStock() throws IOException {
        HttpServletResponse response = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getResponse();
        assert response != null;
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=goodsAuditTask.xlsx");

        String addOutStockRedis = CommonConstants.AD_OUT_STOCK_GOODS_IDS + getFormattedWeekOfMonth(LocalDate.now());

        Long size = 1000L;
        Long page = 1L;
        List<GoodsNewOutStockExportVo> vos = Lists.newArrayList();
        Set<Long> goodsIdSet = new HashSet<>();
        while (true) {
            log.info("exportNewOutStock|page:{}", page);
            List<Long> goodsIds = getRedisData(addOutStockRedis, page++, size);
            if (CollectionUtils.isEmpty(goodsIds)) {
                break;
            }
            List<Long> newOutGoodsIds = goodsTagRelService.lambdaQuery()
                    .in(GoodsTagRel::getGoodsId, goodsIds)
                    .in(GoodsTagRel::getTagId, Lists.newArrayList(100102, 100104, 100105))
                    .select(GoodsTagRel::getGoodsId)
                    .list()
                    .stream().map(GoodsTagRel::getGoodsId).distinct().collect(Collectors.toList());

            Map<Long, Goods> goodsMap = goodsService.lambdaQuery()
                    .in(Goods::getId, goodsIds)
                    .select(Goods::getId, Goods::getName, Goods::getIsShow, Goods::getIsDel)
                    .list().stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

            for (Long goodsId : goodsIds) {
                if (goodsIdSet.contains(goodsId)) {
                    continue;
                }
                goodsIdSet.add(goodsId);
                Goods goods = goodsMap.get(goodsId);
                if (goods == null) {
                    continue;
                }
                GoodsNewOutStockExportVo item = new GoodsNewOutStockExportVo();
                item.setGoodsId(goodsId);
                item.setGoodsName(goods.getName());
                if (newOutGoodsIds.contains(goodsId)) {
                    item.setReason("数据不好/0花费");
                }else {
                    item.setReason("其他");
                }
                vos.add(item);
            }
        }
        goodsIdSet.clear();
        EasyExcel.write(response.getOutputStream(), GoodsNewOutStockExportVo.class)
                .sheet()
                .doWrite(vos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initAdDeliveryGoods(List<AdDpaDataVO> dpaDataList) {
        if (CollectionUtils.isEmpty(dpaDataList)) {
            return;
        }
        List<Long> goodsIds = dpaDataList.stream().map(AdDpaDataVO::getId).collect(Collectors.toList());

        //过滤已存的商品
        List<Long> existGoodsIds = adGoodsDeliveryService.lambdaQuery()
                .in(AdGoodsDelivery::getGoodsId, goodsIds)
                .select(AdGoodsDelivery::getGoodsId)
                .list()
                .stream().map(AdGoodsDelivery::getGoodsId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existGoodsIds)) {
            goodsIds.removeAll(existGoodsIds);
            if (CollectionUtils.isEmpty(goodsIds)) {
                return;
            }
        }

        List<Goods> goodsList = goodsService.lambdaQuery().in(Goods::getId, goodsIds).list();
        if (CollectionUtils.isEmpty(goodsList)) {
            return;
        }
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (k1, k2) -> k2));
        List<Long> passGoodsIds = new ArrayList<>();
        List<Long> rejectGoodsIds = new ArrayList<>();
        List<AdGoodsDelivery> insertList = dpaDataList.stream().map(adDpaDataVO -> {
            Long goodsId = adDpaDataVO.getId();
            Goods goods = goodsMap.get(goodsId);
            if (goods == null) {
                return null;
            }
            AdGoodsDelivery adGoodsDelivery = new AdGoodsDelivery();
            adGoodsDelivery.setGoodsId(goodsId);
            adGoodsDelivery.setCategoryId(Optional.ofNullable(goods.getCategoryId()).orElse(-1L));
            adGoodsDelivery.setShopId(Optional.ofNullable(goods.getShopId()).orElse(-1L));

            if ("in stock".equalsIgnoreCase(adDpaDataVO.getAvailability())) {
                passGoodsIds.add(goodsId);
                adGoodsDelivery.setStatus(2);
            } else {
                rejectGoodsIds.add(goodsId);
                adGoodsDelivery.setStatus(3);
            }

            if ("popular".equalsIgnoreCase(adDpaDataVO.getCustomLabel0())) {
                adGoodsDelivery.setType(2);
                adGoodsDelivery.setBatchDate("20240605");
            } else {
                adGoodsDelivery.setType(1);
                adGoodsDelivery.setBatchDate(adDpaDataVO.getCustomLabel0().replace("上新", ""));
            }
            adGoodsDelivery.setIsLatestBatch(0);
            adGoodsDelivery.setCreateTime(LocalDateTime.now());
            adGoodsDelivery.setCreateUser("TF-SYSTEM");
            return adGoodsDelivery;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(passGoodsIds)) {
            CustomTagRelGoodsReqVO removeCustomId = new CustomTagRelGoodsReqVO();
            removeCustomId.setId(100102L);
            removeCustomId.setRelType(0);
            removeCustomId.setGoodsIdList(passGoodsIds);
            customTagCoreService.customTagRelGoods(removeCustomId);

            CustomTagRelGoodsReqVO addCustomId = new CustomTagRelGoodsReqVO();
            addCustomId.setId(100101L);
            addCustomId.setRelType(1);
            addCustomId.setGoodsIdList(passGoodsIds);
            customTagCoreService.customTagRelGoods(addCustomId);
        }
        if (CollectionUtils.isNotEmpty(rejectGoodsIds)) {
            CustomTagRelGoodsReqVO removeCustomId = new CustomTagRelGoodsReqVO();
            removeCustomId.setId(100101L);
            removeCustomId.setRelType(0);
            removeCustomId.setGoodsIdList(rejectGoodsIds);
            customTagCoreService.customTagRelGoods(removeCustomId);

            CustomTagRelGoodsReqVO addCustomId = new CustomTagRelGoodsReqVO();
            addCustomId.setId(100102L);
            addCustomId.setRelType(1);
            addCustomId.setGoodsIdList(rejectGoodsIds);
            customTagCoreService.customTagRelGoods(addCustomId);
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            adGoodsDeliveryService.insertBatch(insertList);
        }
    }

    @Override
    public List<OutDbGoodsEveryDayDTO> testQueryAdBq(OutDbGoodsEveryDayQueryVO condition) {
        log.info("testQueryAdBq|condition:{}", JSON.toJSONString(condition));
        return outDbGoodsEveryDayService.getDeliveryGoods(condition, OutDbGoodsEveryDayDTO.class);
    }


    public Map<String, Long> scanNewOutStockGoods() {
        // 初始化游标
        Map<String, Long> addOutStockMap = new HashMap<>();
        String redisKey = CommonConstants.AD_IN_STOCK_GOODS_IDS + getFormattedWeekOfMonth(LocalDate.now().minusWeeks(1));
        String addOutStockRedis = CommonConstants.AD_OUT_STOCK_GOODS_IDS + getFormattedWeekOfMonth(LocalDate.now());

        Long size = 1000L;
        Long page = 1L;
        boolean addRedisFlag = true;
        String lockKey = "LOCK_" + addOutStockRedis;
        if (redisApi.hasKey(lockKey)) {
            addRedisFlag = false;
        } else {
            redisApi.del(addOutStockRedis);
        }
        redisApi.set(lockKey, 1, 60L * 10);
        Set<Long> goodsIdSet = new HashSet<>();
        while (true) {
            log.info("scanNewOutStockGoods|page:{}", page);
            List<Long> goodsIds = getRedisData(redisKey, page++, size);
            if (CollectionUtils.isEmpty(goodsIds)) {
                break;
            }
            List<Long> newOutGoodsIds = goodsTagRelService.lambdaQuery()
                    .in(GoodsTagRel::getGoodsId, goodsIds)
                    .in(GoodsTagRel::getTagId, Lists.newArrayList(100102, 100105, 100104))
                    .select(GoodsTagRel::getGoodsId)
                    .list()
                    .stream().map(GoodsTagRel::getGoodsId).distinct().collect(Collectors.toList());

            Map<Long, Long> outStockMap = goodsService.lambdaQuery()
                    .and(wr -> wr.in(Goods::getId, goodsIds).and(wrp -> wrp.ne(Goods::getIsDel, 0).or().ne(Goods::getIsShow, "1")))
                    .or(CollectionUtils.isNotEmpty(newOutGoodsIds), wr -> wr.in(Goods::getId, newOutGoodsIds))
                    .select(Goods::getId, Goods::getCategoryId)
                    .list().stream().collect(Collectors.toMap(Goods::getId, Goods::getCategoryId, (v1, v2) -> v1));
            if (!outStockMap.isEmpty()) {
                List<Long> cateIds = outStockMap.values().stream().distinct().collect(Collectors.toList());
                if (addRedisFlag) {
                    List<Long> goodsIdList = new ArrayList<>(outStockMap.keySet());
                    goodsIdList.removeIf(goodsIdSet::contains);
                    redisApi.lSet(addOutStockRedis, new ArrayList<>(goodsIdList), 60L * 60 * 24 * 14);
                }
                Map<Long, FirstCategoryDto> firstCategoryMap = categoryCoreService.queryFirstCategoryInfo(cateIds);

                for (Map.Entry<Long, Long> entry : outStockMap.entrySet()) {
                    Long goodsId = entry.getKey();
                    if(goodsIdSet.contains(goodsId)){
                        continue;
                    }
                    goodsIdSet.add(goodsId);
                    Long categoryId = entry.getValue();
                    String firstCateName = Optional.ofNullable(firstCategoryMap.get(categoryId)).map(FirstCategoryDto::getFirstCategoryName).orElse("unknown");
                    if (addOutStockMap.containsKey(firstCateName)) {
                        addOutStockMap.put(firstCateName, addOutStockMap.get(firstCateName) + 1);
                    } else {
                        addOutStockMap.put(firstCateName, 1L);
                    }
                }
            }
        }
        goodsIdSet.clear();
        return addOutStockMap;
    }

    public List<Long> getRedisData(String redisKey, Long pageNow, Long pageSize) {
        log.info("getRedisDate|redisKey:{} pageNow:{} pageSize:{}", redisKey, pageNow, pageSize);
        long endIndex = pageSize * pageNow - 1;
        long startIndex = endIndex - pageSize + 1;
        List<Object> objectList = redisApi.lGet(redisKey, startIndex, endIndex);
        if (CollectionUtils.isNotEmpty(objectList)) {
            return objectList.stream().map(Long.class::cast).distinct().collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private static AdGoodsDeliveryReportVO getAdReport(Map<String, Long> reportMap, String batchDate,String suffix) {
        AdGoodsDeliveryReportVO reportVo = new AdGoodsDeliveryReportVO();
        if (reportMap == null || reportMap.isEmpty()) {
            return reportVo;
        }
        List<AdGoodsDeliveryReportVO.AdDeliveryItem> itemList = new ArrayList<>();
        Long total = reportMap.values().stream().reduce(0L, Long::sum);
        reportVo.setTotal(total);
        reportVo.setProportion(BigDecimal.valueOf(100));
        // 填充itemList
        for (Map.Entry<String, Long> entry : reportMap.entrySet()) {
            AdGoodsDeliveryReportVO.AdDeliveryItem item = new AdGoodsDeliveryReportVO.AdDeliveryItem();
            item.setCategoryName(entry.getKey());
            item.setCount(entry.getValue());
            // 计算占比
            BigDecimal proportion = BigDecimal.valueOf(entry.getValue())
                    .divide(BigDecimal.valueOf(total), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            item.setProportion(proportion);
            itemList.add(item);
        }
        itemList.sort(Comparator.comparing(AdGoodsDeliveryReportVO.AdDeliveryItem::getCount).reversed());
        reportVo.setBatchDate((batchDate.length() > 8 ? batchDate.substring(4, 8) : batchDate) + suffix);
        reportVo.setItemList(itemList);
        log.info("getAdReport|reportVo:{}", JSON.toJSONString(reportVo));
        return reportVo;
    }


    public static String getFormattedWeekOfMonth(LocalDate date) {
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekOfMonth = date.get(weekFields.weekOfMonth());
        int year = date.getYear();
        int month = date.getMonthValue();
        return String.format("%d%d%02d", year, month, weekOfMonth);
    }

    static File zipFile(Path sourcePath, String prefix) throws IOException {
        Path directory = Paths.get(FILE_DIRECTORY);
        File tempZipFile = Files.createTempFile(directory, prefix, ".zip").toFile();
        FileOutputStream fos = new FileOutputStream(tempZipFile);
        ZipOutputStream zipOut = new ZipOutputStream(fos);
        ZipEntry zipEntry = new ZipEntry(prefix + ".csv");
        zipOut.putNextEntry(zipEntry);
        BufferedInputStream inputStream = new BufferedInputStream(Files.newInputStream(sourcePath.toFile().toPath()));
        try {
            byte[] buffer = new byte[5 * 1024 * 1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) > 0) {
                zipOut.write(buffer, 0, bytesRead);
            }
        } finally {
            zipOut.closeEntry();
            zipOut.close();
            inputStream.close();
        }
        return tempZipFile;
    }
    private static Pair<List<Path>, Path> compressToZip(List<Path> csvBytesList, Path errorPath, String filePrefix) throws IOException {
        // 指定目录
        Path directory = Paths.get(FILE_DIRECTORY);
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }

//        List<byte[]> csvZipBytes = new ArrayList<>();
        List<Path> csvZipPaths = new ArrayList<>();
        for (int i = 0; i < csvBytesList.size(); i++) {
            Path path = csvBytesList.get(i);
            String prefix = filePrefix + "_" + (i + 1);
            File file = zipFile(path, prefix);
            csvZipPaths.add(file.toPath());
        }

        Path path = null;
        if (errorPath != null) {
            String prefix = "error_" + filePrefix;
            File file = zipFile(errorPath, prefix);
            path = file.toPath();
        }
        // 读取ZIP文件为字节数组
        return new ImmutablePair<>(csvZipPaths, path);
    }

    private String uploadCompressedFile(byte[] fileBytes, String fileName) {
        String serverUrl = bussCommonHost + "/buss-common/upload/uploadGoogleFile";

        RestTemplate restTemplate = new RestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);


        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new ByteArrayResource(fileBytes) {
            @Override
            public String getFilename() {
                return fileName;
            }
        });
        body.add("fileName", fileName);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(serverUrl, requestEntity, String.class);
        String responseStr = response.getBody();
        JSONObject responseJson = JSON.parseObject(responseStr);
        return Optional.ofNullable(responseJson.getString("data")).orElse("");
    }

    private Pair<List<Path>, Path> generateCsvFileBytes(String filePrefix,AdGoodsExportsCondition adGoodsExportsCondition) throws IOException {
        List<Path> filePaths = new ArrayList<>();
        Path errorPath = null;
        try {
            // 指定目录
            Path directory = Paths.get(FILE_DIRECTORY);
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
            }

            int fileCount = 1;
            Long maxId = 0L;
            List<FbErrorGoodsDTO> errorGoodsList = new ArrayList<>();

            do {
                // 创建一个临时文件
                File tempFile = Files.createTempFile(directory, filePrefix + "_" + fileCount, ".csv").toFile();
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile));
                     CSVWriter csvWriter = new CSVWriter(writer)) {
                    // 写入CSV表头
                    csvWriter.writeNext(new String[]{
                            "id", "title", "description", "availability", "condition", "price", "ios_url",
                            "ios_app_store_id", "ios_app_name", "android_url", "android_package", "android_app_name",
                            "link", "image_link", "brand", "custom_label_0", "custom_label_1", "custom_label_2",
                            "custom_label_3", "custom_label_4", "sale_price", "sale_price_effective_date", "product_type",
                            "gender", "ios_cate_url", "android_cate_url","video[0].url"
                    });

                    int batchSize = 2000;
                    int rowCount = 0;
                    boolean hasData = false;
                    while (rowCount < 800000) {
                        List<AdGoodsDelivery> adGoodsDeliveryList = adGoodsDeliveryService.lambdaQuery()
                                .gt(AdGoodsDelivery::getId, maxId)
                                //只要in stock
                                .eq(AdGoodsDelivery::getStatus, 2)
                                .last("limit " + batchSize)
                                .orderByAsc(AdGoodsDelivery::getId)
                                .list();
                        if (CollectionUtils.isEmpty(adGoodsDeliveryList)) {
                            break;
                        }
                        maxId = adGoodsDeliveryList.get(adGoodsDeliveryList.size() - 1).getId();
                        List<FbDataFeedDTO> batchDataList = getDataList(adGoodsDeliveryList, errorGoodsList,adGoodsExportsCondition);
                        if(CollectionUtils.isEmpty(batchDataList)){
                            continue;
                        }

                        if (!batchDataList.isEmpty()) {
                            hasData = true;
                            writeBatch(csvWriter, batchDataList);
                            rowCount += batchDataList.size();
                        }

                        if (!errorGoodsList.isEmpty()) {
                            File tempErrorFile = new File(FILE_DIRECTORY + "/error_" + filePrefix + ".csv");
                            if (!tempErrorFile.exists()) {
                                tempErrorFile.createNewFile();
                            }
                            try (BufferedWriter errorWriter = new BufferedWriter(new FileWriter(tempErrorFile, true));
                                 CSVWriter errorCsvWriter = new CSVWriter(errorWriter)) {
                                writeErrorBatch(errorCsvWriter, errorGoodsList);
                                errorCsvWriter.flush();
                                errorPath = tempErrorFile.toPath();
                            }
                        }
                    }
                    if (hasData) {
                        csvWriter.flush();
                        filePaths.add(tempFile.toPath());
                    }
                    fileCount++;
                }
            } while (filePaths.size() >= fileCount - 1);
            return new ImmutablePair<>(filePaths, errorPath);
        } catch (Exception e) {
            log.error("generateCsvFileBytes|写入临时文件异常:{}", e.getMessage(), e);
            throw e;
        }
    }

    private static void writeBatch(CSVWriter csvWriter, List<FbDataFeedDTO> batch) throws IOException {
        for (FbDataFeedDTO data : batch) {
            csvWriter.writeNext(new String[]{
                    data.getId().toString(), data.getTitle(), data.getDescription(), data.getAvailability(), data.getCondition(),
                    data.getPrice(), data.getIosUrl(), data.getIosAppStoreId(), data.getIosAppName(), data.getAndroidUrl(),
                    data.getAndroidPackage(), data.getAndroidAppName(), data.getLink(), data.getImageLink(), data.getBrand(),
                    data.getCustomLabel0(), data.getCustomLabel1(), data.getCustomLabel2(), data.getCustomLabel3(),
                    data.getCustomLabel4(), data.getSalePrice(), data.getSalePriceEffectiveDate(), data.getProductType(),
                    data.getGender(), data.getIosCateUrl(), data.getAndroidCateUrl(), data.getVideo()
            });
        }
    }

    private static String escapeCsv(String field) {
        if (field == null) {
            return "";
        }
        if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            field = field.replace("\"", "\"\"");
            return "\"" + field + "\"";
        }
        return field;
    }

    private static void writeErrorBatch(CSVWriter errorCsvWriter, List<FbErrorGoodsDTO> batch) throws IOException {
        for (FbErrorGoodsDTO data : batch) {
            errorCsvWriter.writeNext(new String[]{
                    data.getId().toString(), data.getTitle(), data.getCustomLabel(), data.getTagIds()
            });
        }
    }


    private List<FbDataFeedDTO> getDataList(List<AdGoodsDelivery> adGoodsDeliveryList, List<FbErrorGoodsDTO> errorGoodsList,AdGoodsExportsCondition adGoodsExportsCondition) {
        List<Long> goodsIds = adGoodsDeliveryList.stream().map(AdGoodsDelivery::getGoodsId).distinct().collect(Collectors.toList());
        List<Goods> goodsList = goodsService.queryGoodsByIds(goodsIds);
        if (CollectionUtils.isEmpty(goodsList)) {
            return Lists.newArrayList();
        }
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (k1, k2) -> k2));

        List<Long> passGoodsIds = goodsTagRelService.lambdaQuery()
                .in(GoodsTagRel::getGoodsId, goodsIds)
                .eq(GoodsTagRel::getTagId, 100101L)
                .select(GoodsTagRel::getGoodsId)
                .list()
                .stream().map(GoodsTagRel::getGoodsId).distinct().collect(Collectors.toList());
        //新品受下面标签限制
        List<Long> newGoodsIds = new ArrayList<>();
        for (AdGoodsDelivery goodsDelivery : adGoodsDeliveryList) {
            if ((goodsDelivery.getType() == 1 || goodsDelivery.getType() == 3)
                    && goodsDelivery.getIsLatestBatch() == 1
                    && passGoodsIds.contains(goodsDelivery.getGoodsId())) {
                newGoodsIds.add(goodsDelivery.getGoodsId());
            }
        }

        if(CollectionUtils.isNotEmpty(passGoodsIds)) {
            //吐出的要是带有 99标签的
            passGoodsIds = goodsExtConfigService.lambdaQuery()
                    .in(GoodsExtConfig::getGoodsId, passGoodsIds)
                    .in(GoodsExtConfig::getTagId, 99)
                    .eq(GoodsExtConfig::getIsDel, 0)
                    .select(GoodsExtConfig::getGoodsId)
                    .list()
                    .stream().map(GoodsExtConfig::getGoodsId).collect(Collectors.toList());
        }
        List<Long> rejectGoodsIds = goodsTagRelService.lambdaQuery()
                .in(GoodsTagRel::getGoodsId, goodsIds)
                .in(GoodsTagRel::getTagId, Lists.newArrayList(100104L, 100105L, 100102L))
                .select(GoodsTagRel::getGoodsId)
                .list()
                .stream().map(GoodsTagRel::getGoodsId).distinct().collect(Collectors.toList());

        passGoodsIds = passGoodsIds.stream().filter(goodsId -> !rejectGoodsIds.contains(goodsId)).collect(Collectors.toList());
        //新品不受99标签限制
        passGoodsIds.addAll(newGoodsIds);
        // 输入标签过滤
        if(CollectionUtils.isNotEmpty(passGoodsIds)
                && adGoodsExportsCondition != null && adGoodsExportsCondition.getGoodsTag() != null) {
            Long goodsTag = adGoodsExportsCondition.getGoodsTag();
            passGoodsIds = goodsExtConfigService.lambdaQuery()
                    .in(GoodsExtConfig::getGoodsId, passGoodsIds)
                    .eq(GoodsExtConfig::getTagId, goodsTag)
                    .eq(GoodsExtConfig::getIsDel, 0)
                    .select(GoodsExtConfig::getGoodsId)
                    .list()
                    .stream().map(GoodsExtConfig::getGoodsId).collect(Collectors.toList());
        }
        passGoodsIds = passGoodsIds.stream().distinct().collect(Collectors.toList());
        Set<Long> tagIds = new HashSet<>();
        Map<String, List<Long>> customLabelMap = new HashMap<>();
        List<AdCustomLabel> adCustomLabelList = adCustomLabelService.lambdaQuery().list();
        if (CollectionUtils.isNotEmpty(adCustomLabelList)) {
            for (AdCustomLabel adCustomLabel : adCustomLabelList) {
                List<Long> customTagIds = parseTagIds(adCustomLabel.getTagIds());
                tagIds.addAll(customTagIds);
                customLabelMap.put(adCustomLabel.getLabelName(), customTagIds);
            }
        }
        Map<Long, List<Long>> goodsTagMap = getGoodsTagMap(tagIds, goodsIds);

        Map<Long, String> tagIdNameMap = goodsTagCustomService.lambdaQuery()
                .in(GoodsTagCustom::getId, tagIds)
                .select(GoodsTagCustom::getId, GoodsTagCustom::getTagName)
                .list()
                .stream()
                .collect(Collectors.toMap(GoodsTagCustom::getId, GoodsTagCustom::getTagName, (k1, k2) -> k2));

        Map<Long, GoodsImage> goodsImageGroupMap = goodsImageService.queryListByGoodsIds(goodsIds).stream()
                .filter(goodsImage -> goodsImage.getImageType() == 2
                        && StringUtils.isNotBlank(goodsImage.getUrl())
                        && goodsImage.getUrl().contains("https://")
                        && goodsImage.getUrl().contains("images.voghion.com")
                )
                .collect(Collectors.toMap(
                        GoodsImage::getGoodsId,
                        goodsImage -> goodsImage,
                        (existing, replacement) -> existing
                ));

        QueryGoodsVO queryGoodsVO = new QueryGoodsVO();
        queryGoodsVO.setGoodsIds(goodsIds);
        queryGoodsVO.setCountry("DE");
        queryGoodsVO.setCurrency("EUR");
        Map<String, BigDecimal> esCountryPrice = getEsCountryPrice(queryGoodsVO);
        List<FbDataFeedDTO> fbDataFeedList = new ArrayList<>();
        for (AdGoodsDelivery adGoodsDelivery : adGoodsDeliveryList) {
            Long goodsId = adGoodsDelivery.getGoodsId();
            Long categoryId = adGoodsDelivery.getCategoryId();
            Goods goods = goodsMap.get(goodsId);
            if (goods == null) {
                continue;
            }
            if (!passGoodsIds.contains(goodsId)){
                continue;
            }
            // 国家过滤
            if (adGoodsExportsCondition != null && CollectionUtils.isNotEmpty(adGoodsExportsCondition.getCountryCodes())){
                if (StringUtils.isBlank(goods.getCountry())){
                    continue;
                }
                List<String> goodsCountryCodes = Arrays.stream(goods.getCountry().split(",")).collect(Collectors.toList());
                List<String> limitCountryCodes = adGoodsExportsCondition.getCountryCodes();
                // 全包含，商品可售国家，都存在于输入的参数中
                if (!goodsCountryCodes.containsAll(limitCountryCodes)){
                    continue;
                }
            }
            BigDecimal price = Optional.ofNullable(esCountryPrice.get(String.valueOf(goodsId))).orElse(BigDecimal.ZERO);
            if (BigDecimal.ZERO.equals(price)) {
                price = goods.getMinPrice().multiply(new BigDecimal("1.15"));
            }
            FbDataFeedDTO fbDataFeedDTO = new FbDataFeedDTO();
            fbDataFeedDTO.setId(goodsId);
            fbDataFeedDTO.setTitle(substringWithLength(goods.getName(), 100));
            fbDataFeedDTO.setDescription(substringWithLength(goods.getName(), 200));
            boolean availabilityFlag = "1".equals(goods.getIsShow()) && goods.getIsDel() == 0 && passGoodsIds.contains(goodsId);
            //不是in stock的不要
            if (!availabilityFlag) {
                continue;
            }
            fbDataFeedDTO.setAvailability(availabilityFlag ? "in stock" : "out of stock");
            fbDataFeedDTO.setCondition("new");
            fbDataFeedDTO.setPrice(generateVirtualOriginalPrice(price) + "EUR");
            fbDataFeedDTO.setIosUrl("voghion://approuter/home?showIndex=0&gid=" + goodsId);
            fbDataFeedDTO.setIosAppStoreId("1568242949");
            fbDataFeedDTO.setIosAppName("Online shopping app");
            fbDataFeedDTO.setAndroidUrl("voghion://approuter/home?showIndex=0&gid=" + goodsId);
            fbDataFeedDTO.setAndroidPackage("com.voghion.app");
            fbDataFeedDTO.setAndroidAppName("Voghion - Online shopping app");
            fbDataFeedDTO.setLink("https://m.voghion.com/goodsdetail/" + goodsId);
            fbDataFeedDTO.setImageLink(validateAndFixImageUrl(goods.getMainImage()));
            fbDataFeedDTO.setBrand("Voghion");
            fbDataFeedDTO.setSalePriceEffectiveDate("");
            fbDataFeedDTO.setProductType("");
            GoodsImage goodsImage = goodsImageGroupMap.get(goodsId);
            fbDataFeedDTO.setVideo(Optional.ofNullable(goodsImage).map(GoodsImage::getUrl).orElse(""));

            List<Category> categories = categoryCoreService.queryParentCategoryInfoByCategoryId(categoryId);
            if(CollectionUtils.isEmpty(categories)){
                continue;
            }
            String categoryStr = categories.stream().map(Category::getName).collect(Collectors.joining(" > "));
            fbDataFeedDTO.setProductType(categoryStr);

            fbDataFeedDTO.setGender(assignGender(categoryStr));

            List<FrontCategoryDTO> frontCategoryInfo = Optional.ofNullable(getFrontCategoryInfo(categoryId)).orElse(Lists.newArrayList());
            Long value =  401L;
            if (CollectionUtils.isNotEmpty(frontCategoryInfo) && frontCategoryInfo.get(0) != null) {
                value = frontCategoryInfo.get(0).getId();
            }
            String router = "voghion://approuter/category?type=" + (frontCategoryInfo.size() > 1 ? 21 : 14) + "&value=" + value + "&gid=" + goodsId;
            fbDataFeedDTO.setIosCateUrl(router);
            fbDataFeedDTO.setAndroidCateUrl(router);
            if (adGoodsDelivery.getType() == 2 || adGoodsDelivery.getType() == 3) {
                fbDataFeedDTO.setCustomLabel0("popular");
            } else {
                fbDataFeedDTO.setCustomLabel0(adGoodsDelivery.getBatchDate().substring(0, 8) + "上新");
            }
            fbDataFeedDTO.setCustomLabel1("");
            fbDataFeedDTO.setCustomLabel2("");
            fbDataFeedDTO.setCustomLabel3("");
            fbDataFeedDTO.setCustomLabel4("");
            List<Long> goodsTagIds = Optional.ofNullable(goodsTagMap.get(goodsId)).orElse(Lists.newArrayList());
            handleCustomLabel(customLabelMap, goodsTagIds, goods, errorGoodsList, fbDataFeedDTO, tagIdNameMap);
            Category lastOne = categories.get(categories.size() - 1);
            fbDataFeedDTO.setCustomLabel4(lastOne.getName());
            BigDecimal salePrice = price.setScale(2, RoundingMode.HALF_UP);
            fbDataFeedDTO.setSalePrice(salePrice + "EUR");
            fbDataFeedList.add(fbDataFeedDTO);
        }
        return fbDataFeedList;
    }

    private static void handleCustomLabel(Map<String, List<Long>> customLabelMap, List<Long> goodsTagIds, Goods goods,
                                          List<FbErrorGoodsDTO> errorGoodsList, FbDataFeedDTO fbDataFeedDTO,
                                          Map<Long, String> tagIdNameMap) {
        List<Long> customLabel1 = Optional.ofNullable(customLabelMap.get("custom_label_1")).orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(customLabel1)) {
            Collection<Long> customLabel1Intersection = CollectionUtils.intersection(customLabel1, goodsTagIds);
            if (CollectionUtils.isNotEmpty(customLabel1Intersection)) {
                if (customLabel1Intersection.size() > 1) {
                    FbErrorGoodsDTO fbErrorGoodsDTO = new FbErrorGoodsDTO();
                    fbErrorGoodsDTO.setId(goods.getId());
                    fbErrorGoodsDTO.setTitle(goods.getName());
                    fbErrorGoodsDTO.setCustomLabel("custom_label_1");
                    fbErrorGoodsDTO.setTagIds(StringUtils.join(customLabel1Intersection, ","));
                    errorGoodsList.add(fbErrorGoodsDTO);
                } else {
                    Long tagId = customLabel1Intersection.stream().findFirst().orElse(-1L);
                    fbDataFeedDTO.setCustomLabel1(tagIdNameMap.get(tagId));
                }
            }
        }
        List<Long> customLabel2 = Optional.ofNullable(customLabelMap.get("custom_label_2")).orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(customLabel2)) {
            Collection<Long> customLabel2Intersection = CollectionUtils.intersection(customLabel2, goodsTagIds);
            if (CollectionUtils.isNotEmpty(customLabel2Intersection)) {
                if (customLabel2Intersection.size() > 1) {
                    FbErrorGoodsDTO fbErrorGoodsDTO = new FbErrorGoodsDTO();
                    fbErrorGoodsDTO.setId(goods.getId());
                    fbErrorGoodsDTO.setTitle(goods.getName());
                    fbErrorGoodsDTO.setCustomLabel("custom_label_2");
                    fbErrorGoodsDTO.setTagIds(StringUtils.join(customLabel2Intersection, ","));
                    errorGoodsList.add(fbErrorGoodsDTO);
                } else {
                    Long tagId = customLabel2Intersection.stream().findFirst().orElse(-1L);
                    fbDataFeedDTO.setCustomLabel2(tagIdNameMap.get(tagId));
                }
            }
        }
        List<Long> customLabel3 = Optional.ofNullable(customLabelMap.get("custom_label_3")).orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(customLabel3)) {
            Collection<Long> customLabel3Intersection = CollectionUtils.intersection(customLabel3, goodsTagIds);
            if (CollectionUtils.isNotEmpty(customLabel3Intersection)) {
                if (customLabel3Intersection.size() > 1) {
                    FbErrorGoodsDTO fbErrorGoodsDTO = new FbErrorGoodsDTO();
                    fbErrorGoodsDTO.setId(goods.getId());
                    fbErrorGoodsDTO.setTitle(goods.getName());
                    fbErrorGoodsDTO.setCustomLabel("custom_label_3");
                    fbErrorGoodsDTO.setTagIds(StringUtils.join(customLabel3Intersection, ","));
                    errorGoodsList.add(fbErrorGoodsDTO);
                } else {
                    Long tagId = customLabel3Intersection.stream().findFirst().orElse(-1L);
                    fbDataFeedDTO.setCustomLabel3(tagIdNameMap.get(tagId));
                }
            }
        }
        List<Long> customLabel4 = Optional.ofNullable(customLabelMap.get("custom_label_4")).orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(customLabel4)) {
            Collection<Long> customLabel4Intersection = CollectionUtils.intersection(customLabel4, goodsTagIds);
            if (CollectionUtils.isNotEmpty(customLabel4Intersection)) {
                if (customLabel4Intersection.size() > 1) {
                    FbErrorGoodsDTO fbErrorGoodsDTO = new FbErrorGoodsDTO();
                    fbErrorGoodsDTO.setId(goods.getId());
                    fbErrorGoodsDTO.setTitle(goods.getName());
                    fbErrorGoodsDTO.setCustomLabel("custom_label_4");
                    fbErrorGoodsDTO.setTagIds(StringUtils.join(customLabel4Intersection, ","));
                    errorGoodsList.add(fbErrorGoodsDTO);
                } else {
                    Long tagId = customLabel4Intersection.stream().findFirst().orElse(-1L);
                    fbDataFeedDTO.setCustomLabel4(tagIdNameMap.get(tagId));
                }
            }
        }
    }

    private Map<Long, List<Long>> getGoodsTagMap(Set<Long> tagIds, List<Long> goodsIds) {
        Map<Long, List<Long>> goodsTagMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(tagIds)) {
            mergeGoodsTagMap(goodsTagRelService.lambdaQuery()
                    .in(GoodsTagRel::getGoodsId, goodsIds)
                    .in(GoodsTagRel::getTagId, tagIds)
                    .list()
                    .stream()
                    .collect(Collectors.groupingBy(
                            GoodsTagRel::getGoodsId,
                            LinkedHashMap::new,
                            Collectors.mapping(GoodsTagRel::getTagId, Collectors.toList())
                    )), goodsTagMap);

            mergeGoodsTagMap(goodsExtConfigService.lambdaQuery()
                    .in(GoodsExtConfig::getGoodsId, goodsIds)
                    .in(GoodsExtConfig::getTagId, tagIds)
                    .eq(GoodsExtConfig::getIsDel, 0)
                    .list()
                    .stream()
                    .collect(Collectors.groupingBy(
                            GoodsExtConfig::getGoodsId,
                            LinkedHashMap::new,
                            Collectors.mapping(GoodsExtConfig::getTagId, Collectors.toList())
                    )), goodsTagMap);
            goodsTagMap.replaceAll((key, value) -> value.stream().distinct().collect(Collectors.toList()));
        }
        return goodsTagMap;
    }

    private void mergeGoodsTagMap(Map<Long, List<Long>> goodsTagMap, Map<Long, List<Long>> resultMap) {
        goodsTagMap.forEach((key, value) -> resultMap.merge(key, new ArrayList<>(value), (v1, v2) -> {
            v1.addAll(v2);
            return v1;
        }));
    }

    private static List<Long> parseTagIds(String tagIds) {
        if (StringUtils.isBlank(tagIds)) {
            return Lists.newArrayList();
        }
        List<Long> ids = new ArrayList<>();
        for (String id : tagIds.split(",")) {
            ids.add(Long.parseLong(id.trim()));
        }
        return ids;
    }

    public static String assignGender(String productType) {
        if (productType == null || productType.isEmpty()) {
            return "unisex";
        }
        // 转换为小写以忽略大小写
        String lowerCaseProductType = productType.toLowerCase();

        // 定义女性相关关键词
        String[] femaleKeywords = {"women", "woman", "mother", "jewelry", "kid", "baby", "child", "girl", "boy"};

        // 检查是否包含女性相关关键词
        for (String keyword : femaleKeywords) {
            if (lowerCaseProductType.contains(keyword)) {
                return "female";
            }
        }
        // 检查是否包含男性相关关键词
        if (lowerCaseProductType.contains("men's") || lowerCaseProductType.contains("man's")) {
            return "male";
        }
        // 其他情况设置为unisex
        return "unisex";
    }

    private List<FrontCategoryDTO> getFrontCategoryInfo(Long categoryId) {
        String redisKey = "AUTO_DELIVERY_FRONT_CATEGORY_" + categoryId;
        List<FrontCategoryDTO> frontCategoryInfos = new ArrayList<>();
        Object o = redisApi.get(redisKey);
        if (o == null) {
            FrontCategoryInfoVO frontCategoryInfoVO = new FrontCategoryInfoVO();
            frontCategoryInfoVO.setCategoryIds(Lists.newArrayList(categoryId));
            List<FrontCategoryDTO> frontCateList = frontCategoryCoreService.findFrontCategoryByCategoryIds(frontCategoryInfoVO);
            if (CollectionUtils.isNotEmpty(frontCateList)) {
                frontCategoryInfos = getFrontCategoryInfos(frontCateList);
            }
            redisApi.set(redisKey, frontCategoryInfos, 60L * 60 * 4 + RandomUtils.nextInt(1000, 10000));
        } else {
            frontCategoryInfos = (List<FrontCategoryDTO>) o;
        }
        return frontCategoryInfos;
    }


    private List<FrontCategoryDTO> getFrontCategoryInfos(List<FrontCategoryDTO> frontCategoryDtoList) {
//        List<Long> excludeCategoryIds = Lists.newArrayList(127L, 446L, 512L, 502L, 503L, 504L, 560L, 558L);

        List<List<FrontCategoryDTO>> firstFrontCategoryList = new ArrayList<>();
        Map<Long, List<FrontCategoryDTO>> categoryMap = frontCategoryDtoList.stream()
//                .filter(frontCategoryDTO -> !excludeCategoryIds.contains(frontCategoryDTO.getId()))
                .map(frontCategoryDTO -> {
                    if (frontCategoryDTO.getLevel() == 0) {
                        firstFrontCategoryList.add(Lists.newArrayList(frontCategoryDTO));
                    }
                    return frontCategoryDTO;
                }).collect(Collectors.groupingBy(FrontCategoryDTO::getParentId));

        if (CollectionUtils.isNotEmpty(firstFrontCategoryList)) {
            List<List<FrontCategoryDTO>> resList = new ArrayList<>();
            for (List<FrontCategoryDTO> firstFrontCategory : firstFrontCategoryList) {
                List<FrontCategoryDTO> res = new ArrayList<>();
                buildCategory(firstFrontCategory, categoryMap, res);
                if (CollectionUtils.isNotEmpty(res)) {
                    res.sort(Comparator.comparing(FrontCategoryDTO::getLevel));
                    resList.add(res);
                }
            }
            return resList.stream().filter(subList -> subList.size() == 2).findAny()
                    .orElse(resList.stream().filter(subList -> subList.size() == 1).findAny().orElse(Lists.newArrayList()));
        }
        return Lists.newArrayList();
    }

    private void buildCategory(List<FrontCategoryDTO> categoryList, Map<Long, List<FrontCategoryDTO>> categoryMap, List<FrontCategoryDTO> res) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(categoryList)) {
            return;
        }
        categoryList.stream().
                map(vo -> categoryMap.get(vo.getId()))
                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .forEach(list -> buildCategory(list, categoryMap, res));
        res.addAll(categoryList);
    }

    public static String validateAndFixImageUrl(String imageUrl) {
        if (StringUtils.isBlank(imageUrl)) {
            return "";
        }
        if (imageUrl.startsWith("http://http")) {
            return "https://" + imageUrl.substring(7);
        } else if (imageUrl.startsWith("https://https")) {
            return "https://" + imageUrl.substring(8);
        }
        return imageUrl;
    }

    public static String substringWithLength(String input, int length) {
        if (StringUtils.isBlank(input)) {
            return "";
        }
        if (length > input.length()) {
            return input;
        }
        return input.substring(0, length);
    }

    public static BigDecimal generateVirtualOriginalPrice(BigDecimal actualPrice) {
        if (actualPrice == null || actualPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        // 生成55到85之间的随机数
        Random random = new Random();
        // 0到30之间的随机数，加上55，范围就是55到85
        int randomPercentage = 55 + random.nextInt(31);
        // 计算虚拟原价
        BigDecimal discountFactor = new BigDecimal(randomPercentage).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        BigDecimal virtualOriginalPrice = actualPrice.divide(discountFactor, 2, RoundingMode.HALF_UP);
        return virtualOriginalPrice;
    }

    private Map<String, BigDecimal> getEsCountryPrice(QueryGoodsVO queryGoodsVO) {
        if (org.apache.commons.lang.StringUtils.isEmpty(queryGoodsVO.getCountry())) {
            return Maps.newHashMap();
        }

        List<String> goodsIdsStr = queryGoodsVO.getGoodsIds().stream().map(String::valueOf).collect(Collectors.toList());
        return goodsEsService.queryPriceByGoodsIdAndCountry(goodsIdsStr, queryGoodsVO.getCountry());
    }
}
