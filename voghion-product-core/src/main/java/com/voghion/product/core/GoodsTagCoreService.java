package com.voghion.product.core;

import com.colorlight.base.model.PageView;
import com.voghion.product.api.dto.GoodsTagDTO;
import com.voghion.product.api.dto.ShopTagDTO;
import com.voghion.product.api.input.ShopTagInput;
import com.voghion.product.model.po.GoodsTag;
import com.voghion.product.model.vo.GoodsTagVO;
import com.voghion.product.model.vo.condition.GoodsTagCondition;

import java.util.List;

/**
 * GoodsTagCoreService
 *
 * <AUTHOR>
 * @date 2022/12/9
 */
public interface GoodsTagCoreService {
    /**
     * 新增标签
     *
     * @param goodsTagVO 入参
     */
    void addGoodsTag(GoodsTagVO goodsTagVO);

    /**
     * 修改标签配置
     *
     * @param goodsTagVO 入参
     */
    void updateGoodsTag(GoodsTagVO goodsTagVO);


    /**
     * 修改标签活动信息
     *
     * @param goodsTag 入参
     */
    void updateGoodsTagActivityInfo(GoodsTag goodsTag);

    /**
     * 删除标签
     *
     * @param id 主键
     */
    void deleteGoodsTag(Long id);

    /**
     * 分页查询
     *
     * @param goodsTagCondition
     * @return
     */
    PageView<GoodsTagVO> queryPageByCondition(GoodsTagCondition goodsTagCondition);

    /**
     * 修改权重值
     *
     * @param goodsTagVO
     */
    void updateGoodsTagSort(GoodsTagVO goodsTagVO);

    /**
     * 修改展示隐藏
     *
     * @param goodsTagVO
     */
    void updateGoodsTagShow(GoodsTagVO goodsTagVO);

    /**
     * 根据标签id获取标签信息
     *
     * @param tagId
     * @return
     */
    GoodsTagVO getGoodsTagById(Long tagId);

    GoodsTagDTO getGoodsTagInfoById(Long tagId);

    List<GoodsTagDTO> getGoodsTagInfoByIds(List<Long> tagIds);

    List<GoodsTagDTO> queryGoodsTags();

    void addNewGoodsTag(GoodsTagVO goodsTagVO);

    void updateNewGoodsTag(GoodsTagVO goodsTagVO);

    PageView<GoodsTagVO> queryNewPageByCondition(GoodsTagCondition goodsTagCondition);

    List<GoodsTagVO> listAll(GoodsTagCondition goodsTagCondition);

    List<ShopTagDTO> queryShopTagsByInput(ShopTagInput shopTagInput);

}
