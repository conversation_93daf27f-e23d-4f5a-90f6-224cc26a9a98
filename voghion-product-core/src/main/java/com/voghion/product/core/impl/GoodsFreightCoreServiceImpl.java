package com.voghion.product.core.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.Result;
import com.colorlight.base.utils.CheckUtils;
import com.colorlight.base.utils.TransferUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.onlest.GoodsSyncModel;
import com.voghion.product.api.dto.CalculateWholesaleFreightDto;
import com.voghion.product.api.dto.CalculateWholesaleFreightResultDto;
import com.voghion.product.api.dto.CalculateWholesaleFreightSkuDto;
import com.voghion.product.core.GoodsExtConfigCoreService;
import com.voghion.product.core.GoodsFreightCoreService;
import com.voghion.product.model.bo.ItemGoodsInfoExtBo;
import com.voghion.product.model.dto.GoodsFreightDTO;
import com.voghion.product.model.dto.ItemGoodsInfoInputDto;
import com.voghion.product.model.dto.QueryFreight;
import com.voghion.product.model.enums.ProductResultCode;
import com.voghion.product.model.po.*;
import com.voghion.product.model.vo.GoodsFreightVO;
import com.voghion.product.model.vo.ProductInfoInput;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.util.GoodsExtDetailUtils;
import com.voghion.product.util.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoodsFreightCoreServiceImpl extends AbstractCommonServiceImpl implements GoodsFreightCoreService {
    @Resource
    private GoodsFreightService goodsFreightService;

    @Resource
    private GoodsLogisticsService goodsLogisticsService;

    @Resource
    private GoodsLogisticsValueService goodsLogisticsValueService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private CommissionAllocationService commissionAllocationService;

    @Resource
    private CountryFreightService countryFreightService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private GoodsExtDetailService goodsExtDetailService;

    @Resource
    private GoodsExtConfigService goodsExtConfigService;

    @Resource
    private RedisApi redisApi;

    @Resource
    private MqSender mqSender;

    @Resource
    private GoodsService goodsService;

    @Override
    public void initFreightForGoodsLevel(Long goodsId) {
        List<GoodsFreight> goodsFreights = goodsFreightService.queryByGoodsId(goodsId);
        log.info("initFreightForGoodsLevel goodsId:{}, size:{}", goodsId, goodsFreights.size());
        if (CollectionUtils.isEmpty(goodsFreights)) {
            return;
        }
        log.info("开始处理国家运费数据 goodsId:{}, freightSize:{}", goodsId, goodsFreights.size());
        List<GoodsFreight> saveList = new ArrayList<>();
        Map<String, List<GoodsFreight>> countryFreightMap = goodsFreights.stream().collect(Collectors.groupingBy(GoodsFreight::getCode));
        for (Map.Entry<String, List<GoodsFreight>> entry : countryFreightMap.entrySet()) {
            List<GoodsFreight> countryAllFreights = entry.getValue();
            GoodsFreight toSaveFreightData = new GoodsFreight();
            if (countryAllFreights.size() == 1) {
                GoodsFreight originFreight = countryAllFreights.get(0);
                if (originFreight.getSkuId() != null) {
                    originFreight.setIsDel(1);
                    originFreight.setUpdateTime(LocalDateTime.now());
                    saveList.add(originFreight);

                    BeanCopyUtil.copyProperties(originFreight, toSaveFreightData);
                    toSaveFreightData.setId(null);
                    toSaveFreightData.setSkuId(null);
                    toSaveFreightData.setIsDel(0);
                    toSaveFreightData.setCreateTime(LocalDateTime.now());
                    toSaveFreightData.setUpdateTime(LocalDateTime.now());
                    saveList.add(toSaveFreightData);
                }
            } else {
                if (countryAllFreights.stream().anyMatch(goodsFreight -> goodsFreight.getSkuId() == null)) {
                    countryAllFreights.stream().filter(goodsFreight -> goodsFreight.getSkuId() != null).forEach(goodsFreight -> {
                        goodsFreight.setUpdateTime(LocalDateTime.now());
                        goodsFreight.setIsDel(1);
                        saveList.add(goodsFreight);
                    });
                } else {
                    Long maxFreightDataId = countryAllFreights.stream().max(Comparator.comparing(GoodsFreight::getCurrentFreight)).map(GoodsFreight::getId).orElse(null);
                    if (maxFreightDataId == null) {
                        log.info("当前不存在该国家最大国家运费的数据 countryAllFreights:{}", JSON.toJSONString(countryAllFreights));
                        continue;
                    }
                    for (GoodsFreight originFreight : countryAllFreights) {
                        if (maxFreightDataId.equals(originFreight.getId())) {
                            BeanCopyUtil.copyProperties(originFreight, toSaveFreightData);
                            toSaveFreightData.setId(null);
                            toSaveFreightData.setSkuId(null);
                            toSaveFreightData.setCreateTime(LocalDateTime.now());
                            toSaveFreightData.setUpdateTime(LocalDateTime.now());
                            saveList.add(toSaveFreightData);
                        }
                        originFreight.setIsDel(1);
                        originFreight.setUpdateTime(LocalDateTime.now());
                        saveList.add(originFreight);

                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            log.info("saveList:{}", JSON.toJSONString(saveList));
            goodsFreightService.saveOrUpdateBatch(saveList);

        }

        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(goodsId);
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("处理国家运费(手动)");
        goodsSyncModel.setSourceService("vp");
        mqSender.send("SYNC_GOODS_TOPIC_BATCH", goodsSyncModel);

    }

    @Override
    public List<GoodsFreight> queryFreightByGoodsIdsAndCountry(List<Long> goodsIds, String country) {
        QueryWrapper<GoodsFreight> wrapper = new QueryWrapper<>();
        wrapper.in("goods_id", goodsIds);
        wrapper.eq("country", country);
        wrapper.eq("is_del", 0);
        wrapper.eq("gradient_num", 1);
        return goodsFreightService.list(wrapper);
    }

    @Override
    public List<GoodsFreight> queryFreightByGoodsIds(List<Long> goodsIds) {
        QueryWrapper<GoodsFreight> wrapper = new QueryWrapper<>();
        wrapper.in("goods_id", goodsIds);
        wrapper.eq("is_del", 0);
        wrapper.eq("gradient_num", 1);
        return goodsFreightService.list(wrapper);
    }

    @Override
    public List<GoodsFreightDTO> queryByGoodsId(GoodsFreightDTO goodsFreightDTO) {
        if (null == goodsFreightDTO) {
            return new ArrayList<>();
        }
        Long goodsId = goodsFreightDTO.getGoodsId();
        List<GoodsFreight> goodsFreights = goodsFreightService.queryByGoodsId(goodsId);
        if (CollectionUtils.isEmpty(goodsFreights)) {
            return new ArrayList<>();
        }
        return TransferUtils.transferList(goodsFreights, GoodsFreightDTO.class);
    }

    @Override
    public GoodsFreightDTO queryByGoodsId(Long goodsId, String countryName) {
        GoodsFreight goodsFreight = goodsFreightService.queryByGoodsId(goodsId, countryName);
        GoodsFreightDTO goodsFreightDTO = new GoodsFreightDTO();
        TransferUtils.transferBean(goodsFreight, goodsFreightDTO);
        return goodsFreightDTO;
    }

    @Override
    public Map<String, BigDecimal> countFreightByGoodsId(Long goodsId) {
        CheckUtils.notNull(goodsId, ProductResultCode.PARAMETER_ID_ERROR);

        Goods goods = goodsService.getById(goodsId);
        CheckUtils.notNull(goods, ProductResultCode.GOODS_NOT_EXIST);

        GoodsExtDetail goodsExtDetail = goodsExtDetailService.queryGoodsExtDetailByGoodsId(goodsId);
        CheckUtils.notNull(goodsExtDetail, ProductResultCode.GOODS_EXT_DETAIL_NOT_EXIST);

        QueryFreight queryFreight = new QueryFreight();
        queryFreight.setGoodsId(goodsId);
        queryFreight.setCountryCodes(Arrays.stream(goods.getCountry().split(",")).collect(Collectors.toList()));
        queryFreight.setLogisticsProperty(goods.getLogisticsProperty());
        queryFreight.setType(goods.getType());
        queryFreight.setShopId(goods.getShopId());
        queryFreight.setCategoryId(goods.getCategoryId());
        queryFreight.setWeight(GoodsExtDetailUtils.getWeightNumber(goodsExtDetail.getWeight()));
        return countFreight(queryFreight);
    }

    @Override
    public Map<String, BigDecimal> countFreight(QueryFreight queryFreight) {
        if (queryFreight.getGoodsId() != null) {
            Goods goods = goodsService.queryGoodsById(queryFreight.getGoodsId());
            if (queryFreight.getType() == null) {
                queryFreight.setType(goods.getType());
            }
        }

        log.info("计算国家运费1:{}", JSON.toJSONString(queryFreight));
        CheckUtils.check(Objects.isNull(queryFreight.getWeight()) || Double.parseDouble(queryFreight.getWeight()) < 0, ProductResultCode.WEIGHT_ERROR);
        CheckUtils.check(Objects.isNull(queryFreight.getCategoryId()), ProductResultCode.CATEGORY_ERROR);
        CheckUtils.check(Objects.isNull(queryFreight.getLogisticsProperty()), ProductResultCode.LOGISTICS_PROPERTY_NULL);

        //重量为0时，国家运费为0
        if (StringUtils.isBlank(queryFreight.getWeight()) || Double.parseDouble(queryFreight.getWeight()) == 0) {
            return Maps.newHashMap();
        }

        BigDecimal weight = new BigDecimal(queryFreight.getWeight().trim());
//        CheckUtils.check(weight.compareTo(new BigDecimal("2")) >= 0, ProductResultCode.WEIGHT_MUST_LESS_2KG);

        //如果超过1.9KG，必须是大件普货或者大件含电这两个物流属性
//        if (weight.compareTo(new BigDecimal("1.9")) > 0 && queryFreight.getLogisticsProperty() != null && !Lists.newArrayList(16, 17).contains(queryFreight.getLogisticsProperty())) {
//            LogUtils.error(log,"商品超过1.9KG，必须是大件普货或者大件含电这两个物流属性");
//            CheckUtils.check(true, ProductResultCode.OVER_WEIGHT_MUST_BE_LARGE_LOGISTICS);
//        }

        if (queryFreight.getShopId() == null) {
            Long shopId = getShopId();
            queryFreight.setShopId(shopId);
        }

        Category category = categoryService.selectById(queryFreight.getCategoryId());
        CheckUtils.notNull(category, ProductResultCode.CATEGORY_NOT_FIND);

        List<Long> categoryIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(category.getPids())) {
            Arrays.stream(category.getPids().split(",")).forEach(s -> categoryIds.add(Long.parseLong(s)));
        }
        categoryIds.add(queryFreight.getCategoryId());

        //物流场景 1通用 2尾货 3特货(40\45\50标签)
        int logisticsScene = queryFreight.getLogisticsScene();
        if (queryFreight.getGoodsId() != null) {
            List<Long> goodsExtConfigList = goodsExtConfigService.lambdaQuery()
                    .in(GoodsExtConfig::getGoodsId, Collections.singletonList(queryFreight.getGoodsId()))
                    .in(GoodsExtConfig::getTagId, Arrays.asList(40, 45, 50))
                    .eq(GoodsExtConfig::getIsDel, 0)
                    .list()
                    .stream().map(GoodsExtConfig::getGoodsId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(goodsExtConfigList)) {
                LogUtils.info(log, "计算国家运费 该商品:{}, 含40/45/50标签", queryFreight.getGoodsId());
                logisticsScene = 3;
                if (queryFreight.getLogisticsProperty() == 16 || queryFreight.getLogisticsProperty() == 17){
                    CheckUtils.check(true,ProductResultCode.LARGE_LOGISTICS_PROPERTY_ERROR);
                }
            }
        }
        log.info("计算国家运费 该商品:{} 物流场景logisticsScene:{}", queryFreight.getGoodsId(), logisticsScene);

        //校准物流属性与类目的匹配  就近原则
        List<GoodsLogistics> logisticsList = goodsLogisticsService.lambdaQuery()
                .in(GoodsLogistics::getCategoryId, categoryIds)
                .eq(GoodsLogistics::getStatus, 1)
                .list();
        GoodsLogistics goodsLogistics = logisticsList.stream().max(Comparator.comparing(GoodsLogistics::getCategoryId)).orElse(null);
        if (goodsLogistics != null) {
            List<GoodsLogisticsValue> goodsLogisticsValueList = goodsLogisticsValueService.lambdaQuery().eq(GoodsLogisticsValue::getGoodsLogisticsId, goodsLogistics.getId()).list();
            List<Integer> categoryLogisticsPropertyCodes = goodsLogisticsValueList.stream().map(GoodsLogisticsValue::getLogisticsPropertyCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(categoryLogisticsPropertyCodes)) {
                if (queryFreight.getLogisticsProperty() == null || !categoryLogisticsPropertyCodes.contains(queryFreight.getLogisticsProperty())) {
                    GoodsLogisticsValue defaultGoodsLogisticsValue = goodsLogisticsValueList.stream()
                            .filter(goodsLogisticsValue -> goodsLogisticsValue.getIsDefault() == 1)
                            .findAny()
                            .orElseGet(() -> goodsLogisticsValueList.get(0));
                    LogUtils.info(log, "计算国家运费 该商品:{} 当前类目配置的物流属性列表:{} ->不包含当前商品所选物流:{}, 自动替换->{}", queryFreight.getGoodsId(), categoryLogisticsPropertyCodes, queryFreight.getLogisticsProperty(), defaultGoodsLogisticsValue.getLogisticsPropertyCode());
                    queryFreight.setLogisticsProperty(defaultGoodsLogisticsValue.getLogisticsPropertyCode());
                }
            } else if (queryFreight.getLogisticsProperty() == null) {
                queryFreight.setLogisticsProperty(1);
                LogUtils.info(log, "计算国家运费 该商品:{} 无物流属性，默认普货", queryFreight.getGoodsId());
            }
        }


        //根据店铺和类目获取佣金比例 默认0.15
        BigDecimal rate = BigDecimal.ONE;
        if (queryFreight.isContainCommission()) {
            rate = new BigDecimal("0.85");
            List<CommissionAllocation> commissionAllocationList = null;
            if (queryFreight.getShopId() != null) {
                commissionAllocationList = commissionAllocationService.lambdaQuery()
                        .eq(CommissionAllocation::getShopId, queryFreight.getShopId())
                        .in(CommissionAllocation::getCategoryId, categoryIds)
                        .eq(CommissionAllocation::getIsOn, 1)
                        .list();
            }
            if (CollectionUtils.isEmpty(commissionAllocationList)) {
                commissionAllocationList = commissionAllocationService.lambdaQuery()
                        .in(CommissionAllocation::getCategoryId, categoryIds)
                        .isNull(CommissionAllocation::getShopId)
                        .eq(CommissionAllocation::getIsOn, 1)
                        .list();
            }
            if (CollectionUtils.isNotEmpty(commissionAllocationList)) {
                commissionAllocationList.sort((o1, o2) -> categoryIds.indexOf(o2.getCategoryId()) - categoryIds.indexOf(o1.getCategoryId()));
                if (null != commissionAllocationList.get(0).getPercent()) {
                    rate = BigDecimal.ONE.subtract(commissionAllocationList.get(0).getPercent().divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP));
                }
            }
        }

        log.info("计算国家运费 该商品:{} 佣金比例 rate:{}", queryFreight.getGoodsId(), rate);


        //批发商品
        if (queryFreight.getType() != null && queryFreight.getType() == 3) {
            log.info("计算国家运费，走批发商品运费逻辑:{}", queryFreight.getGoodsId());
            return countFreightGradient(queryFreight, logisticsScene, rate);
        }


        log.info("计算国家运费2:{}, logisticsScene:{}, rate:{}", JSON.toJSONString(queryFreight), logisticsScene, rate);
        Map<String, BigDecimal> map = new HashMap<>();
        List<CountryFreight> countryFreights = countryFreightService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(queryFreight.getCountryCodes()), CountryFreight::getReceiverCountry, queryFreight.getCountryCodes())
                .gt(CountryFreight::getMaxWeight, weight)
                .le(CountryFreight::getMinWeight, weight)
                .eq(CountryFreight::getLogisticsProperty, queryFreight.getLogisticsProperty())
                .eq(CountryFreight::getLogisticsScene, logisticsScene)
                .ge(CountryFreight::getFailureTime, LocalDateTime.now())
                .le(CountryFreight::getValidTime, LocalDateTime.now())
                .eq(CountryFreight::getVersion, 2)
                .eq(CountryFreight::getIsDelete, 0)
                .list();
        LogUtils.info(log, "计算国家运费 goodsId: {} : 命中价卡size:{}", queryFreight.getGoodsId(), countryFreights.size());

        if (CollectionUtils.isEmpty(countryFreights)) {
            countryFreights = countryFreightService.lambdaQuery()
                    .in(CollectionUtils.isNotEmpty(queryFreight.getCountryCodes()), CountryFreight::getReceiverCountry, queryFreight.getCountryCodes())
                    .gt(CountryFreight::getMaxWeight, weight)
                    .le(CountryFreight::getMinWeight, weight)
                    .eq(CountryFreight::getLogisticsProperty, 1)
                    .eq(CountryFreight::getLogisticsScene, logisticsScene)
                    .ge(CountryFreight::getFailureTime, LocalDateTime.now())
                    .le(CountryFreight::getValidTime, LocalDateTime.now())
                    .eq(CountryFreight::getVersion, 2)
                    .eq(CountryFreight::getIsDelete, 0)
                    .list();
            LogUtils.info(log, "计算国家运费 goodsId: {} : 重新命中价卡size:{}", queryFreight.getGoodsId(), countryFreights.size());
        }

        if (CollectionUtils.isNotEmpty(countryFreights)) {
            for (CountryFreight countryFreight : countryFreights) {
                BigDecimal fee = countryFreight.getBaseAmount()
                        .add(countryFreight.getWeightParam().multiply(weight))
                        .divide(rate, 2, RoundingMode.HALF_UP);
                map.put(countryFreight.getReceiverCountry(), fee);
            }
            LogUtils.info(log, "计算国家运费 over 该商品:{} ---> result:{}", queryFreight.getGoodsId(), map);
            return map;
        }


        if (weight.compareTo(new BigDecimal("1.9")) < 0) {
            LogUtils.info(log, "计算国家运费 重量<=1.9kg, 未命中物流价卡:{}", queryFreight.getGoodsId());
            CheckUtils.check(true, ProductResultCode.COUNTRY_FREIGHT_NOT_HIT);
        }

        //重量>2kg时
        LogUtils.info(log, "计算国家运费 未命中价卡，走重量校验及兼容:{}", queryFreight.getGoodsId());
        countryFreights = countryFreightService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(queryFreight.getCountryCodes()), CountryFreight::getReceiverCountry, queryFreight.getCountryCodes())
                .eq(CountryFreight::getLogisticsProperty, queryFreight.getLogisticsProperty())
                .eq(CountryFreight::getLogisticsScene, logisticsScene)
                .ge(CountryFreight::getFailureTime, LocalDateTime.now())
                .le(CountryFreight::getValidTime, LocalDateTime.now())
                .eq(CountryFreight::getVersion, 2)
                .eq(CountryFreight::getIsDelete, 0)
                .list();
        if (CollectionUtils.isEmpty(countryFreights)) {
            countryFreights = countryFreightService.lambdaQuery()
                    .in(CollectionUtils.isNotEmpty(queryFreight.getCountryCodes()), CountryFreight::getReceiverCountry, queryFreight.getCountryCodes())
                    .eq(CountryFreight::getLogisticsProperty, 1)
                    .eq(CountryFreight::getLogisticsScene, logisticsScene)
                    .ge(CountryFreight::getFailureTime, LocalDateTime.now())
                    .le(CountryFreight::getValidTime, LocalDateTime.now())
                    .eq(CountryFreight::getVersion, 2)
                    .eq(CountryFreight::getIsDelete, 0)
                    .list();
        }

        CheckUtils.isEmpty(countryFreights, ProductResultCode.COUNTRY_FREIGHT_NOT_HIT);
        for (Map.Entry<String, List<CountryFreight>> entry : countryFreights.stream().collect(Collectors.groupingBy(CountryFreight::getReceiverCountry)).entrySet()) {
            String country = entry.getKey();
            List<CountryFreight> sameCountryFreight = entry.getValue();
            CountryFreight countryFreight = sameCountryFreight.stream().max(Comparator.comparing(CountryFreight::getMaxWeight)).orElse(null);

            assert countryFreight != null;
            BigDecimal fee = countryFreight.getBaseAmount()
                    .add(countryFreight.getWeightParam().multiply(countryFreight.getMaxWeight()))
                    .multiply(weight)
                    .divide(countryFreight.getMaxWeight().multiply(rate), 2, RoundingMode.HALF_UP);
            map.put(country, fee);
        }
        LogUtils.info(log, "计算国家运费 over2 该商品:{} ---> result:{}", queryFreight.getGoodsId(), map);
        return map;
    }


    @Override
    public Map<String, BigDecimal> countFreightGradient(QueryFreight queryFreight, Integer logisticsScene, BigDecimal rate) {
        log.info("计算批发商品国家运费1:{}, logisticsScene:{}, rate:{}", JSON.toJSONString(queryFreight), logisticsScene, rate);
        BigDecimal weight = new BigDecimal(queryFreight.getWeight());
        CheckUtils.check(weight.compareTo(new BigDecimal("0.95")) >= 0, ProductResultCode.GOODS_FREIGHT_GRADIENT_WEIGHT_EXCEED);

        BigDecimal two = new BigDecimal("2");
        BigDecimal three = new BigDecimal("3");

        List<CountryFreight> allCountryFreights = countryFreightService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(queryFreight.getCountryCodes()), CountryFreight::getReceiverCountry, queryFreight.getCountryCodes())
                .ge(CountryFreight::getMaxWeight, weight)
                .eq(CountryFreight::getLogisticsProperty, queryFreight.getLogisticsProperty())
                .eq(CountryFreight::getLogisticsScene, logisticsScene)
                .ge(CountryFreight::getFailureTime, LocalDateTime.now())
                .le(CountryFreight::getValidTime, LocalDateTime.now())
                .eq(CountryFreight::getVersion, 2)
                .eq(CountryFreight::getIsDelete, 0)
                .list();

        Map<String, List<CountryFreight>> countryGroupFreight = allCountryFreights.stream().collect(Collectors.groupingBy(CountryFreight::getReceiverCountry));

        Map<String, BigDecimal> countryFreightMap = new HashMap<>();
        Map<String, List<MutableTriple<BigDecimal, Integer, Integer>>> tripleMap = Maps.newHashMap();

        for (Map.Entry<String, List<CountryFreight>> entry : countryGroupFreight.entrySet()) {
            String country = entry.getKey();
            List<CountryFreight> countryFreightList = entry.getValue();
            countryFreightList.forEach(countryFreight -> {
                if (countryFreight.getMaxWeight().compareTo(new BigDecimal("1.9")) > 0) {
                    countryFreight.setMaxWeight(new BigDecimal("1.9"));
                }
            });
            countryFreightList.sort(Comparator.comparing(CountryFreight::getMaxWeight));

            CountryFreight countryFreight1 = countryFreightList.get(0);
            CountryFreight countryFreight2 = countryFreightList.size() > 1 ? countryFreightList.get(1) : null;

            MutableTriple<BigDecimal, Integer, Integer> triple1;
            MutableTriple<BigDecimal, Integer, Integer> triple2;
            MutableTriple<BigDecimal, Integer, Integer> triple3;
            MutableTriple<BigDecimal, Integer, Integer> triple4;

            List<MutableTriple<BigDecimal, Integer, Integer>> tripleList = null;

            BigDecimal maxNum1;
            BigDecimal maxNum2;
            BigDecimal maxNum3;

            BigDecimal fee1 = countryFreight1.getBaseAmount().add(countryFreight1.getWeightParam().multiply(weight));

            //目前最多只有2档
            switch (countryFreightList.size()) {
                case 1:
                    log.info("计算批发商品国家运费 命中物流价卡数量 = 1");
                    maxNum1 = countryFreight1.getMaxWeight().divide(weight, 4, RoundingMode.HALF_UP);
                    maxNum1 = maxNum1.compareTo(maxNum1.setScale(0, RoundingMode.DOWN)) == 0 ? maxNum1.subtract(BigDecimal.ONE) : maxNum1.setScale(0, RoundingMode.DOWN);

                    BigDecimal fee1_1 = fee1.multiply(new BigDecimal("1")).divide(rate, 2, RoundingMode.HALF_UP);
                    BigDecimal fee1_2;
                    BigDecimal fee1_3;
                    BigDecimal fee1_4;

                    if (maxNum1.compareTo(three) > 0) {
                        log.info("计算批发商品国家运费 第一档最大数量>3 ===> 第一档、第二档各拆成2档 第二档拆分的系数 1.2、1.1");
                        maxNum2 = maxNum1;
                        maxNum1 = three;
                        maxNum3 = maxNum2.add(BigDecimal.ONE).multiply(two);
                        LogUtils.info(log, "1------num1:{}, num2:{}, num3:{}", maxNum1, maxNum2, maxNum3);

                        fee1_2 = countryFreight1.getBaseAmount()
                                .add(countryFreight1.getWeightParam().multiply(weight.multiply(maxNum1.add(BigDecimal.ONE))))
                                .divide(maxNum1.add(BigDecimal.ONE), 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("0.95"))
                                .divide(rate, 2, RoundingMode.HALF_UP);

                        BigDecimal fee2 = countryFreight1.getBaseAmount()
                                .add(countryFreight1.getWeightParam().multiply(weight.multiply(maxNum2)))
                                .add(countryFreight1.getBaseAmount())
                                .add(countryFreight1.getWeightParam().multiply(weight))
                                .divide(maxNum2.add(BigDecimal.ONE), 4, RoundingMode.HALF_UP);

                        fee1_3 = fee2.multiply(new BigDecimal("0.9")).divide(rate, 2, RoundingMode.HALF_UP);
                        fee1_4 = fee2.multiply(new BigDecimal("0.85")).divide(rate, 2, RoundingMode.HALF_UP);

                    } else {
                        log.info("计算批发商品国家运费 第一档最大数量<=3 ===> 第二档拆成3档 系数1.2、1.1、1.0");
                        maxNum2 = maxNum1.add(BigDecimal.ONE).multiply(two);
                        maxNum3 = maxNum2.add(BigDecimal.ONE).multiply(two);
                        LogUtils.info(log, "2------num1:{}, num2:{}, num3:{}", maxNum1, maxNum2, maxNum3);

                        BigDecimal fee2 = countryFreight1.getBaseAmount()
                                .add(countryFreight1.getWeightParam().multiply(weight.multiply(maxNum1)))
                                .add(countryFreight1.getBaseAmount())
                                .add(countryFreight1.getWeightParam().multiply(weight))
                                .divide(maxNum1.add(BigDecimal.ONE), 4, RoundingMode.HALF_UP);

                        fee1_2 = fee2.multiply(new BigDecimal("0.95")).divide(rate, 2, RoundingMode.HALF_UP);
                        fee1_3 = fee2.multiply(new BigDecimal("0.9")).divide(rate, 2, RoundingMode.HALF_UP);
                        fee1_4 = fee2.multiply(new BigDecimal("0.85")).divide(rate, 2, RoundingMode.HALF_UP);
                    }

                    triple1 = new MutableTriple<>(fee1_1, 1, maxNum1.intValue());
                    triple2 = new MutableTriple<>(fee1_2, maxNum1.intValue() + 1, maxNum2.intValue());
                    triple3 = new MutableTriple<>(fee1_3, maxNum2.intValue() + 1, maxNum3.intValue());
                    triple4 = new MutableTriple<>(fee1_4, maxNum3.intValue() + 1, 999999);
                    tripleList = Lists.newArrayList(triple1, triple2, triple3, triple4);

                    break;

                case 2:
                    log.info("计算批发商品国家运费 命中物流价卡数量 = 2");
                    assert countryFreight2 != null;
                    maxNum1 = countryFreight1.getMaxWeight().divide(weight, 4, RoundingMode.HALF_UP);
                    maxNum1 = maxNum1.compareTo(maxNum1.setScale(0, RoundingMode.DOWN)) == 0 ? maxNum1.subtract(BigDecimal.ONE) : maxNum1.setScale(0, RoundingMode.DOWN);

                    BigDecimal fee2_1 = fee1.multiply(new BigDecimal("1")).divide(rate, 2, RoundingMode.HALF_UP);
                    BigDecimal fee2_2;
                    BigDecimal fee2_3;
                    BigDecimal fee2_4;

                    if (maxNum1.compareTo(three) > 0) {
                        log.info("计算批发商品国家运费 第一档最大数量>3 ===> 拆第一档");
                        maxNum2 = maxNum1;
                        maxNum1 = three;

                        maxNum3 = countryFreight2.getMaxWeight().divide(weight, 4, RoundingMode.HALF_UP);
                        maxNum3 = maxNum3.compareTo(maxNum3.setScale(0, RoundingMode.DOWN)) == 0 ? maxNum3.subtract(BigDecimal.ONE) : maxNum3.setScale(0, RoundingMode.DOWN);
                        LogUtils.info(log, "3------num1:{}, num2:{}, num3:{}", maxNum1, maxNum2, maxNum3);
                    } else {
                        log.info("计算批发商品国家运费 第一档最大数量<=3 ===> 拆第二档");
                        maxNum3 = countryFreight2.getMaxWeight().divide(weight, 4, RoundingMode.HALF_UP);
                        maxNum3 = maxNum3.compareTo(maxNum3.setScale(0, RoundingMode.DOWN)) == 0 ? maxNum3.subtract(BigDecimal.ONE) : maxNum3.setScale(0, RoundingMode.DOWN);
                        //劈在1/10
                        maxNum2 = maxNum1.add(maxNum3.subtract(maxNum1.add(BigDecimal.ONE)).divide(BigDecimal.TEN, 0, RoundingMode.UP));
                        LogUtils.info(log, "4------num1:{}, num2:{}, num3:{}", maxNum1, maxNum2, maxNum3);
                    }

                    fee2_2 = countryFreight2.getBaseAmount()
                            .add(countryFreight2.getWeightParam().multiply(weight.multiply(maxNum1.add(BigDecimal.ONE))))
                            .divide(maxNum1.add(BigDecimal.ONE), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("0.95"))
                            .divide(rate, 2, RoundingMode.HALF_UP);

                    fee2_3 = countryFreight2.getBaseAmount()
                            .add(countryFreight2.getWeightParam().multiply(weight.multiply(maxNum2.add(BigDecimal.ONE))))
                            .divide(maxNum2.add(BigDecimal.ONE), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("0.9"))
                            .divide(rate, 2, RoundingMode.HALF_UP);

                    fee2_4 = countryFreight2.getBaseAmount()
                            .add(countryFreight2.getWeightParam().multiply(weight.multiply(maxNum3)))
                            .add(countryFreight2.getBaseAmount())
                            .add(countryFreight2.getWeightParam().multiply(weight))
                            .divide(maxNum3.add(BigDecimal.ONE), 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("0.85"))
                            .divide(rate, 2, RoundingMode.HALF_UP);


                    triple1 = new MutableTriple<>(fee2_1, 1, maxNum1.intValue());
                    triple2 = new MutableTriple<>(fee2_2, maxNum1.intValue() + 1, maxNum2.intValue());
                    triple3 = new MutableTriple<>(fee2_3, maxNum2.intValue() + 1, maxNum3.intValue());
                    triple4 = new MutableTriple<>(fee2_4, maxNum3.intValue() + 1, 999999);
                    tripleList = Lists.newArrayList(triple1, triple2, triple3, triple4);

                    break;

                default:
                    break;
            }
            if (CollectionUtils.isNotEmpty(tripleList)) {
                countryFreightMap.put(country, tripleList.get(0).getLeft());
                tripleMap.put(country, tripleList);
            }
        }

        queryFreight.setFreightGradientDto(tripleMap);
        LogUtils.info(log, "计算批发商品国家运费over，该商品goodsId: {}, queryFreight: {}, countryFreightMap: {}", queryFreight.getGoodsId(), queryFreight, countryFreightMap);
        return countryFreightMap;
    }

    @Override
    public List<GoodsFreight> countAndInitFreight(Goods goods, String weight, List<Long> tagIdList) {
        QueryFreight queryFreight = new QueryFreight();
        queryFreight.setWeight(GoodsExtDetailUtils.getWeightNumber(weight));
        queryFreight.setCategoryId(goods.getCategoryId());
        queryFreight.setLogisticsProperty(goods.getLogisticsProperty());
        queryFreight.setShopId(goods.getShopId());
        queryFreight.setType(goods.getType());
        queryFreight.setCountryCodes(Lists.newArrayList(goods.getCountry().split(",")));
        queryFreight.setLogisticsScene(CollectionUtils.isNotEmpty(tagIdList) && (tagIdList.contains(40L) || tagIdList.contains(45L) || tagIdList.contains(50L)) ? 3 : 1);
        Map<String, BigDecimal> freightMap = countFreight(queryFreight);

        return freightMap.entrySet().stream().map(entry -> {
            GoodsFreight goodsFreight = new GoodsFreight();
            goodsFreight.setGoodsId(goods.getId());
            goodsFreight.setPrice(BigDecimal.ZERO);
            goodsFreight.setCode(entry.getKey());
            goodsFreight.setCurrentFreight(entry.getValue());
            goodsFreight.setCreateTime(LocalDateTime.now());
            goodsFreight.setUpdateTime(LocalDateTime.now());
            goodsFreight.setIsDel(0);
            goodsFreight.setGradientNum(1);
            goodsFreight.setMinNum(1);
            goodsFreight.setMaxNum(999999);
            return goodsFreight;
        }).collect(Collectors.toList());
    }

    @Override
    public Result<BigDecimal> calculateWholesaleFreightAmount(List<CalculateWholesaleFreightDto> dtoList) {
        LogUtils.info(log, "计算批发商品总运费 param: {}", dtoList);
        CheckUtils.isEmpty(dtoList, ProductResultCode.PARAMETER_ERROR);
        dtoList.forEach(dto -> CheckUtils.check(dto.getWeight() == null || dto.getWeight().compareTo(BigDecimal.ZERO) < 0
                || StringUtils.isBlank(dto.getCountry()) || dto.getLogisticsProperty() == null || dto.getLogisticsScene() == null, ProductResultCode.PARAMETER_ERROR));


        BigDecimal totalFreightAmount = BigDecimal.ZERO;
        for (CalculateWholesaleFreightDto dto : dtoList) {
            BigDecimal weight0 =  dto.getWeight().compareTo(new BigDecimal("1.9")) > 0 ? new BigDecimal("1.9") : dto.getWeight();
            List<CountryFreight> countryFreights = countryFreightService.lambdaQuery()
                    .eq(CountryFreight::getReceiverCountry, dto.getCountry())
                    .ge(CountryFreight::getMaxWeight, weight0)
                    .eq(CountryFreight::getLogisticsProperty, dto.getLogisticsProperty())
                    .eq(CountryFreight::getLogisticsScene, dto.getLogisticsScene())
                    .ge(CountryFreight::getFailureTime, LocalDateTime.now())
                    .le(CountryFreight::getValidTime, LocalDateTime.now())
                    .eq(CountryFreight::getVersion, 2)
                    .eq(CountryFreight::getIsDelete, 0)
                    .list();

            CheckUtils.isEmpty(countryFreights, ProductResultCode.COUNTRY_FREIGHT_NOT_HIT);
            CountryFreight countryFreight = countryFreights.get(countryFreights.size() - 1);

            BigDecimal count = dto.getWeight().divide(countryFreight.getMaxWeight(), 0, RoundingMode.UP);
            totalFreightAmount = totalFreightAmount.add(countryFreight.getBaseAmount().multiply(count).add(dto.getWeight().multiply(countryFreight.getWeightParam())));
            LogUtils.info(log, "计算批发商品总运费 totalFreightAmount:{}, dto: {}", totalFreightAmount, dto);
        }

        totalFreightAmount = totalFreightAmount.setScale(2, RoundingMode.UP);
        LogUtils.info(log, "计算批发商品总运费 计算结果: {}", totalFreightAmount);
        return Result.success(totalFreightAmount);
    }

    @Override
    public Result<CalculateWholesaleFreightResultDto> calculateWholesaleFreightInfo(List<CalculateWholesaleFreightSkuDto> dtoList) {
        LogUtils.info(log, "计算批发商品运费信息 param: {}", dtoList);
        CheckUtils.isEmpty(dtoList, ProductResultCode.PARAMETER_ERROR);
        dtoList.forEach(dto -> CheckUtils.check(dto.getSkuId() == null || dto.getGoodsId() == null || dto.getCount() == null || StringUtils.isBlank(dto.getCountry()), ProductResultCode.PARAMETER_ERROR));
        String country = dtoList.stream().map(CalculateWholesaleFreightSkuDto::getCountry).findFirst().orElse(null);

        List<Long> goodsIds = dtoList.stream().map(CalculateWholesaleFreightSkuDto::getGoodsId).distinct().collect(Collectors.toList());
        List<GoodsExtDetail> goodsExtDetailList = goodsExtDetailService.queryGoodsExtDetailByGoodsIds(goodsIds);
        Map<Long, BigDecimal> goodsWeightMap = goodsExtDetailList.stream().collect(Collectors.toMap(GoodsExtDetail::getGoodsId, goodsExtDetail -> new BigDecimal(GoodsExtDetailUtils.getWeightNumber(goodsExtDetail.getWeight())), (v1, v2) -> v1));

        List<Goods> goodsList = goodsService.queryGoodsByIds(goodsIds);
        Map<Integer, List<Goods>> logisticsPropertyGoodsIdsGroupMap = goodsList.stream().collect(Collectors.groupingBy(Goods::getLogisticsProperty));

        Map<Long, Integer> goodsCountMap = dtoList.stream().collect(Collectors.toMap(CalculateWholesaleFreightSkuDto::getGoodsId, CalculateWholesaleFreightSkuDto::getCount, (v1, v2) -> v1 + v2));
        Map<Long, List<CalculateWholesaleFreightSkuDto>> skuInfoGroupMap = dtoList.stream().collect(Collectors.groupingBy(CalculateWholesaleFreightSkuDto::getGoodsId));





        List<Long> logisticsScene3GoodsIds = goodsExtConfigService.lambdaQuery()
                .in(GoodsExtConfig::getGoodsId, goodsIds)
                .in(GoodsExtConfig::getTagId, Arrays.asList(40, 45, 50))
                .eq(GoodsExtConfig::getIsDel, 0)
                .list()
                .stream().map(GoodsExtConfig::getGoodsId).distinct().collect(Collectors.toList());
        LogUtils.info(log, "计算批发商品运费信息==>区分物流场景 商品:{}, 负向商品:{}", goodsIds, logisticsScene3GoodsIds);

        //校准物流属性与类目的匹配  就近原则
//        List<GoodsLogistics> logisticsList = goodsLogisticsService.lambdaQuery()
//                .in(GoodsLogistics::getCategoryId, categoryIds)
//                .eq(GoodsLogistics::getStatus, 1)
//                .list();
//        GoodsLogistics goodsLogistics = logisticsList.stream().max(Comparator.comparing(GoodsLogistics::getCategoryId)).orElse(null);
//        if (goodsLogistics != null) {
//            List<GoodsLogisticsValue> goodsLogisticsValueList = goodsLogisticsValueService.lambdaQuery().eq(GoodsLogisticsValue::getGoodsLogisticsId, goodsLogistics.getId()).list();
//            List<Integer> categoryLogisticsPropertyCodes = goodsLogisticsValueList.stream().map(GoodsLogisticsValue::getLogisticsPropertyCode).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(categoryLogisticsPropertyCodes)) {
//                if (queryFreight.getLogisticsProperty() == null || !categoryLogisticsPropertyCodes.contains(queryFreight.getLogisticsProperty())) {
//                    GoodsLogisticsValue defaultGoodsLogisticsValue = goodsLogisticsValueList.stream()
//                            .filter(goodsLogisticsValue -> goodsLogisticsValue.getIsDefault() == 1)
//                            .findAny()
//                            .orElseGet(() -> goodsLogisticsValueList.get(0));
//                    LogUtils.info(log, "计算国家运费 该商品:{} 当前类目配置的物流属性列表:{} ->不包含当前商品所选物流:{}, 自动替换->{}", queryFreight.getGoodsId(), categoryLogisticsPropertyCodes, queryFreight.getLogisticsProperty(), defaultGoodsLogisticsValue.getLogisticsPropertyCode());
//                    queryFreight.setLogisticsProperty(defaultGoodsLogisticsValue.getLogisticsPropertyCode());
//                }
//            } else if (queryFreight.getLogisticsProperty() == null) {
//                queryFreight.setLogisticsProperty(1);
//                LogUtils.info(log, "计算国家运费 该商品:{} 无物流属性，默认普货", queryFreight.getGoodsId());
//            }
//        }

        CalculateWholesaleFreightResultDto result = new CalculateWholesaleFreightResultDto();
        Map<Long, BigDecimal> skuAmountMap = Maps.newHashMap();
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (Map.Entry<Integer, List<Goods>> entry : logisticsPropertyGoodsIdsGroupMap.entrySet()) {
            Integer logisticsProperty = entry.getKey();
            List<Long> batchGoodsIds = entry.getValue().stream().map(Goods::getId).collect(Collectors.toList());
            LogUtils.info(log, "计算批发商品运费信息 物流属性:{}, batchGoodsIds:{}", logisticsProperty, batchGoodsIds);

            Map<Integer, List<Long>> logisticsSceneGroupMap = Maps.newHashMap();
            List<Long> logisticsScene1BatchGoodsIds = batchGoodsIds.stream().filter(goodsId -> !logisticsScene3GoodsIds.contains(goodsId)).collect(Collectors.toList());
            List<Long> logisticsScene3BatchGoodsIds = batchGoodsIds.stream().filter(logisticsScene3GoodsIds::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(logisticsScene1BatchGoodsIds)) {
                logisticsSceneGroupMap.put(1, logisticsScene1BatchGoodsIds);
            }
            if (CollectionUtils.isNotEmpty(logisticsScene3BatchGoodsIds)) {
                logisticsSceneGroupMap.put(3, logisticsScene3BatchGoodsIds);
            }

            for (Map.Entry<Integer, List<Long>> listEntry : logisticsSceneGroupMap.entrySet()) {
                Integer logisticsScene = listEntry.getKey();
                List<Long> batchGoodsIds2 = listEntry.getValue();

                BigDecimal totalWeight = BigDecimal.ZERO;
                Map<Long, BigDecimal> goodsTotalWeightMap = Maps.newHashMap();

                for (Long batchGoodsId : batchGoodsIds2) {
                    Integer count = goodsCountMap.get(batchGoodsId);
                    BigDecimal weight = goodsWeightMap.get(batchGoodsId);
                    BigDecimal goodsTotalWeight = weight.multiply(new BigDecimal(count));
                    totalWeight = totalWeight.add(goodsTotalWeight);

                    goodsTotalWeightMap.put(batchGoodsId, goodsTotalWeight);
                }

                BigDecimal weight =  totalWeight.compareTo(new BigDecimal("1.9")) > 0 ? new BigDecimal("1.9") : totalWeight;
                List<CountryFreight> countryFreights = countryFreightService.lambdaQuery()
                        .eq(CountryFreight::getReceiverCountry, country)
                        .ge(CountryFreight::getMaxWeight, weight)
                        .eq(CountryFreight::getLogisticsProperty, logisticsProperty)
                        .eq(CountryFreight::getLogisticsScene, logisticsScene)
                        .ge(CountryFreight::getFailureTime, LocalDateTime.now())
                        .le(CountryFreight::getValidTime, LocalDateTime.now())
                        .eq(CountryFreight::getVersion, 2)
                        .eq(CountryFreight::getIsDelete, 0)
                        .list();

                CheckUtils.isEmpty(countryFreights, ProductResultCode.COUNTRY_FREIGHT_NOT_HIT);
                CountryFreight countryFreight = countryFreights.get(countryFreights.size() - 1);

                BigDecimal count = totalWeight.divide(countryFreight.getMaxWeight(), 0, RoundingMode.UP);
                BigDecimal totalFreightAmount = countryFreight.getBaseAmount().multiply(count).add(totalWeight.multiply(countryFreight.getWeightParam()));
                LogUtils.info(log, "计算批发商品运费信息 logisticsProperty:{}, batchGoodsIds:{}, totalWeight:{}, totalFreightAmount:{}", logisticsProperty, batchGoodsIds2, totalWeight, totalFreightAmount);


                for (Long batchGoodsId : batchGoodsIds2) {
                    BigDecimal goodsTotalWeight = goodsTotalWeightMap.get(batchGoodsId);
                    Integer goodsCount = goodsCountMap.get(batchGoodsId);

                    List<CalculateWholesaleFreightSkuDto> skuDtoList = skuInfoGroupMap.get(batchGoodsId);
                    for (CalculateWholesaleFreightSkuDto dto : skuDtoList) {
                        BigDecimal skuFreightAmount = new BigDecimal(dto.getCount()).divide(new BigDecimal(goodsCount), 6, RoundingMode.HALF_UP)
                                .multiply(goodsTotalWeight.divide(totalWeight, 6, RoundingMode.HALF_UP).multiply(totalFreightAmount))
                                .setScale(2, RoundingMode.HALF_UP);
                        skuAmountMap.put(dto.getSkuId(), skuFreightAmount);
                        totalAmount = totalAmount.add(skuFreightAmount);
                    }
                }
            }
        }

        result.setSkuAmountMap(skuAmountMap);
        result.setTotalAmount(totalAmount);
        LogUtils.info(log, "计算批发商品运费信息 result:{}", result);
        return Result.success(result);
    }

    @Override
    public BigDecimal countOldDefaultFreight(QueryFreight queryFreight) {
        log.info("根据旧价卡获取旧逻辑的默认运费 param1:{}", JSON.toJSONString(queryFreight));
        CheckUtils.check(Objects.isNull(queryFreight.getWeight()), ProductResultCode.WEIGHT_ERROR);
        CheckUtils.check(Objects.isNull(queryFreight.getCategoryId()), ProductResultCode.CATEGORY_ERROR);
        CheckUtils.check(Objects.isNull(queryFreight.getLogisticsProperty()), ProductResultCode.LOGISTICS_PROPERTY_NULL);

        //重量为0时，国家运费为0
        if (StringUtils.isBlank(queryFreight.getWeight()) || Double.parseDouble(queryFreight.getWeight()) == 0) {
            return BigDecimal.ZERO;
        }

        if (queryFreight.getShopId() == null) {
            Long shopId = getShopId();
            queryFreight.setShopId(shopId);
        }

        Category category = categoryService.selectById(queryFreight.getCategoryId());
        CheckUtils.notNull(category, ProductResultCode.CATEGORY_NOT_FIND);

        List<Long> categoryIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(category.getPids())) {
            Arrays.stream(category.getPids().split(",")).forEach(s -> categoryIds.add(Long.parseLong(s)));
        }
        categoryIds.add(queryFreight.getCategoryId());


        //物流属性3,5 -> 香水
        //物流属性2,4 -> 特货
        //物流属性普货和不确定，则查询所属类目的物流配置
        if (queryFreight.getLogisticsProperty() == 3 || queryFreight.getLogisticsProperty() == 5) {
            queryFreight.setGoodsType(2);
        }else if (queryFreight.getLogisticsProperty() == 2 || queryFreight.getLogisticsProperty() == 4) {
            queryFreight.setGoodsType(1);
        } else {
            List<GoodsLogistics> logisticsList = goodsLogisticsService.lambdaQuery().in(GoodsLogistics::getCategoryId, categoryIds).list();
            if (CollectionUtils.isNotEmpty(logisticsList)) {
                logisticsList.sort((o1, o2) -> categoryIds.indexOf(o2.getCategoryId()) - categoryIds.indexOf(o1.getCategoryId()));
                GoodsLogistics goodsLogistics = logisticsList.get(0);

                List<GoodsLogisticsValue> list = goodsLogisticsValueService.lambdaQuery().eq(GoodsLogisticsValue::getGoodsLogisticsId, goodsLogistics).list();
                if (CollectionUtils.isNotEmpty(list)) {
                    GoodsLogisticsValue goodsLogisticsValue = list.stream().min((o1, o2) -> {
                        int sort = o2.getIsDefault() - o1.getIsDefault();
                        if (sort == 0) {
                            sort = o1.getLogisticsPropertyCode() - o2.getLogisticsPropertyCode();
                        }
                        return sort;
                    }).orElse(null);

                    if (goodsLogisticsValue != null && goodsLogisticsValue.getLogisticsPropertyCode() != 1 && goodsLogisticsValue.getLogisticsPropertyCode() != 6) {
                        LogUtils.info(log,"类目所属物流标签为特货 category:{}, logisticsProperty:{}",goodsLogistics.getCategoryId(),goodsLogistics.getLogisticsProperty());
                        queryFreight.setGoodsType(1);
                    }
                }
            }
        }

        if (queryFreight.getGoodsType() == null) {
            queryFreight.setGoodsType(0);
        }

        log.info("根据旧价卡获取旧逻辑的默认运费 param2:{}", JSON.toJSONString(queryFreight));

        List<CountryFreight> countryFreightList = Lists.newArrayList();
        countryFreightList.add(new CountryFreight(1, new BigDecimal(0), new BigDecimal(0.03), new BigDecimal(1.03), new BigDecimal(0)));
        countryFreightList.add(new CountryFreight(1, new BigDecimal(0.03), new BigDecimal(0.08), new BigDecimal(0.76), new BigDecimal(9)));
        countryFreightList.add(new CountryFreight(1, new BigDecimal(0.08), new BigDecimal(0.22), new BigDecimal(2.09), new BigDecimal(6.3)));
        countryFreightList.add(new CountryFreight(1, new BigDecimal(0.22), new BigDecimal(5), new BigDecimal(2.8), new BigDecimal(6.4)));
        countryFreightList.add(new CountryFreight(2, new BigDecimal(0), new BigDecimal(2), new BigDecimal(2.9), new BigDecimal(12)));
        countryFreightList.add(new CountryFreight(0, new BigDecimal(0), new BigDecimal(0.03), new BigDecimal(1.03), new BigDecimal(0)));
        countryFreightList.add(new CountryFreight(0, new BigDecimal(0.03), new BigDecimal(0.08), new BigDecimal(0.76), new BigDecimal(9)));
        countryFreightList.add(new CountryFreight(0, new BigDecimal(0.08), new BigDecimal(0.22), new BigDecimal(2.09), new BigDecimal(6.3)));
        countryFreightList.add(new CountryFreight(0, new BigDecimal(0.22), new BigDecimal(15), new BigDecimal(2.8), new BigDecimal(6.4)));

        BigDecimal weight = new BigDecimal(queryFreight.getWeight());
        CountryFreight deFreight = countryFreightList.stream().filter(countryFreight -> queryFreight.getGoodsType().equals(countryFreight.getGoodsType())
                && weight.compareTo(countryFreight.getMinWeight()) >= 0 && weight.compareTo(countryFreight.getMaxWeight()) < 0)
                .findAny().orElse(null);
        if (deFreight == null) {
            log.info("根据旧价卡获取旧逻辑的默认运费 没有匹配价卡，返回0");
            return BigDecimal.ZERO;
        }

        //根据店铺和类目获取佣金比例
        BigDecimal rate = BigDecimal.valueOf(0.85);
        List<CommissionAllocation> commissionAllocationList = commissionAllocationService.lambdaQuery()
                .eq(CommissionAllocation::getShopId, queryFreight.getShopId())
                .in(CommissionAllocation::getCategoryId, categoryIds)
                .eq(CommissionAllocation::getIsOn, 1)
                .list();
        if (CollectionUtils.isNotEmpty(commissionAllocationList)) {
            commissionAllocationList.sort((o1, o2) -> categoryIds.indexOf(o2.getCategoryId()) - categoryIds.indexOf(o1.getCategoryId()));
            if(null != commissionAllocationList.get(0).getPercent()){
                rate = BigDecimal.ONE.subtract(commissionAllocationList.get(0).getPercent().divide(new BigDecimal("100"),2, RoundingMode.HALF_UP));
            }
        }

        BigDecimal fee = deFreight.getBaseAmount()
                .add(deFreight.getWeightParam().multiply(new BigDecimal(queryFreight.getWeight())))
                .divide(rate, 2, RoundingMode.HALF_UP);
        LogUtils.info(log, "根据旧价卡获取旧逻辑的默认运费 返回:{}", fee);
        return fee;
    }


    @Override
    public Boolean saveOrUpdateFreight(ProductInfoInput goodsInfoInput) {
        log.info("更新国家运费 goodsId:{}, type:{}, freightList:{}", goodsInfoInput.getId(), goodsInfoInput.getType(), JSON.toJSONString(goodsInfoInput.getFreightList()));
        if (CollectionUtils.isNotEmpty(goodsInfoInput.getFreightList())) {
            Map<String, List<GoodsFreightVO>> map = goodsInfoInput.getFreightList().stream().collect(Collectors.groupingBy(GoodsFreight::getCode));
            for (Map.Entry<String, List<GoodsFreightVO>> entry : map.entrySet()) {
                CheckUtils.check(entry.getValue().size() > 1, ProductResultCode.SAME_COUNTRY_FREIGHT_EXIST);
            }
        }

        //todo 暂不考虑 普通->批发 和 批发->普通 的场景
        if (goodsInfoInput.getType() != null && goodsInfoInput.getType() == 3) {
            return saveOrUpdateFreightGradient(goodsInfoInput);
        }

        try {
            List<GoodsFreight> saveList = new ArrayList<>();

            List<GoodsFreight> oldFreightList = goodsFreightService.lambdaQuery()
                    .eq(GoodsFreight::getGoodsId, goodsInfoInput.getId())
                    .eq(GoodsFreight::getIsDel, 0)
//                    .eq(GoodsFreight::getGradientNum, 1)
                    .list();

            oldFreightList.stream()
                    .filter(goodsFreight -> goodsFreight.getGradientNum() != null && goodsFreight.getGradientNum() > 1)
                    .peek(goodsFreight -> goodsFreight.setIsDel(1))
                    .forEach(saveList::add);
            oldFreightList = oldFreightList.stream()
                    .filter(goodsFreight -> goodsFreight.getGradientNum() == null || goodsFreight.getGradientNum() == 1)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(oldFreightList)) {
                Map<String, BigDecimal> oldFreightMap = oldFreightList.stream().collect(Collectors.toMap(GoodsFreight::getCode, GoodsFreight::getCurrentFreight, (a, b) -> a));
                goodsInfoInput.getOldOperationData().put("freightMap", oldFreightMap);
            }

            Map<String, GoodsFreight> oldGoodsFreightMap = oldFreightList.stream().collect(Collectors.toMap(GoodsFreight::getCode, Function.identity(), (v1, v2) -> v2));
            List<String> existCodes = Lists.newArrayList(oldGoodsFreightMap.keySet());

            List<GoodsFreightVO> newGoodsFreightList = goodsInfoInput.getFreightList();
            if (CollectionUtils.isNotEmpty(newGoodsFreightList)) {
                Set<String> newCodes = newGoodsFreightList.stream().map(GoodsFreight::getCode).collect(Collectors.toSet());
                newGoodsFreightList.forEach(newFreight -> {
                    GoodsFreight goodsFreight = oldGoodsFreightMap.get(newFreight.getCode());
                    if (null == goodsFreight) {
                        goodsFreight = new GoodsFreight();
                        goodsFreight.setGoodsId(goodsInfoInput.getId());
                        goodsFreight.setCode(newFreight.getCode());
                        goodsFreight.setPrice(BigDecimal.ZERO);
                        goodsFreight.setCreateTime(LocalDateTime.now());
                        goodsFreight.setIsDel(0);
                    }
                    goodsFreight.setMinNum(1);
                    goodsFreight.setMaxNum(999999);
                    goodsFreight.setGradientNum(1);
                    goodsFreight.setCurrentFreight(newFreight.getCurrentFreight());
                    goodsFreight.setUpdateTime(LocalDateTime.now());
                    saveList.add(goodsFreight);

                });
                existCodes.removeAll(newCodes);
                if (CollectionUtils.isNotEmpty(existCodes)) {
                    existCodes.forEach(country -> {
                        GoodsFreight toDeleteFreight = oldGoodsFreightMap.get(country);
                        toDeleteFreight.setIsDel(1);
                        toDeleteFreight.setUpdateTime(LocalDateTime.now());
                        saveList.add(toDeleteFreight);
                    });
                }
            }else {
                oldFreightList.forEach(toDeleteFreight -> {
                    toDeleteFreight.setIsDel(1);
                    toDeleteFreight.setUpdateTime(LocalDateTime.now());
                    saveList.add(toDeleteFreight);
                });
            }
            if (CollectionUtils.isNotEmpty(saveList)) {
                log.info("addGoodsFreightList:{}", JSON.toJSONString(saveList));
                goodsFreightService.saveOrUpdateBatch(saveList);

                Map<String, BigDecimal> newFreightMap = saveList.stream()
                        .filter(goodsFreight -> goodsFreight.getIsDel() == 0)
                        .collect(Collectors.toMap(GoodsFreight::getCode, GoodsFreight::getCurrentFreight, (a, b) -> a));
                goodsInfoInput.getNewOperationData().put("freightMap", newFreightMap);
            }
        } catch (Exception e) {
            log.error("更新国家运费异常:", e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    private Boolean saveOrUpdateFreightGradient(ProductInfoInput goodsInfoInput){
        Map<String, List<MutableTriple<BigDecimal, Integer, Integer>>> freightGradientDto = goodsInfoInput.getFreightGradientDto();
        log.info("更新批发商品国家运费 goodsId: {}, freightGradientDto: {}", goodsInfoInput.getId(), JSON.toJSONString(freightGradientDto));
        Long goodsId = goodsInfoInput.getId();
        try {
            List<GoodsFreight> oldFreightList = goodsFreightService.lambdaQuery().eq(GoodsFreight::getGoodsId, goodsId).eq(GoodsFreight::getIsDel, 0).list();

            Map<String, Map<Integer, BigDecimal>> oldFreightSnapMap = null;
            if (CollectionUtils.isNotEmpty(oldFreightList)) {
                oldFreightSnapMap = oldFreightList.stream().collect(getGoodsFreightMapCollector());
            }
            goodsInfoInput.getOldOperationData().put("freightMap", oldFreightSnapMap);

            //有 -> 无
            if (MapUtils.isEmpty(freightGradientDto)) {
                delOldFreight(oldFreightList);
                return Boolean.TRUE;
            }

            List<GoodsFreight> saveOrUpdateFreightList = getGoodsFreights(oldFreightList, freightGradientDto, goodsId);

            if (CollectionUtils.isNotEmpty(saveOrUpdateFreightList)) {
                log.info("saveOrUpdateFreightList:{}", JSON.toJSONString(saveOrUpdateFreightList));
                goodsFreightService.saveOrUpdateBatch(saveOrUpdateFreightList);

                Map<String, Map<Integer, BigDecimal>> newFreightSnapMap = saveOrUpdateFreightList.stream()
                        .filter(goodsFreight -> goodsFreight.getIsDel() == 0)
                        .collect(getGoodsFreightMapCollector());
                goodsInfoInput.getNewOperationData().put("freightMap", newFreightSnapMap);
            }
        } catch (Exception e) {
            log.error("更新批发商品国家运费异常:", e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @NotNull
    private static Collector<GoodsFreight, ?, Map<String, Map<Integer, BigDecimal>>> getGoodsFreightMapCollector() {
        return Collectors.toMap(GoodsFreight::getCode, goodsFreight -> {
            HashMap<Integer, BigDecimal> map1 = Maps.newHashMap();
            map1.put(goodsFreight.getGradientNum(), goodsFreight.getCurrentFreight());
            return map1;
        }, (map1, map2) -> {
            map1.putAll(map2);
            return map1;
        });
    }

    @NotNull
    private static List<GoodsFreight> getGoodsFreights(List<GoodsFreight> oldFreightList, Map<String, List<MutableTriple<BigDecimal, Integer, Integer>>> freightGradientDto, Long goodsId) {
        Map<String, Map<Integer, GoodsFreight>> oldFreightMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(oldFreightList)) {
            oldFreightMap = oldFreightList.stream().collect(Collectors.toMap(GoodsFreight::getCode, goodsFreight -> {
                Map<Integer, GoodsFreight> map1 = Maps.newHashMap();
                map1.put(goodsFreight.getGradientNum(), goodsFreight);
                return map1;
            }, (map1, map2) -> {
                map1.putAll(map2);
                return map1;
            }));
        }

        List<GoodsFreight> saveOrUpdateFreightList = new ArrayList<>();

        //删除多余
        List<String> oldCountryList = Lists.newArrayList(oldFreightMap.keySet());
        List<String> newCountryList = Lists.newArrayList(freightGradientDto.keySet());

        oldCountryList.removeAll(newCountryList);
        if (CollectionUtils.isNotEmpty(oldCountryList)) {
            oldFreightList.stream()
                    .filter(goodsFreight -> oldCountryList.contains(goodsFreight.getCode()))
                    .peek(goodsFreight -> {
                        goodsFreight.setIsDel(1);
                        goodsFreight.setUpdateTime(LocalDateTime.now());
                    })
                    .forEach(saveOrUpdateFreightList::add);
        }

        for (String country : newCountryList) {
            List<MutableTriple<BigDecimal, Integer, Integer>> tripleList = freightGradientDto.get(country);
            Map<Integer, MutableTriple<BigDecimal, Integer, Integer>> newFreightPriceMap = Maps.newHashMap();
            for (int i = 0; i < tripleList.size(); i++) {
                newFreightPriceMap.put(i + 1, tripleList.get(i));
            }

            Map<Integer, GoodsFreight> oldFreightPriceMap = oldFreightMap.getOrDefault(country, Maps.newHashMap());

            List<Integer> oldGradientNumList = Lists.newArrayList(oldFreightPriceMap.keySet());
            List<Integer> newGradientNumList = Lists.newArrayList(newFreightPriceMap.keySet());

            oldGradientNumList.removeAll(newGradientNumList);
            if (CollectionUtils.isNotEmpty(oldGradientNumList)) {
                for (Integer toDeleteGradientNum : oldGradientNumList) {
                    GoodsFreight toDeleteFreight = oldFreightPriceMap.get(toDeleteGradientNum);
                    toDeleteFreight.setIsDel(1);
                    toDeleteFreight.setUpdateTime(LocalDateTime.now());
                    saveOrUpdateFreightList.add(toDeleteFreight);
                }
            }

            for (Integer newGradientNum : newGradientNumList) {
                GoodsFreight goodsFreight = oldFreightPriceMap.get(newGradientNum);
                MutableTriple<BigDecimal, Integer, Integer> newFreightPrice = newFreightPriceMap.get(newGradientNum);
                if (goodsFreight == null) {
                    goodsFreight = new GoodsFreight();
                    goodsFreight.setGoodsId(goodsId);
                    goodsFreight.setCode(country);
                    goodsFreight.setPrice(BigDecimal.ZERO);
                    goodsFreight.setCreateTime(LocalDateTime.now());
                    goodsFreight.setIsDel(0);
                    goodsFreight.setGradientNum(newGradientNum);
                }
                goodsFreight.setUpdateTime(LocalDateTime.now());
                goodsFreight.setCurrentFreight(newFreightPrice.getLeft());
                goodsFreight.setMinNum(newFreightPrice.getMiddle());
                goodsFreight.setMaxNum(newFreightPrice.getRight());
                saveOrUpdateFreightList.add(goodsFreight);
            }
        }
        return saveOrUpdateFreightList;
    }

    @Override
    public Boolean saveOrUpdateFreight(ItemGoodsInfoInputDto dto, ItemGoodsInfoExtBo extBo) {
        log.info("更新国家运费 goodsId:{}, type:{}, freightList:{}", dto.getId(), dto.getType(), JSON.toJSONString(dto.getFreightList()));
        if (CollectionUtils.isNotEmpty(dto.getFreightList())) {
            Map<String, List<GoodsFreightVO>> map = dto.getFreightList().stream().collect(Collectors.groupingBy(GoodsFreight::getCode));
            for (Map.Entry<String, List<GoodsFreightVO>> entry : map.entrySet()) {
                CheckUtils.check(entry.getValue().size() > 1, ProductResultCode.SAME_COUNTRY_FREIGHT_EXIST);
            }
        }

        // todo 暂不考虑 普通->批发 和 批发->普通 的场景
        if (dto.getType() != null && dto.getType() == 3) {
            return saveOrUpdateFreightGradient(dto, extBo);
        }

        try {
            List<GoodsFreight> saveList = new ArrayList<>();


            List<GoodsFreight> oldFreightList = goodsFreightService.queryAllByGoodsId(dto.getId());

            oldFreightList.stream()
                    .filter(goodsFreight -> goodsFreight.getGradientNum() > 1)
                    .peek(goodsFreight -> goodsFreight.setIsDel(1))
                    .forEach(saveList::add);
            oldFreightList = oldFreightList.stream().filter(goodsFreight -> goodsFreight.getGradientNum() == 1).collect(Collectors.toList());

            extBo.getOldOperationData().setFreightMap(getOldFreightSnapMap(oldFreightList, Collectors.toMap(GoodsFreight::getCode, GoodsFreight::getCurrentFreight, (a, b) -> a)));

            Map<String, GoodsFreight> oldGoodsFreightMap = oldFreightList.stream().collect(Collectors.toMap(GoodsFreight::getCode, Function.identity(), (v1, v2) -> v2));
            List<String> existCodes = Lists.newArrayList(oldGoodsFreightMap.keySet());

            List<GoodsFreightVO> newGoodsFreightList = dto.getFreightList();
            if (CollectionUtils.isNotEmpty(newGoodsFreightList)) {
                Set<String> newCodes = newGoodsFreightList.stream().map(GoodsFreight::getCode).collect(Collectors.toSet());
                newGoodsFreightList.forEach(newFreight -> {
                    GoodsFreight goodsFreight = oldGoodsFreightMap.get(newFreight.getCode());
                    if (null == goodsFreight) {
                        goodsFreight = new GoodsFreight();
                        goodsFreight.setGoodsId(dto.getId());
                        goodsFreight.setCode(newFreight.getCode());
                        goodsFreight.setPrice(BigDecimal.ZERO);
                        goodsFreight.setCreateTime(LocalDateTime.now());
                        goodsFreight.setIsDel(0);
                    }
                    goodsFreight.setMinNum(1);
                    goodsFreight.setMaxNum(999999);
                    goodsFreight.setGradientNum(1);
                    goodsFreight.setCurrentFreight(newFreight.getCurrentFreight());
                    goodsFreight.setUpdateTime(LocalDateTime.now());
                    saveList.add(goodsFreight);

                });
                existCodes.removeAll(newCodes);
                if (CollectionUtils.isNotEmpty(existCodes)) {
                    existCodes.forEach(country -> {
                        GoodsFreight toDeleteFreight = oldGoodsFreightMap.get(country);
                        toDeleteFreight.setIsDel(1);
                        toDeleteFreight.setUpdateTime(LocalDateTime.now());
                        saveList.add(toDeleteFreight);
                    });
                }
            } else {
                oldFreightList.forEach(toDeleteFreight -> {
                    toDeleteFreight.setIsDel(1);
                    toDeleteFreight.setUpdateTime(LocalDateTime.now());
                    saveList.add(toDeleteFreight);
                });
            }
            if (CollectionUtils.isNotEmpty(saveList)) {
                log.info("addGoodsFreightList:{}", JSON.toJSONString(saveList));
                goodsFreightService.saveOrUpdateBatch(saveList);

                Map<String, BigDecimal> newFreightMap = saveList.stream()
                        .filter(goodsFreight -> goodsFreight.getIsDel() == 0)
                        .collect(Collectors.toMap(GoodsFreight::getCode, GoodsFreight::getCurrentFreight, (a, b) -> a));
                extBo.getNewOperationData().setFreightMap(newFreightMap);
            }
        } catch (Exception e) {
            log.error("更新国家运费异常:", e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private Boolean saveOrUpdateFreightGradient(ItemGoodsInfoInputDto dto, ItemGoodsInfoExtBo extBo) {
        Map<String, List<MutableTriple<BigDecimal, Integer, Integer>>> freightGradientDto = extBo.getFreightGradientDto();
        log.info("更新批发商品国家运费 goodsId: {}, freightGradientDto: {}", dto.getId(), JSON.toJSONString(freightGradientDto));
        Long goodsId = dto.getId();
        try {
            List<GoodsFreight> oldFreightList = goodsFreightService.queryAllByGoodsId(goodsId);

            extBo.getOldOperationData().setFreightMap(getOldFreightSnapMap(oldFreightList, getGoodsFreightMapCollector()));

            //有 -> 无
            if (MapUtils.isEmpty(freightGradientDto)) {
                delOldFreight(oldFreightList);
                return Boolean.TRUE;
            }

            List<GoodsFreight> saveOrUpdateFreightList = getGoodsFreights(oldFreightList, freightGradientDto, goodsId);

            if (CollectionUtils.isNotEmpty(saveOrUpdateFreightList)) {
                log.info("saveOrUpdateFreightList:{}", JSON.toJSONString(saveOrUpdateFreightList));
                goodsFreightService.saveOrUpdateBatch(saveOrUpdateFreightList);

                Map<String, Map<Integer, BigDecimal>> newFreightSnapMap = saveOrUpdateFreightList.stream()
                        .filter(goodsFreight -> goodsFreight.getIsDel() == 0)
                        .collect(getGoodsFreightMapCollector());
                extBo.getNewOperationData().setFreightMap(newFreightSnapMap);
            }

        } catch (Exception e) {
            log.error("更新批发商品国家运费异常:", e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private static <R> R getOldFreightSnapMap(List<GoodsFreight> oldFreightList, Collector<GoodsFreight, ?, R> collector) {
        if (CollectionUtils.isEmpty(oldFreightList)) {
            return null;
        }

        return oldFreightList.stream().collect(collector);
    }

    private void delOldFreight(List<GoodsFreight> oldFreightList) {
        if (CollectionUtils.isNotEmpty(oldFreightList)) {
            oldFreightList.forEach(goodsFreight -> {
                goodsFreight.setIsDel(1);
                goodsFreight.setUpdateTime(LocalDateTime.now());
            });

            goodsFreightService.updateBatchById(oldFreightList);
        }
    }

}
