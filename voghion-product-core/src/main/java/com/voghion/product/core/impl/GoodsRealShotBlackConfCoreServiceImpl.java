package com.voghion.product.core.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.PageView;
import com.colorlight.base.utils.CheckUtils;
import com.voghion.product.core.CategoryCoreService;
import com.voghion.product.core.CategoryTreeCoreService;
import com.voghion.product.core.GoodsRealShotBlackConfCoreService;
import com.voghion.product.core.GoodsTagCoreService;
import com.voghion.product.model.dto.GoodsRealShotBlackConfConditionDTO;
import com.voghion.product.model.dto.GoodsRealShotBlackConfDTO;
import com.voghion.product.model.enums.ProductResultCode;
import com.voghion.product.model.po.Category;
import com.voghion.product.model.po.FaMerchantsTag;
import com.voghion.product.model.po.Goods;
import com.voghion.product.model.po.GoodsRealShotBlack;
import com.voghion.product.model.vo.GoodsRealShotBlackConfVO;
import com.voghion.product.model.vo.GoodsTagVO;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class GoodsRealShotBlackConfCoreServiceImpl extends AbstractCommonServiceImpl implements GoodsRealShotBlackConfCoreService {

    private static final String REAL_SHOT_BLACK_REDIS_PREFIX = "REAL_SHOT_BLACK_CONF_";

    @Resource
    private GoodsRealShotBlackService goodsRealShotBlackService;

    @Resource
    private FaMerchantsApplyService faMerchantsApplyService;

    @Resource
    private CategoryCoreService categoryCoreService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private CategoryTreeCoreService categoryTreeCoreService;

    @Resource
    private FaMerchantsTagService faMerchantsTagService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private GoodsTagCoreService goodsTagCoreService;

    @Resource
    private RedisApi redisApi;


    @Override
    public Boolean saveOrUpdateBlackConf(GoodsRealShotBlackConfDTO goodsRealShotBlackConfDTO) {
        CheckUtils.check(StringUtils.isBlank(goodsRealShotBlackConfDTO.getCountries()) && StringUtils.isBlank(goodsRealShotBlackConfDTO.getCategoryIds())
                && StringUtils.isBlank(goodsRealShotBlackConfDTO.getTagId()), ProductResultCode.PARAM_GOODS_REAL_SHOT_CONF_IS_NULL);
        CheckUtils.check(Objects.isNull(goodsRealShotBlackConfDTO.getStatus()), ProductResultCode.PARAM_CONF_STATUS_IS_NULL);
        GoodsRealShotBlack goodsRealShotBlack;
        if(Objects.nonNull(goodsRealShotBlackConfDTO.getId())){
            goodsRealShotBlack = goodsRealShotBlackService.selectById(goodsRealShotBlackConfDTO.getId());
            CheckUtils.check(Objects.isNull(goodsRealShotBlack), ProductResultCode.PARAM_GOODS_REAL_SHOT_CONF_ID_ERROR);
            goodsRealShotBlack.setUpdateBy(getUserName());
            goodsRealShotBlack.setUpdateTime(LocalDateTime.now());
            goodsRealShotBlackService.updateById(goodsRealShotBlack);
        }else{
            goodsRealShotBlack =  new GoodsRealShotBlack();
            goodsRealShotBlack.setCreateBy(getUserName());
            goodsRealShotBlack.setUpdateBy(goodsRealShotBlack.getCreateBy());
            goodsRealShotBlack.setCreateTime(LocalDateTime.now());
        }
        if(StringUtils.isNotBlank(goodsRealShotBlackConfDTO.getCountries())){
            String countryList = Arrays.stream(goodsRealShotBlackConfDTO.getCountries().trim().split("\\n")).collect(Collectors.joining(","));
            goodsRealShotBlack.setCountry(countryList);
        }else{
            goodsRealShotBlack.setCountry("");
        }
        if(StringUtils.isNotBlank(goodsRealShotBlackConfDTO.getCategoryIds())){
            List<Long> categoryIdList = Arrays.stream(goodsRealShotBlackConfDTO.getCategoryIds().trim().split("\\n"))
                    .map(item -> Long.parseLong(item)).collect(Collectors.toList());
            Collection<Category> categoryCollection = categoryService.selectByIds(categoryIdList);
            CheckUtils.check(CollectionUtils.isEmpty(categoryCollection) || categoryCollection.size() != categoryIdList.size(),
                    ProductResultCode.PARAM_CONF_CATEGORY_IS_ERROR);
            String categoryList = categoryIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
            goodsRealShotBlack.setCategoryIds(categoryList);
        }else{
            goodsRealShotBlack.setCategoryIds("");
        }
        if(StringUtils.isNotBlank(goodsRealShotBlackConfDTO.getTagId())){
            GoodsTagVO goodsTag = goodsTagCoreService.getGoodsTagById(Long.valueOf(goodsRealShotBlackConfDTO.getTagId()));
            CheckUtils.notNull(goodsTag,ProductResultCode.GOODS_TAG_NOT_EXIST);
            goodsRealShotBlack.setTagId(goodsRealShotBlackConfDTO.getTagId());
        }else{
            goodsRealShotBlack.setTagId("");
        }
        goodsRealShotBlack.setStatus(goodsRealShotBlackConfDTO.getStatus());
        boolean resultFlag = goodsRealShotBlackService.saveOrUpdate(goodsRealShotBlack);
        //同步所有实拍图黑名单缓存配置
        boolean syncFlag = syncCacheAllConfig();
        return resultFlag && syncFlag;
    }

    /**
     * 同步所有实拍图黑名单缓存配置
     * @return
     */
    @Override
    public Boolean syncCacheAllConfig() {
        //查询实拍图黑名单有效配置
        List<GoodsRealShotBlack> blackConfList = goodsRealShotBlackService.lambdaQuery()
                .eq(GoodsRealShotBlack::getStatus, 1)
                .eq(GoodsRealShotBlack::getIsDel, 0)
                .list();
        if(CollectionUtils.isEmpty(blackConfList)){
            return true;
        }
        Map<String, Set<Long>> cacheRealShotConfMap = new HashMap<>();
        for(GoodsRealShotBlack realShotBlack : blackConfList){
            AtomicReference<String> redisKey = new AtomicReference<>();
            Set<Long> categoryIdSet = new HashSet<>();
            if(StringUtils.isNotBlank(realShotBlack.getCountry())){
                Arrays.stream(realShotBlack.getCountry().split(",")).forEach(item -> {
                    if(StringUtils.isBlank(realShotBlack.getTagId())){
                        redisKey.set(REAL_SHOT_BLACK_REDIS_PREFIX + item + "_ALL");
                    }else{
                        redisKey.set(REAL_SHOT_BLACK_REDIS_PREFIX + item + "_" + realShotBlack.getTagId());
                    }
                    if(StringUtils.isNotBlank(realShotBlack.getCategoryIds())){
                        Map<Long, List<Long>> childCategoryMaps = queryAllChildCategoryIdByParentIds(realShotBlack.getCategoryIds());
                        childCategoryMaps.values().forEach(categoryIdList1 -> categoryIdList1.forEach(categoryIdSet::add));
                    } else {
                        categoryIdSet.add(0L);
                    }
                    fillRealShotCacheMap(cacheRealShotConfMap, redisKey, categoryIdSet);
                });
            }else{
                if(StringUtils.isBlank(realShotBlack.getTagId())){
                    redisKey.set(REAL_SHOT_BLACK_REDIS_PREFIX + "ALL_ALL");
                }else{
                    redisKey.set(REAL_SHOT_BLACK_REDIS_PREFIX + "ALL_" + realShotBlack.getTagId());
                }
                if(StringUtils.isNotBlank(realShotBlack.getCategoryIds())){
                    Map<Long, List<Long>> childCategoryMaps = queryAllChildCategoryIdByParentIds(realShotBlack.getCategoryIds());
                    childCategoryMaps.values().forEach(categoryIdList1 -> categoryIdList1.forEach(categoryIdSet::add));
                } else {
                    categoryIdSet.add(0L);
                }
                fillRealShotCacheMap(cacheRealShotConfMap, redisKey, categoryIdSet);
            }
        }
        if(MapUtils.isNotEmpty(cacheRealShotConfMap)){
            cacheRealShotConfMap.forEach((redisKey, categoryIdSet) -> {
                redisApi.del(redisKey);
                categoryIdSet.forEach(categoryId -> redisApi.sSetAndTime(redisKey, 60 * 60, categoryId));
            });
        }
        return true;
    }

    private static void fillRealShotCacheMap(Map<String, Set<Long>> cacheRealShotConfMap, AtomicReference<String> redisKey, Set<Long> categoryIdSet) {
        if(cacheRealShotConfMap.containsKey(redisKey.get())){
            Set<Long> existCategoryIdSet = cacheRealShotConfMap.get(redisKey.get());
            existCategoryIdSet.addAll(categoryIdSet);
            cacheRealShotConfMap.put(redisKey.get(), existCategoryIdSet);
        } else {
            cacheRealShotConfMap.put(redisKey.get(), categoryIdSet);
        }
    }

    /**
     * 根据类目id查询所有的叶子类目、非叶子类目和本身
     * @param categoryIds
     * @return
     */
    private Map<Long, List<Long>> queryAllChildCategoryIdByParentIds(String categoryIds) {
        List<Long> categoryIdList = Arrays.stream(categoryIds.split(",")).map(item -> Long.parseLong(item)).collect(Collectors.toList());
        return categoryCoreService.queryAllChildCategoryIdByParentIds(categoryIdList);
    }



    @Override
    public PageView<GoodsRealShotBlackConfVO> queryGoodsRealShotBlackConf(GoodsRealShotBlackConfConditionDTO goodsRealShotBlackConfConditionDTO) {
        CheckUtils.notNull(goodsRealShotBlackConfConditionDTO,ProductResultCode.PARAMETER_ERROR);
        LambdaQueryChainWrapper<GoodsRealShotBlack> queryChainWrapper = goodsRealShotBlackService.lambdaQuery();
        if (Objects.nonNull(goodsRealShotBlackConfConditionDTO.getStatus())){
            queryChainWrapper.eq(GoodsRealShotBlack::getStatus,goodsRealShotBlackConfConditionDTO.getStatus());
        }
        if (StringUtils.isNotBlank(goodsRealShotBlackConfConditionDTO.getUpdateBy())){
            queryChainWrapper.like(GoodsRealShotBlack::getUpdateBy,goodsRealShotBlackConfConditionDTO.getUpdateBy());
        }
        // 最后更新时间
        queryChainWrapper.gt(StringUtils.isNotBlank(goodsRealShotBlackConfConditionDTO.getStartTime()), GoodsRealShotBlack::getUpdateTime, goodsRealShotBlackConfConditionDTO.getStartTime());
        queryChainWrapper.lt(StringUtils.isNotBlank(goodsRealShotBlackConfConditionDTO.getEndTime()), GoodsRealShotBlack::getUpdateTime, goodsRealShotBlackConfConditionDTO.getEndTime());
        queryChainWrapper.orderByDesc(GoodsRealShotBlack::getUpdateTime);
        IPage<GoodsRealShotBlack> iPageResult = queryChainWrapper.page(new Page<>(goodsRealShotBlackConfConditionDTO.getPageNow(), goodsRealShotBlackConfConditionDTO.getPageSize()));
        // 封装结果
        List<GoodsRealShotBlack> records = iPageResult.getRecords();
        PageView<GoodsRealShotBlackConfVO> result = new PageView<>((int) goodsRealShotBlackConfConditionDTO.getPageSize(), (int) goodsRealShotBlackConfConditionDTO.getPageNow());
        if (CollectionUtils.isEmpty(records)){
            return result;
        }
        List<GoodsRealShotBlackConfVO> realShotConfVOList = new ArrayList<>();
        records.forEach(value -> {
            GoodsRealShotBlackConfVO goodsRealShotConfVO =new GoodsRealShotBlackConfVO();
            goodsRealShotConfVO.setId(value.getId());
            goodsRealShotConfVO.setTagId(value.getTagId());
            if(StringUtils.isNotBlank(value.getCountry())){
                goodsRealShotConfVO.setCountryList(Arrays.stream(value.getCountry().split(",")).collect(Collectors.toList()));
            }
            if(StringUtils.isNotBlank(value.getCategoryIds())){
                List<Long> categoryIdList = Arrays.stream(value.getCategoryIds().split(",")).map(item -> Long.parseLong(item.trim())).collect(Collectors.toList());
                goodsRealShotConfVO.setCategoryList(categoryIdList);
                Map<Long, String> categoryPathMap = categoryTreeCoreService.getCategoryPathByIds(categoryIdList);
                goodsRealShotConfVO.setCategoryTreeMap(categoryPathMap);
            }
            goodsRealShotConfVO.setStatus(value.getStatus());
            goodsRealShotConfVO.setUpdateTime(value.getUpdateTime());
            goodsRealShotConfVO.setUpdateBy(value.getUpdateBy());
            realShotConfVOList.add(goodsRealShotConfVO);
        });
        result.setRecords(realShotConfVOList);
        result.setPageSize((int) iPageResult.getSize());
        result.setPageNow((int) iPageResult.getCurrent());
        result.setRowCount(iPageResult.getTotal());
        result.setPageCount(iPageResult.getPages());
        return result;
    }

    @Override
    public Boolean checkAppAvailableOfGoods(Long goodsId, String country) {
        Boolean checkResult = true;
        if (goodsId == null || StringUtils.isBlank(country)){
            return true;
        }
        Goods goods = goodsService.queryGoodsById(goodsId);
        if (goods == null){
            return true;
        }
        String redisKeyAll = REAL_SHOT_BLACK_REDIS_PREFIX + country + "_ALL";
        if(redisApi.sHasKey(redisKeyAll, 0L) || redisApi.sHasKey(redisKeyAll, goods.getCategoryId())){
            return false;
        }
        String redisKeyAllTag = REAL_SHOT_BLACK_REDIS_PREFIX  + "ALL_ALL";
        if(redisApi.sHasKey(redisKeyAllTag, 0L) || redisApi.sHasKey(redisKeyAllTag, goods.getCategoryId())){
            return false;
        }
        //查询店铺关联的标签
        List<FaMerchantsTag> shopTagInfoList = faMerchantsTagService.lambdaQuery()
                .eq(FaMerchantsTag::getShopId, goods.getShopId())
                .eq(FaMerchantsTag::getStatus, 1).list();
        if(CollectionUtils.isNotEmpty(shopTagInfoList)){
            for(FaMerchantsTag tagInfo : shopTagInfoList){
                String redisKey = REAL_SHOT_BLACK_REDIS_PREFIX + country + "_"+ tagInfo.getTagId();
                String redisKeyAllCountry = REAL_SHOT_BLACK_REDIS_PREFIX + "ALL_"+ tagInfo.getTagId();
                if(redisApi.sHasKey(redisKey, 0L) || redisApi.sHasKey(redisKey, goods.getCategoryId())){
                    checkResult = false;
                    break;
                }
                if(redisApi.sHasKey(redisKeyAllCountry, 0L) || redisApi.sHasKey(redisKeyAllCountry, goods.getCategoryId())){
                    checkResult = false;
                    break;
                }
            }
        }
        return checkResult;
    }
}
