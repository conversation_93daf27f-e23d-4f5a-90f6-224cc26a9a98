package com.voghion.product.core;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.*;
import com.voghion.product.api.input.*;
import com.voghion.product.api.output.GoodsListVO;
import com.voghion.product.api.output.GoodsOutput;
import com.voghion.product.listener.*;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.enums.DeliveryTypeEnum;
import com.voghion.product.model.po.Goods;
import com.voghion.product.model.po.GoodsItem;
import com.voghion.product.model.po.TongDunGoodsImages;
import com.voghion.product.model.vo.*;
import com.voghion.product.model.vo.GoodsInfoVO;
import com.voghion.product.model.vo.condition.IdCondition;
import com.voghion.product.model.vo.condition.RefreshGoodsPriceCondition;
import com.voghion.product.model.vo.customTag.GoodsRelCustomTagReqVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2021/5/7 21:41
 * @describe
 */
public interface GoodsCoreService {

    Boolean updateTag(List<GoodsTagInfoDTO> goodsTagInfoDTOList);

    Boolean deleteTag(List<GoodsTagInfoDTO> goodsTagInfoDTOList);

    /**
     * 根据条件查询商品分页信息
     *
     * @param queryGoodsVO
     * @return
     */
    PageView<GoodsInfoVO> goodsByPageList(QueryGoodsVO queryGoodsVO);

    /**
     * 根据条件查询商品分页信息(es)
     *
     * @param queryGoodsVO
     * @return
     */
    PageView<GoodsInfoVO> goodsByPageListWithEs(QueryGoodsVO queryGoodsVO);

    /**
     * 根就商品ids查询所有商品
     *
     * @param ids
     * @return
     */
    List<GoodsOutput> queryGoodsByIds(List<Long> ids);

    /**
     * 根就商品ids查询所有商品(后台)
     *
     * @param ids
     * @return
     */
    List<GoodsOutput> queryBackendGoodsByIds(List<Long> ids);

    /**
     * 查询商品表信息
     *
     * @param ids
     * @return
     */
    List<GoodsOutput> listOriginalGoodsInfoByIds(List<Long> ids);

    /**
     * 根据活动商品ids查询商品信息(include minStock)
     *
     * @param ids
     * @return
     */
    List<GoodsOutput> queryActivityGoodsByIds(List<Long> ids);

    /**
     * 根就活动商品ids查询备份活动前原价格
     *
     * @param ids
     * @return
     */
    List<GoodsOutput> queryStartingActivityGoodsByIds(Long startingActivityId, List<Long> ids);


    /**
     * 根就商品ids查询所有商品
     *
     * @param ids
     * @param isValid true 有效的  过滤删除 有效状态  false 不过滤删除  有些状态  null 查询所有
     * @return
     */
    List<GoodsOutput> queryGoodsByIds(List<Long> ids, Boolean isValid);


    /**
     * 根据id查询信息
     *
     * @param id
     * @return
     */
    GoodsDetailInfoVO queryGoodsDetailById(Long id, Long shopId);

    GoodsDetailInfoVO queryGoodsDetailById(Long id, Long shopId, String buyerClientInfo);

    GoodsDetailInfoVO queryGoodsDetailByIdFromTable(Long id, Long shopId);

    Boolean updateGoodsTag(GoodsTagUpdateInput input);


    Boolean deleteGoodsTag(GoodsTagUpdateInput input);

    /**
     * 增加销量
     *
     * @param goodsSales
     * @return
     */
    Boolean updateGoodsSales(Map<Long, Integer> goodsSales);

    PageView<ShopGoodsInfoVO> shopGoodsByPageList(QueryGoodsVO queryGoodsVO);

    String updateGoodsImg(List<Long> goodsId);

    Boolean updateCategory(List<CategoryUpdateVO> list);

    void moveCategory(List<Long> fromCategoryIds, Long toCategoryId);

    void moveCategoryByFile(MultipartFile file);

//    void moveGoodsCategory(RemoveGoodsCategoryCondition condition);

    PageView<GoodsListVO> queryGoodsList(QueryGoodsDTO queryGoodsDTO);

    void exportGoodsListByShop(QueryGoodsVO queryGoodsVO) throws IOException;

    void exportGoodsListByOperation(QueryGoodsVO queryGoodsVO);

    List<SearchResultExport> getSearchExport(QueryGoodsVO queryGoodsVO);

    void lockGoods(List<GoodsLockVO> list);

    Boolean isShow(GoodsIsShowInput goodsIsShowInput);

    void syncSelfSupportInfo(List<Long> goodsIds, Integer isShow);

    void batchIsShow(InputStream inputStream);

    Boolean systemIsShow(GoodsIsShowInput goodsIsShowInput);

    Boolean phpIsShow(GoodsIsShowInput goodsIsShowInput);

    Boolean isDel(GoodsIsDelInput goodsIds);

    Boolean revertDelete(List<Long> goodsIds);

    void batchDelete(InputStream inputStream);

    List<Long> getLockGoods();

    //sku价格信息导出
    List<SkuPriceMsgExportVO> getList(QueryGoodsVO queryGoodsVO);

    void downloadPriceCard(HttpServletResponse resp);

    Boolean updateLockBatchById(List<GoodsOutput> needLockList);

    /*   Boolean queryIsSevenDayGood(Long goodsId);*/

    List<Long> queryGoodsIsSevenDay(List<Long> goodsIds);

    /**
     * 批量修改国家
     *
     * @param goodsList
     * @return
     */
    Boolean updateCountryBatchById(List<Goods> goodsList);

    /**
     * 批量更新库存
     * @param flag true:scm
     */
    void batchUpdateStock(List<GoodsStockImportVO> list,boolean flag);

    /**
     * 批量更新商品重量及运费
     * @param list 待更新商品数据
     * @param source 1.excel导入 2.dubbo调用
     */
    boolean batchUpdateFreight(List<GoodsFreightImportVO> list, int source);

    void batchUpdateFreightForWarehouse(List<GoodsFreightImportForWarehouseVO> list);

    List<GoodsCountryVO> getCountryList();

    GoodsItem queryGoodsItemBySkuId(Long skuId);

    List<GoodsItem> queryGoodsItemBySkuIds(List<Long> skuIds);

    /**
     * 分页根据类目id查询商品id集合
     *
     * @param queryGoodsDTO 分页参数
     * @return 商品id集合
     */
    List<Long> queryGoodsIdsByCategoryIdsPage(QueryGoodsDTO queryGoodsDTO);


    Long queryGoodsCount(Long shopId);

    void calculateAllGoodsSortV2();

    /**
     * 查询商品信息（只查询类目和id）
     *
     * @param ids
     * @return
     */
    List<Goods> queryGoodsCategory(List<Long> ids);


    /**
     * 查询商品信息
     *
     * @param goodsIds
     * @return
     */
    List<SystemBidGoodsImportVo> queryGoods(List<Long> goodsIds);

    /**
     * 同步商品为禁售 + userName
     */
    boolean syncDisableGoodsWithUser(List<Long> goodsIds);

    /**
     * 同步商品为（同盾）禁售-新-带禁售原因
     */
    boolean syncDisableGoodsNew(List<TongDunGoodsImages> tongDunGoodsImages);


    /**
     * 同步商品为上架
     */
    boolean syncPassGoods(List<Long> goodsIds);

    /**
     * 同步商品为上架（为同盾渠道提供）
     * 较syncPassGoods区别在于:上架商品不主动添加商品合规审核任务
     */
    boolean syncPassGoodsForRemote(List<Long> goodsIds);

    /**
     * 同步商品为上架 同上 + userName
     */
    boolean syncPassGoodsNew(List<Long> goodsIds);

    /**
     * 同步商品为同盾驳回 + userName
     */
    boolean syncTongDunRejectGoods(List<Long> goodsIds);

    boolean syncPickedShopPassGoods(List<Long> goodsIds);

    /**
     * 同步商品为小二待审核
     */
    boolean syncPickedShopGoods(List<Long> goodsIds);

    /**
     * 同步商品为待调整-价格相关
     */
    boolean syncAdjustWithPriceGoods(List<Long> goodsIds);

    /**
     * 同步商品为待调整-非价格相关
     */
    boolean syncAdjustNotPriceGoods(List<Long> goodsIds);

    /**
     * 同步商品为禁售(通用)
     */
    boolean syncDisableGoods(List<Long> goodsIds, String disableReason, String createBy);

    /**
     * 同步商品为审核中
     */
    boolean syncAuditGoods(List<Long> goodsIds);

    /**
     * 同步商品为审核中 同上 + userName
     */
    boolean syncAuditGoodsNew(List<Long> goodsIds);

    /**
     * 查询商品sku信息和国家运费信息
     */
    PickedShopGoodsDTO queryPickedShopGoodsInfo(PickedShopGoodsVO vo);

    boolean importGoods(GoodsImportDTO goodsImportDTO);

    PageView<BatchImportGoodsProcessVo> queryImportGoodsProcess(Integer pageNow, Integer pageSize);

    List<GoodsImportSuccessGoodsVO> queryImportSuccessGoods(Long id);

    List<GoodsImportVO> queryImportFailedGoods(Long id);

    Map<Long, Integer> quereyShopDeliveryTypeMap(List<Long> shopIds);

    /**
     * 同步商品为下架
     */
    boolean syncOffGoods(List<Long> goodsIds);

    /**
     * 同步商品为下架 同上 + userName
     */
    boolean syncOffGoodsNew(List<Long> goodsIds);

    /**
     * 商品库存变动，新增商家公告
     *
     * @param goodsInfoInput
     * @return
     */
    Boolean addGoodsStockNotice(List<GoodsStockVO> goodsInfoInput);

    /**
     * 批量商品上新
     */
    void batchGoodsArrival(InputStream inputStream, String applyUser);

    /**
     * 修改上新人
     */
    void updateGoodsArrival(GoodsArrivalVO vo);

    /**
     * 修改上新状态
     */
    void updateArrivalStatus(UpdateArrivalStatusVo vo);

    /**
     * 买手申请上新
     */
    void buyerApplyArrival(GoodsArrivalVO vo);

    /**
     * 分配上新
     */
    void allocArrival();

    void allocArrival(AllocArrivalVO vo);

    void syncGoodsSelf(List<Long> goodsIds);

    /**
     * 申请上新
     */
    void applyArrival(GoodsArrivalVO vo);

    void refreshArrivalStatus();

    void refreshArrivalStatus(boolean isRefresh);

    void modifyArrivalStatus(ModifyArrivalStatusVO vo);

    void refreshArrivalType();

    PageView<GoodsArrivalInfoVO> goodsSelfArrivalByPageList(QueryGoodsArrivalVO queryGoodsArrivalVO);

    void exportGoodsArrival(QueryGoodsArrivalVO queryGoodsArrivalVO) throws IOException;

    Map<String, BigDecimal> calculateGoodsPrice(GoodsCalulatePriceInput input);

    Map<String, BigDecimal> calculateGoodsPriceV2(GoodsCalulatePriceInput input);

    Map<Long, BigDecimal> calculateGoodsPriceV3(GoodsCalulatePriceInput input);

    Map<Long, BigDecimal> rmb2Eub(GoodsCalulatePriceInput input);

    Map<Long, BigDecimal> eub2Rmb(GoodsCalulatePriceInput input);


    Long lowerPrice(Long goodsId,BigDecimal rate);

    Long upPrice(Long goodsId, BigDecimal rate);

    Integer queryProhibitionOrLimitingById(Long goodsId);

    /**
     * 获取商品关联自定义标签列表信息
     * @param goodsId
     * @return
     */
    GoodsRelCustomTagDTO getRelCustomTags(Long goodsId);

    /**
     * 根据商品id批量关联/解除自定义标签列表
     * @param requestVO
     * @return
     */
    Result<Boolean> goodsRelCustomTags(GoodsRelCustomTagReqVO requestVO);

    Boolean goodsChannelAssociation(List<GoodsChannelAssociationVO> voList);

    List<GoodsBriefInfoDTO> queryGoodsBriefInfo(QueryGoodsVO queryGoodsVO);

    /**
     * 批量转变商品的类型为批发商品
     */
    Result<BatchTransferGoodsTypeResult> batchTransferGoodsTypeTo3(InputStream inputStream);

    Result<BatchTransferGoodsTypeResult> batchTransferGoodsType(List<Long> goodsIds);

    /**
     * 批量转变批发商品的类型为普通商品
     */
    BatchTransferGoodsTypeResult batchTransferGoodsTypeTo1(List<Long> goodsIds);

    List<LogisticsProperty> listAllLogisticProperty();

    /**
     * 重新计算并更新商品的国家运费
     */
    Boolean refreshGoodsFreight(Long goodsId);

    Boolean refreshGoodsFreight(Long goodsId, String traceId);

    Boolean refreshGoodsFreight(Long goodsId, Integer type, String traceId);

   List<GoodsInfoExport> exportGoodsInfo(List<Long> goodsIds);

    Set<Long> mismatchingGoodsByDeliveryType(List<Long> goodsIds, DeliveryTypeEnum deliveryTypeEnum);

    Integer queryGoodsLogisticsProperty(Long goodsId);

    /**
     * 重新计算并更新批发商品sku价格
     * @param type 0业务 1手动
     * @param show 是否自动上架
     */
    void refreshWholesaleGoodsPrice(Long goodsId, Integer type, boolean show);

    /**
     * 查看商品申述基础信息
     * @param goodsId
     * @return
     */
    GoodsAppealBaseVO queryAppealBaseInfo(Long goodsId);

    /**
     * 查看商品商家申述详情
     * @param goodsId
     * @return
     */
    GoodsAppealDetailVO queryAppealDetailInfo(Long goodsId);

    /**
     * 撤销商家申述详情
     * @param condition
     * @return
     */
    Boolean revokeAppealInfo(IdCondition condition);

    void batchUpdateCostPrice(InputStream inputStream);

    /**
     * 批量更新商品采购成本
     */
    Map<String, List<Long>> batchUpdateCostPrice(List<UpdateCostPriceDto> dtoList, boolean onlyWhoseSale);

    /**
     * 异步导出商家加降权数据
     * @param queryDto
     * @param id
     */
    void offShelfExport(ProhibitionQueryDto queryDto, Long id);

    /**
     * 下架零库存sku商品：目前只对批发商品做校验
     */
    void   takeOffZeroStockSkuGoods();

    void importUpdateShopInfo(List<GoodsUpdateShopInfoVO> vos, String operator);

    void customizedSyncGoods(Long shopId, String shopName, List<Integer> values);

    /**
     * 根据参数自动计算sku的销售价
     */
    Map<String, BigDecimal> calculateSkuPrice(CalculateSkuPriceParamDto dto);

    /**
     * 根据商品id集合查询对应的标签
     */
    Map<Long, List<Long>> queryTagByGoodsIds(List<Long> goodsIds);

    /**
     * 处理主图url未转化商品
     */
    void fixInValidMainImageGoods();


    Boolean conversionGoodsIsShow(ConversionGoodsIsShowInput conversionGoodsIsShowInput);

    GoodsAndItemInfo queryGoodsAndItem(Long id);

    /**
     * 获取商品说明书详情
     *
     * @param goodsId
     * @param countryKey
     * @return
     */
    GoodsManualVO getGoodsManualByGoodsIdAndCountryType(Long goodsId, String countryKey);

    Long queryTagGoodsCount(GoodsInput input) throws IOException;

    /**
     * 负向批发商品 退出批发((tagId=151 or type=3) && tagId=[40,45,50])
     */
    void negativeGoodsExitWholesale();

    /**
     * 校验商品绑定品牌是否名字违规品牌集合
     * @param goodsId
     * @param brandList
     * @return
     */
    Boolean goodsMatchIllegalBrandList(Long goodsId, List<String> brandList);

    /**
     * 查询店铺实际有效商品数
     */
    Result<Map<Long, Integer>> queryShopRealGoodsCountByShopIds(List<Long> shopIds);

    Result<List<ShopInfoDto>> queryShopInfoByShopIds(List<Long> shopIds);

    /**
     * 模拟查询商品c端实际vat费率
     */
    BigDecimal simulateGetVatRateByGoodsId(Long goodsId, String country);

    void importBrand(InputStream inputStream);

    void batchRefreshPriceByRate(RefreshGoodsPriceCondition condition);
}
