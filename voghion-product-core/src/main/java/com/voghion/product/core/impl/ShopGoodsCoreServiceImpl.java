package com.voghion.product.core.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.lang.exception.CustomException;
import com.colorlight.base.utils.CheckUtils;
import com.colorlight.translate.enums.AwsLang;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.onlest.GoodsSyncModel;
import com.voghion.product.api.dto.*;
import com.voghion.product.api.enums.GoodsEditTypeEnums;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.api.enums.SaleableCountryEnum;
import com.voghion.product.client.ActivityRemoteClientFactory;
import com.voghion.product.client.TranslateClientFactory;
import com.voghion.product.core.*;
import com.voghion.product.listener.*;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.enums.*;
import com.voghion.product.model.po.*;
import com.voghion.product.model.vo.GoodsFreightVO;
import com.voghion.product.model.vo.LogisticsPropertyVo;
import com.voghion.product.model.vo.ProductInfoInput;
import com.voghion.product.model.vo.condition.LockGoodsUpdateCondition;
import com.voghion.product.model.vo.condition.UpdateLockLabelCondition;
import com.voghion.product.mq.MqDelayLevel;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.util.GoodsInfoUtils;
import com.voghion.product.util.LogUtils;
import com.voghion.product.utils.CommonConstants;
import com.voghion.product.utils.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShopGoodsCoreServiceImpl extends AbstractCommonServiceImpl implements ShopGoodsCoreService {


    @Resource
    ShopAddGoodsCoreService shopAddGoodsCoreService;

    @Resource
    GoodsService goodsService;

    @Resource
    GoodsItemService goodsItemService;

    @Resource
    ProductSkuService productSkuService;


    @Autowired
    private TransactionTemplate transactionTemplate;

    @Resource
    private SensitiveWordsCoreServiceImpl sensitiveWordsCoreService;

    @Resource
    private UpdateGoodsInfoCoreService updateGoodsInfoCoreService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private RedisApi redisApi;

    @Autowired
    private MqSender mqSender;

    @Resource
    private SkuLimitPriceCoreService skuLimitPriceCoreService;

    @Resource
    private GoodsFreightService goodsFreightService;

    @Resource
    private FaMerchantsApplyService faMerchantsApplyService;

    @Resource
    private ListingFollowGoodsService listingFollowGoodsService;

    @Resource
    private GoodsEditInfoService goodsEditInfoService;

    @Resource
    private GoodsEditInfoDetailService goodsEditInfoDetailService;

    @Resource
    private RecommendCoreService recommendCoreService;

    @Resource
    private GoodsLockInfoService goodsLockInfoService;

    @Resource
    private GoodsLogisticsCoreService goodsLogisticsCoreService;

    @Resource
    private GoodsExtConfigService goodsExtConfigService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private GoodsLockCoreService goodsLockCoreService;

    @Resource
    private ActivityRemoteClientFactory activityRemoteClientFactory;

    @Resource
    private ActivityOriginalPriceService activityOriginalPriceService;

    @Resource
    private MerchantsBrandMarkService merchantsBrandMarkService;

    @Resource
    private TranslateClientFactory translateClientFactory;

    @Override
    public List<String> batchImportGoods(List<GoodsImportVO> importVO) throws Exception {

        //因为是批量导入，首先得判断每一个新商品
        GoodsInfoInputVO goodsInfoInputVO = new GoodsInfoInputVO();

        List<String> feedBack = new ArrayList<>();

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String voghionToken = request.getHeader("clientInfo");
        // 查询redis
        String vohionTokenStr = redisApi.get(voghionToken);
        // 查看api权限
        String shopName = "";
        Long shopId = null;
        if (StringUtils.isNotBlank(vohionTokenStr)) {
            JSONObject json = JSONObject.parseObject(vohionTokenStr);
            JSONObject user = JSONObject.parseObject(json.getJSONObject("user").toJSONString());
            shopId = user.getLong("shopId");
            shopName = user.getString("shopName");
            if (shopId != null && shopId > 0) {
                goodsInfoInputVO.setShopId(shopId);
                goodsInfoInputVO.setStoreName(shopName);
            } else {
                CheckUtils.check(true, ProductResultCode.IMPORT_ERROR);
            }
            log.info("店铺id为 {}", shopId);
        }

        String key = shopName + shopId.toString();
        boolean haskey = false;//redisApi.hasKey(key);
        if (haskey) {
            CheckUtils.check(true, ProductResultCode.IMPORT_ERROR);
        } else
            redisApi.set(key, key, 120);

        CheckUtils.notNull(goodsInfoInputVO, ProductResultCode.NULL_ERROR);

        Map<String, List<GoodsImportVO>> goodsMap = Maps.newConcurrentMap();
        for (GoodsImportVO goodsImportVO : importVO) {
            try {
                List<GoodsImportVO> goodsImportVOList = goodsMap.get(goodsImportVO.getItemCode());
                if (null == goodsImportVOList) {
                    goodsImportVOList = Lists.newArrayList();
                } else {
                    boolean exist = false;
                    for (GoodsImportVO vo : goodsImportVOList) {
                        if (StringUtils.equals(vo.getSpecs1(), goodsImportVO.getSpecs1()) && StringUtils.equals(vo.getSpecs2(), goodsImportVO.getSpecs2()) && StringUtils.equals(vo.getSpecs3(), goodsImportVO.getSpecs3())) {
                            exist = true;
                            break;
                        }
                    }
                    if (exist) {
                        continue;
                    }
                }

                goodsImportVOList.add(goodsImportVO);
                goodsMap.put(goodsImportVO.getItemCode(), goodsImportVOList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.selectById(shopId);
        CheckUtils.check(Objects.nonNull(faMerchantsApply) && 0 == faMerchantsApply.getIsDisable(), ProductResultCode.SHOP_IS_LOCK);
        GoodsDTO goodsDTO = new GoodsDTO();
        goodsDTO.setShopId(shopId);
        Integer count = goodsService.selectCountGoods(goodsDTO);
        int importGoodsSize = goodsMap.keySet().size();
        CheckUtils.check(count + importGoodsSize > faMerchantsApply.getLimitGoodsCount(), ProductResultCode.GOODS_COUNT_LIMIT_EXCEED);

        for (String itemCode : goodsMap.keySet()) {
            try {
                List<GoodsImportVO> goodsImportVOList = goodsMap.get(itemCode);
                log.info("开始解析:{}",JSONObject.toJSONString(goodsImportVOList));
                BigDecimal minPrice = null;
                BigDecimal maxPrice = null;

                Map<String, List<PropertyValueVO>> propertyMap = Maps.newConcurrentMap();
                String prop1 = null;
                String prop2 = null;
                String prop3 = null;

                String goodsImages = null;
                String goodsDetailImages = null;
                String mainImage = null;
                List<GoodsItemVO> skuList = Lists.newArrayList();
                Long categoryId = null;
                String goodsName = null;
                Integer logisticsProperty = null;
                String description = null;
                Long sizeChartTemplateId = null;
                String weight = null;
                String length = null;
                String width = null;
                String height = null;
                Long brandId = null;
                PackageSizeVO goodsPackageSzie = null;
                for (GoodsImportVO vo : goodsImportVOList) {
                    String sp1 = vo.getSpecs1();
                    String sp2 = vo.getSpecs2();
                    String sp3 = vo.getSpecs3();
                    GoodsItemVO goodsItemVO = new GoodsItemVO();
                    if (StringUtils.isNotEmpty(sp1)) {
                        sp1 = sp1.toLowerCase();
                    }
                    if (StringUtils.isNotEmpty(sp2)) {
                        sp2 = sp2.toLowerCase();
                    }
                    if (StringUtils.isNotEmpty(sp3)) {
                        sp3 = sp3.toLowerCase();
                    }

                    String spImg1 = vo.getSpecs1img();
                    String spImg2 = vo.getSpecs2img();
                    String spImg3 = vo.getSpecs3img();

                    String skuDesc;

                    if (StringUtils.isBlank(mainImage)) {
                        mainImage = vo.getMainImage();
                    }

                    if (StringUtils.isBlank(goodsImages)) {
                        goodsImages = vo.getGoodsImages();
                    }


                    if (StringUtils.isBlank(goodsDetailImages)) {
                        goodsDetailImages = vo.getDetailsImgs();
                    }


                    if (StringUtils.isBlank(goodsName)) {
                        goodsName = vo.getName();
                    }

                    if (StringUtils.isBlank(description)) {
                        description = vo.getDescription();
                    }

                    if (logisticsProperty == null) {
                        logisticsProperty = vo.getLogisticsProperty();
                    }

                    if (sizeChartTemplateId == null) {
                        sizeChartTemplateId = vo.getSizeChartTemplateId();
                    }


                    if (StringUtils.isBlank(sp1) && (StringUtils.isNotBlank(sp2) || StringUtils.isNotBlank(sp3))) {
                        break;
                    }

                    if (StringUtils.isBlank(sp1)) {
                        skuDesc = "Default";
                    } else {
                        String[] args = sp1.split(":");
                        if (args.length != 2) {
                            break;
                        }
                        skuDesc = args[1];
                    }

                    if (StringUtils.isNotBlank(sp2)) {
                        String[] args = sp2.split(":");
                        if (args.length != 2) {
                            break;
                        }
                        skuDesc += "&gt;" + args[1];
                    }
                    if (StringUtils.isNotBlank(sp3)) {
                        String[] args = sp3.split(":");
                        if (args.length != 2) {
                            break;
                        }
                        skuDesc += "&gt;" + args[1];
                    }
                    //log.info("解析当前item:{}",JSONObject.toJSONString(vo));
                    prop1 = gernatePropertyMap(sp1, spImg1, propertyMap);
                    prop2 = gernatePropertyMap(sp2, spImg2, propertyMap);
                    prop3 = gernatePropertyMap(sp3, spImg3, propertyMap);

                    if (null == prop1) {
                        log.info("prop1为空，取默认规格 sp1:{}, propertyMap:{}", sp1, JSON.toJSONString(propertyMap));
                        prop1 = "Default";
                        PropertyValueVO propertyValueVO = new PropertyValueVO();
                        propertyValueVO.setValue("Default");
                        propertyValueVO.setOrginalValue("Default");
                        List<PropertyValueVO> propertyVOList = Lists.newArrayList();
                        propertyVOList.add(propertyValueVO);
                        propertyMap.put("Default", propertyVOList);
                    }

                    BigDecimal skuPrice = vo.getSkuPrice();
                    if (null == minPrice) {
                        minPrice = vo.getSkuPrice();
                    }

                    if (null == maxPrice) {
                        maxPrice = vo.getSkuPrice();
                    }


                    if (skuPrice.compareTo(maxPrice) > 0) {
                        maxPrice = skuPrice;
                    }

                    if (skuPrice.compareTo(minPrice) < 0) {
                        minPrice = skuPrice;
                    }

                    goodsItemVO.setName(goodsName);
                    goodsItemVO.setPrice(priceGernate(skuPrice));
                    goodsItemVO.setMarketPrice(marketPriceRuleGernate(skuPrice));
                    goodsItemVO.setOrginalMarketPrice(skuPrice);
                    goodsItemVO.setPvalueDesc(skuDesc);
                    goodsItemVO.setOrginalPrice(skuPrice);
                    goodsItemVO.setStock(vo.getStock());
                    goodsItemVO.setWeight(vo.getWeight());
                    goodsItemVO.setSkuImage(vo.getSkuImg());
                    if (StringUtils.isBlank(goodsItemVO.getSkuImage()) && StringUtils.isNotBlank(spImg1)) {
                        goodsItemVO.setSkuImage(spImg1);
                    }
                    goodsItemVO.setOriginalSkuId(vo.getSkuId());
                    goodsItemVO.setOriginalProductId(itemCode);
                    goodsItemVO.setMinPurchaseQuantity(1);
                    if (StringUtils.isNotBlank(vo.getPackageSize())) {
                        PackageSizeVO packageSizeVO = new PackageSizeVO();
                        String[] s = vo.getPackageSize().split("\\*");
                        packageSizeVO.setLength(s[0]);
                        packageSizeVO.setWidth(s[1]);
                        packageSizeVO.setHeight(s[2]);
                        goodsItemVO.setPackageSize(packageSizeVO);

                        if (length == null || width == null | height == null) {
                            length = packageSizeVO.getLength();
                            width = packageSizeVO.getWidth();
                            height = packageSizeVO.getHeight();
                        }
                    }

                    if (null == categoryId) {
                        categoryId = vo.getCategoryId();
                    }

                    if (null == goodsPackageSzie) {
                        goodsPackageSzie = goodsItemVO.getPackageSize();
                    }

                    if (null == weight) {
                        weight = goodsItemVO.getWeight();
                    }

                    if (null == brandId) {
                        brandId = vo.getBrandId();
                    }
                    skuList.add(goodsItemVO);
                }

                goodsInfoInputVO.setSkuList(skuList);

                if (StringUtils.isBlank(prop1) || null == propertyMap) {
                    feedBack.add(String.format("商品(%s):属性缺失", itemCode));
                    log.info("商品导入失败,属性缺失:{}", itemCode);
                    continue;
                }
                goodsInfoInputVO.setProperties(gernatePropertyVOListByMap(propertyMap, prop1, prop2, prop3));

                if (StringUtils.isBlank(mainImage) || !mainImage.contains("http")) {
                    feedBack.add(String.format("商品(%s):主图异常", itemCode));
                    log.info("当前异常数据-主图异常:{}", itemCode);
                    continue;
                }
                if (StringUtils.isBlank(goodsDetailImages) || StringUtils.isBlank(goodsImages)) {
                    feedBack.add(String.format("商品(%s):轮播图/详情图缺失", itemCode));
                    log.info("当前异常数据-轮播或详情异常:{}", itemCode);
                    continue;
                }

                List<String> detailImgList = Lists.newArrayList();
                List<String> goodsImageList = Lists.newArrayList();
                String[] detailsImgs = goodsDetailImages.replaceAll("，", ",").split(",");
                String[] goodsImageStr = goodsImages.replaceAll("，", ",").split(",");


                goodsInfoInputVO.setCategoryId(categoryId);
                goodsInfoInputVO.setWeight(weight);
                goodsInfoInputVO.setPackageSize(goodsPackageSzie);
                detailImgList.addAll(Arrays.asList(detailsImgs));
                goodsImageList.addAll(Arrays.asList(goodsImageStr));
                goodsInfoInputVO.setName(goodsName);
                goodsInfoInputVO.setOrginalName(goodsName);
                goodsInfoInputVO.setMainImage(mainImage);
                goodsInfoInputVO.setDetailsImgs(detailImgList);
                goodsInfoInputVO.setGoodsImages(goodsImageList);
                goodsInfoInputVO.setMaxPrice(priceGernate(maxPrice));
                goodsInfoInputVO.setMinPrice(priceGernate(minPrice));
                goodsInfoInputVO.setShopId(shopId);
                goodsInfoInputVO.setStoreName(shopName);
                goodsInfoInputVO.setShowPrice(priceGernate(minPrice));
                goodsInfoInputVO.setMaxMarketPrice(marketPriceRuleGernate(maxPrice));
                goodsInfoInputVO.setMinMarketPrice(marketPriceRuleGernate(minPrice));
                goodsInfoInputVO.setOrignalMaxMarketPrice(maxPrice);
                goodsInfoInputVO.setOrignalMinMarketPrice(minPrice);
                goodsInfoInputVO.setOrignalMaxPrice(maxPrice);
                goodsInfoInputVO.setOrignalMinPrice(minPrice);
                goodsInfoInputVO.setShowMarketPrice(minPrice);
                goodsInfoInputVO.setItemCode(itemCode);
                goodsInfoInputVO.setDescription(description);
                goodsInfoInputVO.setLogisticsProperty(logisticsProperty == null ? 1 : logisticsProperty);
                goodsInfoInputVO.setSizeChartTemplateId(sizeChartTemplateId);
                goodsInfoInputVO.setOperationLogType(OperationLogTypeEnums.IMPORT_GOODS.getCode());
                goodsInfoInputVO.setBrandId(brandId);
                String errorMessage = shopAddGoodsCoreService.addGoods(goodsInfoInputVO).getValue();
                if (StringUtils.isNotBlank(errorMessage)) {
                    feedBack.add(errorMessage);
                }
            } catch (CustomException e) {
                feedBack.add(String.format("商品(%s):%s", itemCode, e.getBusinessResultCode().getMsg()));
                log.error("{} 新增失败(校验未通过)", itemCode, e);
            } catch (Exception e) {
                feedBack.add(String.format("商品(%s):新增异常", itemCode));
                log.error("{} 解析失败:{}", itemCode, e.getMessage(), e);
            }
        }

        redisApi.del(key);
        return feedBack;
    }

    @Override
    public Map<String, String> batchImportGoodsNew(List<GoodsImportVO> importVO, Long shopId, String shopName, Map<String, Long> successGoodsIdMap) {
//        List<String> feedBack = new ArrayList<>();
        Map<String, String> errorMsgMap = Maps.newHashMap();
        String key = shopName + shopId.toString();

        Map<String, List<GoodsImportVO>> goodsMap = Maps.newConcurrentMap();
        for (GoodsImportVO goodsImportVO : importVO) {
            try {
                List<GoodsImportVO> goodsImportVOList = goodsMap.get(goodsImportVO.getItemCode());
                if (null == goodsImportVOList) {
                    goodsImportVOList = Lists.newArrayList();
                } else {
                    boolean exist = false;
                    for (GoodsImportVO vo : goodsImportVOList) {
                        if (StringUtils.equals(vo.getSpecs1(), goodsImportVO.getSpecs1()) && StringUtils.equals(vo.getSpecs2(), goodsImportVO.getSpecs2()) && StringUtils.equals(vo.getSpecs3(), goodsImportVO.getSpecs3())) {
                            exist = true;
                            break;
                        }
                    }
                    if (exist) {
                        continue;
                    }
                }

                goodsImportVOList.add(goodsImportVO);
                goodsMap.put(goodsImportVO.getItemCode(), goodsImportVOList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.selectById(shopId);
        CheckUtils.check(Objects.nonNull(faMerchantsApply) && 0 == faMerchantsApply.getIsDisable(), ProductResultCode.SHOP_IS_LOCK);
        GoodsDTO goodsDTO = new GoodsDTO();
        goodsDTO.setShopId(shopId);
        Integer count = goodsService.selectCountGoods(goodsDTO);
        int importGoodsSize = goodsMap.keySet().size();
        CheckUtils.check(count + importGoodsSize > faMerchantsApply.getLimitGoodsCount(), ProductResultCode.GOODS_COUNT_LIMIT_EXCEED);

        Map<String, String> translateMap = Maps.newHashMap();
        for (String itemCode : goodsMap.keySet()) {
            try {
                //因为是批量导入，首先得判断每一个新商品
                GoodsInfoInputVO goodsInfoInputVO = new GoodsInfoInputVO();
                goodsInfoInputVO.setShopId(shopId);
                goodsInfoInputVO.setStoreName(shopName);

                List<GoodsImportVO> goodsImportVOList = goodsMap.get(itemCode);
                log.info("开始解析:{}",JSONObject.toJSONString(goodsImportVOList));

                checkImportGoodsParam(goodsImportVOList);

                BigDecimal minPrice = null;
                BigDecimal maxPrice = null;

                Map<String, List<PropertyValueVO>> propertyMap = Maps.newConcurrentMap();
                String prop1 = null;
                String prop2 = null;
                String prop3 = null;

                String goodsImages = null;
                String goodsDetailImages = null;
                String mainImage = null;
                List<GoodsItemVO> skuList = Lists.newArrayList();
                Long categoryId = null;
                String goodsName = null;
                Integer logisticsProperty = null;
                String description = null;
                Long sizeChartTemplateId = null;
                String weight = null;
                String length = null;
                String width = null;
                String height = null;
                Long brandId = null;
                PackageSizeVO goodsPackageSzie = null;
                for (GoodsImportVO vo : goodsImportVOList) {
                    String sp1 = vo.getSpecs1();
                    String sp2 = vo.getSpecs2();
                    String sp3 = vo.getSpecs3();
                    GoodsItemVO goodsItemVO = new GoodsItemVO();
                    if (StringUtils.isNotEmpty(sp1)) {
                        sp1 = sp1.toLowerCase().replace("-"," ");
                    }
                    if (StringUtils.isNotEmpty(sp2)) {
                        sp2 = sp2.toLowerCase().replace("-"," ");
                    }
                    if (StringUtils.isNotEmpty(sp3)) {
                        sp3 = sp3.toLowerCase().replace("-"," ");
                    }

                    String spImg1 = vo.getSpecs1img();
                    String spImg2 = vo.getSpecs2img();
                    String spImg3 = vo.getSpecs3img();

                    String skuDesc;

                    if (StringUtils.isBlank(mainImage)) {
                        mainImage = vo.getMainImage();
                    }

                    if (StringUtils.isBlank(goodsImages)) {
                        goodsImages = vo.getGoodsImages();
                    }


                    if (StringUtils.isBlank(goodsDetailImages)) {
                        goodsDetailImages = vo.getDetailsImgs();
                    }


                    if (StringUtils.isBlank(goodsName)) {
                        goodsName = vo.getName();
                    }

                    if (StringUtils.isBlank(description)) {
                        description = vo.getDescription();
                    }

                    if (logisticsProperty == null) {
                        logisticsProperty = vo.getLogisticsProperty();
                    }

                    if (sizeChartTemplateId == null) {
                        sizeChartTemplateId = vo.getSizeChartTemplateId();
                    }

                    if (StringUtils.isBlank(sp1) && (StringUtils.isNotBlank(sp2) || StringUtils.isNotBlank(sp3))) {
                        break;
                    }

                    Map<String, String> skuProperty = Maps.newConcurrentMap();
                    if (StringUtils.isBlank(sp1)) {
                        skuDesc = "Default";
                        skuProperty.put("Default", "Default");
                    } else {
                        String[] args = sp1.split(":");
                        if (args.length != 2) {
                            break;
                        }

                        String args0 = obtainEnContent(args[0], translateMap);
                        String args1 = obtainEnContent(args[1], translateMap);
                        skuDesc = args1;
                        skuProperty.put(args0, args1);
                    }

                    if (StringUtils.isNotBlank(sp2)) {
                        String[] args = sp2.split(":");
                        if (args.length != 2) {
                            break;
                        }

                        String args0 = obtainEnContent(args[0], translateMap);
                        String args1 = obtainEnContent(args[1], translateMap);
                        skuDesc += "&gt;" + args1;
                        skuProperty.put(args0, args1);
                    }
                    if (StringUtils.isNotBlank(sp3)) {
                        String[] args = sp3.split(":");
                        if (args.length != 2) {
                            break;
                        }

                        String args0 = obtainEnContent(args[0], translateMap);
                        String args1 = obtainEnContent(args[1], translateMap);
                        skuDesc += "&gt;" + args1;
                        skuProperty.put(args0, args1);
                    }
                    //log.info("解析当前item:{}",JSONObject.toJSONString(vo));
                    prop1 = gernatePropertyMap(sp1, spImg1, propertyMap);
                    prop2 = gernatePropertyMap(sp2, spImg2, propertyMap);
                    prop3 = gernatePropertyMap(sp3, spImg3, propertyMap);

                    if (null == prop1) {
                        log.info("prop1为空，取默认规格 sp1:{}, propertyMap:{}", sp1, JSON.toJSONString(propertyMap));
                        prop1 = "Default";
                        PropertyValueVO propertyValueVO = new PropertyValueVO();
                        propertyValueVO.setValue("Default");
                        propertyValueVO.setOrginalValue("Default");
                        List<PropertyValueVO> propertyVOList = Lists.newArrayList();
                        propertyVOList.add(propertyValueVO);
                        propertyMap.put("Default", propertyVOList);
                    }

                    BigDecimal skuPrice = vo.getSkuPrice();
                    if (null == minPrice) {
                        minPrice = vo.getSkuPrice();
                    }

                    if (null == maxPrice) {
                        maxPrice = vo.getSkuPrice();
                    }


                    if (skuPrice.compareTo(maxPrice) > 0) {
                        maxPrice = skuPrice;
                    }

                    if (skuPrice.compareTo(minPrice) < 0) {
                        minPrice = skuPrice;
                    }

                    goodsItemVO.setName(goodsName);
                    goodsItemVO.setPrice(priceGernate(skuPrice));
                    goodsItemVO.setMarketPrice(marketPriceRuleGernate(skuPrice));
                    goodsItemVO.setOrginalMarketPrice(skuPrice);
                    goodsItemVO.setPvalueDesc(skuDesc);
                    goodsItemVO.setOrginalPrice(skuPrice);
                    goodsItemVO.setStock(vo.getStock());
                    goodsItemVO.setWeight(vo.getWeight());
                    goodsItemVO.setSkuImage(vo.getSkuImg());
                    if (StringUtils.isBlank(goodsItemVO.getSkuImage()) && StringUtils.isNotBlank(spImg1)) {
                        goodsItemVO.setSkuImage(spImg1);
                    }

                    goodsItemVO.setSkuProperty(skuProperty);
                    goodsItemVO.setOriginalSkuId(vo.getSkuId());
                    goodsItemVO.setOriginalProductId(itemCode);
                    goodsItemVO.setMinPurchaseQuantity(1);
                    if (StringUtils.isNotBlank(vo.getPackageSize())) {
                        PackageSizeVO packageSizeVO = new PackageSizeVO();
                        String[] s = vo.getPackageSize().split("\\*");
                        if (s.length == 3) {
                            packageSizeVO.setLength(s[0]);
                            packageSizeVO.setWidth(s[1]);
                            packageSizeVO.setHeight(s[2]);
                            goodsItemVO.setPackageSize(packageSizeVO);

                            if (length == null || width == null | height == null) {
                                length = packageSizeVO.getLength();
                                width = packageSizeVO.getWidth();
                                height = packageSizeVO.getHeight();
                            }
                        }
                    }

                    if (null == categoryId) {
                        categoryId = vo.getCategoryId();
                    }

                    if (null == goodsPackageSzie) {
                        goodsPackageSzie = goodsItemVO.getPackageSize();
                    }

                    if (null == weight) {
                        weight = goodsItemVO.getWeight();
                    }

                    if (null == brandId) {
                        brandId = vo.getBrandId();
                    }
                    skuList.add(goodsItemVO);
                }

                goodsInfoInputVO.setSkuList(skuList);

                if (StringUtils.isBlank(prop1) || null == propertyMap) {
                    errorMsgMap.put(itemCode, String.format("商品(%s):规格缺失", itemCode));
                    log.info("商品导入失败,规格缺失:{}", itemCode);
                    continue;
                }

                List<String> propList = Lists.newArrayList(prop1, prop2, prop3).stream().filter(Objects::nonNull).collect(Collectors.toList());
                if (Sets.newHashSet(propList).size() != propList.size()) {
                    errorMsgMap.put(itemCode, String.format("商品(%s):属性名重复", itemCode));
                    log.info("当前异常数据-属性名重复:{}", itemCode);
                    continue;
                }

                goodsInfoInputVO.setProperties(gernatePropertyVOListByMap(propertyMap, prop1, prop2, prop3));

                if (StringUtils.isBlank(mainImage) || !mainImage.contains("http")) {
                    errorMsgMap.put(itemCode, String.format("商品(%s):主图异常", itemCode));
                    log.info("当前异常数据-主图异常:{}", itemCode);
                    continue;
                }
                if (StringUtils.isBlank(goodsDetailImages) || StringUtils.isBlank(goodsImages)) {
                    errorMsgMap.put(itemCode, String.format("商品(%s):轮播图/详情图缺失", itemCode));
                    log.info("当前异常数据-轮播或详情异常:{}", itemCode);
                    continue;
                }

                List<String> detailImgList = Lists.newArrayList();
                List<String> goodsImageList = Lists.newArrayList();
                String[] detailsImgs = goodsDetailImages.replaceAll("，", ",").split(",");
                String[] goodsImageStr = goodsImages.replaceAll("，", ",").split(",");

                if (logisticsProperty == null) {
                    List<LogisticsPropertyVo> logisticsPropertyVos = goodsLogisticsCoreService.queryByCategoryId(categoryId);
                    logisticsProperty = logisticsPropertyVos.stream().map(LogisticsPropertyVo::getCode).findFirst().orElse(1);
                }

                if (brandId != null && brandId != 0) {
                    MerchantsBrandMark merchantsBrandMark = merchantsBrandMarkService.getById(brandId);
                    if (merchantsBrandMark != null) {
                        brandId = merchantsBrandMark.getMerchantsBrandStoreId();
                    }
                }

                goodsInfoInputVO.setCategoryId(categoryId);
                goodsInfoInputVO.setWeight(weight);
                goodsInfoInputVO.setPackageSize(goodsPackageSzie);
                detailImgList.addAll(Arrays.asList(detailsImgs));
                goodsImageList.addAll(Arrays.asList(goodsImageStr));
                goodsInfoInputVO.setName(goodsName);
                goodsInfoInputVO.setOrginalName(goodsName);
                goodsInfoInputVO.setMainImage(mainImage);
                goodsInfoInputVO.setDetailsImgs(detailImgList);
                goodsInfoInputVO.setGoodsImages(goodsImageList);
                goodsInfoInputVO.setMaxPrice(priceGernate(maxPrice));
                goodsInfoInputVO.setMinPrice(priceGernate(minPrice));
                goodsInfoInputVO.setShopId(shopId);
                goodsInfoInputVO.setStoreName(shopName);
                goodsInfoInputVO.setShowPrice(priceGernate(minPrice));
                goodsInfoInputVO.setMaxMarketPrice(marketPriceRuleGernate(maxPrice));
                goodsInfoInputVO.setMinMarketPrice(marketPriceRuleGernate(minPrice));
                goodsInfoInputVO.setOrignalMaxMarketPrice(maxPrice);
                goodsInfoInputVO.setOrignalMinMarketPrice(minPrice);
                goodsInfoInputVO.setOrignalMaxPrice(maxPrice);
                goodsInfoInputVO.setOrignalMinPrice(minPrice);
                goodsInfoInputVO.setShowMarketPrice(minPrice);
                goodsInfoInputVO.setItemCode(itemCode);
                goodsInfoInputVO.setDescription(description);
                goodsInfoInputVO.setLogisticsProperty(logisticsProperty == null ? 1 : logisticsProperty);
                goodsInfoInputVO.setSizeChartTemplateId(sizeChartTemplateId);
                goodsInfoInputVO.setOperationLogType(OperationLogTypeEnums.IMPORT_GOODS.getCode());
                goodsInfoInputVO.setBrandId(brandId);
                goodsInfoInputVO.setChannel(ChannelEnums.EXCEL_IMPORT.getCode());
                goodsInfoInputVO.setOperator("EXCEL_IMPORT");
                String errorMessage = shopAddGoodsCoreService.addGoods(goodsInfoInputVO).getValue();
                if (StringUtils.isNotBlank(errorMessage)) {
                    errorMsgMap.put(itemCode, errorMessage);
                } else if (goodsInfoInputVO.getId() != null) {
                    successGoodsIdMap.put(itemCode, goodsInfoInputVO.getId());
                }
            } catch (CustomException e) {
                errorMsgMap.put(itemCode, String.format("商品(%s):%s", itemCode, e.getBusinessResultCode().getMsg()));
                log.info("{} 新增失败(校验未通过):{}", itemCode, e.getBusinessResultCode().getMsg(), e);
            } catch (Exception e) {
                errorMsgMap.put(itemCode, String.format("商品(%s):新增异常", itemCode));
                log.info("{} 解析失败:{}", itemCode, e.getMessage(), e);
            }
        }

        redisApi.del(key);
        return errorMsgMap;
    }

    private String obtainEnContent(String content, Map<String, String> translateMap) {
        if (GoodsInfoUtils.containZHCharacter(content)) {
            String translate = translateMap.get(content);
            if (StringUtils.isNotBlank(translate)) {
                return translate;
            }

            log.info("当前内容不为英文开始翻译:{}", content);
            String result = translateClientFactory.translate(content, AwsLang.ZH, AwsLang.EN);
            result = GoodsInfoUtils.translateRepair(result);
            translateMap.put(content, result);
            return result;
        }

        return content;
    }

    private void checkImportGoodsParam(List<GoodsImportVO> goodsImportVOList) {
        for (GoodsImportVO vo : goodsImportVOList) {
            CheckUtils.notNull(vo.getItemCode(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "商品编号"));
            CheckUtils.notNull(vo.getName(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "商品名称"));
            CheckUtils.notNull(vo.getCategoryId(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "类目信息"));
            CheckUtils.notNull(vo.getMainImage(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "商品主图"));
            CheckUtils.notNull(vo.getWeight(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "重量"));
            CheckUtils.notNull(vo.getSpecs1(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "规格1"));
            CheckUtils.notNull(vo.getSkuId(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "商品SKU编号"));
            CheckUtils.notNull(vo.getSkuPrice(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "商品SKU价格"));
            CheckUtils.notNull(vo.getStock(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "商品SKU库存"));
            CheckUtils.notNull(vo.getGoodsImages(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "商品轮播图"));
            CheckUtils.notNull(vo.getDetailsImgs(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "商品详情图"));
            CheckUtils.notNull(vo.getBrandId(), CustomResultCode.fill(ProductResultCode.GOODS_IMPORT_PARAM_MISS, "品牌ID"));

//            if (StringUtils.isBlank(vo.getItemCode())
//                    || StringUtils.isBlank(vo.getName())
//                    || Objects.isNull(vo.getCategoryId())
//                    || StringUtils.isBlank(vo.getMainImage())
//                    || StringUtils.isBlank(vo.getWeight())
//                    || StringUtils.isBlank(vo.getSpecs1())
//                    || StringUtils.isBlank(vo.getSkuId())
//                    || Objects.isNull(vo.getSkuPrice())
//                    || Objects.isNull(vo.getStock())
//                    || StringUtils.isBlank(vo.getGoodsImages())
//                    || StringUtils.isBlank(vo.getDetailsImgs())
//                    || Objects.isNull(vo.getBrandId())
//            ) {
//                CheckUtils.check(true, ProductResultCode.GOODS_IMPORT_PARAM_MISS);
//            }
        }
    }


    @Override
    public List<String> AEImport(List<AEImportVO> importVO) throws Exception {
        Map<String, List<AEImportVO>> goodsMap = Maps.newConcurrentMap();
        for (AEImportVO goodsImportVO : importVO) {
            try {
                List<AEImportVO> goodsImportVOList = goodsMap.get(goodsImportVO.getItemCode());
                if (null == goodsImportVOList) {
                    goodsImportVOList = Lists.newArrayList();
                }

                goodsImportVOList.add(goodsImportVO);
                goodsMap.put(goodsImportVO.getItemCode(), goodsImportVOList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        GoodsInfoInputVO goodsInfoInputVO = new GoodsInfoInputVO();

        for (String itemCode : goodsMap.keySet()) {
            try {
                List<AEImportVO> list = goodsMap.get(itemCode);
                BigDecimal price = null;
                Map<String, List<PropertyValueVO>> propertyMap = Maps.newConcurrentMap();
                Map<String, Set<String>> propertyNameMap = Maps.newConcurrentMap();
                String prop1 = null;
                String prop2 = null;
                String sp1 = null;
                String sp2 = null;
                BigDecimal defaultDelivery = null;
                List<GoodsItemVO> skuList = Lists.newArrayList();
                String cateName = null;
                String goodsName = null;
                List<String> goodsImages = null;
                List<String> goodsDetailImages = null;
                String mainImage = null;
                String weight = null;
                PackageSizeVO goodsPackageSzie = null;

                for (int i = 0; i < list.size(); i++) {
                    AEImportVO vo = list.get(i);
                    if (i == 0) {
                        String excelCate = vo.getCategoryString();
                        String s1 = null;
                        String cateId = null;
                        if (excelCate.contains("(")) {
                            s1 = excelCate.substring(excelCate.indexOf("("));
                            s1.substring(s1.indexOf(":"));
                            cateId = s1.substring(s1.indexOf(":") + 1, s1.indexOf(")"));
                            cateName = excelCate.substring(0, excelCate.indexOf("("));
                            goodsInfoInputVO.setCategoryId(Long.valueOf(cateId.trim()));
                        }
                        weight = vo.getWeight();
                        goodsName = vo.getGoodsName();
                        goodsImages = Arrays.asList(vo.getGoodsImages().split(","));
                        goodsDetailImages = Arrays.asList(vo.getDetailImgs().split(","));
                        mainImage = goodsImages.get(0);
                    }
                    GoodsItemVO goodsItemVO = new GoodsItemVO();
                    String skuDesc = "";
                    String skuSpecs = vo.getSkuSpecs();
                    if (StringUtils.isBlank(skuSpecs)) {
                        continue;
                    }
                    String[] split = skuSpecs.split(";");
                    sp1 = split[0].toLowerCase();
                    sp2 = split[1].toLowerCase();
                    final String[] split1 = sp1.split(":");
                    final String[] split2 = sp2.split(":");

                    if (split1.length != 2) {
                        break;
                    }
                    if (split2.length != 2) {
                        break;
                    }
                    skuDesc = split1[1] + "&gt;" + split2[1];

                    prop1 = gernatePropertyMapNew(sp1, vo.getSkuImg(), propertyMap, propertyNameMap);
                    prop2 = gernatePropertyMapNew(sp2, vo.getSkuImg(), propertyMap, propertyNameMap);
                    price = vo.getSkuPrice();
                    defaultDelivery = vo.getDefaultDelivery();
                    goodsItemVO.setName(goodsName);
                    goodsItemVO.setPrice(priceGernate(price));
                    goodsItemVO.setMarketPrice(marketPriceRuleGernate(price));
                    goodsItemVO.setOrginalMarketPrice(price);
                    goodsItemVO.setPvalueDesc(skuDesc);
                    goodsItemVO.setOrginalPrice(price);
                    goodsItemVO.setStock(vo.getStock());
                    goodsItemVO.setWeight(weight);
                    goodsItemVO.setSkuImage(vo.getSkuImg());
                    goodsItemVO.setOriginalSkuId(vo.getOriginalSkuId());
                    goodsItemVO.setOriginalProductId(vo.getItemCode());
                    goodsItemVO.setDefaultDelivery(defaultDelivery);
                    PackageSizeVO packageSizeVO = new PackageSizeVO();
                    packageSizeVO.setLength("1");
                    packageSizeVO.setWidth("1");
                    packageSizeVO.setHeight("1");
                    goodsItemVO.setPackageSize(packageSizeVO);
                    skuList.add(goodsItemVO);
                }

                if (StringUtils.isBlank(prop1) || null == propertyMap) {
                    log.info("当前异常数据-属性异常:{}", itemCode);
                    continue;
                }
                if (StringUtils.isBlank(mainImage) || !mainImage.contains("http")) {
                    log.info("当前异常数据-主图异常:{}", itemCode);
                    continue;
                }
                if ((CollectionUtils.isEmpty(goodsDetailImages)) || CollectionUtils.isEmpty(goodsImages)) {
                    log.info("当前异常数据-轮播或详情异常:{}", itemCode);
                    continue;
                }
                if (null == defaultDelivery || defaultDelivery.compareTo(BigDecimal.ZERO) <= 0) {
                    log.info("当前异常数据-默认运费异常{}", itemCode);
                    continue;
                }
                goodsInfoInputVO.setShopId(988879L);
                goodsInfoInputVO.setProperties(gernatePropertyVOListByMap(propertyMap, prop1, prop2, null));
                goodsInfoInputVO.setSkuList(skuList);
                goodsInfoInputVO.setCategoryString(cateName.trim());
                goodsInfoInputVO.setWeight(weight);
                goodsInfoInputVO.setPackageSize(goodsPackageSzie);
                goodsInfoInputVO.setName(goodsName);
                goodsInfoInputVO.setOrginalName(goodsName);
                goodsInfoInputVO.setMainImage(mainImage);
                goodsInfoInputVO.setDetailsImgs(goodsDetailImages);
                goodsInfoInputVO.setGoodsImages(goodsImages);
                goodsInfoInputVO.setMaxPrice(priceGernate(price));
                goodsInfoInputVO.setMinPrice(priceGernate(price));

                goodsInfoInputVO.setStoreName("BRAWNY CLOTHING");
                goodsInfoInputVO.setShowPrice(priceGernate(price));
                goodsInfoInputVO.setMaxMarketPrice(marketPriceRuleGernate(price));
                goodsInfoInputVO.setMinMarketPrice(marketPriceRuleGernate(price));
                goodsInfoInputVO.setOrignalMaxMarketPrice(price);
                goodsInfoInputVO.setOrignalMinMarketPrice(price);
                goodsInfoInputVO.setOrignalMaxPrice(price);
                goodsInfoInputVO.setOrignalMinPrice(price);
                goodsInfoInputVO.setShowMarketPrice(price);
                goodsInfoInputVO.setItemCode(itemCode);
                goodsInfoInputVO.setOperationLogType(OperationLogTypeEnums.AE_IMPORT_GOODS.getCode());
                shopAddGoodsCoreService.addGoods(goodsInfoInputVO);

            } catch (Exception e) {
                log.info("AE导入解析失败{}", itemCode);
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public List<String> FannoImport(List<FannoImportVO> importVO) throws Exception {
        GoodsInfoInputVO goodsInfoInputVO = new GoodsInfoInputVO();
        getShopMsg(goodsInfoInputVO);
        Boolean ourCate = false;
        Map<String, List<FannoImportVO>> goodsMap = Maps.newConcurrentMap();
        for (FannoImportVO goodsImportVO : importVO) {
            try {
                List<FannoImportVO> goodsImportVOList = goodsMap.get(goodsImportVO.getItemCode());
                if (null == goodsImportVOList) {
                    goodsImportVOList = Lists.newArrayList();
                }

                goodsImportVOList.add(goodsImportVO);
                goodsMap.put(goodsImportVO.getItemCode(), goodsImportVOList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        for (String orginalGoodsId : goodsMap.keySet()) {
            try {
                List<FannoImportVO> goodsImportVOList = goodsMap.get(orginalGoodsId);
                //log.info("开始解析:{}",JSONObject.toJSONString(goodsImportVOList));
                BigDecimal minPrice = null;
                BigDecimal maxPrice = null;
                List<String> goodsImageList = new ArrayList<>();
                Boolean dirtyData = false;

                Map<String, List<PropertyValueVO>> propertyMap = Maps.newConcurrentMap();
                String prop1 = null;
                String prop2 = null;
                String goodsImages = null;
                String mainImage = null;

                List<GoodsItemVO> skuList = Lists.newArrayList();
                String cateName = null;
                String goodsName = null;


                String weight = null;
                PackageSizeVO goodsPackageSzie = null;
                for (FannoImportVO vo : goodsImportVOList) {
                    String sp1 = "color:" + vo.getColor();
                    String sp2 = "size:" + vo.getSize();

                    GoodsItemVO goodsItemVO = new GoodsItemVO();
                    if (StringUtils.isNotEmpty(sp1)) {
                        sp1 = sp1.toLowerCase();
                    }
                    if (StringUtils.isNotEmpty(sp2)) {
                        sp2 = sp2.toLowerCase();
                    }
                    if (CollectionUtils.isEmpty(goodsImageList)) {
                        goodsImageList.add(vo.getImg1());
                        goodsImageList.add(vo.getImg2());
                        goodsImageList.add(vo.getImg3());
                        goodsImageList.add(vo.getImg4());
                        goodsImageList.add(vo.getImg5());
                        goodsImageList.add(vo.getImg6());
                        goodsImageList.add(vo.getImg7());
                        goodsImageList.add(vo.getImg8());
                    }
                    if (CollectionUtils.isEmpty(goodsImageList)) {
                        goodsImageList.add(vo.getMainImage());
                    }

                    String sp1Img = vo.getColorImg();

                    String skuDesc = sp1;

                    if (StringUtils.isBlank(mainImage)) {
                        mainImage = vo.getMainImage();
                    }
//                    if (StringUtils.isNotBlank(vo.getSizeImage())) {
//                        goodsDetailImages = vo.getSizeImage() + ",";
//                    }
//                    if (defaultDelivery == null) {
                    BigDecimal defaultDelivery = vo.getDefaultDelivery();
//                    }


                    if (StringUtils.isBlank(goodsName)) {
                        goodsName = vo.getGoodsName();
                    }


                    if (StringUtils.isBlank(sp1)) {
                        skuDesc = "Default";
                    } else {
                        String[] args = sp1.split(":");
                        if (args.length != 2) {
                            break;
                        }
                        skuDesc = args[1];
                    }

                    if (StringUtils.isNotBlank(sp2)) {
                        String[] args = sp2.split(":");
                        if (args.length != 2) {
                            break;
                        }
                        skuDesc += "&gt;" + args[1];
                    }

                    //log.info("解析当前item:{}",JSONObject.toJSONString(vo));
                    prop1 = gernatePropertyMap(sp1, sp1Img, propertyMap);
                    prop2 = gernatePropertyMap(sp2, null, propertyMap);

                    if (null == prop1) {
                        prop1 = "Default";
                        PropertyValueVO propertyValueVO = new PropertyValueVO();
                        propertyValueVO.setValue("Default");
                        propertyValueVO.setOrginalValue("Default");
                        List<PropertyValueVO> propertyVOList = Lists.newArrayList();
                        propertyVOList.add(propertyValueVO);
                        propertyMap.put("Default", propertyVOList);
                    }

                    BigDecimal skuPrice = vo.getSkuPrice();
                    if (null == minPrice) {
                        minPrice = vo.getSkuPrice();
                    }

                    if (null == maxPrice) {
                        maxPrice = vo.getSkuPrice();
                    }


                    if (skuPrice.compareTo(maxPrice) > 0) {
                        maxPrice = skuPrice;
                    }

                    if (skuPrice.compareTo(minPrice) < 0) {
                        minPrice = skuPrice;
                    }

                    goodsItemVO.setPrice(priceGernate(skuPrice));
                    goodsItemVO.setMarketPrice(marketPriceRuleGernate(skuPrice));
                    goodsItemVO.setOrginalMarketPrice(skuPrice);
                    goodsItemVO.setPvalueDesc(skuDesc);
                    goodsItemVO.setOrginalPrice(skuPrice);
                    goodsItemVO.setStock(vo.getStock());
                    double weight1 = Double.parseDouble(vo.getWeight());
                    weight1 = weight1 / 1000;
                    goodsItemVO.setWeight(String.valueOf(weight1));
                    goodsItemVO.setSkuImage(sp1Img);
                    goodsItemVO.setOriginalSkuId(vo.getOriginalSkuId());
                    goodsItemVO.setOriginalProductId(vo.getItemCode());
                    goodsItemVO.setDefaultDelivery(defaultDelivery);
                    if (null == defaultDelivery || defaultDelivery.compareTo(BigDecimal.ZERO) < 0) {
                        dirtyData = true;
                    }
                    if (StringUtils.isNotBlank(vo.getHeight())
                            && StringUtils.isNotBlank(vo.getLength())
                            && StringUtils.isNotBlank(vo.getWeight())) {
                        PackageSizeVO packageSizeVO = new PackageSizeVO();
                        packageSizeVO.setLength(vo.getLength());
                        packageSizeVO.setWidth(vo.getWidth());
                        packageSizeVO.setHeight(vo.getHeight());
                        goodsItemVO.setPackageSize(packageSizeVO);
                    }


                    if (StringUtils.isBlank(cateName)) {
                        String excelCate = vo.getCategoryString();
                        String cateId = null;
                        if (StringUtils.isNotBlank(excelCate)) {
                            if (excelCate.contains("(") && excelCate.contains(":") && excelCate.contains(")")) {
                                String s1 = excelCate.substring(excelCate.indexOf("("));
                                s1.substring(s1.indexOf(":"));
                                cateId = s1.substring(s1.indexOf(":") + 1, s1.indexOf(")"));
                                cateName = excelCate.substring(0, excelCate.indexOf("("));
                                goodsInfoInputVO.setCategoryId(Long.valueOf(cateId.trim()));
                                ourCate = true;
                            } else {
                                cateId = excelCate.substring(0, excelCate.indexOf("/"));
                                goodsInfoInputVO.setCategoryId(Long.valueOf(cateId));

                            }
                        }
                    }

                    if (null == goodsPackageSzie) {
                        goodsPackageSzie = goodsItemVO.getPackageSize();
                    }

                    if (null == weight) {
                        weight = goodsItemVO.getWeight();
                    }
                    skuList.add(goodsItemVO);
                }

                goodsInfoInputVO.setSkuList(skuList);
                if (StringUtils.isBlank(prop1) || null == propertyMap) {
                    log.info("当前异常数据-属性异常:{}", orginalGoodsId);
                    continue;
                }
                goodsInfoInputVO.setProperties(gernatePropertyVOListByMap(propertyMap, prop1, prop2, null));

                if (StringUtils.isBlank(mainImage) || !mainImage.contains("http")) {
                    log.info("当前异常数据-主图异常:{}", orginalGoodsId);
                    continue;
                }
                if (CollectionUtils.isEmpty(goodsImageList)) {
                    log.info("当前异常数据-轮播异常:{}", orginalGoodsId);
                    continue;
                }
                if (dirtyData == true) {
                    log.info("当前异常数据-默认运费异常{}", orginalGoodsId);
                    continue;
                }

                goodsInfoInputVO.setWeight(weight);
                goodsInfoInputVO.setPackageSize(goodsPackageSzie);
                goodsInfoInputVO.setName(goodsName);
                goodsInfoInputVO.setOrginalName(goodsName);
                goodsInfoInputVO.setMainImage(mainImage);
                goodsInfoInputVO.setDetailsImgs(goodsImageList);
                goodsInfoInputVO.setGoodsImages(goodsImageList);
                goodsInfoInputVO.setMaxPrice(priceGernate(maxPrice));
                goodsInfoInputVO.setMinPrice(priceGernate(minPrice));
                goodsInfoInputVO.setShowPrice(priceGernate(minPrice));
                goodsInfoInputVO.setMaxMarketPrice(marketPriceRuleGernate(maxPrice));
                goodsInfoInputVO.setMinMarketPrice(marketPriceRuleGernate(minPrice));
                goodsInfoInputVO.setOrignalMaxMarketPrice(maxPrice);
                goodsInfoInputVO.setOrignalMinMarketPrice(minPrice);
                goodsInfoInputVO.setOrignalMaxPrice(maxPrice);
                goodsInfoInputVO.setOrignalMinPrice(minPrice);
                goodsInfoInputVO.setShowMarketPrice(minPrice);
                goodsInfoInputVO.setItemCode(orginalGoodsId);
                if (!ourCate) {
                    goodsInfoInputVO.setForeignChannel("fanno");
                }
                goodsInfoInputVO.setOperationLogType(OperationLogTypeEnums.FANNO_IMPORT_GOODS.getCode());
                shopAddGoodsCoreService.addGoods(goodsInfoInputVO);
            } catch (Exception ex) {
                log.info("{} 解析失败", orginalGoodsId);
                ex.printStackTrace();
            }
        }
        String key = goodsInfoInputVO.getStoreName() + goodsInfoInputVO.getShopId().toString();
        redisApi.del(key);
        return null;
    }


    @Override
    public Boolean batchImportGoodsSkuFee(List<GoodsSkuImportVO> importVOs,boolean flag) {

        if (CollectionUtils.isEmpty(importVOs)) {
            return true;
        }
        List<GoodsSkuVO> goodsSkuVOS = BeanCopyUtil.copyListProperties(importVOs, GoodsSkuVO::new);
        //校验导入数据
        goodsSkuVOS.forEach(vo -> CheckUtils.check(vo.getId() == null || vo.getOrginalPrice() == null || vo.getOrginalPrice().compareTo(BigDecimal.ZERO) < 0,
                CustomResultCode.fill(ProductResultCode.EXCEL_PARAM_ERROR, String.valueOf(vo.getId()))));

        Map<Long, List<GoodsSkuVO>> goodsSkuGroupMap = goodsSkuVOS.stream().collect(Collectors.groupingBy(GoodsSkuVO::getId));
        List<Long> goodsIds = Lists.newArrayList(goodsSkuGroupMap.keySet());
        Long shopId = getShopId();
        if (flag) {
            //scm 平台审核改价，shopId直接根据goodsId反查
            List<Goods> goods = goodsService.queryShopIdsByGoodsIds(goodsIds);
            CheckUtils.check(CollectionUtils.isEmpty(goods), ProductResultCode.LOGIN_ERROR);
            shopId = goods.get(0).getShopId();
        }
//        CheckUtils.check(null == shopId, ProductResultCode.LOGIN_ERROR);

        List<GoodsItem> goodsItemList = goodsItemService.queryGoodsIdsList(goodsIds);
        Map<Long, List<GoodsItem>> goodsItemGroupMap = goodsItemList.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

        log.info("batchImportGoodsSkuFee 当前shopId:{}", shopId);
        boolean isSelfSupport = false;
        boolean isWarehouseOverSea = false;
//        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);
        if (shopId != null) {
            FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(shopId);
            CheckUtils.notNull(faMerchantsApply, ProductResultCode.SHOP_IS_NOT_EXIST);
            isSelfSupport = faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.SELF_SUPPORT_SCM.getCode()) || faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.SELF_SUPPORT.getCode());
            isWarehouseOverSea = faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.DIRECT.getCode());
        } else {
            isSelfSupport = true;
        }

        List<Goods> otherShopGoodsList = goodsService.lambdaQuery()
                .in(Goods::getId, goodsIds)
                .ne(Goods::getShopId, shopId)
                .list();
        if (CollectionUtils.isNotEmpty(otherShopGoodsList) && !isSelfSupport) {
            String notBelongGoodsIds = otherShopGoodsList.stream().map(goods -> goods.getId().toString()).collect(Collectors.joining(","));
            CheckUtils.check(true, CustomResultCode.fill(ProductResultCode.EXIST_GOODS_NOT_BELONG_SHOP, notBelongGoodsIds));
        }

        if (!isSelfSupport) {
            List<Long> existEditGoodsIds = goodsEditInfoService.lambdaQuery()
                    .in(GoodsEditInfo::getGoodsId, goodsIds)
                    .eq(GoodsEditInfo::getStatus, 0)
                    .list()
                    .stream().map(GoodsEditInfo::getGoodsId).collect(Collectors.toList());
            CheckUtils.check(CollectionUtils.isNotEmpty(existEditGoodsIds),CustomResultCode.fill(ProductResultCode.GOODS_EDIT_APPLY_EXIST_EXT,existEditGoodsIds.stream().map(String::valueOf).collect(Collectors.joining(","))));
        }

        Map<Long, Goods> goodsMap = goodsService.listByIds(goodsIds).stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));
        Map<Long, List<GoodsFreight>> goodsFreightMap = goodsFreightService.queryByGoodsIds(goodsIds).stream().collect(Collectors.groupingBy(GoodsFreight::getGoodsId));
        //默认运费取德国运费
//        Map<Long, BigDecimal> goodsDefaultFreightMap = goodsFreightMap.entrySet()
//                .stream()
//                .collect(Collectors.toMap(Map.Entry::getKey, longListEntry -> longListEntry.getValue()
//                        .stream()
//                        .filter(goodsFreight -> SaleableCountryEnum.DE.name().equals(goodsFreight.getCode()))
//                        .map(GoodsFreight::getCurrentFreight)
//                        .findFirst().orElse(BigDecimal.ZERO)));

        Map<Long, Long> goodsListingIdMap = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getGoodsId, goodsIds)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .list()
                .stream()
                .collect(Collectors.toMap(ListingFollowGoods::getGoodsId, ListingFollowGoods::getListingId, (v1, v2) -> v1));

        Map<Long, String> goodsLockLabelMap = goodsLockInfoService.lambdaQuery()
                .in(GoodsLockInfo::getGoodsId, goodsIds)
                .eq(GoodsLockInfo::getIsDel, 0)
                .list()
                .stream()
                .collect(Collectors.toMap(GoodsLockInfo::getGoodsId, GoodsLockInfo::getLabel, (v1, v2) -> v1));

        List<GoodsExtConfig> goodsExtConfigList = goodsExtConfigService.selectGoodsTagConfig(goodsIds, CommonConstants.FLASH_DEAL_TAG_ID);
        List<Long> inFlashDealGoodsIds = goodsExtConfigList.stream().map(GoodsExtConfig::getGoodsId).collect(Collectors.toList());

        String userName = (flag ? "scm供应商" : getUserName()) + ":批量修改SKU价格";
        Date now = new Date();

        List<GoodsOperationLogDto> goodsOperationLogDtoList = Lists.newArrayList();
        List<GoodsSyncModel> goodsSyncModelList = Lists.newArrayList();
        List<GoodsEditInfo> goodsEditInfoList = Lists.newArrayList();
        Map<Long, String> goodsContentMap = Maps.newHashMap();

        //检测sku限价信息
        for (Long goodsId : goodsIds) {
            List<GoodsSkuVO> updateSkuList = goodsSkuGroupMap.get(goodsId);
            if (CollectionUtils.isEmpty(updateSkuList)) {
                LogUtils.info(log, "batchImportGoodsSkuFee ==> return111 goodsId: {}", goodsId);
                continue;
            }

            Goods goods = goodsMap.get(goodsId);
            if (null == goods) {
                LogUtils.info(log, "batchImportGoodsSkuFee ==> return222 goodsId: {}", goodsId);
                continue;
            }

            if (goods.getType() == 3) {
                LogUtils.info(log, "batchImportGoodsSkuFee ==> return333 goodsId: {}", goodsId);
                continue;
            }

            List<GoodsItem> goodsItems = goodsItemGroupMap.get(goodsId);
            if (CollectionUtils.isEmpty(goodsItems)) {
                LogUtils.info(log, "batchImportGoodsSkuFee ==> return444 goodsId: {}", goodsId);
                continue;
            }

            Map<Long, GoodsItem> skuIdItemMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getSkuId, Function.identity(), (v1, v2) -> v1));
            Long listingId = goodsListingIdMap.get(goodsId);
            String lockLabel = goodsLockLabelMap.get(goodsId);

            boolean needAudit = false;
            boolean isLock = goods.getIsLock() == 1 || listingId != null;

            if (updateSkuList.size() == 1 && updateSkuList.get(0).getSkuId() == null) {
                BigDecimal updatePrice = updateSkuList.get(0).getOrginalPrice();
                for (GoodsItem goodsItem : goodsItems) {
                    if (isLock && updatePrice.compareTo(goodsItem.getOrginalPrice()) > 0) {
                        needAudit = true;
                    }
                }
            } else {
                for (GoodsSkuVO goodsSkuVO : updateSkuList) {
                    if (goodsSkuVO.getSkuId() == null) {
                        continue;
                    }
                    GoodsItem goodsItem = skuIdItemMap.get(goodsSkuVO.getSkuId());
                    if (goodsItem == null) {
                        continue;
                    }
                    if (isLock && goodsSkuVO.getOrginalPrice().compareTo(goodsItem.getOrginalPrice()) > 0) {
                        needAudit = true;
                        break;
                    }
                }
            }
            needAudit = needAudit && !isSelfSupport;
            LogUtils.info(log, "batchImportGoodsSkuFee ==> goodsId:{}, isLock:{}, listingId:{}, needAudit:{}", goodsId, goods.getIsLock(), listingId, needAudit);


            Map<Long, BigDecimal> oldData = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getSkuId, GoodsItem::getOrginalPrice, (v1, v2) -> v1));

            Map<Long, GoodsSkuVO> updateSkuPriceMap = updateSkuList.stream().filter(goodsSkuVO -> goodsSkuVO.getSkuId() != null).collect(Collectors.toMap(GoodsSkuVO::getSkuId, Function.identity(), (v1, v2) -> v1));
            List<GoodsEditPriceDto.SkuChangeDto> skuChangeDtoList = Lists.newArrayList();
            Set<Integer> changePrice = new HashSet<>();
            for (GoodsItem oldGoodsItem : goodsItems) {
                if (updateSkuList.size() == 1 && updateSkuList.get(0).getSkuId() == null) {
                    GoodsSkuVO goodsSkuVO = updateSkuList.get(0);
                    BigDecimal updatePrice = goodsSkuVO.getOrginalPrice();
                    BigDecimal defaultDelivery = goodsSkuVO.getDefaultDelivery();
                    if (updatePrice.compareTo(oldGoodsItem.getOrginalPrice()) != 0 || (isWarehouseOverSea && defaultDelivery != null && defaultDelivery.compareTo(oldGoodsItem.getDefaultDelivery()) != 0)) {
                        if (oldGoodsItem.getOrginalPrice().compareTo(updatePrice) > 0 || (isWarehouseOverSea && defaultDelivery != null && oldGoodsItem.getDefaultDelivery().compareTo(defaultDelivery) > 0)) {
                            changePrice.add(0);
                        } else {
                            changePrice.add(1);
                        }
                        skuChangeDtoList.add(new GoodsEditPriceDto.SkuChangeDto(oldGoodsItem.getId(), oldGoodsItem.getOrginalPrice(), updatePrice, oldGoodsItem.getStock(), oldGoodsItem.getStock(), oldGoodsItem.getDefaultDelivery(), goodsSkuVO.getDefaultDelivery() == null ? oldGoodsItem.getDefaultDelivery() : goodsSkuVO.getDefaultDelivery()));
                        oldGoodsItem.setOrginalPrice(updatePrice);
                        if (isWarehouseOverSea && goodsSkuVO.getDefaultDelivery() != null) {
                            oldGoodsItem.setDefaultDelivery(goodsSkuVO.getDefaultDelivery());
                        }
                        oldGoodsItem.setPrice(oldGoodsItem.getOrginalPrice().add(oldGoodsItem.getDefaultDelivery()));

                    }
                    oldGoodsItem.setMinPurchaseQuantity(goodsSkuVO.getMinPurchaseQuantity());
                    oldGoodsItem.setUpdateTime(now);
                } else {
                    GoodsSkuVO goodsSkuVO = updateSkuPriceMap.get(oldGoodsItem.getSkuId());
                    if (goodsSkuVO != null) {
                        BigDecimal updatePrice = goodsSkuVO.getOrginalPrice();
                        BigDecimal defaultDelivery = goodsSkuVO.getDefaultDelivery();
                        if (updatePrice.compareTo(oldGoodsItem.getOrginalPrice()) != 0 || (isWarehouseOverSea && defaultDelivery != null && defaultDelivery.compareTo(oldGoodsItem.getDefaultDelivery()) != 0)) {
                            if(oldGoodsItem.getOrginalPrice().compareTo(updatePrice) > 0 || (isWarehouseOverSea && defaultDelivery != null && oldGoodsItem.getDefaultDelivery().compareTo(defaultDelivery) > 0)){
                                changePrice.add(0);
                            }else {
                                changePrice.add(1);
                            }
                            skuChangeDtoList.add(new GoodsEditPriceDto.SkuChangeDto(oldGoodsItem.getId(), oldGoodsItem.getOrginalPrice(), updatePrice, oldGoodsItem.getStock(), oldGoodsItem.getStock(), oldGoodsItem.getDefaultDelivery(), goodsSkuVO.getDefaultDelivery() == null ? oldGoodsItem.getDefaultDelivery() : goodsSkuVO.getDefaultDelivery()));
                            oldGoodsItem.setOrginalPrice(updatePrice);
                            if (isWarehouseOverSea && goodsSkuVO.getDefaultDelivery() != null) {
                                oldGoodsItem.setDefaultDelivery(goodsSkuVO.getDefaultDelivery());
                            }
                            oldGoodsItem.setPrice(oldGoodsItem.getOrginalPrice().add(oldGoodsItem.getDefaultDelivery()));
                        }
                        oldGoodsItem.setMinPurchaseQuantity(goodsSkuVO.getMinPurchaseQuantity());
                        oldGoodsItem.setUpdateTime(now);
                    }
                }
            }

            if (CollectionUtils.isEmpty(skuChangeDtoList)) {
                continue;
            }

            //sku限价校验
            ProductInfoInput productInfoInput = new ProductInfoInput();
            productInfoInput.setCategoryId(goods.getCategoryId());
            productInfoInput.setSkuInfo(BeanCopyUtil.transformList(goodsItems, GoodsItemDTO.class));
            productInfoInput.setFreightList(BeanCopyUtil.transformList(goodsFreightMap.getOrDefault(goodsId, Collections.emptyList()), GoodsFreightVO.class));

            if (!isSelfSupport) {
                try {
                    skuLimitPriceCoreService.checkSkuLimitPrice(productInfoInput);
                } catch (CustomException e) {
                    LogUtils.info(log, "batchImportGoodsSkuFee ==> 类目限价校验失败 goodsId: {}, msg: {}", goodsId, e.getBusinessResultCode().getMsg());
                    continue;
                }
            }

            String extraInfo = "";
            boolean inFlashDeal = inFlashDealGoodsIds.contains(goodsId);

            if (!needAudit) {
                LogUtils.info(log, "batchImportGoodsSkuFee ==> 无需审批直接更新 goodsId: {}", goodsId);
                List<BigDecimal> priceList = goodsItems.stream().map(GoodsItem::getPrice).collect(Collectors.toList());
                priceList.stream().max(BigDecimal::compareTo).ifPresent(goods::setMaxPrice);
                priceList.stream().min(BigDecimal::compareTo).ifPresent(goods::setMinPrice);
                goods.setUpdateTime(LocalDateTime.now());
                goodsService.updateById(goods);
                goodsItemService.updateBatchById(goodsItems);

                //改价 --> 自动退出flashDeal
                if (inFlashDeal) {
                    LogUtils.info(log, "batchImportGoodsSkuFee --> 自动退出flashDeal goodsId:{}", goodsId);
                    extraInfo = "【商家主动调价自动退出flashDeal】";

                    //解除锁定标签
                    UpdateLockLabelCondition condition = new UpdateLockLabelCondition(Collections.singletonList(goodsId), Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()),"自动解标签(改价自动退出flashDeal)", OperationLogTypeEnums.UNLOCK_AUTO.getCode());
                    goodsLockCoreService.removeLockByApi(condition);
                    LogUtils.info(log, "batchImportGoodsSkuFee --> 自动退出flashDeal,解除锁定标签 goodsId:{}", goodsId);

                    //删除活动标签
                    BindTagDTO bindTagDTO = new BindTagDTO();
                    bindTagDTO.setGoodsIds(Collections.singletonList(goodsId));
                    bindTagDTO.setTagId(CommonConstants.FLASH_DEAL_TAG_ID);
                    goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
                    LogUtils.info(log, "batchImportGoodsSkuFee --> 自动退出flashDeal,删除活动标签 goodsId:{}", goodsId);

                    //退出活动
                    Long activityId = activityRemoteClientFactory.exitFlashDeal(goodsId);
                    LogUtils.info(log, "batchImportGoodsSkuFee --> 自动退出flashDeal,退出活动 goodsId:{}, activityId:{}", goodsId, activityId);

                    //删除活动价格备份信息
                    if (activityId != null) {
                        List<ActivityOriginalPrice> activityOriginalPriceList = activityOriginalPriceService.queryPriceByActivityIdAndGoodsId(activityId, goodsId);
                        if (CollectionUtils.isNotEmpty(activityOriginalPriceList)) {
                            activityOriginalPriceService.deleteByIds(activityOriginalPriceList.stream().map(ActivityOriginalPrice::getId).collect(Collectors.toList()));
                            LogUtils.info(log, "batchImportGoodsSkuFee --> 自动退出flashDeal,删除活动价格备份信息 goodsId:{}, size:{}", goodsId, activityOriginalPriceList.size());
                        }
                    }
                }

                GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                        .goodsId(goodsId)
                        .type(OperationLogTypeEnums.IMPORT_SKU_PRICE)
                        .content(OperationLogTypeEnums.IMPORT_SKU_PRICE.getDesc())
                        .oldData(JSON.toJSONString(oldData))
                        .newData(JSON.toJSONString(updateSkuList))
                        .status(1)
                        .user(userName);
                goodsOperationLogDtoList.add(goodsOperationLogDto);
//                mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);

                GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                goodsSyncModel.setGoodsId(goodsId);
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("商家批量修改SKU价格&默认运费导入(无需审核)");
                goodsSyncModel.setSourceService("vp");
                goodsSyncModelList.add(goodsSyncModel);
//                mqSender.sendDelay("SYNC_GOODS_TOPIC_BATCH", goodsSyncModel, MqDelayLevel.TEN_SEC);
            }


            GoodsEditPriceDto goodsEditPriceDto = new GoodsEditPriceDto();
            goodsEditPriceDto.setSkuChangeDtoList(skuChangeDtoList);

            List<String> specialTagList = Lists.newArrayList();
            if (lockLabel != null) {
                specialTagList = GoodsLockLabelTypEnums.listNamesByCodes(lockLabel);
            }
            if (listingId != null) {
                specialTagList.add("listing");
            }

            GoodsEditInfo editInfo = new GoodsEditInfo();
            editInfo.setStatus(0);
            editInfo.setGoodsId(goods.getId());
            editInfo.setCategoryId(goods.getCategoryId());
            editInfo.setShopId(goods.getShopId());
            editInfo.setShopName(goods.getShopName());
            if (!needAudit) {
                editInfo.setAuditUser("system");
                editInfo.setAuditTime(LocalDateTime.now());
                editInfo.setStatus(1);
            }
            editInfo.setApplyUser(userName);
            editInfo.setApplyTime(LocalDateTime.now());
            editInfo.setApplyReason(extraInfo);
            editInfo.setIsDel(0);
            editInfo.setType(GoodsEditTypeEnums.PRICE.getCode());
            editInfo.setContent(JSON.toJSONString(goodsEditPriceDto));
            editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
            editInfo.setUpdateTime(LocalDateTime.now());
            if(changePrice.size() > 1){
                editInfo.setAddPrice(2);
            }else{
                if(!changePrice.isEmpty()){
                    editInfo.setAddPrice(changePrice.contains(0) ? 0 : 1);
                }
            }
            goodsEditInfoList.add(editInfo);
            goodsContentMap.put(goods.getId(), JSON.toJSONString(goodsEditPriceDto));

            LogUtils.info(log, "batchImportGoodsSkuFee ==> 更新完成 goodsId: {}", goodsId);
        }

        if (CollectionUtils.isNotEmpty(goodsOperationLogDtoList)) {
            goodsOperationLogDtoList.forEach(goodsOperationLogDto -> mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto));
        }
        if (CollectionUtils.isNotEmpty(goodsSyncModelList)) {
            goodsSyncModelList.forEach(goodsSyncModel -> mqSender.sendDelay("SYNC_GOODS_TOPIC_BATCH", goodsSyncModel, MqDelayLevel.THIRTY_SEC));
        }
        if (CollectionUtils.isNotEmpty(goodsEditInfoList)) {
            goodsEditInfoService.saveBatch(goodsEditInfoList);

            List<GoodsEditInfoDetail> saveDetailList = goodsEditInfoList.stream().map(goodsEditInfo -> {
                GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
                goodsEditInfoDetail.setEditId(goodsEditInfo.getId());
                goodsEditInfoDetail.setGoodsId(goodsEditInfo.getGoodsId());
                goodsEditInfoDetail.setContent(goodsContentMap.get(goodsEditInfo.getGoodsId()));
                return goodsEditInfoDetail;
            }).collect(Collectors.toList());
            goodsEditInfoDetailService.saveBatch(saveDetailList);

            // 批量写入推荐算法日志
            recommendCoreService.batchSaveRecommendGoodsChangeLog(goodsEditInfoList);
        }
        LogUtils.info(log, "batchImportGoodsSkuFee ==> over!");
        return Boolean.TRUE;
    }

    private List<PropertyVO> gernatePropertyVOListByMap(Map<String, List<PropertyValueVO>> propertyMap, String prop1, String prop2, String prop3) {
        List<PropertyVO> propertyVOList = Lists.newArrayList();
        List<PropertyValueVO> propertyValueLists = propertyMap.get(prop1);

        PropertyVO propertyVO = new PropertyVO();
        propertyVO.setName(prop1);
        propertyVO.setOrginalName(prop1);
        propertyVO.setValues(propertyValueLists);
        propertyVOList.add(propertyVO);

        if (StringUtils.isNotBlank(prop2)) {
            List<PropertyValueVO> propertyValueLists2 = propertyMap.get(prop2);
            if (CollectionUtils.isNotEmpty(propertyValueLists2)) {
                PropertyVO propertyVO2 = new PropertyVO();
                propertyVO2.setName(prop2);
                propertyVO2.setOrginalName(prop2);
                propertyVO2.setValues(propertyValueLists2);
                propertyVOList.add(propertyVO2);
            }
        }

        if (StringUtils.isNotBlank(prop3)) {
            List<PropertyValueVO> propertyValueLists3 = propertyMap.get(prop3);
            if (CollectionUtils.isNotEmpty(propertyValueLists3)) {
                PropertyVO propertyVO3 = new PropertyVO();
                propertyVO3.setName(prop3);
                propertyVO3.setOrginalName(prop3);
                propertyVO3.setValues(propertyValueLists3);
                propertyVOList.add(propertyVO3);
            }
        }


        return propertyVOList;

    }


    private String gernatePropertyMap(String propertyDesc, String propertyValueImg, Map<String, List<PropertyValueVO>> propertyMap) {
        if (StringUtils.isBlank(propertyDesc)) {
            return null;
        }

        String[] args = propertyDesc.replaceAll("：", ":").split(":");
        if (args.length != 2) {
            return null;
        }

        String propertyName = args[0];
        String propertyValue = args[1];
        List<PropertyValueVO> propertyValueList = propertyMap.get(propertyName);
        if (null == propertyValueList) {
            propertyValueList = Lists.newArrayList();
        }

        List<String> existValues = propertyValueList.stream().map(PropertyValueVO::getValue).collect(Collectors.toList());
        if (existValues.contains(propertyValue)) {
            return propertyName;
        }

        PropertyValueVO propertyValueVO = new PropertyValueVO();
        propertyValueVO.setValue(propertyValue);
        propertyValueVO.setOriginalValue(propertyValue);
        propertyValueVO.setMainUrl(propertyValueImg);
        propertyValueVO.setImgUrl(propertyValueImg);
        propertyValueList.add(propertyValueVO);
        propertyMap.put(propertyName, propertyValueList);

        return propertyName;
    }

    private String gernatePropertyMapNew(String propertyDesc, String propertyValueImg, Map<String, List<PropertyValueVO>> propertyMap, Map<String, Set<String>> propertyNameMap) {
        String propertyName = null;
        if (StringUtils.isNotBlank(propertyDesc)) {
            String[] args = propertyDesc.split(":");
            if (args.length != 2) {
                return null;
            }
            propertyName = args[0];
            if (propertyName.equals("颜色")) {
                propertyName = "color";
            }
            if (propertyName.equals("尺码")) {
                propertyName = "size";
                propertyValueImg = null;
            }
            List<PropertyValueVO> propertyValueList = propertyMap.get(propertyName);

            Set<String> strings = propertyNameMap.get(propertyName);
            if (null == strings) {
                strings = new HashSet<>();
            }
            if (CollectionUtils.isEmpty(propertyValueList)) {
                propertyValueList = new ArrayList<>();

            }

            if (!strings.contains(args[1])) {
                PropertyValueVO propertyValueVO = new PropertyValueVO();
                propertyValueVO.setValue(args[1]);
                propertyValueVO.setOriginalValue(args[1]);
                propertyValueVO.setMainUrl(propertyValueImg);
                propertyValueVO.setImgUrl(propertyValueImg);
                propertyValueList.add(propertyValueVO);
                propertyMap.put(propertyName, propertyValueList);
                strings.add(args[1]);
                propertyNameMap.put(propertyName, strings);
            }

        }


        return propertyName;
    }

    List<String> getAeDetailImg(String detailImg) {
        List<String> result = new ArrayList<>();
        String pattern = "(?<=src=\").*?(?=\" s)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(detailImg);
        while (m.find()) {
            String sourceC = m.group();
            result.add(sourceC);
        }


        return result;
    }


    public static BigDecimal priceGernate(BigDecimal orginalPrice) {
        if (null != orginalPrice) {
            return orginalPrice;
//            ShopPriceRuleEnum[] shopPriceRuleEnums = ShopPriceRuleEnum.values();
//            for (ShopPriceRuleEnum shopPriceRuleEnum : shopPriceRuleEnums) {
//                if (orginalPrice.compareTo(shopPriceRuleEnum.getBeginPrice()) >= 0 && orginalPrice.compareTo(shopPriceRuleEnum.getEndPrice()) <= 0) {
//                    return orginalPrice.multiply(shopPriceRuleEnum.getPlatformRatio()).setScale(2, RoundingMode.HALF_UP);
//                }
//            }
        }
        return orginalPrice;
    }

    public static BigDecimal marketPriceRuleGernate(BigDecimal orginalPrice) {
        if (null != orginalPrice) {
//            ShopPriceRuleEnum[] shopPriceRuleEnums = ShopPriceRuleEnum.values();
//            for (ShopPriceRuleEnum shopPriceRuleEnum : shopPriceRuleEnums) {
//                if (orginalPrice.compareTo(shopPriceRuleEnum.getBeginPrice()) >= 0 && orginalPrice.compareTo(shopPriceRuleEnum.getEndPrice()) <= 0) {
//                    return orginalPrice.multiply(shopPriceRuleEnum.getMarketRatio()).setScale(2, RoundingMode.HALF_UP);
//                }
//            }
            return orginalPrice;
        }
        return orginalPrice;
    }


    String getCateId(String excelCateId) {
        String pattern = "(?<=\\().{0,100}(?=\\))";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(excelCateId);
        String cid = "";
        while (m.find()) {
            cid = m.group();
        }
        return cid;
    }


    void getShopMsg(GoodsInfoInputVO goodsInfoInputVO) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String voghionToken = request.getHeader("clientInfo");
        // 查询redis
        String vohionTokenStr = redisApi.get(voghionToken);
        // 查看api权限
        String shopName = "";
        Long shopId = null;
        if (StringUtils.isNotBlank(vohionTokenStr)) {
            JSONObject json = JSONObject.parseObject(vohionTokenStr);
            JSONObject user = JSONObject.parseObject(json.getJSONObject("user").toJSONString());
            shopId = user.getLong("shopId");
            shopName = user.getString("shopName");
            if (shopId != null && shopId > 0) {
                goodsInfoInputVO.setShopId(shopId);
                goodsInfoInputVO.setStoreName(shopName);
            } else {
                CheckUtils.check(true, ProductResultCode.IMPORT_ERROR);
            }
            log.info("店铺id为 {}", shopId);
        }

        String key = shopName + shopId.toString();
        boolean haskey = false;//redisApi.hasKey(key);
        if (haskey) {
            CheckUtils.check(true, ProductResultCode.IMPORT_ERROR);
        } else
            redisApi.set(key, key, 120);

        CheckUtils.notNull(goodsInfoInputVO, ProductResultCode.NULL_ERROR);

    }

}
