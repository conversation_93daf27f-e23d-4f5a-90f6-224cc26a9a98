package com.voghion.product.core.impl;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.colorlight.base.utils.CheckUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.onlest.GoodsSyncModel;
import com.voghion.boot.common.enums.CountryCodeEnum;
import com.voghion.comment.api.output.GoodsCommentPageDTO;
import com.voghion.comment.api.output.GoodsCommentQueryDto;
import com.voghion.comment.api.output.ShopGoodsCommentDTO;
import com.voghion.es.dto.VatCondition;
import com.voghion.es.dto.VatDto;
import com.voghion.es.model.GoodsExtConfigModel;
import com.voghion.es.service.GoodsEsService;
import com.voghion.merchant.api.input.NoticeInput;
import com.voghion.product.api.dto.BindTagDTO;
import com.voghion.product.api.dto.GoodsOperationLogDto;
import com.voghion.product.api.dto.GoodsReductionVO;
import com.voghion.product.api.dto.SkuReductionPriceVO;
import com.voghion.product.api.enums.GoodsEditTypeEnums;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.api.enums.SaleableCountryEnum;
import com.voghion.product.api.input.GoodsIsDelInput;
import com.voghion.product.bigquery.dto.OutDbGoodsEveryDayDTO;
import com.voghion.product.bigquery.service.OutDbGoodsEveryDayService;
import com.voghion.product.bq.OutDbGoodsEveryDayEsService;
import com.voghion.product.client.GoodsCommentFactory;
import com.voghion.product.client.ImageClientFactory;
import com.voghion.product.client.NoticeClientFactory;
import com.voghion.product.core.*;
import com.voghion.product.enums.DeliveryTypeBinaryEnum;
import com.voghion.product.listener.ListingGoodsHotUpdateVO;
import com.voghion.product.model.bo.GoodsSkuBo;
import com.voghion.product.model.bo.ListingGoodsSortBo;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.enums.*;
import com.voghion.product.model.po.*;
import com.voghion.product.model.po.goods.GoodsSkuPo;
import com.voghion.product.model.vo.*;
import com.voghion.product.model.vo.condition.*;
import com.voghion.product.model.vo.listing.UpdateAcceptReductionPushVo;
import com.voghion.product.model.vo.listingFollowGoods.ListingFollowGoodsCheckVo;
import com.voghion.product.model.vo.listing.ListingReductionPushVo;
import com.voghion.product.model.vo.listing.ShopAcceptReductionVo;
import com.voghion.product.mq.MqDelayLevel;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.support.ClientInfoSupportService;
import com.voghion.product.util.AssertsUtils;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.util.LogUtils;
import com.voghion.product.utils.CommonConstants;
import com.voghion.product.utils.GoodsTransferUtils;
import com.voghion.product.utils.RedisKeyConstants;
import com.voghion.product.utils.MathUtils;
import com.voghion.user.remove.dto.UserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.time.StopWatch;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.Asserts;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ListingInfoCoreServiceImpl extends AbstractCommonServiceImpl implements ListingInfoCoreService {

    private static long randomPageStart = 1;
    @Resource
    private ListingInfoService listingInfoService;

    @Resource
    private ListingFollowGoodsService listingFollowGoodsService;

    @Resource
    private ListingShopWhiteListService listingShopWhiteListService;

    @Resource
    private NewAddGoodsCoreService newAddGoodsCoreService;

    @Resource
    private UpdateGoodsInfoCoreService updateGoodsInfoCoreService;

    @Resource
    private GoodsCoreService goodsCoreService;

    @Resource
    private GoodsItemService goodsItemService;

    @Resource
    private GoodsCommentFactory goodsCommentFactory;

    @Resource
    private ClientInfoSupportService clientInfoSupportService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private CategoryTreeCoreService categoryTreeCoreService;

    @Resource
    private FaMerchantsApplyService faMerchantsApplyService;

    @Resource
    private CategoryMainService categoryMainService;

    @Resource
    private OutDbGoodsEveryDayService outDbGoodsEveryDayService;

    @Resource
    private OutDbGoodsEveryDayEsService outDbGoodsEveryDayEsService;

    @Resource
    private ImageClientFactory imageClientFactory;
    @Resource
    private FaMerchantsApplyCoreService faMerchantsApplyCoreService;

    @Resource
    private SensitiveWordsCoreService sensitiveWordsCoreService;

    @Resource
    private SensitiveWordsService sensitiveWordsService;

    @Resource(name = "goodsInfoPool")
    private Executor executor;

    @Resource
    private GoodsExtConfigService goodsExtConfigService;

    @Resource
    private FaMerchantsTagService faMerchantsTagService;

    @Resource
    private GoodsExtDetailService goodsExtDetailService;

    @Resource
    private GoodsVatConfigService goodsVatConfigService;

    @Resource
    private GoodsEsService goodsEsService;

    @Resource
    private SearchTypeMatchBinaryConfigService searchTypeMatchBinaryConfigService;

    @Resource
    private RedisApi redisApi;

    @Resource
    private MqSender mqSender;

    @Value("${voghion.dev}")
    private String env;

    @Value("${listing.white.redis.status:0}")
    private String listingWhiteRedisStatus;

//    @Resource
//    private ProductNacosConfig productNacosConfig;

    @Value("${replace.order.exclude.tag:0}")
    private String excludeTagIds;
    @Resource
    private GoodsFreightService goodsFreightService;

    @Resource
    private AssessmentShopService assessmentShopService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private TongDunGoodsImageCoreService tongDunGoodsImageCoreService;

    @Resource
    private WarehouseOverseasConfigCoreService warehouseOverseasConfigCoreService;

    @Resource
    private GoodsFreightCoreService goodsFreightCoreService;

    @Resource(name = "addFullGoodsInfoPool")
    private Executor addFullGoodsInfoPool;

    @Resource
    private GoodsSkuService goodsSkuService;

    @Resource
    private GoodsLockInfoService goodsLockInfoService;

    @Resource
    private GoodsEditInfoService goodsEditInfoService;

    @Resource
    private GoodsEditInfoDetailService goodsEditInfoDetailService;

    @Resource
    private NoticeClientFactory noticeClientFactory;

    private String BATCH_SAVE_LISTING_LOCK = "BATCH_SAVE_LISTING_LOCK";

    public static Long CUSTOM_GOODS_TAG_ID = 503L;

    @Override
    public PageView<ListingInfoListVo> pageListingInfo(ListingInfoQueryCondition condition) {
        LogUtils.info(log, "listing竞标商品列表【运营】 condition:{}", condition);

        if (CollectionUtils.isNotEmpty(condition.getIds())) {
            List<Long> listingIds = Lists.newArrayList(condition.getIds());
            Integer pageSize = condition.getPageSize();
            condition = new ListingInfoQueryCondition();
            condition.setIds(listingIds);
            condition.setPageSize(pageSize);
        }

        if (condition.getSort() == null) {
            condition.setSort(4);
        }
        if (condition.getIsAsc() == null) {
            condition.setIsAsc(0);
        }

        PageView<ListingInfoListVo> pageView = new PageView<>(condition.getPageSize(), condition.getPageNow());

        List<Long> listingIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(condition.getListingIdListStr())) {
            listingIds = Arrays.stream(condition.getListingIdListStr()
                    .split("\n"))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }
        List<Long> sourceGoodsIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(condition.getSourceGoodsId())) {
            sourceGoodsIds = Arrays.stream(condition.getSourceGoodsId()
                            .split("\n"))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
        }


        List<Long> categoryIds = Lists.newArrayList();
        if (condition.getCategoryId() != null) {
            categoryIds = categoryService.queryAllChildCategoryByParentId(condition.getCategoryId())
                    .stream()
                    .map(Category::getId)
                    .collect(Collectors.toList());
            categoryIds.add(condition.getCategoryId());
        }

        if (condition.getFollowGoodsId() != null) {
            ListingFollowGoods followGoods = listingFollowGoodsService.lambdaQuery()
                    .eq(ListingFollowGoods::getGoodsId, condition.getFollowGoodsId())
                    .eq(ListingFollowGoods::getIsDel, 0)
//                    .eq(ListingFollowGoods::getStatus, 1)
                    .one();
            if (followGoods == null) {
                return pageView;
            } else {
                listingIds.clear();
                listingIds.add(followGoods.getListingId());
            }
        }

        List<Long> existTopListingIds = Lists.newArrayList();
        if (condition.getShowTop() != null) {
            existTopListingIds = listingFollowGoodsService.lambdaQuery()
                    .eq(ListingFollowGoods::getIsTop, 1)
                    .eq(ListingFollowGoods::getIsDel, 0)
                    .eq(ListingFollowGoods::getStatus, 1)
                    .select(ListingFollowGoods::getListingId)
                    .list()
                    .stream().map(ListingFollowGoods::getListingId).collect(Collectors.toList());

            if (condition.getShowTop() == 1 && CollectionUtils.isEmpty(existTopListingIds)) {
                return pageView;
            }
        }

        List<Integer> shopTagValues = null;
        boolean allShopTag = false;
        if (condition.getShopTag() != null) {
            if (condition.getShopTag() == -1) {
                allShopTag = true;
            } else {
                shopTagValues = searchTypeMatchBinaryConfigService.getDecimalValuesByEnumNames(Collections.singletonList(
                        String.valueOf(condition.getShopTag())), 1);
            }
        }

        List<Integer> deliveryTypeValues = null;
        boolean allDeliveryType = false;
        if (condition.getDeliveryType() != null) {
            if (condition.getDeliveryType() == -1) {
                allDeliveryType = true;
            } else {
                deliveryTypeValues = searchTypeMatchBinaryConfigService.getDecimalValuesByEnumNames(Collections.singletonList(
                        String.valueOf(condition.getDeliveryType())), 2);
            }
        }

        Integer shopWhiteListType;
        if (condition.getShopWhiteListType() != null) {
            shopWhiteListType = condition.getShopWhiteListType();
        } else {
            shopWhiteListType = -1;
        }

        LogUtils.info(log, "param1 ==> condition:{}", condition);
        LogUtils.info(log, "param2 ==> listingIds:{}, excludeListingIds:{}, categoryIds:{}, sourceGoodsIds:{}, shopTagValues:{}, deliveryTypeValues:{}",
                condition, listingIds, categoryIds, sourceGoodsIds, shopTagValues, deliveryTypeValues);

        IPage<ListingInfo> page = listingInfoService.lambdaQuery()
                .eq(condition.getListingType() != null, ListingInfo::getListingType, condition.getListingType())
                .and(shopWhiteListType == 0, wrapper -> wrapper.eq(ListingInfo::getShopWhiteListType, 0)
                        .eq(ListingInfo::getShopTag, 0)
                        .eq(ListingInfo::getDeliveryType, 0))
                .and(shopWhiteListType == 1, wrapper -> wrapper.ne(ListingInfo::getShopWhiteListType, 0)
                        .or().ne(ListingInfo::getShopTag, 0)
                        .or().ne(ListingInfo::getDeliveryType, 0))
                .eq(allShopTag, ListingInfo::getShopTag, 0)
                .in(CollectionUtils.isNotEmpty(shopTagValues), ListingInfo::getShopTag, shopTagValues)
                .eq(allDeliveryType, ListingInfo::getDeliveryType, 0)
                .in(CollectionUtils.isNotEmpty(deliveryTypeValues), ListingInfo::getDeliveryType, deliveryTypeValues)
                .in(CollectionUtils.isNotEmpty(listingIds), ListingInfo::getId, listingIds)
                .in(Objects.equals(condition.getShowTop(), 1), ListingInfo::getId, existTopListingIds)
                .notIn(Objects.equals(condition.getShowTop(), 0), ListingInfo::getId, existTopListingIds)
                .in(CollectionUtils.isNotEmpty(categoryIds), ListingInfo::getCategoryId, categoryIds)
                .in(CollectionUtils.isNotEmpty(sourceGoodsIds), ListingInfo::getSourceGoodsId, sourceGoodsIds)
                .eq(StringUtils.isNotBlank(condition.getName()), ListingInfo::getName, condition.getName())
                .eq(ListingInfo::getIsDel, 0)
                .eq(condition.getStatus() != null, ListingInfo::getStatus, condition.getStatus())
                .eq(condition.getAlgorithmStatus() != null, ListingInfo::getAlgorithmStatus, condition.getAlgorithmStatus())
                .eq(StringUtils.isNotBlank(condition.getCreateUser()), ListingInfo::getCreateUser, condition.getCreateUser())
                .eq(StringUtils.isNotBlank(condition.getUpdateUser()), ListingInfo::getUpdateUser, condition.getUpdateUser())
                .ge(condition.getStartCreateTime() != null, ListingInfo::getCreateTime, condition.getStartCreateTime())
                .le(condition.getEndCreateTime() != null, ListingInfo::getCreateTime, condition.getEndCreateTime())
                .ge(condition.getStartUpdateTime() != null, ListingInfo::getUpdateTime, condition.getStartUpdateTime())
                .le(condition.getEndUpdateTime() != null, ListingInfo::getUpdateTime, condition.getEndUpdateTime())
                .ge(condition.getMinHot() != null, ListingInfo::getHot, condition.getMinHot())
                .le(condition.getMaxHot() != null, ListingInfo::getHot, condition.getMaxHot())
                .ge(condition.getMinSevenSale() != null, ListingInfo::getSevenSale, condition.getMinSevenSale())
                .le(condition.getMaxSevenSale() != null, ListingInfo::getSevenSale, condition.getMaxSevenSale())
                .gt(condition.getExistSign() == 1, ListingInfo::getFollowCount, 1)
                .eq(condition.getExistSign() == 2, ListingInfo::getFollowCount, 1)
                .orderBy(condition.getSort() == 1, condition.getIsAsc() == 1, ListingInfo::getSevenSale)
                .orderBy(condition.getSort() == 2, condition.getIsAsc() == 1, ListingInfo::getHot)
                .orderBy(condition.getSort() == 3, condition.getIsAsc() == 1, ListingInfo::getFollowCount)
                .orderBy(condition.getSort() == 4, condition.getIsAsc() == 1, ListingInfo::getCreateTime)
                .orderBy(condition.getSort() == 5, condition.getIsAsc() == 1, ListingInfo::getUpdateTime)
                .page(new Page<>(condition.getPageNow(), condition.getPageSize()));

        List<ListingInfo> records = page.getRecords();
        LogUtils.info(log, "records.size:{}", CollectionUtils.size(records));
        if (CollectionUtils.isEmpty(records)) {
            return pageView;
        }

        List<Long> goodsIdList = records.stream().map(ListingInfo::getGoodsId).collect(Collectors.toList());
        Map<Long, Goods> goodsMap = goodsService.queryGoodsByAllIds(goodsIdList)
                .stream()
                .collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

        List<GoodsExtConfig> customTagGoods = goodsExtConfigService.selectGoodsTagConfig(goodsIdList,CUSTOM_GOODS_TAG_ID);
        Set<Long> customTagGoodsIdSet = customTagGoods.stream().map(GoodsExtConfig::getGoodsId).collect(Collectors.toSet());

        List<Long> categoryIdList = records.stream().map(ListingInfo::getCategoryId).collect(Collectors.toList());
        Map<Long, String> categoryPathMap = categoryTreeCoreService.getCategoryPathByIds(categoryIdList);

        List<Long> listingIdList = records.stream().map(ListingInfo::getId).collect(Collectors.toList());
//        Map<Long, List<ListingFollowGoods>> listingFollowGoodsGroupMap = listingFollowGoodsService.lambdaQuery()
//                .in(ListingFollowGoods::getListingId, listingIdList)
//                .eq(ListingFollowGoods::getIsDel, 0)
//                .list()
//                .stream().collect(Collectors.groupingBy(ListingFollowGoods::getListingId));

        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getListingId, listingIdList)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .list();

        List<Long> allFollowGoodsIds = listingFollowGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        List<Long> showGoodsIds;
        if (CollectionUtils.isNotEmpty(allFollowGoodsIds)) {
            showGoodsIds = goodsService.lambdaQuery()
                    .in(Goods::getId, allFollowGoodsIds)
                    .eq(Goods::getIsDel, 0)
                    .eq(Goods::getIsShow, GoodsIsShowEnums.SHELF.getType().toString())
                    .select(Goods::getId)
                    .list().stream().map(Goods::getId).collect(Collectors.toList());
        } else {
            showGoodsIds = Lists.newArrayList();
        }

        Map<Long, List<ListingFollowGoods>> listingFollowGoodsGroupMap = listingFollowGoodsList.stream().collect(Collectors.groupingBy(ListingFollowGoods::getListingId));

        ListingInfoQueryCondition finalCondition = condition;
        List<ListingInfoListVo> voList = records.stream()
                .filter(listingInfo -> {
                    if (finalCondition.getExistSign() == 2) {
                        List<ListingFollowGoods> listingFollowGoods = listingFollowGoodsGroupMap.getOrDefault(listingInfo.getId(), Collections.emptyList());
                        return listingFollowGoods.size() == 1 && listingFollowGoods.get(0).getGoodsId().equals(listingInfo.getSourceGoodsId());
                    }
                    return true;
                })
                .map(listingInfo -> {
                    ListingInfoListVo vo = BeanCopyUtil.transform(listingInfo, ListingInfoListVo.class);
                    vo.setListingId(listingInfo.getId());
                    vo.setCategoryName(categoryPathMap.get(listingInfo.getCategoryId()));
                    Goods goods = goodsMap.get(listingInfo.getGoodsId());
                    if (goods != null) {
                        vo.setMainImage(goods.getMainImage());
                        vo.setCountry(goods.getCountry());
                    }

                    if (listingInfo.getShopWhiteListType() == 0 && listingInfo.getShopTag() == 0
                            && listingInfo.getDeliveryType() == 0) {
                        vo.setShopWhiteList("all");
                    } else {
                        vo.setShopWhiteList("部分");
                    }

                    if (listingInfo.getShopTag() != 0) {
                        List<String> names = searchTypeMatchBinaryConfigService.getEnumNamesByDecimalValue(listingInfo.getShopTag(), 1);
                        List<Long> shopTags = names.stream().map(Long::parseLong).collect(Collectors.toList());
                        vo.setShopTags(shopTags);
                    }
                    if (listingInfo.getDeliveryType() != 0) {
                        List<String> names = searchTypeMatchBinaryConfigService.getEnumNamesByDecimalValue(listingInfo.getDeliveryType(), 2);
                        List<Integer> deliveryTypes = names.stream().map(Integer::parseInt).collect(Collectors.toList());
                        vo.setDeliveryTypes(deliveryTypes);
                    }

                    vo.setSuggestPrice(listingInfo.getMinPrice() + "~" + listingInfo.getMaxPrice());

                    List<ListingFollowGoods> listingFollowGoods = listingFollowGoodsGroupMap.getOrDefault(listingInfo.getId(), Collections.emptyList());
                    long size = CollectionUtils.isEmpty(listingFollowGoods) ? 0 : listingFollowGoods.stream().filter(followGoods -> showGoodsIds.contains(followGoods.getGoodsId())).count();
                    vo.setMatchGoodsSize(Math.toIntExact(size));

                    vo.setExistTop(listingFollowGoods.stream().anyMatch(followGoods -> followGoods.getIsTop() == 1) ? 1 : 0);
                    vo.setSourceGoodsId(listingInfo.getSourceGoodsId());

                    if (customTagGoodsIdSet.contains(listingInfo.getGoodsId())){
                        vo.setIsCustomGoods(true);
                    }
                    return vo;
                }).collect(Collectors.toList());


        pageView.setRecords(voList);
        pageView.setRowCount(page.getTotal());
        return pageView;
    }

    @Override
    public void exportListingInfo(ListingInfoQueryCondition condition) {
        Long taskId = taskInit("listingTemplate" + System.currentTimeMillis() + ".xlsx", getUserId());
        LogUtils.info(log, "异步导出listing模板列表 taskId:{}", taskId);
        String traceId = getTraceId();

        if (CollectionUtils.isNotEmpty(condition.getIds())) {
            String idsStr = StringUtils.join(condition.getIds(), "\n");
            condition = new ListingInfoQueryCondition();
            condition.setListingIdListStr(idsStr);
        }
        condition.setPageSize(10000);
        ListingInfoQueryCondition finalCondition = condition;

        executor.execute(() -> {
            PageView<ListingInfoListVo> pageView = pageListingInfo(finalCondition);
            List<ListingInfoListVo> records = pageView.getRecords();
            LogUtils.info(log, "result taskId:{}, result.size:{}, traceId:{}", taskId, records != null ? records.size() : 0, traceId);
            if (CollectionUtils.isEmpty(records)) {
                return;
            }

            List<Long> listingIds = records.stream().map(ListingInfoListVo::getListingId).collect(Collectors.toList());
            List<ListingFollowGoods> followGoodsList = listingFollowGoodsService.lambdaQuery()
                    .in(ListingFollowGoods::getListingId, listingIds)
                    .eq(ListingFollowGoods::getIsDel, 0)
                    .select(ListingFollowGoods::getGoodsId, ListingFollowGoods::getListingId)
                    .list();
            Map<Long, List<ListingFollowGoods>> listingFollowGoodsGroupMap = followGoodsList.stream().collect(Collectors.groupingBy(ListingFollowGoods::getListingId));

            List<Long> followGoodsIds = followGoodsList.stream().map(ListingFollowGoods::getGoodsId).distinct().collect(Collectors.toList());
            List<Long> validFollowGoodsIds = goodsService.lambdaQuery()
                    .in(Goods::getId, followGoodsIds)
                    .eq(Goods::getIsDel, 0)
                    .eq(Goods::getIsShow, GoodsIsShowEnums.SHELF.getType().toString())
                    .select(Goods::getId)
                    .list()
                    .stream().map(Goods::getId).collect(Collectors.toList());

            List<ListingInfoExportVo> vos = records.stream().map(listingInfoListVo -> {
                ListingInfoExportVo exportVo = BeanCopyUtil.transform(listingInfoListVo, ListingInfoExportVo.class);
                //0草稿中 1已发布 -1关闭中
                exportVo.setStatusStr(listingInfoListVo.getStatus() == 0 ? "草稿中" : listingInfoListVo.getStatus() == 1 ? "已发布" : "关闭中");

                List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsGroupMap.get(exportVo.getListingId());
                if (CollectionUtils.isNotEmpty(listingFollowGoodsList)) {
                    String validFollowGoodsIdListStr = listingFollowGoodsList.stream()
                            .map(ListingFollowGoods::getGoodsId)
                            .filter(validFollowGoodsIds::contains)
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    exportVo.setFollowGoodsIds(validFollowGoodsIdListStr);
                }
                return exportVo;
            }).collect(Collectors.toList());

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ExcelWriter writer;
            byte[] bytes = new byte[0];
            try {
                writer = new ExcelWriterBuilder()
                        .autoCloseStream(true)
                        .excelType(ExcelTypeEnum.XLSX)
                        .file(byteArrayOutputStream)
                        .head(ListingInfoExportVo.class)
                        .build();

                WriteSheet writeSheet = new WriteSheet();
                writeSheet.setSheetName("listing");
                writer.write(vos, writeSheet);
                writer.finish();

                bytes = byteArrayOutputStream.toByteArray();
            } catch (Exception e) {
                e.printStackTrace();
            }
            LogUtils.info(log, "exportListingInfo ..1 traceId:{}", traceId);
            String fileUrl = null;
            //todo 兼容测试环境
            if (env.equals("test") || env.equals("dev")) {
                LogUtils.info(log, "exportListingInfo ..2 traceId:{}", traceId);
                String url = "https://pre-tools.voghion.com/buss-common/upload/fileInfo";

                try {
                    HttpClient httpClient = HttpClients.createDefault();
                    MultipartEntityBuilder builder = MultipartEntityBuilder.create();
                    builder.addBinaryBody("file", bytes, ContentType.DEFAULT_BINARY, "listingTemplate" + System.currentTimeMillis() + ".xlsx");

                    HttpPost httpPost = new HttpPost(url);
                    httpPost.setEntity(builder.build());
//                    httpPost.addHeader("content-type", "multipart/form-data;");

                    HttpResponse response = httpClient.execute(httpPost);
                    Result result = JSON.parseObject(EntityUtils.toString(response.getEntity()), Result.class);
                    fileUrl = (String) result.getData();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                LogUtils.info(log, "exportListingInfo ..3 traceId:{}", traceId);
                fileUrl = imageClientFactory.upload(bytes, "listingTemplate" + System.currentTimeMillis() + ".xlsx");
                LogUtils.info(log, "exportListingInfo ..4 traceId:{}", traceId);
            }

            if(StringUtils.isNotBlank(fileUrl)){
                LogUtils.info(log, "exportListingInfo ..5 fileUrl:{}, traceId:{}",fileUrl, traceId);
                taskFinish(taskId, fileUrl);
            }
        });
    }

    @Override
    public List<Long> checkSaveListingInfo(GoodsIdsStrCondition condition) {
        CheckUtils.notEmpty(condition.getGoodsIdListStr(), ProductResultCode.PARAMETER_ID_ERROR);

        List<Long> goodsIds = Arrays.stream(condition.getGoodsIdListStr().split("\n")).map(Long::parseLong).collect(Collectors.toList());
        CheckUtils.check(goodsIds.size() > 1000, ProductResultCode.LIMIT_1000);

        return goodsExtConfigService.lambdaQuery()
                .eq(GoodsExtConfig::getTagId, 106L)
                .in(GoodsExtConfig::getGoodsId, goodsIds)
                .in(GoodsExtConfig::getIsDel, 0)
                .list().stream().map(GoodsExtConfig::getGoodsId).distinct().collect(Collectors.toList());
    }

    @Override
    public void saveListingInfo(GoodsIdsStrCondition condition) {
        LogUtils.info(log, "新增竞标商品 condition:{}", condition);
        CheckUtils.notEmpty(condition.getGoodsIdListStr(), ProductResultCode.PARAMETER_ID_ERROR);

        List<Long> goodsIds = Arrays.stream(condition.getGoodsIdListStr().split("\n")).map(Long::parseLong).collect(Collectors.toList());
        CheckUtils.check(goodsIds.size() > 1000, ProductResultCode.LIMIT_1000);

        Object lock = redisApi.get("BATCH_SAVE_LISTING_LOCK");
        CheckUtils.check(lock != null, ProductResultCode.TRY_LOCK_FAIL);

        GoodsOperationResultVo vo = null;
        try {
            redisApi.set("BATCH_SAVE_LISTING_LOCK", 1, 300);
            vo = batchSaveListingInfo(goodsIds);
        } catch (Exception e) {
            throw e;
        } finally {
            redisApi.del("BATCH_SAVE_LISTING_LOCK");
        }
        if (vo != null && CollectionUtils.isNotEmpty(vo.getFailGoodsIds())) {
            CheckUtils.check(true, CustomResultCode.fill(ProductResultCode.LISTING_ADD_GOODS_ERROR, StringUtils.join(vo.getFailGoodsIds(), ",")));
        }
    }

    @Override
    public GoodsOperationResultVo batchSaveListingInfo(List<Long> sourceGoodsIds) {
        LogUtils.info(log, "批量新增竞标商品:{}", sourceGoodsIds);
        CheckUtils.isEmpty(sourceGoodsIds, ProductResultCode.PARAMETER_ID_ERROR);
        sourceGoodsIds = sourceGoodsIds.stream().distinct().collect(Collectors.toList());
        List<Long> originalGoodsIds = Lists.newArrayList(sourceGoodsIds);

        String operator = getUserName();
        LocalDateTime now = LocalDateTime.now();
        List<Long> successGoodsIds = Lists.newArrayList();
        List<Long> failGoodsIds = Lists.newArrayList();

        List<Long> existListingGoodsIds = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getGoodsId, sourceGoodsIds)
                .eq(ListingFollowGoods::getIsDel, 0)
                .select(ListingFollowGoods::getGoodsId)
                .list()
                .stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        listingInfoService.lambdaQuery()
                .in(ListingInfo::getSourceGoodsId, sourceGoodsIds)
                .eq(ListingInfo::getIsDel, 0)
                .select(ListingInfo::getSourceGoodsId)
                .list()
                .stream().map(ListingInfo::getSourceGoodsId).forEach(existListingGoodsIds::add);
        existListingGoodsIds = existListingGoodsIds.stream().distinct().collect(Collectors.toList());
        LogUtils.info(log, "批量新增竞标商品 existListingGoodsIds:{}", existListingGoodsIds);

        if (CollectionUtils.isNotEmpty(existListingGoodsIds)) {
            failGoodsIds.addAll(existListingGoodsIds);
            sourceGoodsIds.removeAll(existListingGoodsIds);
        }

        CheckUtils.isEmpty(sourceGoodsIds, CustomResultCode.fill(ProductResultCode.LISTING_GOODS_ALREADY_EXIST, originalGoodsIds.stream().map(Objects::toString).collect(Collectors.joining(";"))));

        List<Goods> sourceGoodsList = goodsService.queryGoodsByIds(sourceGoodsIds);
        CheckUtils.isEmpty(sourceGoodsList, ProductResultCode.GOODS_NOT_EXIST);
//        goodsList.forEach(goods -> CheckUtils.check(goods.getType() == 3, ProductResultCode.LISTING_CANNOT_FROM_WHOLESALE));

        sourceGoodsIds = sourceGoodsList.stream().map(Goods::getId).collect(Collectors.toList());
        Map<Long, BigDecimal> sourceGoodsVatMap = goodsVatConfigService.lambdaQuery()
                .in(GoodsVatConfig::getGoodsId, sourceGoodsIds)
                .eq(GoodsVatConfig::getEffectStatus, 1)
                .list()
                .stream().collect(Collectors.toMap(GoodsVatConfig::getGoodsId, GoodsVatConfig::getVat, (v1, v2) -> v2));

        List<Long> sourceShopIds = sourceGoodsList.stream().map(Goods::getShopId).distinct().collect(Collectors.toList());
        Map<Long, Integer> sourceShopDeliveryTypeMap = faMerchantsApplyService.lambdaQuery()
                .in(FaMerchantsApply::getId, sourceShopIds)
                .list()
                .stream().collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getDeliveryType, (v1, v2) -> v1));

        Map<Long, List<Long>> goodsTagGroupMap = goodsExtConfigService.lambdaQuery()
                .in(GoodsExtConfig::getGoodsId, sourceGoodsIds)
                .in(GoodsExtConfig::getTagId, Arrays.asList(40, 50))
                .eq(GoodsExtConfig::getIsDel, 0)
                .list()
                .stream()
                .collect(Collectors.groupingBy(GoodsExtConfig::getGoodsId, Collectors.mapping(GoodsExtConfig::getTagId, Collectors.toList())));

        Map<Long, List<Long>> shopTagGroupMap = faMerchantsTagService.lambdaQuery()
                .in(FaMerchantsTag::getShopId, sourceShopIds)
                .eq(FaMerchantsTag::getStatus, 1)
                .list()
                .stream()
                .collect(Collectors.groupingBy(FaMerchantsTag::getShopId, Collectors.mapping(FaMerchantsTag::getTagId, Collectors.toList())));

        Map<String, VatDto> vatMap = goodsEsService.getVatMap();
        List<Goods> tongdunGoodsList = new ArrayList<>();
        for (Goods sourceGoods : sourceGoodsList) {
            LogUtils.info(log, "批量新增竞标商品 开始goodsId:{}", sourceGoods.getId());
            Long sourceGoodsId = sourceGoods.getId();
            if (sourceGoods.getType() == 3) {
                failGoodsIds.add(sourceGoodsId);
                continue;
            }

            Goods goods;
            try {
                GoodsCopyDto copyDto = new GoodsCopyDto();
                copyDto.setGoodsId(sourceGoodsId);
                copyDto.setType(2);
                copyDto.setShopId(CommonConstants.LISTING_GOODS_SHOP_ID);
                copyDto.setShopName(CommonConstants.LISTING_GOODS_SHOP_NAME);
                copyDto.setIsShow(GoodsIsShowEnums.TEMPLATE);
                copyDto.setIsLock(0);
                copyDto.setNeedTag(true);
                copyDto.setSourceGoodsDeliveryType(sourceShopDeliveryTypeMap.get(sourceGoods.getShopId()));
                goods = newAddGoodsCoreService.copyAddGoods(copyDto);
            } catch (Exception e) {
                LogUtils.error(log, "批量新增竞标商品 transformChanceGoods copy失败 源goodsId:{}", sourceGoodsId, e);
                failGoodsIds.add(sourceGoodsId);
                continue;
            }

            Integer type;
            Integer shopTag;
            List<GoodsExtConfig> goodsExtConfigs = goodsExtConfigService.lambdaQuery()
                    .eq(GoodsExtConfig::getGoodsId, sourceGoodsId)
                    .in(GoodsExtConfig::getTagId, Arrays.asList(40, 45, 50))
                    .eq(GoodsExtConfig::getIsDel, 0)
                    .list();
            if (CollectionUtils.isNotEmpty(goodsExtConfigs)) {
                // type改为1，标签改为7(146、507、514)
                type = 1;
                shopTag = 7;
            } else {
                type = 0;
                shopTag = 0;
            }

            //动态调价
            GoodsExtConfigModel goodsExtConfigModel = new GoodsExtConfigModel();
            goodsExtConfigModel.setTagIds(goodsTagGroupMap.get(sourceGoodsId));
            goodsExtConfigModel.setShopTagIds(shopTagGroupMap.get(sourceGoods.getShopId()));
            BigDecimal vatRate = goodsEsService.getVatRate(
                    new VatCondition()
                            .setVatRate(sourceGoodsVatMap.get(sourceGoodsId))
                            .setGoodsId(sourceGoodsId)
                            .setCountry(SaleableCountryEnum.DE.name())
                            .setCategoryId(sourceGoods.getCategoryId())
                            .setShopId(sourceGoods.getShopId())
                            .setGoodsExtConfigModel(goodsExtConfigModel)
                            .setDeliveryType(sourceShopDeliveryTypeMap.getOrDefault(sourceGoods.getShopId(), DeliveryTypeEnum.AGENT.getCode()))
                            .setVatMap(vatMap));

            List<ListingInfo> deletedListingInfos = listingInfoService.lambdaQuery()
                    .eq(ListingInfo::getSourceGoodsId, sourceGoodsId)
                    .eq(ListingInfo::getIsDel, 1)
                    .list();
            ListingInfo listingInfo;
            if (CollectionUtils.isNotEmpty(deletedListingInfos)) {
                listingInfo = deletedListingInfos.get(0);
            } else {
                Random random = new Random();
                Long listingId = (long) (random.nextInt(900000000) + 100000000);
                listingInfo = listingInfoService.getById(listingId);
                while (listingInfo != null) {
                    listingId = (long) (random.nextInt(900000000) + 100000000);
                    listingInfo = listingInfoService.getById(listingId);
                }
                LogUtils.info(log, "批量新增竞标商品 源goodsId:{}, 生成listingId:{}", sourceGoodsId, listingId);
                listingInfo = new ListingInfo();
                listingInfo.setId(listingId);
                listingInfo.setSourceGoodsId(sourceGoodsId);
            }
            listingInfo.setGoodsId(goods.getId());
            listingInfo.setName(goods.getName());
            listingInfo.setCategoryId(goods.getCategoryId());
            listingInfo.setMinPrice(goods.getMinPrice());
            listingInfo.setMaxPrice(goods.getMaxPrice());
            listingInfo.setBaseVatRate(vatRate);
            listingInfo.setBasePrice(goodsEsService.getVatPrice(vatRate, goods.getMaxPrice()));
            listingInfo.setAlgorithmStatus(0);
            listingInfo.setStatus(0);
            listingInfo.setListingType(type);
//            if (directShopIds.contains(toAddGoods.getShopId())) {
//                listingInfo.setDeliveryType(DeliveryTypeBinaryEnum.DIRECT.getValue());
//            }
            listingInfo.setShopTag(shopTag);
            listingInfo.setHot(new Random().nextInt(100) + 1);
            listingInfo.setFollowCount(sourceGoods.getIsDel() == 1 ? 0 : 1);
            listingInfo.setIsDel(0);
            listingInfo.setCreateTime(now);
            listingInfo.setUpdateTime(now);
            listingInfo.setCreateUser(operator);
            listingInfo.setUpdateUser(operator);
            listingInfoService.saveOrUpdate(listingInfo);
            LogUtils.info(log, "listingId:{}", listingInfo.getId());


            List<ListingFollowGoods> deletedListingFollowGoods = listingFollowGoodsService.lambdaQuery()
                    .eq(ListingFollowGoods::getGoodsId, sourceGoodsId)
                    .eq(ListingFollowGoods::getListingId, listingInfo.getId())
                    .eq(ListingFollowGoods::getIsDel, 1)
                    .list();
            ListingFollowGoods listingFollowGoods;
            if (CollectionUtils.isNotEmpty(deletedListingFollowGoods)) {
                listingFollowGoods = deletedListingFollowGoods.get(0);
            } else {
                listingFollowGoods = new ListingFollowGoods();
                listingFollowGoods.setListingId(listingInfo.getId());
                listingFollowGoods.setGoodsId(sourceGoodsId);
            }

            listingFollowGoods.setShopId(sourceGoods.getShopId());
            listingFollowGoods.setSort(999);
            listingFollowGoods.setScoreSort(999);
            listingFollowGoods.setCountryScoreSort(getCountryScoreSort(sourceGoods));
            listingFollowGoods.setIsTop(0);
            listingFollowGoods.setStatus(1);
            listingFollowGoods.setIsDel(sourceGoods.getIsDel());
            listingFollowGoods.setCreateTime(now);
            listingFollowGoods.setUpdateTime(now);
            listingFollowGoodsService.saveOrUpdate(listingFollowGoods);

//            TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("id", goodsId);
//            Map<String, Object> map = new HashMap<>();
//            map.put("listingId", listingId);
//            goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), termQueryBuilder, map);

            successGoodsIds.add(sourceGoodsId);
            tongdunGoodsList.add(goods);
        }

        if (CollectionUtils.isNotEmpty(successGoodsIds)) {
            for (Long successGoodsId : successGoodsIds) {
                GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                goodsSyncModel.setGoodsId(successGoodsId);
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("批量新增竞标商品");
                goodsSyncModel.setSourceService("vp");
                mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", goodsSyncModel, MqDelayLevel.FIVE_SEC);
            }
        }

        if (CollectionUtils.isNotEmpty(tongdunGoodsList)) {
            List<Long> tdGoodsIds = tongdunGoodsList.stream().map(Goods::getId).collect(Collectors.toList());

            List<Long> tagIds = new ArrayList<>(goodsExtConfigCoreService.getComplianceLabelTagIds());
            tagIds.add(99L);

            List<Long> noReviewGoodsIds = goodsExtConfigCoreService.selectGoodsIdsBuyTag(tdGoodsIds, tagIds);
            List<Goods> reviewGoods = tongdunGoodsList.stream().filter(g -> !noReviewGoodsIds.contains(g.getId())).collect(Collectors.toList());

            tongDunGoodsImageCoreService.addTongDun(reviewGoods, 98);
        }

        return new GoodsOperationResultVo(successGoodsIds, failGoodsIds);
    }

    @NotNull
    private static String getCountryScoreSort(Goods goods) {
        List<String> sellableCountriesList = getSellableCountriesList(goods.getCountry());
        AssertsUtils.isValidateTrue(CollectionUtils.isNotEmpty(sellableCountriesList), "该商品没有可售国家.");

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < sellableCountriesList.size(); i++) {
            if (i != 0) {
                sb.append(",");
            }

            String country = sellableCountriesList.get(i);
            sb.append(country).append(":999");
        }

        return sb.toString();
    }

    @Override
    public void deleteListingInfo(ListingCondition condition) {
        CheckUtils.check(condition == null, ProductResultCode.PARAMETER_ID_ERROR);
        CheckUtils.check(CollectionUtils.isEmpty(condition.getListingIds()), ProductResultCode.PARAMETER_ID_ERROR);

        List<Long> listingIds = condition.getListingIds().stream().filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());

        for (Long listingId : listingIds) {
            ListingInfo listingInfo = listingInfoService.getById(listingId);
            if (listingInfo == null) {
                continue;
            }

            List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                    .eq(ListingFollowGoods::getListingId, listingId)
                    .eq(ListingFollowGoods::getIsDel, 0)
                    .list();

            listingInfo.setIsDel(1);
            listingInfo.setUpdateTime(LocalDateTime.now());
            listingInfo.setUpdateUser(getUserName());
            listingInfoService.updateById(listingInfo);

            if (CollectionUtils.isNotEmpty(listingFollowGoodsList)) {
                listingFollowGoodsService.lambdaUpdate()
                        .set(ListingFollowGoods::getIsDel, 1)
                        .set(ListingFollowGoods::getUpdateTime, LocalDateTime.now())
                        .set(ListingFollowGoods::getUpdateUser, getUserName())
                        .in(ListingFollowGoods::getId, listingFollowGoodsList.stream().map(ListingFollowGoods::getId).collect(Collectors.toList()))
                        .update();

                for (ListingFollowGoods listingFollowGoods : listingFollowGoodsList) {
                    GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                    goodsSyncModel.setGoodsId(listingFollowGoods.getGoodsId());
                    goodsSyncModel.setSyncTime(System.currentTimeMillis());
                    goodsSyncModel.setBusiness("删除listing商品");
                    goodsSyncModel.setSourceService("vp");
                    mqSender.send("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel));
                }
            }

            Goods goods = goodsService.getById(listingInfo.getGoodsId());
            if (goods != null && goods.getIsDel() == 0) {
                GoodsIsDelInput goodsIsDelInput = new GoodsIsDelInput();
                goodsIsDelInput.setGoodsIds(Lists.newArrayList(goods.getId()));
                goodsCoreService.isDel(goodsIsDelInput);
            }
        }
    }

    @Override
    public void deleteListingFollowGoods(ListingFollowGoodsCondition condition) {
        CheckUtils.check(condition == null, ProductResultCode.PARAMETER_ID_ERROR);
        CheckUtils.check(CollectionUtils.isEmpty(condition.getListingFollowGoodsIds()), ProductResultCode.PARAMETER_ID_ERROR);

        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getGoodsId, condition.getListingFollowGoodsIds())
                .eq(ListingFollowGoods::getIsDel, 0)
                .list();

        Map<Long, List<ListingFollowGoods>> listingMap = listingFollowGoodsList.stream().collect(Collectors.groupingBy(ListingFollowGoods::getListingId));

        if (MapUtils.isNotEmpty(listingMap)) {
            listingFollowGoodsService.lambdaUpdate().set(ListingFollowGoods::getIsDel, 1)
                    .set(ListingFollowGoods::getUpdateTime, LocalDateTime.now())
                    .set(ListingFollowGoods::getUpdateUser, getUserName())
                    .in(ListingFollowGoods::getId, listingFollowGoodsList.stream().map(ListingFollowGoods::getId).collect(Collectors.toList()))
                    .update();

            for (ListingFollowGoods listingFollowGoods : listingFollowGoodsList) {
                GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                goodsSyncModel.setGoodsId(listingFollowGoods.getGoodsId());
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("删除listing关联商品");
                goodsSyncModel.setSourceService("vp");
                mqSender.send("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel));
            }

            listingMap.forEach((k, v) -> {
                ListingInfo listingInfo = listingInfoService.getById(k);
                if (listingInfo != null) {
                    Integer followCount = Math.max(0, listingInfo.getFollowCount() - v.size());
                    listingInfoService.lambdaUpdate()
                            .set(ListingInfo::getFollowCount, followCount)
                            .set(ListingInfo::getUpdateTime, LocalDateTime.now())
                            .eq(ListingInfo::getId, listingInfo.getId())
                            .update();
                }
            });
        }
    }

    @Override
    public void updateStatus(ListingInfoUpdateStatusCondition condition) {
        LogUtils.info(log, "发布/取消发布listing模板 condition:{}", condition);
        CheckUtils.check(condition.getListingId() == null || condition.getStatus() == null, ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(condition.getStatus() != null && condition.getStatus() != 1 && condition.getStatus() != -1, ProductResultCode.PARAMETER_ERROR);

        ListingInfo listingInfo = listingInfoService.getById(condition.getListingId());
        CheckUtils.notNull(listingInfo, ProductResultCode.LISTING_NOT_EXIST);

        listingInfo.setStatus(condition.getStatus());
        listingInfo.setUpdateTime(LocalDateTime.now());
        listingInfo.setUpdateUser(getUserName());
        listingInfoService.updateById(listingInfo);
    }

    @Override
    public void batchUpdateStatus(ListingInfoBatchUpdateStatusCondition condition) {
        LogUtils.info(log, "批量发布/批量关闭listing模板 condition:{}", condition);
        CheckUtils.check(CollectionUtils.isEmpty(condition.getListingIds()) || condition.getStatus() == null, ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(condition.getListingIds().size() > 100, ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(condition.getStatus() != null && condition.getStatus() != 1 && condition.getStatus() != -1, ProductResultCode.PARAMETER_ERROR);
        List<ListingInfo> listingInfos = Lists.newArrayList(listingInfoService.listByIds(condition.getListingIds()));
        CheckUtils.check(listingInfos.isEmpty(), ProductResultCode.LISTING_NOT_EXIST);
        listingInfos.forEach(item -> {
            item.setStatus(condition.getStatus());
            item.setUpdateTime(LocalDateTime.now());
            item.setUpdateUser(getUserName());
        });
        listingInfoService.updateBatchById(listingInfos);
    }

    @Override
    public void updateAlgorithmStatus(ListingInfoUpdateAlgorithmStatusCondition condition) {
        LogUtils.info(log, "变更listing模板算法状态 condition:{}", condition);
        CheckUtils.check(condition.getListingId() == null || condition.getAlgorithmStatus() == null, ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(condition.getAlgorithmStatus() != null && condition.getAlgorithmStatus() != 1 && condition.getAlgorithmStatus() != -1, ProductResultCode.PARAMETER_ERROR);

        ListingInfo listingInfo = listingInfoService.getById(condition.getListingId());
        CheckUtils.notNull(listingInfo, ProductResultCode.LISTING_NOT_EXIST);

        listingInfo.setAlgorithmStatus(condition.getAlgorithmStatus());
        listingInfo.setUpdateTime(LocalDateTime.now());
        listingInfo.setUpdateUser(getUserName());
        listingInfoService.updateById(listingInfo);

        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, condition.getListingId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .list();
        if (CollectionUtils.isNotEmpty(listingFollowGoodsList)) {
            for (ListingFollowGoods listingFollowGoods : listingFollowGoodsList) {
                listingFollowGoods.setStatus(condition.getAlgorithmStatus());
                listingFollowGoods.setUpdateTime(LocalDateTime.now());
            }
            listingFollowGoodsService.updateBatchById(listingFollowGoodsList);
        }
    }

    @Override
    public void updateHot(ListingInfoUpdateHotCondition condition) {
        LogUtils.info(log, "修改listing模板热度 condition:{}", condition);
        CheckUtils.check(condition.getListingId() == null || condition.getHot() == null, ProductResultCode.PARAMETER_ERROR);

        ListingInfo listingInfo = listingInfoService.getById(condition.getListingId());
        CheckUtils.notNull(listingInfo, ProductResultCode.LISTING_NOT_EXIST);

        listingInfo.setHot(condition.getHot());
        listingInfo.setUpdateTime(LocalDateTime.now());
        listingInfo.setUpdateUser(getUserName());
        listingInfoService.updateById(listingInfo);
    }

    @Override
    public void batchUpdateHot(List<ListingGoodsHotUpdateVO> list) {
        LogUtils.info(log, "批量修改listing模板热度 condition:{}", list);
        CheckUtils.isEmpty(list,ProductResultCode.EXCEL_DATA_EMPTY);
        list.forEach(vo -> CheckUtils.check(vo.getHot() == null || vo.getListingId() == null, ProductResultCode.EXCEL_DATA_PRAM_NULL));

        Map<Long, Integer> map = list.stream().collect(Collectors.toMap(ListingGoodsHotUpdateVO::getListingId, ListingGoodsHotUpdateVO::getHot, (v1, v2) -> v2));
        List<ListingInfo> listingInfos = Lists.newArrayList(listingInfoService.listByIds(map.keySet()));
        CheckUtils.check(CollectionUtils.isEmpty(listingInfos), ProductResultCode.LISTING_NOT_EXIST);

        listingInfos.forEach(item -> {
            Integer hot = map.get(item.getId());
            if (hot > 1) {
                item.setHot(hot);
                item.setUpdateTime(LocalDateTime.now());
                item.setUpdateUser(getUserName());
            }
        });
        listingInfoService.updateBatchById(listingInfos);
    }

    @Override
    public void calculateListingGoodsHot() {
        LogUtils.info(log,"开始计算更新listing模板热度");
        ListingInfo listingInfo = new ListingInfo();
        listingInfo.setIsDel(0);
        int start =0 ,pageSize =200;
        int size = 200;
        while (size == 200) {
            start++;
            LogUtils.info(log, "start:{}", start);
            IPage<ListingInfo> listingInfoIPage = listingInfoService.selectPage(listingInfo, start, pageSize);
            if (listingInfoIPage != null) {
                List<ListingInfo> records = listingInfoIPage.getRecords();
                size = records.size();
                LogUtils.info(log, "size:{}", size);
                // listingId
                List<Long> listingIds = records.stream().map(ListingInfo::getId).collect(Collectors.toList());
                LambdaQueryWrapper<ListingFollowGoods> listingFollowGoodsLambdaQueryWrapper = Wrappers.lambdaQuery();
                listingFollowGoodsLambdaQueryWrapper
                        .in(ListingFollowGoods::getListingId,listingIds)
                        .eq(ListingFollowGoods::getIsDel,0);
                List<ListingFollowGoods> listingFollowGoods = listingFollowGoodsService.list(listingFollowGoodsLambdaQueryWrapper);

                Map<Long, List<ListingFollowGoods>> followGoodsGroupMap = listingFollowGoods.stream().collect(Collectors.groupingBy(ListingFollowGoods::getListingId));
                List<Long> goodsIds = listingFollowGoods.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());

                List<OutDbGoodsEveryDayDTO> outDbGoodsEveryDay = outDbGoodsEveryDayEsService.getGoodsSales(goodsIds, 15, OutDbGoodsEveryDayDTO.class);
                Map<Long, Integer> goodsMap = outDbGoodsEveryDay.stream().collect(Collectors.toMap(OutDbGoodsEveryDayDTO::getGoodsId, OutDbGoodsEveryDayDTO::getDealCnt, (v1, v2) -> v1));
//                Map<Long, Long> goodsMap = goodsService.listByIds(goodsIds).stream().collect(Collectors.toMap(Goods::getId, Goods::getSales));

                for (ListingInfo record : records) {
                    List<ListingFollowGoods> followGoodsList = followGoodsGroupMap.get(record.getId());
                    if (CollectionUtils.isNotEmpty(followGoodsList)) {
                        List<Long> followGoodsIds = followGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
                        Integer totalSales = followGoodsIds.stream().map(goodsMap::get).filter(Objects::nonNull).reduce(0, Integer::sum);
                        record.setHot(totalSales);
                    }
                }
                listingInfoService.updateBatchById(records);


            } else {
                LogUtils.info(log, "listingInfoIPage is null");
                size = 0;
            }
        }
        LogUtils.info(log,"计算更新listing模板热度 --> end");
    }

    private int  getHot(int virtualSales) {
        int hot;
        if(virtualSales ==0){
            hot = 12;
        }else if (0 < virtualSales && virtualSales <= 20) {
            hot = virtualSales * 11;
        } else if (virtualSales <= 90) {
            hot = virtualSales * 13;
        } else if (virtualSales <= 180) {
            hot = virtualSales * 15;
        } else {
            hot = virtualSales * 17;
        }
        return hot;
    }

    @Override
    public Map<String, Integer> queryFollowGoodsShowGroup(ListingFollowGoodsQueryCondition condition) {
        LogUtils.info(log, "查看指定listing模板关联商品不同在架状态的商品数量 listingId:{}", condition.getListingId());
        Map<String, Integer> resultMap = Maps.newHashMap();

        List<Long> followGoodsIds = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, condition.getListingId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .select(ListingFollowGoods::getGoodsId)
                .list()
                .stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(followGoodsIds)) {
            resultMap.put("全部", 0);
            return resultMap;
        }

        resultMap.put("全部", followGoodsIds.size());
        Map<String, List<Goods>> showGoodsGroupMap = goodsService.listByIds(followGoodsIds).stream().collect(Collectors.groupingBy(Goods::getIsShow));
        for (Map.Entry<String, List<Goods>> entry : showGoodsGroupMap.entrySet()) {
            resultMap.put(GoodsIsShowEnums.getDescByType(entry.getKey()), entry.getValue().size());
        }
        return resultMap;
    }

    @Override
    public PageView<ListingFollowGoodsVo> pageListingFollowGoods(ListingFollowGoodsQueryCondition condition) {
        LogUtils.info(log, "查看指定listing模板的关联商品列表 condition:{}", condition);
        CheckUtils.notNull(condition.getListingId(), ProductResultCode.PARAMETER_ID_ERROR);

        PageView<ListingFollowGoodsVo> pageView = new PageView<>(condition.getPageSize(), condition.getPageNow());

        List<ListingFollowGoods> allFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, condition.getListingId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .list();
        if (CollectionUtils.isEmpty(allFollowGoodsList)) {
            return pageView;
        }
        pageView.setRowCount(allFollowGoodsList.size());

        if (condition.getIsShow() != null) {
            List<Long> goodsIdList = allFollowGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
            List<Long> filterGoodsIdList = goodsService.lambdaQuery()
                    .in(Goods::getId, goodsIdList)
                    .eq(Goods::getIsDel, 0)
                    .eq(Goods::getIsShow, condition.getIsShow().toString())
                    .select(Goods::getId)
                    .list()
                    .stream().map(Goods::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterGoodsIdList)) {
                pageView.setRowCount(0);
                return pageView;
            }
            allFollowGoodsList = allFollowGoodsList.stream().filter(followGoods -> filterGoodsIdList.contains(followGoods.getGoodsId())).collect(Collectors.toList());
            pageView.setRowCount(allFollowGoodsList.size());
        }

        List<ListingFollowGoods> records = allFollowGoodsList.stream()
                .sorted((o1, o2) -> o2.getIsTop().compareTo(o1.getIsTop()) == 0 ? o1.getScoreSort().compareTo(o2.getScoreSort()) : o2.getIsTop().compareTo(o1.getIsTop()))
                .skip((condition.getPageNow() - 1) * condition.getPageSize())
                .limit(condition.getPageSize())
                .collect(Collectors.toList());


//        IPage<ListingFollowGoods> page = listingFollowGoodsService.lambdaQuery()
//                .eq(ListingFollowGoods::getListingId, condition.getListingId())
//                .eq(ListingFollowGoods::getIsDel, 0)
//                .eq(ListingFollowGoods::getStatus, 1)
//                .orderByDesc(ListingFollowGoods::getIsTop)
//                .orderByAsc(ListingFollowGoods::getSort)
//                .page(new Page<>(condition.getPageNow(), condition.getPageSize()));
//        if (CollectionUtils.isEmpty(page.getRecords())) {
//            return pageView;
//        }

//        List<ListingFollowGoods> records = page.getRecords();
        List<Long> goodsIds = records.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        List<Long> shopIds = records.stream().map(ListingFollowGoods::getShopId).distinct().collect(Collectors.toList());

        Map<Long, ShopGoodsCommentDTO> goodsCommentDTOMap = goodsCommentFactory.queryCountAndAverageByGoodsIds(goodsIds);
        Map<Long, ShopGoodsCommentDTO> shopCommentDTOMap = goodsCommentFactory.queryCountAndAverageByShopIds(shopIds);
        Map<Long, FaMerchantsApply> faMerchantsApplyMap = faMerchantsApplyService.listByIds(shopIds).stream().collect(Collectors.toMap(FaMerchantsApply::getId, Function.identity(), (v1, v2) -> v1));
        Map<Long, Goods> goodsMap = goodsService.listByIds(goodsIds).stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> warehouseOverSeaGoodsIds = goodsExtConfigService.selectGoodsTagConfig(goodsIds, 4L).stream().map(GoodsExtConfig::getGoodsId).collect(Collectors.toList());

        //7日销量
        List<OutDbGoodsEveryDayDTO> outDbGoodsEveryDay = outDbGoodsEveryDayEsService.getGoodsSalesByDays(goodsIds, Arrays.asList(7, 30), OutDbGoodsEveryDayDTO.class);
        Map<Long, List<OutDbGoodsEveryDayDTO>> goodsEveryDayGroupMap = outDbGoodsEveryDay.stream().collect(Collectors.groupingBy(OutDbGoodsEveryDayDTO::getGoodsId));

        ListingInfo listingInfo = listingInfoService.getById(condition.getListingId());

        List<ListingFollowGoodsVo> vos = records.stream().map(listingFollowGoods -> {
            ListingFollowGoodsVo vo = new ListingFollowGoodsVo();
            vo.setId(listingFollowGoods.getId());
            vo.setGoodsId(listingFollowGoods.getGoodsId());
            vo.setShopId(listingFollowGoods.getShopId());
            vo.setCreateTime(listingFollowGoods.getCreateTime());
            vo.setSort(listingFollowGoods.getScoreSort());
            vo.setCountryScoreSortMap(getCountryScoreSortMap(listingFollowGoods.getCountryScoreSort()));
            vo.setIsTop(listingFollowGoods.getIsTop());
            vo.setIsSource(listingInfo.getSourceGoodsId().equals(listingFollowGoods.getGoodsId()) ? 1 : 0);
            vo.setIsWarehouseOverSea(warehouseOverSeaGoodsIds.contains(listingFollowGoods.getGoodsId()) ? 1 : 0);
            vo.setAcceptReductionPush(listingFollowGoods.getAcceptReductionPush());
            vo.setScoreSort(listingFollowGoods.getScoreSort());
            //todo 全部权重 展示商品在不同国家的权重

            List<OutDbGoodsEveryDayDTO> outDbGoodsEveryDayDTOS = goodsEveryDayGroupMap.get(listingFollowGoods.getGoodsId());
            if (CollectionUtils.isNotEmpty(outDbGoodsEveryDayDTOS)) {
                Map<Integer, OutDbGoodsEveryDayDTO> goodsEveryDayDTOMap = outDbGoodsEveryDayDTOS.stream().collect(Collectors.toMap(OutDbGoodsEveryDayDTO::getRunDays, Function.identity(), (v1, v2) -> v1));

                OutDbGoodsEveryDayDTO goodsEveryDay7 = goodsEveryDayDTOMap.get(7);
                if (goodsEveryDay7 != null) {
                    vo.setSales(goodsEveryDay7.getDealCnt());
                }

                OutDbGoodsEveryDayDTO goodsEveryDay30 = goodsEveryDayDTOMap.get(30);
                if (goodsEveryDay30 != null) {
                    if (goodsEveryDay30.getDealUndeliveryRefundRate() != null) {
                        vo.setDealNotSoldRate(BigDecimal.valueOf(goodsEveryDay30.getDealUndeliveryRefundRate()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP) + "%");
                    }
                    if (goodsEveryDay30.getBadCommentCnt() != null && goodsEveryDay30.getCommentCnt() != null && goodsEveryDay30.getCommentCnt() != 0) {
                        vo.setBadCommentsRate(BigDecimal.valueOf(goodsEveryDay30.getBadCommentCnt()).multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(goodsEveryDay30.getCommentCnt()), 2, RoundingMode.HALF_UP).toString() + "%");
                    }
                }
            }

            FaMerchantsApply faMerchantsApply = faMerchantsApplyMap.get(listingFollowGoods.getShopId());
            if (faMerchantsApply != null) {
                vo.setShopName(faMerchantsApply.getShopname());
                vo.setPrincipal(faMerchantsApply.getPrincipal());
                vo.setDeliveryType(DeliveryTypeEnum.getNameByCode(faMerchantsApply.getDeliveryType()));
            }

            Goods goods = goodsMap.get(listingFollowGoods.getGoodsId());
            if (goods != null) {
                vo.setPrice(goods.getMinPrice() + "~" + goods.getMaxPrice());
                vo.setIsShow(Integer.parseInt(goods.getIsShow()));
                vo.setMainImage(goods.getMainImage());
            }

            ShopGoodsCommentDTO goodsCommentDTO = goodsCommentDTOMap.get(listingFollowGoods.getGoodsId());
            if (goodsCommentDTO != null) {
                vo.setGoodsComment(goodsCommentDTO.getCommentCount() + "/" + BigDecimal.valueOf(goodsCommentDTO.getCommentAverage()).setScale(1, RoundingMode.HALF_UP));
            }

            ShopGoodsCommentDTO shopCommentDTO = shopCommentDTOMap.get(listingFollowGoods.getShopId());
            if (shopCommentDTO != null) {
                vo.setShopComment(shopCommentDTO.getCommentCount() + "/" + BigDecimal.valueOf(shopCommentDTO.getCommentAverage()).setScale(1, RoundingMode.HALF_UP));
            }

            //

            return vo;
        }).collect(Collectors.toList());
        pageView.setRecords(vos);
//        pageView.setRowCount(page.getTotal());
        return pageView;
    }

    private Map<String, String> getCountryScoreSortMap(String countryScoreSort) {
        if (StringUtils.isBlank(countryScoreSort)) {
            return Maps.newHashMap();
        }

        String[] countrySort = countryScoreSort.split(",");
        Map<String, String> countrySortMap = Maps.newHashMap();
        for (String cSort : countrySort) {
            String[] split = cSort.split(":");
            countrySortMap.put(CountryCodeEnum.of(split[0]).getNameCn(), split[1]);
        }

        return countrySortMap;
    }

    @Override
    public ShopGoodsCommentDTO queryTotalCountAndAverage(ListingCommentQueryCondition condition) {
        LogUtils.info(log, "查询该listing总评论数/评论分 condition:{}", condition);
        CheckUtils.notNull(condition.getListingId(), ProductResultCode.PARAMETER_ID_ERROR);

        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, condition.getListingId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .list();

        if (CollectionUtils.isEmpty(listingFollowGoodsList)) {
            ShopGoodsCommentDTO dto = new ShopGoodsCommentDTO();
            dto.setCommentAverage(0d);
            dto.setCommentCount(0);
            return dto;
        }

        List<Long> goodsIds = listingFollowGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        ShopGoodsCommentDTO shopGoodsCommentDTO = goodsCommentFactory.queryTotalCountAndAverageByGoodsIds(goodsIds);
        shopGoodsCommentDTO.setCommentAverage(BigDecimal.valueOf(shopGoodsCommentDTO.getCommentAverage()).setScale(1, RoundingMode.HALF_UP).doubleValue());
        return shopGoodsCommentDTO;
    }

    @Override
    public PageView<ListingInfoCommentVo> pageListingComment(ListingCommentQueryCondition condition) {
        LogUtils.info(log, "查看指定listing模板的评论列表 condition:{}", condition);
        CheckUtils.notNull(condition.getListingId(), ProductResultCode.PARAMETER_ID_ERROR);

        PageView<ListingInfoCommentVo> pageView = new PageView<>(condition.getPageSize(), condition.getPageNow());

        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, condition.getListingId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .list();
        if (CollectionUtils.isEmpty(listingFollowGoodsList)) {
            return pageView;
        }

        List<Long> goodsIds = listingFollowGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());

        GoodsCommentQueryDto queryDto = new GoodsCommentQueryDto();
        queryDto.setGoodsIds(goodsIds);
        queryDto.setPageNow(condition.getPageNow());
        queryDto.setPageSize(condition.getPageSize());
        PageView<GoodsCommentPageDTO> goodsCommentPageDTOPageView = goodsCommentFactory.pageCommentsByGoodsIds(queryDto);


        if (CollectionUtils.isNotEmpty(goodsCommentPageDTOPageView.getRecords())) {
            List<GoodsCommentPageDTO> records = goodsCommentPageDTOPageView.getRecords();
            List<Long> resultGoodsIds = records.stream().map(GoodsCommentPageDTO::getGoodsId).collect(Collectors.toList());
            Map<Long, Goods> goodsMap = goodsService.listByIds(resultGoodsIds).stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

            List<ListingInfoCommentVo> voList = records.stream().map(dto -> {
                ListingInfoCommentVo vo = new ListingInfoCommentVo();
                vo.setImage(StringUtils.join(dto.getImgUrl(), ","));
                vo.setComment(dto.getComment());
                vo.setVideo(dto.getVideoUrl());
                vo.setScore(dto.getScore());
                vo.setCreateTime(dto.getCreateTime());
                vo.setGoodsId(dto.getGoodsId());
                vo.setSkuId(dto.getSkuId());
                vo.setIsHidden(0);
                Goods goods = goodsMap.get(dto.getGoodsId());
                if (goods != null) {
                    vo.setShopId(goods.getShopId());
                    vo.setShopName(goods.getShopName());
                }
                return vo;
            }).collect(Collectors.toList());
            pageView.setRecords(voList);
        }
        pageView.setRowCount(goodsCommentPageDTOPageView.getRowCount());
        return pageView;
    }

    @Override
    public Long signUp(ListingCondition condition) {
        LogUtils.info(log, "商家参与竞标 condition:{}", condition);
        CheckUtils.notNull(condition.getListingId(), ProductResultCode.PARAMETER_ID_ERROR);

        ListingInfo listingInfo = listingInfoService.getById(condition.getListingId());
        CheckUtils.check(listingInfo.getIsDel() == 1, ProductResultCode.LISTING_NOT_EXIST);
        CheckUtils.check(listingInfo.getStatus() != 1, ProductResultCode.LISTING_STATUS_CAN_NOT_SIGN);

        Long shopId = getShopId();
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);

        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(shopId);
        CheckUtils.notNull(faMerchantsApply, ProductResultCode.SHOP_ID_NULL);
        CheckUtils.check(faMerchantsApply.getIsSale() != 1, ProductResultCode.SHOP_ERROR);

        boolean exit = false;
        Integer listingType = listingInfo.getListingType();
        List<Long> shopTagList = faMerchantsApplyCoreService.queryShopTagByShopId(shopId);
        if (listingType == 1) {
            if (CollectionUtils.isEmpty(shopTagList)) {
                exit = true;
            } else {
                if (!shopTagList.contains(146L) && !shopTagList.contains(507L) && !shopTagList.contains(514L)) {
                    exit = true;
                }
            }
            CheckUtils.check(exit, ProductResultCode.NOT_IN_WHITE);
        }

        Integer shopTag = listingInfo.getShopTag();
        Integer deliveryType = listingInfo.getDeliveryType();
        Integer shopWhiteListType = listingInfo.getShopWhiteListType();

        List<Integer> configs = new ArrayList<>();
        if (shopTag != 0) {
            configs.add(0);
        }
        if (deliveryType != 0) {
            configs.add(1);
        }
        if (shopWhiteListType != 0) {
            configs.add(2);
        }

        boolean configExit = true;
        if (CollectionUtils.isNotEmpty(configs)) {
            for (Integer config : configs) {
                if (config == 0) {
                    List<String> shopTagListStrings = shopTagList.stream().distinct().map(String::valueOf).collect(Collectors.toList());
                    List<Integer> tagList = searchTypeMatchBinaryConfigService.getEnumValuesByEnumNames(shopTagListStrings, 1);

                    List<Integer> shopTags = MathUtils.splitBinary(shopTag);
                    shopTags.retainAll(tagList);
                    if (CollectionUtils.isNotEmpty(shopTags)) {
                        configExit = false;
                        break;
                    }
                } else if (config == 1) {
                    List<String> deliveryTypeListStrings = Collections.singletonList(String.valueOf(faMerchantsApply.getDeliveryType()));
                    List<Integer> deliveryTypeList = searchTypeMatchBinaryConfigService.getEnumValuesByEnumNames(deliveryTypeListStrings, 2);

                    List<Integer> deliveryTypes = MathUtils.splitBinary(deliveryType);
                    deliveryTypes.retainAll(deliveryTypeList);
                    if (CollectionUtils.isNotEmpty(deliveryTypes)) {
                        configExit = false;
                        break;
                    }
                } else {
                    List<ListingShopWhiteList> whiteList = listingShopWhiteListService.lambdaQuery()
                            .eq(ListingShopWhiteList::getListingId, condition.getListingId())
                            .eq(ListingShopWhiteList::getShopId, shopId)
                            .eq(ListingShopWhiteList::getIsDel, 0)
                            .list();
                    if (CollectionUtils.isNotEmpty(whiteList)) {
                        configExit = false;
                        break;
                    }
                }
            }
        } else {
            configExit = false;
        }
        CheckUtils.check(configExit, ProductResultCode.NOT_IN_WHITE);

        if (faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.DIRECT.getCode())) {
            List<String> countryList = warehouseOverseasConfigCoreService.queryCountryByShopId(shopId);
            Asserts.check(countryList.contains(SaleableCountryEnum.DE.name()) || countryList.contains(SaleableCountryEnum.GB.name()), "您的售卖国家没有德国或英国，无法参与竞标");
        }

        //主营店铺校验
        if (faMerchantsApply.getCategoryMainId() != null) {
            CategoryMain categoryMain = categoryMainService.getById(faMerchantsApply.getCategoryMainId());
            CheckUtils.notNull(categoryMain, ProductResultCode.CATEGORY_MAIN_NOT_EXIST);
            List<Long> allList = Lists.newArrayList();
            if (StringUtils.isNotBlank(categoryMain.getLeafCategoryIds())) {
                Arrays.stream(categoryMain.getLeafCategoryIds().split(",")).map(Long::parseLong).forEach(allList::add);
            }
            if (StringUtils.isNotBlank(categoryMain.getLeafSecondCategoryIds())) {
                Arrays.stream(categoryMain.getLeafSecondCategoryIds().split(",")).map(Long::parseLong).forEach(allList::add);
            }
            CheckUtils.check(!allList.contains(listingInfo.getCategoryId()), ProductResultCode.CATEGORY_MAIN_NOT_INCLUDE);
        }

        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, condition.getListingId())
                .eq(ListingFollowGoods::getShopId, shopId)
//                .eq(ListingFollowGoods::getIsDel, 0)
                .one();
        CheckUtils.check(listingFollowGoods != null, ProductResultCode.LISTING_ALREADY_SIGN_UP);

        GoodsCopyDto copyDto = new GoodsCopyDto();
        copyDto.setGoodsId(listingInfo.getGoodsId());
        copyDto.setType(3);
        copyDto.setShopId(shopId);
        copyDto.setShopName(getShopName());
        copyDto.setIsShow(GoodsIsShowEnums.WAIT_AUDIT);
        copyDto.setIsLock(0);
        copyDto.setNeedTag(true);
        copyDto.setDeliveryType(faMerchantsApply.getDeliveryType());
        Goods goods = newAddGoodsCoreService.copyAddGoods(copyDto);

        listingFollowGoods = new ListingFollowGoods();
        listingFollowGoods.setListingId(listingInfo.getId());
        listingFollowGoods.setGoodsId(goods.getId());
        listingFollowGoods.setShopId(shopId);
        listingFollowGoods.setSort(999);
        listingFollowGoods.setScoreSort(999);
        listingFollowGoods.setCountryScoreSort(getCountryScoreSort(goods));
        listingFollowGoods.setIsTop(0);
        listingFollowGoods.setStatus(1);
        listingFollowGoods.setIsDel(0);
        listingFollowGoods.setCreateTime(LocalDateTime.now());
        listingFollowGoods.setUpdateTime(LocalDateTime.now());
        listingFollowGoodsService.save(listingFollowGoods);

        listingInfo.setFollowCount(listingInfo.getFollowCount() + 1);
        listingInfoService.updateById(listingInfo);

        return goods.getId();

    }

    @Override
    public List<ListingSkuPriceVo> listShopPriceBySku(ListingSkuPriceQueryCondition condition) {
        //仅展示上架状态的sku
        //按价格低到高->编辑时间先后
        LogUtils.info(log, "查看listing模板指定sku的跟卖商品价格列表 condition:{}", condition);
        CheckUtils.check(condition.getListingId() == null || StringUtils.isBlank(condition.getSkuName()), ProductResultCode.PARAMETER_ERROR);

        List<ListingFollowGoods> followGoodsList = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, condition.getListingId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .list();

        if (CollectionUtils.isEmpty(followGoodsList)) {
            return Collections.emptyList();
        }

        List<Long> goodsIds = followGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        Map<Long, Goods> goodsMap = goodsService.listByIds(goodsIds).stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

        List<GoodsItem> goodsItemList = goodsItemService.lambdaQuery()
                .in(GoodsItem::getGoodsId, goodsIds)
                .eq(GoodsItem::getName, condition.getSkuName())
                .list();

        return goodsItemList.stream().map(goodsItem -> {
            ListingSkuPriceVo skuPriceVo = BeanCopyUtil.transform(goodsItem, ListingSkuPriceVo.class);
            skuPriceVo.setOriginalPrice(goodsItem.getOrginalPrice());
            Goods goods = goodsMap.get(goodsItem.getGoodsId());
            if (goods != null) {
                skuPriceVo.setShopId(goods.getShopId());
                skuPriceVo.setShopName(goods.getShopName());
            }
            return skuPriceVo;
        }).collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    @Override
    public PageView<ListingInfoVO> pageListInfo4Shop(ListingShopPageVO vo) {

        // 查询白名单
        ShopUserDTO shopInfo = clientInfoSupportService.getShopInfo();
        Long shopId = shopInfo.getShopId();

        Integer type = getListingTypeByShopId(shopId);

        List<Integer> shopTagValues = getShopTagValuesByShopId(shopId);

        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(shopId);
        CheckUtils.notNull(faMerchantsApply, ProductResultCode.SHOP_ID_NULL);
        if (faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.SELF_SUPPORT_SCM.getCode())) {
            LogUtils.info(log, "pageListInfo4Shop ==> 该店铺为scm自营店铺");
            return new PageView<>();
        }
        List<Integer> deliveryTypeValues = getDeliveryTypeValues(faMerchantsApply);

        Set<Long> allowList = listingShopWhiteListService.lambdaQuery()
                .eq(ListingShopWhiteList::getIsDel, 0)
                .eq(ListingShopWhiteList::getShopId, shopId)
                .select(ListingShopWhiteList::getListingId, ListingShopWhiteList::getShopId)
                .list()
                .stream().map(ListingShopWhiteList::getListingId).collect(Collectors.toSet());

        List<Long> listingIdList = null;
        if (StringUtils.isNotBlank(vo.getListingId())) {
            listingIdList = Stream.of(vo.getListingId().split("\n")).map(Long::valueOf).distinct().collect(Collectors.toList());
        }

        CategoryMain categoryMain = categoryMainService.getById(faMerchantsApply.getCategoryMainId());
        CheckUtils.notNull(categoryMain, ProductResultCode.CATEGORY_MAIN_NOT_EXIST);
        Set<Long> categoryIds = new HashSet<>();
        if(StringUtils.isNotBlank(categoryMain.getLeafCategoryIds()))
            Arrays.stream(categoryMain.getLeafCategoryIds().split(",")).map(Long::parseLong).forEach(categoryIds::add);
        if(StringUtils.isNotBlank(categoryMain.getLeafSecondCategoryIds()))
            Arrays.stream(categoryMain.getLeafSecondCategoryIds().split(",")).map(Long::parseLong).forEach(categoryIds::add);
        if (vo.getCategoryId() != null) {
            List<Category> categories = categoryService.queryAllChildCategoryByParentId(vo.getCategoryId());
            Set<Long> collect = categories.stream().map(Category::getId).collect(Collectors.toSet());
            collect.add(vo.getCategoryId());
            categoryIds.retainAll(collect);
        }
        if (CollectionUtils.isEmpty(categoryIds)) {
            log.info("pageListInfo4Shop categoryIds is null shopId ={}", shopId);
            return new PageView<>();
        }

        PageView<ListingInfoVO> pageView = new PageView<>();
        pageView.setPageNow((int) vo.getPageNow());
        pageView.setPageSize((int) vo.getPageSize());
        pageView.setPageCount(0);
        pageView.setRowCount(0);

        List<Long> signupListingIdList = null;
        if(Objects.nonNull(vo.getIsSignup())){
            List<ListingFollowGoods> signupList = listingFollowGoodsService.lambdaQuery().eq(ListingFollowGoods::getShopId, shopId).eq(ListingFollowGoods::getIsDel, 0).list();
            signupListingIdList = signupList.stream().map(ListingFollowGoods::getListingId).collect(Collectors.toList());
            if(Objects.equals(vo.getIsSignup(), 1) && CollectionUtils.isEmpty(signupList)){
                LogUtils.info(log, "pageListInfo4Shop ==> return 5");
                return pageView;
            }
        }

        IPage<ListingInfo> page;
        long total;
        if (Objects.equals(vo.getOrderByType(), 2) || Objects.equals(vo.getOrderByType(), 3)) {
            LambdaQueryChainWrapper<ListingInfo> dataWrapper = getListingInfoQueryChainWrapper(allowList, shopTagValues, deliveryTypeValues, vo,
                    signupListingIdList, listingIdList, categoryIds);
            page = dataWrapper
                    .eq(type != null, ListingInfo::getListingType, type)
                    .orderByDesc(Objects.equals(vo.getOrderByType(), 2), ListingInfo::getSevenSale)
                    .orderByDesc(Objects.equals(vo.getOrderByType(), 3), ListingInfo::getHot)
                    .page(new Page<>(vo.getPageNow(), vo.getPageSize()));
            total = page.getTotal();
        } else {
            if (type != null) {
                LambdaQueryChainWrapper<ListingInfo> dataWrapper = getListingInfoQueryChainWrapper(allowList, shopTagValues, deliveryTypeValues, vo,
                        signupListingIdList, listingIdList, categoryIds);
                page = dataWrapper.eq(ListingInfo::getListingType, 0)
                        .orderByDesc(ListingInfo::getUpdateTime)
                        .page(new Page<>(vo.getPageNow(), vo.getPageSize()));
                total = page.getTotal();
            } else {
                long pageSize = vo.getPageSize();
                long pageNow = vo.getPageNow();
                if (pageSize == 20 || pageSize == 50 || pageSize == 100) {
                    long positiveNum = 100;

                    long positivePageSize = (long) (pageSize * 0.8);
                    long negativePageSize = (long) (pageSize * 0.2);

                    LambdaQueryChainWrapper<ListingInfo> positiveCountWrapper = getListingInfoQueryChainWrapper(allowList, shopTagValues, deliveryTypeValues, vo,
                            signupListingIdList, listingIdList, categoryIds);
                    positiveCountWrapper.eq(ListingInfo::getListingType, 0);
                    long positiveTotal = positiveCountWrapper.count();

                    LambdaQueryChainWrapper<ListingInfo> negativeCountWrapper = getListingInfoQueryChainWrapper(allowList, shopTagValues, deliveryTypeValues, vo,
                            signupListingIdList, listingIdList, categoryIds);
                    negativeCountWrapper.eq(ListingInfo::getListingType, 1);
                    long negativeTotal = negativeCountWrapper.count();

                    total = positiveTotal + negativeTotal;
                    log.info("positive listing info num: {}, negative listing info num: {}", positiveTotal, negativeTotal);

                    LambdaQueryChainWrapper<ListingInfo> positiveDataWrapper = getListingInfoQueryChainWrapper(allowList, shopTagValues, deliveryTypeValues, vo,
                            signupListingIdList, listingIdList, categoryIds);
                    LambdaQueryChainWrapper<ListingInfo> negativeDataWrapper = getListingInfoQueryChainWrapper(allowList, shopTagValues, deliveryTypeValues, vo,
                            signupListingIdList, listingIdList, categoryIds);
                    positiveDataWrapper.orderByDesc(ListingInfo::getUpdateTime);
                    negativeDataWrapper.orderByDesc(ListingInfo::getUpdateTime);

                    // 如果正向listing的个数少于100个
                    if (positiveTotal < positiveNum) {
                        long skipPages = positiveTotal / pageSize;
                        if (pageNow <= skipPages) {
                            positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                            page = positiveDataWrapper.page(new Page<>(vo.getPageNow(), vo.getPageSize()));
                        } else {
                            long positiveRemainder = positiveTotal % pageSize;
                            page = new Page<>();
                            // 如果正向listing的个数能整除pageSize
                            if (positiveRemainder == 0) {
                                negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                long offset = (vo.getPageNow() - 1 - skipPages) * pageSize;
                                negativeDataWrapper.last("limit " + pageSize + " offset " + offset);
                                List<ListingInfo> negativeList = negativeDataWrapper.list();
                                page.setRecords(negativeList);
                            } else {
                                long negativeNeededNum = pageSize - positiveRemainder;
                                // 正向和负向拼接
                                if (pageNow == skipPages + 1) {
                                    positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                                    List<ListingInfo> positiveList = positiveDataWrapper.page(new Page<>(vo.getPageNow(), vo.getPageSize())).getRecords();

                                    negativeDataWrapper.last("limit " + negativeNeededNum);
                                    negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                    List<ListingInfo> negativeList = negativeDataWrapper.list();

                                    positiveList.addAll(negativeList);
                                    page.setRecords(positiveList);
                                } else {
                                    negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                    long offset = (vo.getPageNow() - 2 - skipPages) * pageSize + negativeNeededNum;
                                    negativeDataWrapper.last("limit " + pageSize + " offset " + offset);
                                    List<ListingInfo> negativeList = negativeDataWrapper.list();
                                    page.setRecords(negativeList);
                                }
                            }
                        }
                    } else { // 正向listingInfo的个数大于等于100
                        long skipPages = positiveNum / pageSize;
                        if (pageNow <= skipPages) {
                            positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                            page = positiveDataWrapper.page(new Page<>(vo.getPageNow(), vo.getPageSize()));
                        } else {
                            long positiveMixPage = (positiveTotal - positiveNum) / positivePageSize + skipPages;
                            long positiveRemainder = (positiveTotal - positiveNum) % positivePageSize;

                            long negativeMixPage = negativeTotal / negativePageSize + skipPages;
                            long negativeRemainder = negativeTotal % negativePageSize;
                            page = new Page<>();
                            if (positiveMixPage > negativeMixPage) {
                                if (pageNow <= negativeMixPage) {
                                    positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                                    long offset = (vo.getPageNow() - 1 - skipPages) * positivePageSize + positiveNum;
                                    positiveDataWrapper.last("limit " + positivePageSize + " offset " + offset);
                                    List<ListingInfo> positiveList = positiveDataWrapper.list();

                                    negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                    List<ListingInfo> negativeList = negativeDataWrapper.page(new Page<>(vo.getPageNow() - skipPages, negativePageSize)).getRecords();

                                    positiveList.addAll(negativeList);
                                    Collections.shuffle(positiveList);
                                    page.setRecords(positiveList);
                                } else {
                                    long positiveOffset = (negativeMixPage - skipPages) * positivePageSize + positiveNum;

                                    // 负向listingInfo的个数能整除
                                    if (negativeRemainder == 0) {
                                        positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                                        long offset = (vo.getPageNow() - 1 - negativeMixPage) * pageSize + positiveOffset;
                                        positiveDataWrapper.last("limit " + pageSize + " offset " + offset);
                                        List<ListingInfo> positiveList = positiveDataWrapper.list();
                                        page.setRecords(positiveList);
                                    } else {
                                        long positiveNeededNum = pageSize - negativeRemainder;
                                        if (pageNow == negativeMixPage + 1) {
                                            negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                            List<ListingInfo> negativeList = negativeDataWrapper.page(new Page<>(vo.getPageNow() - skipPages, negativePageSize)).getRecords();

                                            positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                                            positiveDataWrapper.last("limit " + positiveNeededNum + " offset " + positiveOffset);
                                            List<ListingInfo> positiveList = positiveDataWrapper.list();

                                            positiveList.addAll(negativeList);
                                            Collections.shuffle(positiveList);
                                            page.setRecords(positiveList);
                                        } else {
                                            positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                                            long offset = (vo.getPageNow() - 2 - negativeMixPage) * pageSize + positiveOffset + positiveNeededNum;
                                            positiveDataWrapper.last("limit " + pageSize + " offset " + offset);
                                            List<ListingInfo> positiveList = positiveDataWrapper.list();
                                            page.setRecords(positiveList);
                                        }
                                    }
                                }
                            } else if (positiveMixPage == negativeMixPage) {
                                positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                                long offset = (vo.getPageNow() - 1 - skipPages) * positivePageSize + positiveNum;
                                positiveDataWrapper.last("limit " + positivePageSize + " offset " + offset);
                                List<ListingInfo> positiveList = positiveDataWrapper.list();

                                negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                List<ListingInfo> negativeList = negativeDataWrapper.page(new Page<>(vo.getPageNow() - skipPages, negativePageSize)).getRecords();

                                positiveList.addAll(negativeList);
                                Collections.shuffle(positiveList);
                                page.setRecords(positiveList);
                            } else {
                                if (pageNow <= positiveMixPage) {
                                    positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                                    long offset = (vo.getPageNow() - 1 - skipPages) * positivePageSize + positiveNum;
                                    positiveDataWrapper.last("limit " + positivePageSize + " offset " + offset);
                                    List<ListingInfo> positiveList = positiveDataWrapper.list();

                                    negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                    List<ListingInfo> negativeList = negativeDataWrapper.page(new Page<>(vo.getPageNow() - skipPages, negativePageSize)).getRecords();

                                    positiveList.addAll(negativeList);
                                    Collections.shuffle(positiveList);
                                    page.setRecords(positiveList);
                                } else {
                                    long negativeOffset = (positiveMixPage - skipPages) * negativePageSize;

                                    // 正向listingInfo的个数能整除
                                    if (positiveRemainder == 0) {
                                        negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                        long offset = (vo.getPageNow() - 1 - positiveMixPage) * pageSize + negativeOffset;
                                        negativeDataWrapper.last("limit " + pageSize + " offset " + offset);
                                        List<ListingInfo> negativeList = negativeDataWrapper.list();
                                        page.setRecords(negativeList);
                                    } else {
                                        long negativeNeededNum = pageSize - positiveRemainder;
                                        if (pageNow == positiveMixPage + 1) {
                                            positiveDataWrapper.eq(ListingInfo::getListingType, 0);
                                            long offset = (positiveMixPage - skipPages) * positivePageSize + positiveNum;
                                            positiveDataWrapper.last("limit " + positivePageSize + " offset " + offset);
                                            List<ListingInfo> positiveList = positiveDataWrapper.list();

                                            negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                            negativeDataWrapper.last("limit " + negativeNeededNum + " offset " + negativeOffset);
                                            List<ListingInfo> negativeList = negativeDataWrapper.list();

                                            positiveList.addAll(negativeList);
                                            Collections.shuffle(positiveList);
                                            page.setRecords(positiveList);
                                        } else {
                                            negativeDataWrapper.eq(ListingInfo::getListingType, 1);
                                            long offset = (vo.getPageNow() - 2 - positiveMixPage) * pageSize + negativeOffset + negativeNeededNum;
                                            negativeDataWrapper.last("limit " + pageSize + " offset " + offset);
                                            List<ListingInfo> negativeList = negativeDataWrapper.list();
                                            page.setRecords(negativeList);
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    LambdaQueryChainWrapper<ListingInfo> dataWrapper = getListingInfoQueryChainWrapper(allowList, shopTagValues, deliveryTypeValues, vo,
                            signupListingIdList, listingIdList, categoryIds);
                    page = dataWrapper
                            .orderByDesc(ListingInfo::getUpdateTime)
                            .page(new Page<>(vo.getPageNow(), vo.getPageSize()));
                    total = page.getTotal();
                }
            }
        }

//        pageView.setPageCount(page.getPages());
        pageView.setRowCount(total);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            LogUtils.info(log, "pageListInfo4Shop ==> return 6 ==> listingIdList:{}, categoryIds:{}", listingIdList, categoryIds);
            return pageView;
        }
        List<Long> listingGoodsIdList = page.getRecords().stream().map(ListingInfo::getGoodsId).collect(Collectors.toList());
        Collection<Goods> goods = goodsService.listByIds(listingGoodsIdList);
        Map<Long, Goods> goodsMap = goods.stream().collect(Collectors.toMap(Goods::getId, Function.identity()));
        List<Long> listingIds = page.getRecords().stream().map(ListingInfo::getId).collect(Collectors.toList());
        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery().eq(ListingFollowGoods::getShopId, shopId).in(ListingFollowGoods::getListingId, listingIds).list();
        Map<Long, ListingFollowGoods> listingMap = listingFollowGoodsList.stream().collect(Collectors.toMap(ListingFollowGoods::getListingId, Function.identity(), (a, b) -> a));
        List<Long> existCategoryIds = page.getRecords().stream().map(ListingInfo::getCategoryId).collect(Collectors.toList());
        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(existCategoryIds);

        List<GoodsExtConfig> customTagGoods = goodsExtConfigService.selectGoodsTagConfig(listingGoodsIdList,CUSTOM_GOODS_TAG_ID);
        Set<Long> customTagGoodsIdSet = customTagGoods.stream().map(GoodsExtConfig::getGoodsId).collect(Collectors.toSet());

        List<ListingInfoVO> collect = page.getRecords().stream()
                .map(e -> {
                    ListingInfoVO listingInfoVO = new ListingInfoVO();
                    BeanCopyUtil.copyProperties(e, listingInfoVO);
                    listingInfoVO.setListingId(e.getId());
                    listingInfoVO.setMainImage(goodsMap.get(e.getGoodsId()).getMainImage());
                    listingInfoVO.setCategoryPath(categoryTreeMap.get(e.getCategoryId()));
                    Integer sevenSale = e.getSevenSale();
                    listingInfoVO.setSevenSale(Objects.isNull(sevenSale) || Objects.equals(sevenSale, 0) ? 10 : sevenSale * 10);
                    ListingFollowGoods listingFollowGoods = listingMap.get(e.getId());
                    listingInfoVO.setIsSignup(Objects.isNull(listingFollowGoods) ? 0 : 1);
                    if(Objects.nonNull(listingFollowGoods)){
                        listingInfoVO.setIsDel(listingFollowGoods.getIsDel());
                        listingInfoVO.setFlowGoodsId(listingFollowGoods.getGoodsId());
                    }

                    if (customTagGoodsIdSet.contains(e.getGoodsId())){
                        listingInfoVO.setIsCustomGoods(true);
                    }
                    return listingInfoVO;
                }).collect(Collectors.toList());
        pageView.setRecords(collect);
        return pageView;
    }

    private LambdaQueryChainWrapper<ListingInfo> getListingInfoQueryChainWrapper(Set<Long> allowList,
                                                                                 List<Integer> shopTagValues,
                                                                                 List<Integer> deliveryTypeValues,
                                                                                 ListingShopPageVO vo,
                                                                                 List<Long> signupListingIdList,
                                                                                 List<Long> listingIdList,
                                                                                 Set<Long> categoryIds) {
        return listingInfoService.lambdaQuery()
                .and(wrapper -> wrapper
                        .eq(ListingInfo::getShopWhiteListType, 0)
                        .eq(ListingInfo::getShopTag, 0)
                        .eq(ListingInfo::getDeliveryType, 0)
                        .or(wrapper2 -> wrapper2
                                .in(CollectionUtils.isNotEmpty(allowList), ListingInfo::getId, allowList).or()
                                .in(CollectionUtils.isNotEmpty(shopTagValues), ListingInfo::getShopTag, shopTagValues).or()
                                .in(CollectionUtils.isNotEmpty(deliveryTypeValues), ListingInfo::getDeliveryType, deliveryTypeValues)))
                .eq(ListingInfo::getStatus, 1)
                .eq(ListingInfo::getIsDel, 0)
                .in(Objects.equals(vo.getIsSignup(), 1), ListingInfo::getId, signupListingIdList)
                .notIn(Objects.equals(vo.getIsSignup(), 0) && CollectionUtils.isNotEmpty(signupListingIdList), ListingInfo::getId, signupListingIdList)
                .in(CollectionUtils.isNotEmpty(listingIdList), ListingInfo::getId, listingIdList)
                .in(CollectionUtils.isNotEmpty(categoryIds), ListingInfo::getCategoryId, categoryIds)
                .le(Objects.nonNull(vo.getSevenSaleEnd()), ListingInfo::getSevenSale, vo.getSevenSaleEnd())
                .ge(Objects.nonNull(vo.getSevenSaleStart()), ListingInfo::getSevenSale, vo.getSevenSaleStart());
    }

    @Override
    public List<ListingShopWhiteListPageVO> getListingShopWhiteList(ListingShopWhiteListQueryVO vo) {
        CheckUtils.check(Objects.isNull(vo) || Objects.isNull(vo.getListingId()), ProductResultCode.PARAMETER_ERROR);
        List<ListingShopWhiteList> list = listingShopWhiteListService.lambdaQuery().eq(ListingShopWhiteList::getIsDel, 0)
                .eq(ListingShopWhiteList::getListingId, vo.getListingId()).list();
        Map<Long, String> principalMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(list)){
            List<Long> shopIdList = list.stream().map(ListingShopWhiteList::getShopId).collect(Collectors.toList());
            List<FaMerchantsApply> faMerchantsApplyList = faMerchantsApplyService.lambdaQuery().in(FaMerchantsApply::getId, shopIdList).list();
            if(CollectionUtils.isNotEmpty(faMerchantsApplyList)){
                principalMap = faMerchantsApplyList.stream().filter(e -> StringUtils.isNotBlank(e.getPrincipal())).collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getPrincipal));
            }
        }

        Map<Long, String> finalPrincipalMap = principalMap;
        return CollectionUtils.emptyIfNull(list).stream()
                .map(e -> {
                    ListingShopWhiteListPageVO pageVO = new ListingShopWhiteListPageVO();
                    pageVO.setId(e.getId());
                    pageVO.setShopId(e.getShopId());
                    pageVO.setShopName(e.getShopName());
                    pageVO.setOperator(e.getOperator());
                    pageVO.setCreateTime(e.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    pageVO.setPrincipal(finalPrincipalMap.get(e.getShopId()));
                    return pageVO;
                }).collect(Collectors.toList());
    }

    @Override
    public void addListingShopWhiteList(ListingShopWhiteListAddVO vo) {
        log.info("addListingShopWhiteList vo = {}", JSON.toJSONString(vo));
        String shopIdStr = vo.getShopIdStr();
        CheckUtils.check(Objects.isNull(vo.getListingId()) || StringUtils.isBlank(shopIdStr), ProductResultCode.PARAMETER_ERROR);
        List<Long> shopIdList = Stream.of(shopIdStr.split("\n")).map(Long::valueOf).distinct().collect(Collectors.toList());
        CheckUtils.check(CollectionUtils.isEmpty(shopIdList), ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(shopIdList.size() > 500, ProductResultCode.LISTING_RELATION_NUM_EXCEED_MAX_ERROR);

        List<ListingShopWhiteList> list = listingShopWhiteListService.lambdaQuery().eq(ListingShopWhiteList::getListingId, vo.getListingId())
                .eq(ListingShopWhiteList::getIsDel, 0)
                .in(ListingShopWhiteList::getShopId, shopIdList).list();
        if (CollectionUtils.isNotEmpty(list)) {
            CheckUtils.check(true, CustomResultCode.fill(ProductResultCode.LISTING_SHOP_WHITE_EXIST,
                    StringUtils.join(list.stream().map(ListingShopWhiteList::getShopId).collect(Collectors.toList()), ",")
            ));
        }
        List<FaMerchantsApply> faMerchantsApplyList = faMerchantsApplyService.lambdaQuery().in(FaMerchantsApply::getId, shopIdList).list();
        Set<Long> queryShopIdSet = faMerchantsApplyList.stream().map(FaMerchantsApply::getId).collect(Collectors.toSet());
        List<Long> notExistList = shopIdList.stream().filter(e -> !queryShopIdSet.contains(e)).collect(Collectors.toList());
        log.info("addListingShopWhiteList shopIdStr = {}, queryShopIdSet = {}, notExistList ={}", shopIdStr, queryShopIdSet, notExistList);
        CheckUtils.check(CollectionUtils.isNotEmpty(notExistList), CustomResultCode.fill(ProductResultCode.SHOP_NOT_EXIST, StringUtils.join(notExistList, ",")));
        Map<Long, String> shopNameMap = faMerchantsApplyList.stream().collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getShopname));
        UserDTO operationUser = clientInfoSupportService.getOperationUser();
        List<ListingShopWhiteList> collect = shopIdList.stream()
                .map(e -> {
                    ListingShopWhiteList listingShopWhiteList = new ListingShopWhiteList();
                    listingShopWhiteList.setShopId(e);
                    listingShopWhiteList.setListingId(vo.getListingId());
                    listingShopWhiteList.setShopName(shopNameMap.get(e));
                    listingShopWhiteList.setOperator(operationUser.getName());
                    return listingShopWhiteList;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            listingShopWhiteListService.insertBatch(collect);
            List<Long> addListingIds = collect.stream().map(ListingShopWhiteList::getListingId).distinct().collect(Collectors.toList());
            listingInfoService.lambdaUpdate()
                    .set(ListingInfo::getShopWhiteListType, 1)
                    .set(ListingInfo::getUpdateTime, LocalDateTime.now())
                    .set(ListingInfo::getUpdateUser, getUserName())
                    .in(ListingInfo::getId, addListingIds)
                    .eq(ListingInfo::getShopWhiteListType, 0)
                    .update();
        }
    }

    @Override
    public void batchAddShopWhiteList(ListingShopWhiteListBatchAddVo vo) {
        LogUtils.info(log, "批量新增listing可报名商家 vo:{}", vo);
        CheckUtils.check(StringUtils.isBlank(vo.getListingIdStr()) || StringUtils.isBlank(vo.getShopIdStr()), ProductResultCode.PARAMETER_ERROR);
        List<Long> listingIds = Arrays.stream(vo.getListingIdStr().trim().split("\n")).map(s -> Long.parseLong(s.trim())).distinct().collect(Collectors.toList());
        List<Long> shopIds = Arrays.stream(vo.getShopIdStr().trim().split("\n")).map(s -> Long.parseLong(s.trim())).distinct().collect(Collectors.toList());
        CheckUtils.check(CollectionUtils.isEmpty(listingIds) || CollectionUtils.isEmpty(shopIds), ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(listingIds.size() > 500 || shopIds.size() > 500, ProductResultCode.LISTING_RELATION_NUM_EXCEED_MAX_ERROR);

        Map<Long, FaMerchantsApply> faMerchantsApplyMap = faMerchantsApplyService.listByIds(shopIds).stream().collect(Collectors.toMap(FaMerchantsApply::getId, Function.identity(), (v1, v2) -> v1));

        Collection<Long> disjunction = CollectionUtils.disjunction(shopIds, faMerchantsApplyMap.keySet());
        CheckUtils.check(CollectionUtils.isNotEmpty(disjunction), CustomResultCode.fill(ProductResultCode.SHOP_NOT_EXIST, disjunction.toString()));

//        Map<Long, Long> shopCategoryMainIdMap = faMerchantsApplyMap.values().stream().collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getCategoryMainId, (v1, v2) -> v1));

        List<String> existWhiteList = listingShopWhiteListService.lambdaQuery()
                .in(ListingShopWhiteList::getListingId, listingIds)
                .in(ListingShopWhiteList::getShopId, shopIds)
                .eq(ListingShopWhiteList::getIsDel, 0)
                .list()
                .stream()
                .map(listingShopWhiteList -> listingShopWhiteList.getListingId() + "-" + listingShopWhiteList.getShopId()).collect(Collectors.toList());

        String operator = getUserName();
        List<ListingShopWhiteList> saveList = Lists.newArrayList();
        for (Long listingId : listingIds) {
            for (Long shopId : shopIds) {
                if (existWhiteList.contains(listingId + "-" + shopId)) {
                    continue;
                }
                ListingShopWhiteList listingShopWhiteList = new ListingShopWhiteList();
                listingShopWhiteList.setListingId(listingId);
                listingShopWhiteList.setShopId(shopId);
                FaMerchantsApply faMerchantsApply = faMerchantsApplyMap.get(shopId);
                if (faMerchantsApply != null) {
                    listingShopWhiteList.setShopName(faMerchantsApply.getShopname());
                }
                listingShopWhiteList.setIsDel(0);
                listingShopWhiteList.setOperator(operator);
                listingShopWhiteList.setCreateTime(LocalDateTime.now());
                listingShopWhiteList.setUpdateTime(LocalDateTime.now());
                saveList.add(listingShopWhiteList);
            }
        }

        if (CollectionUtils.isNotEmpty(saveList)) {
            listingShopWhiteListService.saveBatch(saveList);
            List<Long> addListingIds = saveList.stream().map(ListingShopWhiteList::getListingId).distinct().collect(Collectors.toList());
            listingInfoService.lambdaUpdate()
                    .set(ListingInfo::getShopWhiteListType, 1)
                    .set(ListingInfo::getUpdateTime, LocalDateTime.now())
                    .set(ListingInfo::getUpdateUser, getUserName())
                    .in(ListingInfo::getId, addListingIds)
                    .eq(ListingInfo::getShopWhiteListType, 0)
                    .update();
        }
    }

    @Override
    public void deleteListingShopWhiteList(Long id) {
        CheckUtils.check(Objects.isNull(id), ProductResultCode.PARAMETER_ERROR);
        ListingShopWhiteList listingShopWhiteList = listingShopWhiteListService.lambdaQuery()
                .eq(ListingShopWhiteList::getId, id)
                .eq(ListingShopWhiteList::getIsDel, 0)
                .one();
        if (listingShopWhiteList != null) {
            listingShopWhiteListService.lambdaUpdate()
                    .set(ListingShopWhiteList::getIsDel, 1)
                    .set(ListingShopWhiteList::getUpdateTime, LocalDateTime.now())
                    .eq(ListingShopWhiteList::getId, id)
                    .eq(ListingShopWhiteList::getIsDel, 0)
                    .update();

            Long listingId = listingShopWhiteList.getListingId();
            List<ListingShopWhiteList> list = listingShopWhiteListService.lambdaQuery().eq(ListingShopWhiteList::getListingId, listingId)
                    .eq(ListingShopWhiteList::getIsDel, 0).list();
            if (CollectionUtils.isEmpty(list)) {
                listingInfoService.lambdaUpdate()
                        .set(ListingInfo::getShopWhiteListType, 0)
                        .set(ListingInfo::getUpdateTime, LocalDateTime.now())
                        .set(ListingInfo::getUpdateUser, getUserName())
                        .eq(ListingInfo::getId, listingId)
                        .eq(ListingInfo::getShopWhiteListType, 1)
                        .update();
            }
        }
    }

    @Override
    public void batchDeleteShopWhiteList(List<Long> ids) {
        CheckUtils.isEmpty(ids, ProductResultCode.PARAMETER_ID_ERROR);

        List<ListingShopWhiteList> listingShopWhiteLists = listingShopWhiteListService.lambdaQuery()
                .in(ListingShopWhiteList::getId, ids)
                .eq(ListingShopWhiteList::getIsDel, 0).list();
        if (CollectionUtils.isNotEmpty(listingShopWhiteLists)) {
            listingShopWhiteListService.lambdaUpdate()
                    .in(ListingShopWhiteList::getId, ids)
                    .eq(ListingShopWhiteList::getIsDel, 0)
                    .set(ListingShopWhiteList::getIsDel, 1)
                    .set(ListingShopWhiteList::getUpdateTime, LocalDateTime.now())
                    .update();
            List<Long> listingIds = listingShopWhiteLists.stream().map(ListingShopWhiteList::getListingId)
                    .distinct().collect(Collectors.toList());

            List<Long> existListingIds = listingShopWhiteListService.lambdaQuery().in(ListingShopWhiteList::getListingId, listingIds)
                    .eq(ListingShopWhiteList::getIsDel, 0).select(ListingShopWhiteList::getListingId)
                    .list().stream().map(ListingShopWhiteList::getListingId).distinct().collect(Collectors.toList());
            listingIds.removeAll(existListingIds);

            if (CollectionUtils.isNotEmpty(listingIds)) {
                listingInfoService.lambdaUpdate()
                        .set(ListingInfo::getShopWhiteListType, 0)
                        .set(ListingInfo::getUpdateTime, LocalDateTime.now())
                        .set(ListingInfo::getUpdateUser, getUserName())
                        .in(ListingInfo::getId, listingIds)
                        .eq(ListingInfo::getShopWhiteListType, 1)
                        .update();
            }
        }
    }

    @Override
    public void setIsTop(ListingGoodsIsTopUpdateVO vo) {
        CheckUtils.check(Objects.isNull(vo) || Objects.isNull(vo.getId()) || Objects.isNull(vo.getIsTop()), ProductResultCode.PARAMETER_ERROR);
        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.getById(vo.getId());
        log.info("setIsTop vo = {}, listingFollowGoods = {}", JSON.toJSONString(vo), JSON.toJSONString(listingFollowGoods));
        Long listingId = listingFollowGoods.getListingId();
        String userName = getUserName();
        if (Objects.equals(vo.getIsTop(), 1)) {
            List<ListingFollowGoods> updateList = Lists.newArrayList();
            ListingFollowGoods oldTopGoods = listingFollowGoodsService.lambdaQuery()
                    .eq(ListingFollowGoods::getListingId, listingId)
                    .eq(ListingFollowGoods::getIsDel, 0)
                    .eq(ListingFollowGoods::getStatus, 1)
                    .eq(ListingFollowGoods::getIsTop, 1)
                    .one();
            if (oldTopGoods != null) {
                oldTopGoods.setIsTop(0);
                oldTopGoods.setUpdateTime(LocalDateTime.now());
                oldTopGoods.setUpdateUser(userName);
                updateList.add(oldTopGoods);
            }
            listingFollowGoods.setIsTop(1);
            listingFollowGoods.setUpdateTime(LocalDateTime.now());
            listingFollowGoods.setUpdateUser(userName);
            updateList.add(listingFollowGoods);
            listingFollowGoodsService.updateBatchById(updateList);
        } else if(Objects.equals(vo.getIsTop(), 0)){
            listingFollowGoodsService.lambdaUpdate()
                    .set(ListingFollowGoods::getIsTop, 0)
                    .set(ListingFollowGoods::getUpdateTime, LocalDateTime.now())
                    .set(ListingFollowGoods::getUpdateUser, userName)
                    .eq(ListingFollowGoods::getId, listingFollowGoods.getId())
                    .update();
        }
    }

    @Override
    public void syncListingGoodsSort() {
        log.info("syncListingGoodsSort start");
        executor.execute(()->{
            long start = System.currentTimeMillis();
            int pageNum = 0, pageSize = 100;
            IPage<ListingInfo> page;
            do {
                page = listingInfoService.lambdaQuery().eq(ListingInfo::getIsDel, 0).page(new Page<>(pageNum++, pageSize));
                List<ListingInfo> records = page.getRecords();
                if (CollectionUtils.isEmpty(records)) {
                    log.info("syncListingGoodsSort records is empty, pageNum = {}", pageNum);
                    break;
                }
                records.forEach(this::syncListingGoodsSort);
            }while (CollectionUtils.isNotEmpty(page.getRecords()));
            log.info("syncListingGoodsSort end, cost = {}ms", System.currentTimeMillis() - start);
        });
    }

    @Override
    public void syncListingGoodsSort(SyncListingGoodsSortDTO dto) {
        log.info("syncListingGoodsSort start");
        String traceId = MDC.get("traceId");
        executor.execute(()->{
            MDC.put("traceId", traceId);
            try {
                Map<Long, AssessmentShop> assessmentShopMap = assessmentShopService.lambdaQuery().list().stream()
                        .collect(Collectors.toMap(AssessmentShop::getShopId, Function.identity(), (v1, v2) -> v1));
                LocalDateTime dateTime = LocalDateTime.now()
                        .withHour(2)
                        .withMinute(0)
                        .withSecond(0)
                        .withNano(0);
                long start = System.currentTimeMillis();
                int pageNum = 0, pageSize = 100;
                IPage<ListingInfo> page;
                do {
                    page = listingInfoService.lambdaQuery().eq(ListingInfo::getIsDel, 0).page(new Page<>(pageNum++, pageSize));
                    List<ListingInfo> records = page.getRecords();
                    if (CollectionUtils.isEmpty(records)) {
                        log.info("syncListingGoodsSort records is empty, pageNum = {}", pageNum);
                        break;
                    }
                    records.forEach(record -> {
                        try {
                            syncListingGoodsSort(record, assessmentShopMap, dto, dateTime);
                        } catch (Exception e) {
                            log.error("syncListingGoodsSort Execution error. record:{} e.", JSON.toJSONString(record), e);
                        }
                    });
                }while (CollectionUtils.isNotEmpty(page.getRecords()));
                log.info("syncListingGoodsSort end, cost = {}ms", System.currentTimeMillis() - start);
            } catch (Exception e) {
                log.error("syncListingGoodsSort error.", e);
            } finally {
                MDC.remove("traceId");
            }
        });
    }

    @Override
    public List<ListingInfoVO> randomListingGoodsList(Integer pageSize) {
        Long shopId = clientInfoSupportService.getShopInfo().getShopId();
        if(Objects.isNull(shopId)){
            log.info("randomListingGoodsList shopId is null");
            return null;
        }

        Integer type = getListingTypeByShopId(shopId);

        List<Integer> shopTagValues = getShopTagValuesByShopId(shopId);
        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(shopId);
        List<Integer> deliveryTypeValues = getDeliveryTypeValues(faMerchantsApply);

        Set<Long> allowList = listingShopWhiteListService.lambdaQuery()
                .eq(ListingShopWhiteList::getIsDel, 0)
                .eq(ListingShopWhiteList::getShopId, shopId)
                .select(ListingShopWhiteList::getListingId, ListingShopWhiteList::getShopId)
                .list()
                .stream().map(ListingShopWhiteList::getListingId).collect(Collectors.toSet());

        List<Long> shopCategoryIdList = getShopCategoryIdList(shopId);
        if(CollectionUtils.isEmpty(shopCategoryIdList)){
            log.info("randomListingGoodsList shopCategoryIdList is empty, shop id = {}", shopId);
            return new ArrayList<>();
        }
        List<ListingFollowGoods> list = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getShopId, shopId)
                .eq(ListingFollowGoods::getIsDel,0)
                .eq(ListingFollowGoods::getStatus, 1)
                .list();

        List<Long> existIdList = list.stream().map(ListingFollowGoods::getListingId).distinct().collect(Collectors.toList());
        IPage<ListingInfo> page = listingInfoService.lambdaQuery()
                .and(wrapper -> wrapper
                        .eq(ListingInfo::getShopWhiteListType, 0)
                        .eq(ListingInfo::getShopTag, 0)
                        .eq(ListingInfo::getDeliveryType, 0)
                        .or(wrapper2 -> wrapper2
                                .in(CollectionUtils.isNotEmpty(allowList), ListingInfo::getId, allowList).or()
                                .in(CollectionUtils.isNotEmpty(shopTagValues), ListingInfo::getShopTag, shopTagValues).or()
                                .in(CollectionUtils.isNotEmpty(deliveryTypeValues), ListingInfo::getDeliveryType, deliveryTypeValues)))
                .eq(type != null, ListingInfo::getListingType, type)
                .eq(ListingInfo::getStatus, 1)
                .in(CollectionUtils.isNotEmpty(shopCategoryIdList), ListingInfo::getCategoryId, shopCategoryIdList)
                .notIn(CollectionUtils.isNotEmpty(existIdList), ListingInfo::getId, existIdList)
                .orderByDesc(ListingInfo::getCreateTime)
                .page(new Page<>(randomPageStart++, pageSize));
        if(CollectionUtils.isEmpty(page.getRecords())){
            randomPageStart = 1L;
            page = listingInfoService.lambdaQuery()
                    .and(wrapper -> wrapper
                            .eq(ListingInfo::getShopWhiteListType, 0)
                            .eq(ListingInfo::getShopTag, 0)
                            .eq(ListingInfo::getDeliveryType, 0)
                            .or(wrapper2 -> wrapper2
                                    .in(CollectionUtils.isNotEmpty(allowList), ListingInfo::getId, allowList).or()
                                    .in(CollectionUtils.isNotEmpty(shopTagValues), ListingInfo::getShopTag, shopTagValues).or()
                                    .in(CollectionUtils.isNotEmpty(deliveryTypeValues), ListingInfo::getDeliveryType, deliveryTypeValues)))
                    .eq(type != null, ListingInfo::getListingType, type)
                    .eq(ListingInfo::getStatus, 1)
                    .in(CollectionUtils.isNotEmpty(shopCategoryIdList), ListingInfo::getCategoryId, shopCategoryIdList)
                    .notIn(CollectionUtils.isNotEmpty(existIdList), ListingInfo::getId, existIdList)
                    .orderByDesc(ListingInfo::getCreateTime)
                    .page(new Page<>(randomPageStart++, pageSize));
        }
        List<ListingInfo> records = page.getRecords();
        if(CollectionUtils.isEmpty(page.getRecords())){
            return new ArrayList<>();
        }
        List<Long> goodsIdList = records.stream().map(ListingInfo::getGoodsId).collect(Collectors.toList());
        Collection<Goods> goodsList = goodsService.listByIds(goodsIdList);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity()));
        List<ListingInfoVO> collect = page.getRecords().stream()
                .map(listingInfo -> {
                    ListingInfoVO goodsVo = new ListingInfoVO();
                    BeanCopyUtil.copyProperties(listingInfo, goodsVo);
                    goodsVo.setListingId(listingInfo.getId());
                    Goods goods = goodsMap.get(listingInfo.getGoodsId());
                    if(Objects.nonNull(goods)){
                        goodsVo.setMainImage(goods.getMainImage());
                        goodsVo.setMaxPrice(goods.getMaxPrice());
                        goodsVo.setMinPrice(goods.getMinPrice());
                    }
                    return goodsVo;
                }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public Long getListingTopGoods(Long listingId, List<Long> goodsIdList) {
        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery().eq(ListingFollowGoods::getListingId, listingId).eq(ListingFollowGoods::getStatus, 1)
                .eq(ListingFollowGoods::getIsDel, 0)
                .in(ListingFollowGoods::getGoodsId, goodsIdList)
                .orderByDesc(ListingFollowGoods::getIsTop).orderByDesc(ListingFollowGoods::getSort).last("limit 1").one();
        return Objects.isNull(listingFollowGoods) ? null : listingFollowGoods.getGoodsId();
    }

    private List<Long> getShopCategoryIdList(Long shopId){

        FaMerchantsApply faMerchantsApply = faMerchantsApplyCoreService.queryById(shopId);
        Long categoryMainId = faMerchantsApply.getCategoryMainId();
        if(Objects.isNull(categoryMainId)){
            log.info("getShopCategoryIdList categoryMainId is null");
            return null;
        }
        CategoryMain categoryMain = categoryMainService.getById(categoryMainId);
        List<Long> mainCategoryIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(categoryMain.getLeafCategoryIds())) {
            Arrays.stream(categoryMain.getLeafCategoryIds().split(",")).map(Long::parseLong).forEach(mainCategoryIds::add);
        }
        if (StringUtils.isNotBlank(categoryMain.getLeafSecondCategoryIds())) {
            Arrays.stream(categoryMain.getLeafSecondCategoryIds().split(",")).map(Long::parseLong).forEach(mainCategoryIds::add);
        }
        return mainCategoryIds;
    }

    void syncListingGoodsSort(ListingInfo listingInfo, Map<Long, AssessmentShop> map, SyncListingGoodsSortDTO dto,
                              LocalDateTime dateTime) {
        List<ListingFollowGoods> list = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, listingInfo.getId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .list();

        if (CollectionUtils.isEmpty(list)) {
            log.info("syncListingGoodsSort list is empty, listingInfo = {}", JSON.toJSONString(listingInfo));
            return;
        }
        List<Long> goodsIdList = list.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        Collection<Goods> goods = goodsService.lambdaQuery().eq(Goods::getIsDel, 0)
                .eq(Goods::getIsShow, 1)
                .in(Goods::getId, goodsIdList)
                .list();
        if (CollectionUtils.isEmpty(goods)) {
            log.warn("syncListingGoodsSort goods is empty. goodsIdList:{}", JSONObject.toJSONString(goodsIdList));
            return;
        }

        List<GoodsSkuBo> goodsSkuList = getGoodsSkuList(goods);
        Map<Long, List<GoodsSkuBo>> goodsSkuMap = goodsSkuList.stream().collect(Collectors.groupingBy(GoodsSkuBo::getGoodsId));
        Map<Long, Pair<BigDecimal, BigDecimal>> maxPriceMap = goods.stream().filter(g -> g.getMaxPrice() != null && goodsSkuMap.containsKey(g.getId()))
                .collect(Collectors.toMap(Goods::getId, (g) -> {
                    List<GoodsSkuBo> goodsSkuBos = goodsSkuMap.get(g.getId());
                    BigDecimal originalPrice = goodsSkuBos.stream().max(Comparator.comparing(GoodsSkuBo::getOriginalPrice)).map(GoodsSkuBo::getOriginalPrice).orElse(goodsSkuBos.get(0).getOriginalPrice());
                    return new MutablePair<>(originalPrice, g.getMaxPrice());
                }));
        log.debug("syncListingGoodsSort maxPriceMap = {}, listingInfo = {}", maxPriceMap, JSON.toJSONString(listingInfo));

        // 配置样品数量，默认为100
        final BigDecimal basicSampleNum = BigDecimal.valueOf(dto.getBasicSampleNum());
        // 平台成交不卖率
        final BigDecimal platformNoSellRefundRate = BigDecimal.valueOf(dto.getPlatformNoSellRefundRate());
        // 平台质量退款率
        final BigDecimal platformQualityRefundRate = BigDecimal.valueOf(dto.getPlatformQualityRefundRate());
        // 成交不卖惩罚门槛
        final BigDecimal noSellPunishThreshold = BigDecimal.valueOf(dto.getNoSellPunishThreshold());
        // 成交不卖惩罚
        final BigDecimal noSellPunishMul = BigDecimal.valueOf(dto.getNoSellPunishMul());
        // 质量退款惩罚门槛
        final BigDecimal qualityPunishThreshold = BigDecimal.valueOf(dto.getQualityPunishThreshold());
        // 质量退款惩罚
        final BigDecimal qualityPunishMul = BigDecimal.valueOf(dto.getQualityPunishMul());

        // 可售国家
        Map<Long, String> goodsCountryMap = goods.stream().filter(o -> StringUtils.isNotBlank(o.getCountry())).collect(Collectors.toMap(Goods::getId, Goods::getCountry));

        // 填充各商品的 不卖成交率 & 质量退款率 & 商品可售国家
        Collection<ListingGoodsSortBo> goodsListingGoodsSortList = getListingGoodsSortBos(map, dateTime, list, goodsCountryMap, basicSampleNum, platformNoSellRefundRate, platformQualityRefundRate);
        Map<Long, ListingGoodsSortBo> sortMap = goodsListingGoodsSortList.stream().collect(Collectors.toMap(ListingGoodsSortBo::getGoodsId, Function.identity(), (v1, v2) -> v1));
        log.debug("syncListingGoodsSort sortMap:{}", JSONObject.toJSONString(sortMap));

        // 获取国家运费
        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(Lists.newArrayList(maxPriceMap.keySet()));
        Map<Long, Map<String, BigDecimal>> goodsFreightMap = goodsFreights.stream().collect(Collectors.groupingBy(GoodsFreight::getGoodsId, Collectors.mapping(Function.identity(), Collectors.toMap(GoodsFreight::getCode, GoodsFreight::getCurrentFreight, (v1, v2) -> v2))));

        // 异步填充综合排序信息
        asyncFillCountryCompositeScoreMap(list, maxPriceMap, sortMap, goodsFreightMap, noSellPunishThreshold, noSellPunishMul, qualityPunishThreshold, qualityPunishMul, goodsSkuMap);
        log.debug("syncListingGoodsSort async filling countryCompositeScore sortMap:{}", JSONObject.toJSONString(sortMap));

        LocalDateTime now = LocalDateTime.now();
        Map<Long, String> globalRankByCountry = globalRankByCountry(goodsListingGoodsSortList);

        list.sort(Comparator.comparing(ListingFollowGoods::getCompositeScore));
        int sort = 1;

        Optional<ListingFollowGoods> any = list.stream().filter(g -> {
            ListingGoodsSortBo goodsSortBo = sortMap.get(g.getGoodsId());
            List<String> sellableCountriesList = goodsSortBo.getSellableCountriesList();
            return sellableCountriesList.contains("DE") || sellableCountriesList.contains("GB");
        }).findAny();

        final Long firstGoodsId = any.map(ListingFollowGoods::getGoodsId).orElse(null);
        final BigDecimal firstCompositeScore = any.map(followGoods -> BigDecimal.valueOf(followGoods.getCompositeScore())).orElse(null);
        final BigDecimal firstSuggestedPrice = firstGoodsId != null ? maxPriceMap.get(firstGoodsId).getRight() : null;
        for (ListingFollowGoods listingFollowGoods : list) {
            Long goodsId = listingFollowGoods.getGoodsId();
            String countrySorted = globalRankByCountry.getOrDefault(goodsId, "");
            BigDecimal suggestedPrice = getSuggestedPrice(firstSuggestedPrice, sortMap, goodsId, firstCompositeScore, listingFollowGoods.getCompositeScore(), firstGoodsId, maxPriceMap.get(goodsId));

            listingFollowGoods.setSuggestedPrice(suggestedPrice.floatValue());
            listingFollowGoods.setCountryScoreSort(countrySorted);
            listingFollowGoods.setScoreSort(sort++);
            listingFollowGoods.setUpdateTime(now);
        }

        listingFollowGoodsService.updateBatchById(list);
        log.info("syncListingGoodsSort list = {}", JSON.toJSONString(list));
    }

    private static BigDecimal getSuggestedPrice(BigDecimal firstSuggestedPrice, Map<Long, ListingGoodsSortBo> sortMap, Long goodsId, BigDecimal firstCompositeScore, float compositeScore, Long firstGoodsId, Pair<BigDecimal, BigDecimal> pair) {
        if (compositeScore == 999999999f) {
            return BigDecimal.ZERO;
        }

        if (firstSuggestedPrice == null) {
            return BigDecimal.ZERO;
        }

        if (firstCompositeScore == null) {
            return BigDecimal.ZERO;
        }

        if (Objects.equals(firstGoodsId, goodsId)) {
            return firstSuggestedPrice;
        }

        ListingGoodsSortBo goodsSort = sortMap.get(goodsId);
        List<String> mustCountry = Lists.newArrayList("DE", "GB");
        mustCountry.retainAll(goodsSort.getSellableCountriesList());
        if (CollectionUtils.isEmpty(mustCountry)) {
            return BigDecimal.ZERO;
        }

        BigDecimal maxPriceCoefficient = goodsSort.getNoSellMul().multiply(goodsSort.getQualityMul()).multiply(goodsSort.getWeightingCoefficient());
        BigDecimal maxPrice = firstCompositeScore.divide(maxPriceCoefficient, 4, RoundingMode.FLOOR);
        BigDecimal suggestPrice = maxPrice.multiply(new BigDecimal("0.99")).setScale(2, RoundingMode.FLOOR);

        BigDecimal goodsMaxOriginalPrice = pair.getLeft();
        BigDecimal goodsMaxPrice = pair.getRight();
        BigDecimal defaultDelivery = goodsMaxPrice.subtract(goodsMaxOriginalPrice);
        if (goodsMaxOriginalPrice.compareTo(BigDecimal.ZERO) == 0 || suggestPrice.compareTo(goodsMaxPrice) >= 0 || suggestPrice.compareTo(defaultDelivery) <= 0) {
            return BigDecimal.ZERO;
        }
        if (suggestPrice.subtract(defaultDelivery).divide(goodsMaxOriginalPrice, 4, RoundingMode.HALF_UP).compareTo(new BigDecimal("0.97")) > 0) {
            suggestPrice = goodsMaxOriginalPrice.multiply(new BigDecimal("0.97")).add(defaultDelivery).setScale(2, RoundingMode.HALF_UP);
        }
        return suggestPrice;
    }

    private List<GoodsSkuBo> getGoodsSkuList(Collection<Goods> goods) {
        List<Long> goodsIdList = goods.stream().map(Goods::getId).distinct().collect(Collectors.toList());
//        List<GoodsSkuPo> goodsSkuPos = goodsSkuService.findListByGoodsIds(goodsIdList);
//
//        List<Long> scGoodsIds = goodsSkuPos.stream().map(GoodsSkuPo::getGoodsId).distinct().collect(Collectors.toList());
//        if (CollectionUtils.isEqualCollection(scGoodsIds, goodsIdList)) {
//            return BeanCopyUtil.transformList(goodsSkuPos, GoodsSkuBo.class);
//        }

//        goodsIdList = goodsIdList.stream().filter(id -> !scGoodsIds.contains(id)).distinct().collect(Collectors.toList());
        List<GoodsItem> goodsItems = goodsItemService.queryGoodsIdsList(goodsIdList);
        List<GoodsSkuBo> goodsSkuList = Lists.newArrayList();
//        if (CollectionUtils.isNotEmpty(goodsSkuPos)) {
//            goodsSkuList.addAll(BeanCopyUtil.transformList(goodsSkuPos, GoodsSkuBo.class));
//        }

        if (CollectionUtils.isNotEmpty(goodsItems)) {
            List<GoodsSkuBo> collect = goodsItems.stream().map(item -> {
                GoodsSkuBo transform = BeanCopyUtil.transform(item, GoodsSkuBo.class);
                transform.setId(item.getSkuId());
                transform.setOriginalPrice(item.getOrginalPrice());
                transform.setOriginalMarketPrice(item.getOrginalMarketPrice());
                return transform;
            }).collect(Collectors.toList());
            goodsSkuList.addAll(collect);
        }

        return goodsSkuList;
    }

    public static Map<Long, String> globalRankByCountry(Collection<ListingGoodsSortBo> goodsList) {
        // 1. 收集所有国家
        Set<String> allCountries = goodsList.stream()
                .flatMap(bo -> {
                    Map<String, BigDecimal> map = bo.getCountryCompositeScoreMap();
                    return map == null ? Stream.empty() : map.keySet().stream();
                })
                .collect(Collectors.toSet());

        // 2. 对每个国家，生成 goodsId -> rank 的映射
        Map<String, Map<Long, Integer>> countryToRankMap = new HashMap<>();
        for (String country : allCountries) {
            // 按该国家分数升序排序 goodsList
            List<Long> sortedGoodsIds = goodsList.stream()
                    .filter(bo -> {
                        BigDecimal score = bo.getCountryCompositeScoreMap().get(country);
                        return score != null;
                    })
                    .sorted(Comparator.comparing(bo -> bo.getCountryCompositeScoreMap().get(country)))
                    .map(ListingGoodsSortBo::getGoodsId)
                    .collect(Collectors.toList());

            // 编号并放入 map
            Map<Long, Integer> rankMap = new HashMap<>();
            for (int i = 0; i < sortedGoodsIds.size(); i++) {
                rankMap.put(sortedGoodsIds.get(i), i + 1);  // 从 1 开始排名
            }
            countryToRankMap.put(country, rankMap);
        }

        // 3. 构造最终每个 goodsId 的排名串
        Map<Long, String> result = new HashMap<>();
        for (ListingGoodsSortBo bo : goodsList) {
            Long goodsId = bo.getGoodsId();
            String rankingStr = bo.getSellableCountriesList().stream()
                    .sorted()
                    .map(country -> {
                        int rank = countryToRankMap.get(country)
                                .getOrDefault(goodsId, 999);
                        return country + ":" + rank;
                    })
                    .collect(Collectors.joining(","));
            result.put(goodsId, rankingStr);
        }

        return result;
    }

    private void asyncFillCountryCompositeScoreMap(List<ListingFollowGoods> list, Map<Long, Pair<BigDecimal, BigDecimal>> maxPriceMap, Map<Long, ListingGoodsSortBo> sortMap,
                                                   Map<Long, Map<String, BigDecimal>> goodsFreightMap, BigDecimal noSellPunishThreshold, BigDecimal noSellPunishMul,
                                                   BigDecimal qualityPunishThreshold, BigDecimal qualityPunishMul, Map<Long, List<GoodsSkuBo>> goodsSkuMap) {
        String traceId = MDC.get("traceId");
        CountDownLatch cdl = new CountDownLatch(list.size());
        for (ListingFollowGoods listingFollowGoods : list) {
            final Long goodsId = listingFollowGoods.getGoodsId();
            final ListingFollowGoods item = listingFollowGoods;
            Pair<BigDecimal, BigDecimal> maxPricePair = maxPriceMap.get(goodsId);
            ListingGoodsSortBo goodsSort = sortMap.get(goodsId);
            if (goodsSort == null) {
                log.warn("syncListingGoodsSort goodsSort is empty. goodsId:{}", goodsId);
                item.setCompositeScore(new BigDecimal("999999999").floatValue());
                item.setCountryScoreSort("");
                cdl.countDown();
                continue;
            }

            addFullGoodsInfoPool.execute(() -> {
                try {
                    MDC.put("traceId", traceId);

                    // 综合分加权系数
                    BigDecimal weightingCoefficient = getWeightingCoefficient(goodsSkuMap, listingFollowGoods);
                    goodsSort.setWeightingCoefficient(weightingCoefficient);

                    // 不买成交率系数
                    BigDecimal noSellMul = getMul(noSellPunishThreshold, noSellPunishMul, goodsSort.getAdjustedNoSellRefundRate());

                    // 质量退款率系数
                    BigDecimal qualityMul = getMul(qualityPunishThreshold, qualityPunishMul, goodsSort.getAdjustedQualityRefundRate());

                    goodsSort.setNoSellMul(noSellMul);

                    goodsSort.setQualityMul(qualityMul);
                    log.info("syncListingGoodsSort using threads. goodsId:{} goodsSort:{} maxPricePair:{}", goodsId, JSONObject.toJSONString(goodsSort), JSONObject.toJSONString(maxPricePair));

                    BigDecimal compositeScore = maxPricePair == null || maxPricePair.getRight() == null ? new BigDecimal("999999999") : calculateComprehensiveScore(maxPricePair.getRight(), goodsSort);
                    item.setCompositeScore(compositeScore.floatValue());

                    fillCountryCompositeScoreMap(goodsFreightMap, goodsId, goodsSort, maxPricePair);
                } catch (Exception e) {
                    log.error("syncListingGoodsSort fillCountryCompositeScoreMap error. goodsId:{} ", goodsId, e);
                } finally {
                    MDC.remove("traceId");
                    cdl.countDown();
                }
            });
        }

        try {
            if (!cdl.await(30, TimeUnit.SECONDS)) {
                log.warn("syncListingGoodsSort fillCountryCompositeScoreMap timeout.");
            }
        } catch (InterruptedException e) {
            LogUtils.error(log, "syncListingGoodsSort fillCountryCompositeScoreMap CountDownLatch await. list:{} ", list, e);
        }
    }

    private static BigDecimal getWeightingCoefficient(Map<Long, List<GoodsSkuBo>> goodsSkuMap, ListingFollowGoods listingFollowGoods) {
        List<GoodsSkuBo> items = goodsSkuMap.get(listingFollowGoods.getGoodsId());
        if (CollectionUtils.isNotEmpty(items)) {
            int total = items.size();
            int num = (int) items.stream().filter(item -> {
                Integer skuStatus = item.getSkuStatus();
                Long stock = item.getStock();
                if (skuStatus == null || stock == null) {
                    return false;
                }
                return skuStatus == 1 && stock > 0;
            }).count();

            BigDecimal res = BigDecimal.valueOf(total - num).divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP);
            if (res.compareTo(new BigDecimal("0.5")) >= 0) {
                return BigDecimal.ONE.add(res);
            }
        }

        return BigDecimal.ONE;
    }

    private void fillCountryCompositeScoreMap(Map<Long, Map<String, BigDecimal>> goodsFreightMap, Long goodsId, ListingGoodsSortBo goodsSort, Pair<BigDecimal, BigDecimal> maxPricePair) {
        Map<String, BigDecimal> freightMap = goodsFreightMap.get(goodsId);

        // 可售国家
        List<String> sellableCountriesList = goodsSort.getSellableCountriesList();

        Map<String, BigDecimal> countryCompositeScoreMap = goodsSort.getCountryCompositeScoreMap();
        if (MapUtils.isEmpty(countryCompositeScoreMap)) {
            countryCompositeScoreMap = Maps.newHashMap();
            goodsSort.setCountryCompositeScoreMap(countryCompositeScoreMap);
        }

        BigDecimal maxPrice;
        for (String country : sellableCountriesList) {
            if (maxPricePair != null) {
                BigDecimal freight = MapUtils.isEmpty(freightMap) ? BigDecimal.ZERO :freightMap.getOrDefault(country, BigDecimal.ZERO);
                if (freight.compareTo(BigDecimal.ZERO) > 0) {
                    if (maxPricePair.getLeft() == null) {
                        countryCompositeScoreMap.put(country, new BigDecimal("999999999"));
                        continue;
                    }

                    maxPrice = maxPricePair.getLeft().add(freight);
                } else {
                    if (maxPricePair.getRight() == null) {
                        countryCompositeScoreMap.put(country, new BigDecimal("999999999"));
                        continue;
                    }

                    maxPrice = maxPricePair.getRight();
                }

                // 计算综合分数
                BigDecimal compositeScore = calculateComprehensiveScore(maxPrice, goodsSort);
                countryCompositeScoreMap.put(country, compositeScore);
            } else {
                countryCompositeScoreMap.put(country, new BigDecimal("999999999"));
            }

        }
    }

    @NotNull
    private BigDecimal calculateComprehensiveScore(BigDecimal maxPrice, ListingGoodsSortBo goodsSort) {
        return maxPrice.multiply(goodsSort.getNoSellMul()).multiply(goodsSort.getQualityMul()).multiply(goodsSort.getWeightingCoefficient()).setScale(2, RoundingMode.HALF_UP);
    }

    @NotNull
    private static BigDecimal getMul(BigDecimal threshold, BigDecimal punishMul, BigDecimal adjustedRate) {
        BigDecimal noSellMul;
        if (adjustedRate.compareTo(threshold) <= 0) {
            noSellMul = BigDecimal.ONE;
        } else {
            noSellMul = ((adjustedRate.subtract(threshold)).multiply(punishMul)).add(BigDecimal.ONE);
        }

        return noSellMul;
    }

    @NotNull
    private Collection<ListingGoodsSortBo> getListingGoodsSortBos(Map<Long, AssessmentShop> map, LocalDateTime dateTime, List<ListingFollowGoods> list, Map<Long, String> goodsCountryMap, BigDecimal basicSampleNum, BigDecimal platformNoSellRefundRate, BigDecimal platformQualityRefundRate) {
        String traceId = MDC.get("traceId");
        Collection<ListingGoodsSortBo> goodsListingGoodsSortList = Collections.synchronizedCollection(Lists.newArrayList());
        CountDownLatch cdl = new CountDownLatch(list.size());
        list.forEach(listingFollowGoods -> {
            addFullGoodsInfoPool.execute(() -> {
                try {
                    MDC.put("traceId", traceId);
                    fillAdjustedNoSellRefundRateAndQualityRefundRate(map, goodsCountryMap, dateTime, basicSampleNum, platformNoSellRefundRate, platformQualityRefundRate, listingFollowGoods, goodsListingGoodsSortList);
                } catch (Exception e) {
                    log.error("syncListingGoodsSort fillAdjustedNoSellRefundRateAndQualityRefundRate fail.", e);
                } finally {
                    cdl.countDown();
                    MDC.remove("traceId");
                }
            });
        });

        try {
            if (!cdl.await(30, TimeUnit.SECONDS)) {
                log.warn("syncListingGoodsSort Not all tasks finished within timeout.");
            }
        } catch (InterruptedException e) {
            LogUtils.error(log, "syncListingGoodsSort CountDownLatch await. list:{} ", list, e);
        }

        return goodsListingGoodsSortList;
    }

    private void fillAdjustedNoSellRefundRateAndQualityRefundRate(Map<Long, AssessmentShop> map, Map<Long, String> goodsCountryMap, LocalDateTime dateTime, BigDecimal basicSampleNum, BigDecimal platformNoSellRefundRate,
                                                                  BigDecimal platformQualityRefundRate, ListingFollowGoods listingFollowGoods, Collection<ListingGoodsSortBo> goodsListingGoodsSortDeque) {
        // 应发货总数
        final BigDecimal goodsRealWarehouseInCnt;
        // 应收货订单数
        final BigDecimal goodsReceiveTotalCnt;
        // 成交不卖率
        final BigDecimal goodsNoSellRefundRate;
        // 质量退款率
        final BigDecimal goodsQualityRefundRate;

        LocalDateTime updateTime = listingFollowGoods.getUpdateTime();
        if (updateTime == null || updateTime.isBefore(dateTime)) {
            goodsRealWarehouseInCnt = BigDecimal.ZERO;
            goodsReceiveTotalCnt = BigDecimal.ZERO;
            goodsNoSellRefundRate = BigDecimal.ZERO;
            goodsQualityRefundRate = BigDecimal.ZERO;
            listingFollowGoods.setRealNoSellRefundCnt(0);
            listingFollowGoods.setRealWarehouseInCnt(0);
            listingFollowGoods.setNoSellRefundRate(0f);
            listingFollowGoods.setQualityRefundCnt(0);
            listingFollowGoods.setReceiveTotalCnt(0);
            listingFollowGoods.setQualityRefundRate(0f);
        } else {
            goodsRealWarehouseInCnt = BigDecimal.valueOf(listingFollowGoods.getRealWarehouseInCnt());
            goodsReceiveTotalCnt = BigDecimal.valueOf(listingFollowGoods.getReceiveTotalCnt());
            goodsNoSellRefundRate = BigDecimal.valueOf(listingFollowGoods.getNoSellRefundRate());
            goodsQualityRefundRate = BigDecimal.valueOf(listingFollowGoods.getQualityRefundRate());
        }

        AssessmentShop assessmentShop = map.get(listingFollowGoods.getShopId());
        // 店铺应发货总数
        final BigDecimal shopRealWarehouseInCnt;
        // 店铺应收货订单数
        final BigDecimal shopReceiveTotalCnt;
        // 店铺成交不卖率
        final BigDecimal shopNoSellRefundRate;
        // 店铺质量退款率
        final BigDecimal shopQualityRefundRate;
        if (assessmentShop == null) {
            shopRealWarehouseInCnt = BigDecimal.ZERO;
            shopReceiveTotalCnt = BigDecimal.ZERO;
            shopNoSellRefundRate = BigDecimal.ZERO;
            shopQualityRefundRate = BigDecimal.ZERO;
        } else {
            shopRealWarehouseInCnt = BigDecimal.valueOf(assessmentShop.getRealWarehouseInCnt());
            shopReceiveTotalCnt = BigDecimal.valueOf(assessmentShop.getReceiveTotalCnt());
            shopNoSellRefundRate = BigDecimal.valueOf(assessmentShop.getNoSellRefundRate());
            shopQualityRefundRate = BigDecimal.valueOf(assessmentShop.getQualityRefundRate());
        }

        // 计算调整后的不卖成交率
        BigDecimal adjustedNoSellRefundRate = calListingAdjustedRate(basicSampleNum, goodsRealWarehouseInCnt, shopRealWarehouseInCnt,
                goodsNoSellRefundRate, shopNoSellRefundRate, platformNoSellRefundRate);

        // 计算调整后的质量退款率
        BigDecimal adjustedQualityRefundRate = calListingAdjustedRate(basicSampleNum, goodsReceiveTotalCnt, shopReceiveTotalCnt,
                goodsQualityRefundRate, shopQualityRefundRate, platformQualityRefundRate);

        // 获取商品可售国家
        List<String> sellableCountriesList = getSellableCountriesList(goodsCountryMap.get(listingFollowGoods.getGoodsId()));
        log.info("syncListingGoodsSort goodsId:{} adjustedNoSellRefundRate:{} adjustedQualityRefundRate:{}", listingFollowGoods.getGoodsId(), adjustedNoSellRefundRate.floatValue(), adjustedQualityRefundRate.floatValue());

        ListingGoodsSortBo bo = ListingGoodsSortBo.builder().goodsId(listingFollowGoods.getGoodsId()).adjustedNoSellRefundRate(adjustedNoSellRefundRate).
                adjustedQualityRefundRate(adjustedQualityRefundRate).sellableCountriesList(sellableCountriesList).build();
        goodsListingGoodsSortDeque.add(bo);
    }

    private BigDecimal calListingAdjustedRate(BigDecimal basicSampleNum, BigDecimal goodsRealSold, BigDecimal shopRealSold,
                                              BigDecimal goodsRate, BigDecimal shopRate, BigDecimal platformRate) {
        BigDecimal goodsWeight;
        BigDecimal shopWeight;
        BigDecimal platformWeight;
        if (goodsRealSold.compareTo(basicSampleNum) >= 0) {
            goodsWeight = BigDecimal.ONE;
            shopWeight = BigDecimal.ZERO;
            platformWeight = BigDecimal.ZERO;
        } else if (goodsRealSold.compareTo(BigDecimal.ZERO) <= 0) {
            goodsWeight = BigDecimal.ZERO;
            if (shopRealSold.compareTo(basicSampleNum) >= 0) {
                shopWeight = BigDecimal.ONE;
                platformWeight = BigDecimal.ZERO;
            } else if (shopRealSold.compareTo(BigDecimal.ZERO) <= 0) {
                shopWeight = BigDecimal.ZERO;
                platformWeight = BigDecimal.ONE;
            } else {
                shopWeight = shopRealSold.divide(basicSampleNum, 2, RoundingMode.HALF_UP);
                platformWeight = BigDecimal.ONE.subtract(shopWeight);
            }
        } else {
            goodsWeight = goodsRealSold.divide(basicSampleNum, 2, RoundingMode.HALF_UP);
            BigDecimal num = shopRealSold.subtract(goodsRealSold);
            if (num.compareTo(basicSampleNum) >= 0) {
                shopWeight = BigDecimal.ONE.subtract(goodsWeight);
                platformWeight = BigDecimal.ZERO;
            } else if (num.compareTo(BigDecimal.ZERO) <= 0) {
                shopWeight = BigDecimal.ZERO;
                platformWeight = BigDecimal.ONE.subtract(goodsWeight);
            } else {
                shopWeight = num.divide(basicSampleNum, 2, RoundingMode.HALF_UP);
                BigDecimal subtract = BigDecimal.ONE.subtract(goodsWeight);
                shopWeight = shopWeight.compareTo(subtract) > 0 ? subtract : shopWeight;
                platformWeight = BigDecimal.ONE.subtract(shopWeight).subtract(goodsWeight);
            }
        }

        BigDecimal goodsMultiply = goodsWeight.multiply(goodsRate);
        BigDecimal shopMultiply = shopWeight.multiply(shopRate);
        BigDecimal platformMultiply = platformWeight.multiply(platformRate);
        return goodsMultiply.add(shopMultiply).add(platformMultiply);
    }

    @NotNull
    private static List<String> getSellableCountriesList(String sellableCountries) {
        if (StringUtils.isBlank(sellableCountries)) {
            return Collections.emptyList();
        }

        return Lists.newArrayList(sellableCountries.split(","));
    }

    void syncListingGoodsSort(ListingInfo listingInfo) {
        List<ListingFollowGoods> list = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, listingInfo.getId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .list();

        if (CollectionUtils.isEmpty(list)) {
            log.info("syncListingGoodsSort list is empty, listingInfo = {}", JSON.toJSONString(listingInfo));
            return;
        }
        List<Long> goodsIdList = list.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        Collection<Goods> goods = goodsService.listByIds(goodsIdList);
        Map<Long, BigDecimal> maxPriceMap = goods.stream().collect(Collectors.toMap(Goods::getId, Goods::getMaxPrice));
        log.info("syncListingGoodsSort maxPriceMap = {}, listingInfo = {}", maxPriceMap, JSON.toJSONString(listingInfo));
        list.sort((Comparator.comparing(o -> maxPriceMap.getOrDefault(o.getGoodsId(), BigDecimal.ZERO))));
        int sort = 1;
        LocalDateTime now = LocalDateTime.now();
        for (ListingFollowGoods listingFollowGoods : list) {
            listingFollowGoods.setSort(sort++);
            listingFollowGoods.setUpdateTime(now);
        }
        listingFollowGoodsService.updateBatchById(list);

        log.info("syncListingGoodsSort list = {}", JSON.toJSONString(list));

        // 7日销量
//        List<OutDbGoodsEveryDayDTO> outDbGoodsEveryDay = outDbGoodsEveryDayEsService.getGoodsSales(goodsIdList, 7, OutDbGoodsEveryDayDTO.class);
//        int listingSevenDaySale = outDbGoodsEveryDay.stream().filter(e -> Objects.nonNull(e.getDealCnt())).mapToInt(OutDbGoodsEveryDayDTO::getDealCnt).sum();
//        listingInfo.setSevenSale(listingSevenDaySale);
//        listingInfoService.updateById(listingInfo);
//        log.info("syncListingGoodsSort listingInfo ={}, listingSevenDaySale ={}", JSON.toJSONString(listingInfo), listingSevenDaySale);
    }

    @Override
    public void refreshNegativeListingGoodsVat() {
        LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) start");
        List<ListingInfo> listingInfoList = Lists.newArrayList();
        long pageNow = 1;
        long pageEnd = 100;

        while (pageNow <= pageEnd) {
            IPage<ListingInfo> page = listingInfoService.lambdaQuery()
                    .eq(ListingInfo::getIsDel, 0)
                    .isNotNull(ListingInfo::getBasePrice)
                    .orderByAsc(ListingInfo::getCreateTime)
                    .select(ListingInfo::getId, ListingInfo::getGoodsId, ListingInfo::getBasePrice)
                    .page(new Page<>(pageNow, 100));
            List<ListingInfo> records = page.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            listingInfoList.addAll(records);

            pageEnd = page.getPages();
            pageNow++;
        }
        LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) listing模板数量:{}", listingInfoList.size());

        if (CollectionUtils.isEmpty(listingInfoList)) {
            LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 模板数量为空，返回1");
            return;
        }

        List<Long> negativeGoodsIds = Lists.newArrayList();
        List<Long> ListingTemplateGoodsIds = listingInfoList.stream().map(ListingInfo::getGoodsId).filter(Objects::nonNull).collect(Collectors.toList());
        for (List<Long> batchGoodsIds : Lists.partition(ListingTemplateGoodsIds, 100)) {
            goodsExtConfigService.lambdaQuery()
                    .in(GoodsExtConfig::getGoodsId, batchGoodsIds)
                    .in(GoodsExtConfig::getTagId, 40, 50)
                    .eq(GoodsExtConfig::getIsDel, 0)
                    .select(GoodsExtConfig::getGoodsId)
                    .list()
                    .stream()
                    .map(GoodsExtConfig::getGoodsId)
                    .forEach(negativeGoodsIds::add);
        }
        LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 负向模板数量:{}", negativeGoodsIds.size());

        if (CollectionUtils.isEmpty(negativeGoodsIds)) {
            LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 负向商品为空，返回2");
            return;
        }

        listingInfoList = listingInfoList.stream()
                .filter(listingInfo -> negativeGoodsIds.contains(listingInfo.getGoodsId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listingInfoList)) {
            LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 负向模板数量为空，返回3");
            return;
        }

        int batch = 1;
        for (List<ListingInfo> batchListingInfoList : Lists.partition(listingInfoList, 100)) {
            LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) batch:{} start", batch);
            List<Long> listingIds = batchListingInfoList.stream().map(ListingInfo::getId).collect(Collectors.toList());
            Map<Long, List<ListingFollowGoods>> listingFollowGoodsGroupMap = listingFollowGoodsService.lambdaQuery()
                    .in(ListingFollowGoods::getListingId, listingIds)
                    .eq(ListingFollowGoods::getIsDel, 0)
                    .list()
                    .stream().collect(Collectors.groupingBy(ListingFollowGoods::getListingId));
            Map<String, VatDto> vatMap = goodsEsService.getVatMap();

            for (ListingInfo listingInfo : batchListingInfoList) {
                List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsGroupMap.get(listingInfo.getId());
                if (CollectionUtils.isEmpty(listingFollowGoodsList)) {
                    LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 模板无跟卖商品，跳过 listingId:{}", listingInfo.getId());
                    continue;
                }

                if (listingInfo.getBasePrice() == null) {
                    LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 基准价为空，跳过 listingId:{}", listingInfo.getId());
                    continue;
                }

                List<Long> followGoodsIds = listingFollowGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
                Map<Long, Goods> goodsMap = goodsService.queryGoodsByIds(followGoodsIds).stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));
                Map<Long, GoodsVatConfig> goodsVatConfigMap = goodsVatConfigService.lambdaQuery()
                        .in(GoodsVatConfig::getGoodsId, followGoodsIds)
                        .list()
                        .stream()
                        .collect(Collectors.toMap(GoodsVatConfig::getGoodsId, Function.identity(), (v1, v2) -> v1));

                List<Long> categoryIds = goodsMap.values().stream().map(Goods::getCategoryId).collect(Collectors.toList());
                Map<Long, String> categoryNameMap = categoryService.queryCategoryByIds(categoryIds).stream().collect(Collectors.toMap(Category::getId, Category::getName, (v1, v2) -> v1));

                List<Long> shopIds = goodsMap.values().stream().map(Goods::getShopId).collect(Collectors.toList());
                Map<Long, Integer> shopDeliveryTypeMap = faMerchantsApplyCoreService.queryByShopIds(shopIds).stream().collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getDeliveryType, (v1, v2) -> v2));
                Map<Long, List<Long>> shopTagGroupMap = faMerchantsTagService.lambdaQuery().in(FaMerchantsTag::getShopId, shopIds).eq(FaMerchantsTag::getStatus, 1).list()
                        .stream().collect(Collectors.groupingBy(FaMerchantsTag::getShopId, Collectors.mapping(FaMerchantsTag::getTagId, Collectors.toList())));

                Map<Long, List<Long>> goodsTagGroupMap = goodsExtConfigService.lambdaQuery().in(GoodsExtConfig::getGoodsId, followGoodsIds).eq(GoodsExtConfig::getIsDel, 0).list()
                        .stream().collect(Collectors.groupingBy(GoodsExtConfig::getGoodsId, Collectors.mapping(GoodsExtConfig::getTagId, Collectors.toList())));


                List<GoodsVatConfig> saveList = Lists.newArrayList();
                for (ListingFollowGoods listingFollowGoods : listingFollowGoodsList) {
                    Goods goods = goodsMap.get(listingFollowGoods.getGoodsId());
                    if (goods == null) {
                        LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 商品数量为空，跳过 goodsId:{}, listingId:{}", listingFollowGoods.getGoodsId(), listingInfo.getId());
                        continue;
                    }

                    GoodsVatConfig goodsVatConfig = goodsVatConfigMap.get(listingFollowGoods.getGoodsId());
                    // price >= 基准价，走平台默认
                    if (goods.getMaxPrice().compareTo(listingInfo.getBasePrice()) >= 0) {
                        LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 价格>=基准价，走平台默认 goodsId:{}, listingId:{}", listingFollowGoods.getGoodsId(), listingInfo.getId());
                        if (goodsVatConfig != null && goodsVatConfig.getEffectStatus() == 1) {
                            goodsVatConfig.setEffectStatus(0);
                            goodsVatConfig.setUpdateTime(LocalDateTime.now());
                            saveList.add(goodsVatConfig);
                        }
                        continue;
                    }

                    // price < 基准价 ===> 新vat = (基准价/price - 1) * 0.9(系数)
                    BigDecimal vat = listingInfo.getBasePrice()
                            .divide(goods.getMaxPrice(), 4, RoundingMode.HALF_UP)
                            .subtract(BigDecimal.ONE)
                            .multiply(new BigDecimal("0.9"))
                            .multiply(new BigDecimal("100"))
                            .setScale(2, RoundingMode.HALF_UP);
                    LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价)  goodsId:{}, 计算出vat:{}", listingFollowGoods.getGoodsId(), vat);

                    // 新vat <= 0.15+0.05(负向标追加) ===> 走默认
                    GoodsExtConfigModel goodsExtConfigModel = new GoodsExtConfigModel();
                    goodsExtConfigModel.setTagIds(goodsTagGroupMap.get(goods.getId()));
                    goodsExtConfigModel.setShopTagIds(shopTagGroupMap.get(goods.getShopId()));

                    BigDecimal originalVat = goodsEsService.getVatRate(
                            new VatCondition()
                                    .setGoodsId(goods.getId())
                                    .setCountry("DE")
                                    .setCategoryId(goods.getCategoryId())
                                    .setGoodsExtConfigModel(goodsExtConfigModel)
                                    .setShopId(goods.getShopId())
                                    .setDeliveryType(shopDeliveryTypeMap.get(goods.getShopId()))
                                    .setVatMap(vatMap)
                    );
                    LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) goodsId:{}, 计算新vat:{}, 原自身vat:{}", listingFollowGoods.getGoodsId(), vat, originalVat);

                    if (vat.compareTo(originalVat) <= 0) {
                        LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 新vat<=原自身vat，走自身 goodsId:{}, listingId:{}", listingFollowGoods.getGoodsId(), listingInfo.getId());
//                        if (goodsVatConfig != null && goodsVatConfig.getEffectStatus() == 1) {
//                            goodsVatConfig.setEffectStatus(0);
//                            goodsVatConfig.setUpdateTime(LocalDateTime.now());
//                            saveList.add(goodsVatConfig);
//                        }
                        continue;
                    }

                    if (goodsVatConfig == null) {
                        goodsVatConfig = new GoodsVatConfig();
                        goodsVatConfig.setGoodsId(listingFollowGoods.getGoodsId());
                        goodsVatConfig.setCreateTime(LocalDateTime.now());
                        goodsVatConfig.setChangeStatus(0);
                        goodsVatConfig.setCategoryId(goods.getCategoryId());
                        goodsVatConfig.setCategoryName(categoryNameMap.get(goods.getCategoryId()));
                        goodsVatConfig.setShopId(goods.getShopId());
                        goodsVatConfig.setShopName(goods.getShopName());
                        goodsVatConfig.setYesterdayPayCount(0L);
                        goodsVatConfig.setCurrentDayPayCount(0L);
                        goodsVatConfig.setMinPrice(goods.getMinPrice());
                        goodsVatConfig.setMaxPrice(goods.getMaxPrice());
                        goodsVatConfig.setMainImage(goods.getMainImage());

                    }
                    goodsVatConfig.setVat(vat);
                    goodsVatConfig.setEffectStatus(1);
                    goodsVatConfig.setUpdateTime(LocalDateTime.now());
                    saveList.add(goodsVatConfig);
                }

                LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) 更新vat配置数量:{}, listingId:{}", saveList.size(), listingInfo.getId());
                if (CollectionUtils.isNotEmpty(saveList)) {
                    goodsVatConfigService.saveOrUpdateBatch(saveList);
                }
            }
            LogUtils.info(log, "定时刷新负向listing商品的vat费率(动态调价) batch:{} end", batch);
            batch++;
        }
    }


    @Override
    public void refreshListingFollowCountByFollowGoodsIds(List<Long> followGoodsIds) {
        LogUtils.info(log, "根据跟卖商品Id刷新listing跟卖数量快照 start ===> followGoodsIds:{}", followGoodsIds);
        if (CollectionUtils.isEmpty(followGoodsIds)) {
            return;
        }
        List<Long> listingIds = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getGoodsId, followGoodsIds)
                .list()
                .stream().map(ListingFollowGoods::getListingId).collect(Collectors.toList());

        refreshListingFollowCountByIds(listingIds);
    }

    @Override
    public void refreshListingFollowCountByIds(List<Long> listingIds) {
        LogUtils.info(log, "根据listingId刷新listing跟卖数量快照 start ===> listingIds:{}",listingIds);
        if (CollectionUtils.isEmpty(listingIds)) {
            return;
        }

        List<ListingInfo> listingInfoList = Lists.newArrayList(listingInfoService.listByIds(listingIds));
        if (CollectionUtils.isEmpty(listingInfoList)) {
            return;
        }

        Map<Long, List<ListingFollowGoods>> listingFollowGoodsMap = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getListingId, listingIds)
                .eq(ListingFollowGoods::getIsDel, 0)
                .select(ListingFollowGoods::getListingId, ListingFollowGoods::getGoodsId)
                .list()
                .stream().collect(Collectors.groupingBy(ListingFollowGoods::getListingId));

        for (ListingInfo listingInfo : listingInfoList) {
            listingInfo.setFollowCount(CollectionUtils.size(listingFollowGoodsMap.get(listingInfo.getId())));
        }
        listingInfoService.updateBatchById(listingInfoList);
    }

    @Override
    public void refreshAllListingFollowCount() {
        LogUtils.info(log, "刷新所有listing跟卖数量快照 start");
        List<ListingInfo> listingInfoList = Lists.newArrayList();
        long pageNow = 1;
        long pageEnd = 100;

        while (pageNow <= pageEnd) {
            IPage<ListingInfo> page = listingInfoService.lambdaQuery()
                    .eq(ListingInfo::getIsDel, 0)
                    .orderByAsc(ListingInfo::getCreateTime)
                    .select(ListingInfo::getId)
                    .page(new Page<>(pageNow, 100));
            List<ListingInfo> records = page.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            listingInfoList.addAll(records);

            pageEnd = page.getPages();
            pageNow++;
        }
        LogUtils.info(log, "刷新所有listing跟卖数量快照 listing模板数量:{}", listingInfoList.size());

        if (CollectionUtils.isEmpty(listingInfoList)) {
            LogUtils.info(log, "刷新所有listing跟卖数量快照 模板数量为空，返回1");
            return;
        }

        for (List<ListingInfo> list : Lists.partition(listingInfoList, 100)) {
            refreshListingFollowCountByIds(list.stream().map(ListingInfo::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public void refreshListingGoodsName() {
        Set<String> sensitiveWordsSet;
        Object o = redisApi.get(RedisKeyConstants.SENSITIVE_WORDS_SET_KEY);
        if (Objects.nonNull(o)) {
            sensitiveWordsSet = (Set<String>) o;
        } else {
            // 获取敏感词对象集合
            SensitiveWords ex = new SensitiveWords();
            ex.setStatus(1);
            List<SensitiveWords> sensitiveWords = sensitiveWordsService.selectList(ex);
            sensitiveWordsSet = sensitiveWords.stream().map(sensitiveWord -> sensitiveWord.getWord().trim().toLowerCase()).collect(Collectors.toSet());
            redisApi.set(RedisKeyConstants.SENSITIVE_WORDS_SET_KEY, sensitiveWordsSet, 60 * 60);
        }
        List<ListingInfo> listingInfoList = listingInfoService.list();
        if(CollectionUtils.isEmpty(listingInfoList)){
            return;
        }
        for(ListingInfo listingInfo : listingInfoList){
            Goods goods = goodsService.getById(listingInfo.getGoodsId());
            if(Objects.isNull(goods)){
                continue;
            }
            String listingGoodsOriginName = goods.getName();
            List<String> goodsNameWordsList = Lists.newArrayList(listingGoodsOriginName.toLowerCase().split(" "));
            goodsNameWordsList.retainAll(sensitiveWordsSet);
            if(goodsNameWordsList.size() > 0){
                log.info("refreshListingGoodsName listingInfo hit sensitive words,listingId:{},sensitiveWords:{}",listingInfo.getId(), JSON.toJSONString(goodsNameWordsList));
                String newName = Arrays.stream(listingGoodsOriginName.split(" ")).filter(item -> !sensitiveWordsSet.contains(item.toLowerCase())).collect(Collectors.joining(" "));
                listingInfo.setName(newName);
                listingInfo.setUpdateUser("system");
                listingInfo.setUpdateTime(LocalDateTime.now());
                listingInfoService.updateById(listingInfo);
                List<Long> goodsIdList = listingFollowGoodsService.lambdaQuery().eq(ListingFollowGoods::getListingId, listingInfo.getId()).list()
                        .stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(goodsIdList)){
                    continue;
                }
                goodsIdList.stream().forEach(goodsId->{
                    Goods goodsInfo = new Goods();
                    goodsInfo.setId(goodsId);
                    goodsInfo.setName(newName);
                    goodsInfo.setUpdateTime(LocalDateTime.now());
                    log.info("refreshListingGoodsName goodsInfo goodsId:{}",goodsId);
                    goodsService.updateById(goodsInfo);
                    GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                    goodsSyncModel.setGoodsId(goodsInfo.getId());
                    goodsSyncModel.setSyncTime(System.currentTimeMillis());
                    goodsSyncModel.setBusiness("定时刷新listing商品出现的违禁词");
                    goodsSyncModel.setSourceService("vp");
                    mqSender.sendDelay("SYNC_GOODS_TOPIC_BATCH", goodsSyncModel, MqDelayLevel.TEN_SEC);
                    GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                            .goodsId(goodsInfo.getId())
                            .type(OperationLogTypeEnums.UPDATE_GOODS_DETAIL)
                            .content(OperationLogTypeEnums.UPDATE_GOODS_DETAIL.getDesc() + ":命中敏感词,修改名称")
                            .status(1)
                            .oldData(listingGoodsOriginName)
                            .newData(newName)
                            .user("system-job");
                    mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
                });
            }
        }
    }

    @Override
    public Result<ListingReplaceGoodsDTO> queryOrderReplaceGoods(ReplaceGoodsInfoDTO replaceGoodsInfoDTO) {
        if (replaceGoodsInfoDTO.getStock() == null) {
            replaceGoodsInfoDTO.setStock(1L);
        }
        ListingReplaceGoodsDTO listingReplaceGoodsDTO = new ListingReplaceGoodsDTO();
        listingReplaceGoodsDTO.setOrgGoodsId(replaceGoodsInfoDTO.getGoodsId());
        listingReplaceGoodsDTO.setOrgSkuId(replaceGoodsInfoDTO.getSkuId());
        List<ReplaceGoodsInfoDTO> replaceGoodsInfoDTOList = new ArrayList<>();

        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery()
                .select(ListingFollowGoods::getListingId, ListingFollowGoods::getShopId)
                .eq(ListingFollowGoods::getGoodsId, replaceGoodsInfoDTO.getGoodsId())
                .eq(ListingFollowGoods::getIsDel, 0)
                .one();
        //判断是不是listing 商品
        if (listingFollowGoods == null) {
            return Result.success(listingReplaceGoodsDTO);
        }
        listingReplaceGoodsDTO.setOrgListingId(listingFollowGoods.getListingId());

        ListingInfo listingInfo = listingInfoService.getById(listingFollowGoods.getListingId());
        //判断listing是否允许转单 且转单几率大于0
        if (listingInfo == null || listingInfo.getIsReplace() == 0 || Float.compare(listingInfo.getReplaceRate(), 0F) <= 0) {
            return Result.success(listingReplaceGoodsDTO);
        }
        //判断店铺类型如果为自营不转单
        FaMerchantsApply faMerchantsApply = faMerchantsApplyCoreService.queryById(listingFollowGoods.getShopId());
        if (faMerchantsApply.getDeliveryType() != 0) {
            return Result.success(listingReplaceGoodsDTO);
        }

        List<Long> collect = listingFollowGoodsService.lambdaQuery()
                .select(ListingFollowGoods::getGoodsId)
                .eq(ListingFollowGoods::getListingId, listingFollowGoods.getListingId())
                .eq(ListingFollowGoods::getIsDel, 0).list()
                .stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return Result.success(listingReplaceGoodsDTO);
        }

        List<Goods> goods = getGoodsList(replaceGoodsInfoDTO, collect, faMerchantsApply);
        if (CollectionUtils.isEmpty(goods)) {
            return Result.success(listingReplaceGoodsDTO);
        }

        List<GoodsExtDetail> goodsExtDetails = goodsExtDetailService.queryGoodsExtDetailByGoodsIds(collect);
        if (CollectionUtils.isEmpty(goodsExtDetails)) {
            return Result.success(listingReplaceGoodsDTO);
        }

        Map<Long, Goods> goodsMap = goods.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> a));

        Map<Long, GoodsExtDetail> goodsExtDetailMap = goodsExtDetails.stream().collect(Collectors.toMap(GoodsExtDetail::getGoodsId, Function.identity(), (a, b) -> a));
        //查询商品标签，如果标签中包含配置的不可转单 标签则不抓单
        List<GoodsExtConfig> extConfigList = goodsExtConfigService.lambdaQuery().select(GoodsExtConfig::getGoodsId, GoodsExtConfig::getTagId)
                .in(GoodsExtConfig::getGoodsId, goodsMap.keySet())
                .eq(GoodsExtConfig::getIsDel, 0)
                .list();
        Map<Long, List<Long>> goodsTagMap = extConfigList.stream().collect(
                Collectors.groupingBy(
                        GoodsExtConfig::getGoodsId,
                        Collectors.mapping(GoodsExtConfig::getTagId, Collectors.toList())));
        if (CollectionUtils.isNotEmpty(goodsTagMap.get(replaceGoodsInfoDTO.getGoodsId()))) {
            //防止影响原tagList
            ArrayList<Long> tagIds = new ArrayList<>(goodsTagMap.get(replaceGoodsInfoDTO.getGoodsId()));
            tagIds.retainAll(Arrays.asList(
                    Arrays.stream(excludeTagIds.split(","))
                            .map(String::trim)
                            .map(Long::parseLong)
                            .toArray(Long[]::new)));
            //存在不可转换标签商品直接返回
            if (CollectionUtils.isNotEmpty(tagIds)) {
                return Result.success(listingReplaceGoodsDTO);
            }
        }

        //比对商品价格 先获取所有sku价格
        List<GoodsItem> itemsList = goodsItemService.lambdaQuery().in(GoodsItem::getGoodsId, goodsMap.keySet()).list();
        Map<Long, BigDecimal> goodsFreightMap = goodsFreightService.queryByGoodsIdList(
                        Lists.newArrayList(goodsMap.keySet()), Lists.newArrayList(replaceGoodsInfoDTO.getCountry()))
                .stream().collect(Collectors.toMap(GoodsFreight::getGoodsId, GoodsFreight::getCurrentFreight, (a, b) -> a));
        Optional<GoodsItem> first = itemsList.stream().filter(goodsItem -> Long.compare(replaceGoodsInfoDTO.getSkuId(), goodsItem.getSkuId()) == 0).findFirst();
        if (!first.isPresent()) {
            return Result.success(listingReplaceGoodsDTO);
        }
        //取出需要比对的sku
        String propertyValueRecordSnap = first.get().getPropertyValueRecordSnap();

        if (propertyValueRecordSnap == null){
            return Result.success(listingReplaceGoodsDTO);
        }
        //取出需要比对的sku
        String tmp = propertyValueRecordSnap.substring(propertyValueRecordSnap.indexOf("-")+1);
        if (StringUtils.isEmpty(tmp)) {
            return Result.success(listingReplaceGoodsDTO);
        }
        Map<Long, GoodsItem> goodsItemMap = itemsList.stream().filter(a -> a.getPropertyValueRecordSnap()!= null && tmp.equals(a.getPropertyValueRecordSnap().substring(a.getPropertyValueRecordSnap().indexOf("-")+1))).collect(Collectors.toMap(GoodsItem::getGoodsId, Function.identity(), (a, b) -> a));
        if (MapUtils.isEmpty(goodsItemMap)) {
            return Result.success(listingReplaceGoodsDTO);
        }


        for (Long goodsId : goodsItemMap.keySet()) {
            //原商品过滤掉
            if (Long.compare(replaceGoodsInfoDTO.getGoodsId(), goodsId) == 0) {
                continue;
            }

            ReplaceGoodsInfoDTO replaceGoods = new ReplaceGoodsInfoDTO();
            GoodsItem goodsItem = goodsItemMap.get(goodsId);
            //sku无库存 或下架过滤掉
            if (goodsItem.getSkuStatus() == null || goodsItem.getSkuStatus() == 0L || goodsItem.getStock() < replaceGoodsInfoDTO.getStock()) {
                continue;
            }
            BigDecimal goodsFreight = goodsFreightMap.get(goodsId);
            if (goodsFreight == null){
                goodsFreight = BigDecimal.ZERO;
            }

            BigDecimal vatBeforePrice = goodsItem.getOrginalPrice().add(goodsFreight);
            if (vatBeforePrice.compareTo(replaceGoodsInfoDTO.getVatBeforePrice()) >= 0) {
                continue;
            }
            GoodsExtDetail goodsExtDetail = goodsExtDetailMap.get(goodsId);
            Goods goodsInfo = goodsMap.get(goodsId);
            replaceGoods.setVatBeforePrice(vatBeforePrice);
            replaceGoods.setSkuId(goodsItem.getSkuId());
            replaceGoods.setGoodsId(goodsId);
            replaceGoods.setShopId(goodsInfo.getShopId());
            replaceGoods.setTagList(goodsTagMap.get(goodsId));
            replaceGoods.setShopSkuId(goodsItem.getOriginalSkuId());
            replaceGoods.setItemCode(goodsExtDetail.getItemCode());
            replaceGoodsInfoDTOList.add(replaceGoods);
        }
        listingReplaceGoodsDTO.setReplaceRate(listingInfo.getReplaceRate());
        List<ReplaceGoodsInfoDTO> collect1 = replaceGoodsInfoDTOList.stream().sorted(Comparator.comparing(r -> r.getVatBeforePrice())).limit(2).collect(Collectors.toList());
        listingReplaceGoodsDTO.setReplaceGoodsInfoDTOList(collect1);
        return Result.success(listingReplaceGoodsDTO);
    }

    @NotNull
    private List<Goods> getGoodsList(ReplaceGoodsInfoDTO replaceGoodsInfoDTO, List<Long> collect, FaMerchantsApply faMerchantsApply) {
        List<Goods> goods = goodsService.queryGoodsByIds(collect, Boolean.TRUE);
        goods = goods.stream().filter(goodsInfo -> goodsInfo.getCountry().contains(replaceGoodsInfoDTO.getCountry())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goods)) {
            return Collections.emptyList();
        }

        List<Long> eligibleShopIds = getEligibleShopIds(faMerchantsApply, goods);
        if (CollectionUtils.isEmpty(eligibleShopIds)) {
            return Collections.emptyList();
        }

        // 判断是否有符合发货方式的商品
        return goods.stream().filter(goodsInfo -> eligibleShopIds.contains(goodsInfo.getShopId())).collect(Collectors.toList());
    }

    @NotNull
    private List<Long> getEligibleShopIds(FaMerchantsApply faMerchantsApply, List<Goods> goods) {
        List<Integer> deliveryTypeList = Lists.newArrayList(DeliveryTypeEnum.AGENT.getCode(), DeliveryTypeEnum.SELF_SUPPORT.getCode(), DeliveryTypeEnum.SELF_SUPPORT_SCM.getCode());
        List<Long> shopIds = goods.stream().map(Goods::getShopId).distinct().collect(Collectors.toList());
        List<FaMerchantsApply> faMerchantsApplies = faMerchantsApplyCoreService.queryByShopIds(shopIds);
        return faMerchantsApplies.stream().filter(fa -> deliveryTypeList.contains(fa.getDeliveryType())).map(FaMerchantsApply::getId).distinct().collect(Collectors.toList());
    }

    @Override
    public void modifyListingConfig(ListingInfoConfigAddVO vo) {
        LogUtils.info(log, "新增listingInfo配置 vo:{}", vo);
        CheckUtils.check(vo.getListingId() == null, ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(vo.getListingType() == null || vo.getIsReplace() == null,
                ProductResultCode.PARAMETER_ERROR);
        if (vo.getListingType() == 1) {
            CheckUtils.check(CollectionUtils.isEmpty(vo.getTagIds()), ProductResultCode.LISTING_CONFIG_UPDATE_ERROR);
            CheckUtils.check(!vo.getTagIds().contains(146L) && !vo.getTagIds().contains(507L) && !vo.getTagIds().contains(514L),
                    ProductResultCode.LISTING_CONFIG_UPDATE_ERROR);
        }

        ListingInfo listingInfo = listingInfoService.getById(vo.getListingId());
        CheckUtils.check(listingInfo == null, ProductResultCode.LISTING_NOT_EXIST);

        Integer shopTagSum;
        if (CollectionUtils.isNotEmpty(vo.getTagIds())) {
            List<String> tagIdsString = vo.getTagIds().stream().filter(Objects::nonNull).distinct()
                    .map(String::valueOf).collect(Collectors.toList());
            List<Integer> shopTagEnumValues = searchTypeMatchBinaryConfigService.getEnumValuesByEnumNames(tagIdsString, 1);
            shopTagSum = MathUtils.sum(shopTagEnumValues);
        } else {
            shopTagSum = 0;
        }

        Integer deliveryTypeSum;
        if (CollectionUtils.isNotEmpty(vo.getDeliveryTypes())) {
            List<String> deliveryTypesString = vo.getDeliveryTypes().stream().filter(Objects::nonNull).distinct()
                    .map(String::valueOf).collect(Collectors.toList());
            List<Integer> deliveryTypeEnumValues = searchTypeMatchBinaryConfigService.getEnumValuesByEnumNames(deliveryTypesString, 2);
            deliveryTypeSum = MathUtils.sum(deliveryTypeEnumValues);
        } else {
            deliveryTypeSum = 0;
        }

        Integer type = null;
        if (listingInfo.getListingType() == 1 && vo.getListingType() == 0) {
            type = 0;
        } else if (listingInfo.getListingType() == 0 && vo.getListingType() == 1) {
            type = 1;
        }

        listingInfo.setShopTag(shopTagSum);
        listingInfo.setDeliveryType(deliveryTypeSum);
        listingInfo.setListingType(vo.getListingType());
        listingInfo.setReplaceRate(vo.getReplaceRate());
        listingInfo.setIsReplace(vo.getIsReplace());
        listingInfo.setUpdateTime(LocalDateTime.now());
        listingInfo.setUpdateUser(getUserName());

        searchTypeMatchBinaryConfigService.insert(Collections.singletonList(shopTagSum), 1);
        searchTypeMatchBinaryConfigService.insert(Collections.singletonList(deliveryTypeSum), 2);
        listingInfoService.updateById(listingInfo);
        if (type != null) {
            refreshGoodsTag(listingInfo.getId(), listingInfo.getGoodsId(), type);
        }
    }

    @Override
    public void batchModifyListingConfig(ListingInfoConfigBatchAddVO vo) {
        LogUtils.info(log, "批量新增listingInfo配置 vo:{}", vo);
        CheckUtils.check(StringUtils.isBlank(vo.getListingIdStr()) || vo.getOperationType() == null,
                ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(vo.getOperationType() != 0 && vo.getOperationType() != 1,
                ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(CollectionUtils.isEmpty(vo.getTagIds()) && CollectionUtils.isEmpty(vo.getDeliveryTypes()),
                ProductResultCode.PARAMETER_ERROR);

        List<Long> listingIds = Arrays.stream(vo.getListingIdStr().trim().split("\n")).filter(Objects::nonNull)
                .map(s -> Long.parseLong(s.trim()))
                .distinct().collect(Collectors.toList());

        CheckUtils.check(CollectionUtils.isEmpty(listingIds), ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(listingIds.size() > 500, ProductResultCode.LISTING_RELATION_NUM_EXCEED_MAX_ERROR);

        Map<Long, ListingInfo> listingInfoMap = listingInfoService.lambdaQuery()
                .in(ListingInfo::getId, listingIds)
                .eq(ListingInfo::getIsDel, 0)
                .list()
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ListingInfo::getId, Function.identity(), (v1, v2) -> v1));

        CheckUtils.check(MapUtils.isEmpty(listingInfoMap), ProductResultCode.LISTING_NOT_EXIST);

        List<Integer> shopTags;
        boolean hasShopTag;
        if (CollectionUtils.isNotEmpty(vo.getTagIds())) {
            List<String> tagIdsString = vo.getTagIds().stream().filter(Objects::nonNull).distinct()
                    .map(String::valueOf).collect(Collectors.toList());
            shopTags = searchTypeMatchBinaryConfigService.getEnumValuesByEnumNames(tagIdsString, 1);
            hasShopTag = true;
        } else {
            shopTags = Collections.emptyList();
            hasShopTag = false;
        }

        List<Integer> deliveryTypes;
        boolean hasDeliveryType;
        if (CollectionUtils.isNotEmpty(vo.getDeliveryTypes())) {
             List<String> deliveryTypesString = vo.getDeliveryTypes().stream().filter(Objects::nonNull).distinct()
                     .map(String::valueOf).collect(Collectors.toList());
            deliveryTypes = searchTypeMatchBinaryConfigService.getEnumValuesByEnumNames(deliveryTypesString, 2);
            hasDeliveryType = true;
        } else {
            deliveryTypes = Collections.emptyList();
            hasDeliveryType = false;
        }

        List<Long> failListingId = new ArrayList<>();

        for (Long listingId : listingIds) {
            ListingInfo listingInfo = listingInfoMap.get(listingId);
            if (listingInfo == null) {
                continue;
            }
            if (hasShopTag) {
                Integer shopTag = listingInfo.getShopTag();
                List<Integer> tags = MathUtils.splitBinary(shopTag);
                if (vo.getOperationType() == 0) {
                    List<Integer> merge = Stream.concat(tags.stream(), shopTags.stream())
                            .distinct().collect(Collectors.toList());
                    listingInfo.setShopTag(MathUtils.sum(merge));
                } else {
                    tags.removeAll(shopTags);
                    // 如果listing type为负向，但标签不包含146和507 和 514
                    if (!tags.contains(1) && !tags.contains(2) && !tags.contains(4) && listingInfo.getListingType() == 1) {
                        failListingId.add(listingId);
                        continue;
                    }
                    listingInfo.setShopTag(MathUtils.sum(tags));
                }
            }
            if (hasDeliveryType) {
                Integer deliveryType = listingInfo.getDeliveryType();
                List<Integer> types = MathUtils.splitBinary(deliveryType);
                if (vo.getOperationType() == 0) {
                    List<Integer> merge = Stream.concat(types.stream(), deliveryTypes.stream())
                            .distinct().collect(Collectors.toList());
                    listingInfo.setDeliveryType(MathUtils.sum(merge));
                } else {
                    types.removeAll(deliveryTypes);
                    listingInfo.setDeliveryType(MathUtils.sum(types));
                }
            }
            listingInfo.setUpdateTime(LocalDateTime.now());
            listingInfo.setUpdateUser(getUserName());
        }

        if (CollectionUtils.isNotEmpty(failListingId)) {
            for (Long listingId : failListingId) {
                listingInfoMap.remove(listingId);
            }
        }

        if (MapUtils.isNotEmpty(listingInfoMap)) {
            Collection<ListingInfo> listingInfos = listingInfoMap.values();
            List<Integer> shopTagValues = listingInfos.stream().map(ListingInfo::getShopTag).collect(Collectors.toList());
            List<Integer> deliveryTypeValues = listingInfos.stream().map(ListingInfo::getDeliveryType).collect(Collectors.toList());
            searchTypeMatchBinaryConfigService.insert(shopTagValues, 1);
            searchTypeMatchBinaryConfigService.insert(deliveryTypeValues, 2);
            listingInfoService.updateBatchById(listingInfos);
        }

        CheckUtils.check(CollectionUtils.isNotEmpty(failListingId), CustomResultCode.fill(ProductResultCode.LISTING_CONFIG_BATCH_UPDATE_ERROR,
                StringUtils.join(failListingId, ",")));
    }

    @Override
    public void refreshListingConfig() {
        // 刷新listing type
        List<ListingInfo> listingInfos = listingInfoService.lambdaQuery()
                .eq(ListingInfo::getIsDel, 0).list();
        List<Long> sourceGoodsIds = listingInfos.stream().map(ListingInfo::getSourceGoodsId).collect(Collectors.toList());

        Map<Long, List<GoodsExtConfig>> configMap = goodsExtConfigService.lambdaQuery()
                .in(GoodsExtConfig::getGoodsId, sourceGoodsIds)
                .in(GoodsExtConfig::getTagId, Arrays.asList(40, 45, 50))
                .eq(GoodsExtConfig::getIsDel, 0)
                .list().stream().collect(Collectors.groupingBy(GoodsExtConfig::getGoodsId));

        List<Long> ids = listingInfos.stream().filter(listingInfo -> {
            List<GoodsExtConfig> goodsExtConfigs = configMap.get(listingInfo.getSourceGoodsId());
            return CollectionUtils.isNotEmpty(goodsExtConfigs);
        }).map(ListingInfo::getId).collect(Collectors.toList());
        listingInfoService.lambdaUpdate().set(ListingInfo::getListingType, 1)
                // 有负向标签，type改为1，标签改为7(146、507、514)
                .set(ListingInfo::getShopTag, 7)
                .in(ListingInfo::getId, ids).update();

        // 刷新shop white list type
//        List<Long> listingIds = listingShopWhiteListService.lambdaQuery()
//                .eq(ListingShopWhiteList::getIsDel, 0)
//                .select(ListingShopWhiteList::getListingId)
//                .list().stream().map(ListingShopWhiteList::getListingId).distinct().collect(Collectors.toList());
//        listingInfoService.lambdaUpdate().set(ListingInfo::getShopWhiteListType, 1)
//                .in(ListingInfo::getId, listingIds).update();
    }

    @Override
    public void refreshListingType() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 刷新listing type
        List<ListingInfo> listingInfos = listingInfoService.lambdaQuery()
                .eq(ListingInfo::getIsDel, 0).list();

        List<Long> listingIds = listingInfos.stream().map(ListingInfo::getId).collect(Collectors.toList());

        List<Long> sourceGoodsIds = listingInfos.stream().map(ListingInfo::getSourceGoodsId).collect(Collectors.toList());

        Map<Long, List<GoodsExtConfig>> configMap = goodsExtConfigService.lambdaQuery()
                .in(GoodsExtConfig::getGoodsId, sourceGoodsIds)
                .in(GoodsExtConfig::getTagId, Arrays.asList(40, 45, 50))
                .eq(GoodsExtConfig::getIsDel, 0)
                .list().stream().collect(Collectors.groupingBy(GoodsExtConfig::getGoodsId));

        List<Long> ids = listingInfos.stream().filter(listingInfo -> {
            List<GoodsExtConfig> goodsExtConfigs = configMap.get(listingInfo.getSourceGoodsId());
            return CollectionUtils.isNotEmpty(goodsExtConfigs);
        }).map(ListingInfo::getId).collect(Collectors.toList());
        listingIds.removeAll(ids);

        LocalDateTime now = LocalDateTime.now();

        if (CollectionUtils.isNotEmpty(listingIds)) {
            // 没有负向标签，type改为0，标签改为0
            listingInfoService.lambdaUpdate().set(ListingInfo::getListingType, 0)
                    .set(ListingInfo::getShopTag, 0)
                    .set(ListingInfo::getUpdateTime, now)
                    .in(ListingInfo::getId, listingIds)
                    .update();
        }

        if (CollectionUtils.isNotEmpty(ids)) {
            // 有负向标签，type改为1，标签改为7(146、507、514)
            listingInfoService.lambdaUpdate().set(ListingInfo::getListingType, 1)
                    .set(ListingInfo::getShopTag, 7)
                    .set(ListingInfo::getUpdateTime, now)
                    .in(ListingInfo::getId, ids)
                    .update();
        }

        stopWatch.stop();
        log.info("refresh listing type:{} ms", stopWatch.getTime());
    }

    @Override
    public void syncGoodsAssessmentData(List<GoodsAssessmentVO> goodsAssessmentVOS) {
        Map<Long, GoodsAssessmentVO> goodsAssessmentVOMap = goodsAssessmentVOS.stream().filter(Objects::nonNull)
                .filter(goodsAssessmentVO -> goodsAssessmentVO.getGoodsId() != null)
                .collect(Collectors.toMap(GoodsAssessmentVO::getGoodsId, Function.identity(), (v1, v2) -> v1));
        Set<Long> goodsIds = goodsAssessmentVOMap.keySet();

        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery().in(ListingFollowGoods::getGoodsId, goodsIds)
                .eq(ListingFollowGoods::getIsDel, 0).list();
        List<ListingFollowGoods> res = listingFollowGoodsList.stream().map(listingFollowGoods -> {
            GoodsAssessmentVO goodsAssessmentVO = goodsAssessmentVOMap.get(listingFollowGoods.getGoodsId());
            if (goodsAssessmentVO != null) {
                listingFollowGoods.setRealNoSellRefundCnt(goodsAssessmentVO.getRealNoSellRefundCnt());
                listingFollowGoods.setRealWarehouseInCnt(goodsAssessmentVO.getRealWarehouseInCnt());
                listingFollowGoods.setNoSellRefundRate(goodsAssessmentVO.getNoSellRefundRate());
                listingFollowGoods.setQualityRefundCnt(goodsAssessmentVO.getQualityRefundCnt());
                listingFollowGoods.setReceiveTotalCnt(goodsAssessmentVO.getReceiveTotalCnt());
                listingFollowGoods.setQualityRefundRate(goodsAssessmentVO.getQualityRefundRate());
                listingFollowGoods.setUpdateTime(LocalDateTime.now());
                return listingFollowGoods;
            } else {
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(res)) {
            listingFollowGoodsService.saveOrUpdateBatch(res);
        }
    }

    @Override
    public void calListingCompositeScore(AssessmentGoodsVO vo) {
        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery().eq(ListingFollowGoods::getGoodsId, vo.getGoodsId())
                .eq(ListingFollowGoods::getIsDel, 0).one();
        if (listingFollowGoods == null) {
            return;
        }
        Long shopId = listingFollowGoods.getShopId();
        AssessmentShop assessmentShop = assessmentShopService.lambdaQuery().eq(AssessmentShop::getShopId, shopId).one();

        Collection<Goods> goods = goodsService.lambdaQuery().eq(Goods::getIsDel, 0)
                .eq(Goods::getIsShow, 1)
                .in(Goods::getId, Lists.newArrayList(vo.getGoodsId()))
                .list();
        List<GoodsSkuBo> goodsSkuList = getGoodsSkuList(goods);
        Map<Long, List<GoodsSkuBo>> goodsSkuMap = goodsSkuList.stream().collect(Collectors.groupingBy(GoodsSkuBo::getGoodsId));
        Map<Long, Pair<BigDecimal, BigDecimal>> maxPriceMap = goods.stream().filter(g -> g.getMaxPrice() != null)
                .collect(Collectors.toMap(Goods::getId, (g) -> {
                    List<GoodsSkuBo> goodsSkuBos = goodsSkuMap.get(g.getId());
                    BigDecimal originalPrice = goodsSkuBos.stream().max(Comparator.comparing(GoodsSkuBo::getOriginalPrice)).map(GoodsSkuBo::getOriginalPrice).orElse(goodsSkuBos.get(0).getOriginalPrice());
                    return new MutablePair<>(originalPrice, g.getMaxPrice());
                }));

        // 配置样品数量，默认为100
        final BigDecimal basicSampleNum = BigDecimal.valueOf(100);
        // 平台成交不卖率
        final BigDecimal platformNoSellRefundRate = new BigDecimal("0.025");
        // 平台质量退款率
        final BigDecimal platformQualityRefundRate = new BigDecimal("0.025");
        // 成交不卖惩罚门槛
        final BigDecimal noSellPunishThreshold = new BigDecimal("0.01");
        // 成交不卖惩罚
        final BigDecimal noSellPunishMul = BigDecimal.valueOf(2);
        // 质量退款惩罚门槛
        final BigDecimal qualityPunishThreshold = new BigDecimal("0.01");
        // 质量退款惩罚
        final BigDecimal qualityPunishMul = BigDecimal.valueOf(2);

        final BigDecimal goodsRealNoSellRefundCnt = BigDecimal.valueOf(vo.getRealNoSellRefundCnt());
        final BigDecimal goodsRealWarehouseInCnt = BigDecimal.valueOf(vo.getRealWarehouseInCnt());
        final BigDecimal goodsNoSellRefundRate = goodsRealNoSellRefundCnt.divide(goodsRealWarehouseInCnt, 4, RoundingMode.HALF_UP);
        final BigDecimal goodsQualityRefundCnt = BigDecimal.valueOf(vo.getQualityRefundCnt());
        final BigDecimal goodsReceiveTotalCnt = BigDecimal.valueOf(vo.getReceiveTotalCnt());
        final BigDecimal goodsQualityRefundRate = goodsQualityRefundCnt.divide(goodsReceiveTotalCnt, 4, RoundingMode.HALF_UP);

        final BigDecimal shopRealWareHouseInCnt = BigDecimal.valueOf(assessmentShop.getRealWarehouseInCnt());
        final BigDecimal shopReceiveTotalCnt = BigDecimal.valueOf(assessmentShop.getReceiveTotalCnt());
        final BigDecimal shopNoSellRefundRate = BigDecimal.valueOf(assessmentShop.getNoSellRefundRate());
        final BigDecimal shopQualityRefundRate = BigDecimal.valueOf(assessmentShop.getQualityRefundRate());

        // 计算调整后的不卖成交率
        BigDecimal adjustedNoSellRefundRate = calListingAdjustedRate(basicSampleNum, goodsRealWarehouseInCnt, shopRealWareHouseInCnt,
                goodsNoSellRefundRate, shopNoSellRefundRate, platformNoSellRefundRate);

        // 计算调整后的质量退款率
        BigDecimal adjustedQualityRefundRate = calListingAdjustedRate(basicSampleNum, goodsReceiveTotalCnt, shopReceiveTotalCnt,
                goodsQualityRefundRate, shopQualityRefundRate, platformQualityRefundRate);

        // 可售国家
        Map<Long, String> goodsCountryMap = goods.stream().filter(o -> StringUtils.isNotBlank(o.getCountry())).collect(Collectors.toMap(Goods::getId, Goods::getCountry));

        // 获取国家运费
        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(Lists.newArrayList(maxPriceMap.keySet()));
        Map<Long, Map<String, BigDecimal>> goodsFreightMap = goodsFreights.stream().collect(Collectors.groupingBy(GoodsFreight::getGoodsId, Collectors.mapping(Function.identity(), Collectors.toMap(GoodsFreight::getCode, GoodsFreight::getCurrentFreight))));

        // 获取商品可售国家
        List<String> sellableCountriesList = getSellableCountriesList(goodsCountryMap.get(listingFollowGoods.getGoodsId()));

        ListingGoodsSortBo goodsSort = ListingGoodsSortBo.builder().goodsId(listingFollowGoods.getGoodsId()).adjustedNoSellRefundRate(adjustedNoSellRefundRate).
                adjustedQualityRefundRate(adjustedQualityRefundRate).sellableCountriesList(sellableCountriesList).build();

        BigDecimal weightingCoefficient = getWeightingCoefficient(goodsSkuMap, listingFollowGoods);
        goodsSort.setWeightingCoefficient(weightingCoefficient);

        Pair<BigDecimal, BigDecimal> maxPricePair = maxPriceMap.get(vo.getGoodsId());

        // 计算综合分数
        BigDecimal compositeScore = calculateComprehensiveScore(maxPricePair.getRight(), goodsSort);
        fillCountryCompositeScoreMap(goodsFreightMap, vo.getGoodsId(), goodsSort, maxPricePair);

        log.info("【calListingCompositeScore】goodsSort:{}", JSONObject.toJSONString(goodsSort));

        listingFollowGoods.setRealNoSellRefundCnt(goodsRealNoSellRefundCnt.intValue());
        listingFollowGoods.setRealWarehouseInCnt(goodsRealWarehouseInCnt.intValue());
        listingFollowGoods.setNoSellRefundRate(goodsNoSellRefundRate.floatValue());
        listingFollowGoods.setQualityRefundCnt(goodsQualityRefundCnt.intValue());
        listingFollowGoods.setReceiveTotalCnt(goodsReceiveTotalCnt.intValue());
        listingFollowGoods.setQualityRefundRate(goodsQualityRefundRate.floatValue());
        listingFollowGoods.setCompositeScore(compositeScore.floatValue());
        listingFollowGoodsService.updateById(listingFollowGoods);
    }

    @Override
    public void refreshGoodsTag(Long goodsId, Integer type) {
        ListingInfo one = listingInfoService.lambdaQuery()
                .eq(ListingInfo::getGoodsId, goodsId)
                .eq(ListingInfo::getIsDel, 0)
                .one();
        if (one != null) {
            if (type == 0) {
                // type改为0，标签改为0
                one.setListingType(0);
                one.setShopTag(0);
            } else {
                // type改为1，标签改为3(146、507、514)
                one.setListingType(1);
                one.setShopTag(7);
            }
            one.setUpdateTime(LocalDateTime.now());
            listingInfoService.updateById(one);
            refreshGoodsTag(one.getId(), goodsId, type);
        }
    }

    @Override
    public void syncListingReplaceStatus(List<Long> listingIds) {
        listingInfoService.lambdaUpdate()
                .set(ListingInfo::getIsReplace, 1)
                .set(ListingInfo::getReplaceRate, 0.5f)
                .set(ListingInfo::getUpdateTime, LocalDateTime.now())
                .in(ListingInfo::getId, listingIds)
                .eq(ListingInfo::getIsReplace, 0)
                .update();
    }

    private void refreshGoodsTag(Long listingId, Long goodsId, Integer type) {
        List<Long> goodsIds = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getListingId, listingId)
                .list().stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        goodsIds.add(goodsId);
        goodsIds = goodsIds.stream().distinct().collect(Collectors.toList());

        // 负向改正向，删除40，45，50标
        if (type == 0) {
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setGoodsIds(goodsIds);
            bindTagDTO.setTagIdList(Arrays.asList(40L, 45L, 50L));
            goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
        } else { // 正向改负向，打上40标，删除99标
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setGoodsIds(goodsIds);
            bindTagDTO.setTagId(40L);
            goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
        }
    }

    private float calListingAdjustedRate(int basicSampleNum, int goodsRealSold, int shopRealSold,
                                         float goodsRate, float shopRate, float platformRate) {
        float goodsWeight;
        float shopWeight;
        float platformWeight;
        if (goodsRealSold >= basicSampleNum) {
            goodsWeight = 1f;
            shopWeight = 0f;
            platformWeight = 0f;
        } else if (goodsRealSold <= 0) {
            goodsWeight = 0f;
            if (shopRealSold >= basicSampleNum) {
                shopWeight = 1f;
                platformWeight = 0f;
            } else if (shopRealSold <= 0) {
                shopWeight = 0f;
                platformWeight = 1f;
            } else {
                shopWeight = BigDecimal.valueOf((float) shopRealSold / basicSampleNum)
                        .setScale(2, RoundingMode.HALF_UP).floatValue();
                platformWeight = 1f - shopWeight;
            }
        } else {
            goodsWeight = BigDecimal.valueOf((float) goodsRealSold / basicSampleNum)
                    .setScale(2, RoundingMode.HALF_UP).floatValue();
            int num = shopRealSold - goodsRealSold;
            if (num >= basicSampleNum) {
                shopWeight = 1f - goodsWeight;
                platformWeight = 0f;
            } else if (num <= 0) {
                shopWeight = 0f;
                platformWeight = 1 - goodsWeight;
            } else {
                shopWeight = BigDecimal.valueOf((float) num / basicSampleNum)
                        .setScale(2, RoundingMode.HALF_UP).floatValue();
                shopWeight = Math.min(shopWeight, 1f - goodsWeight);
                platformWeight = 1f - shopWeight - goodsWeight;
            }
        }

        return BigDecimal.valueOf(goodsWeight * goodsRate + shopWeight * shopRate +
                platformWeight * platformRate).setScale(4, RoundingMode.HALF_UP).floatValue();
    }

    private float calCompositeScore(float maxPrice, float adjustedNoSellRefundRate, float noSellPunishThreshold, float noSellPunishMul,
                                    float adjustedQualityRefundRate, float qualityPunishThreshold, float qualityPunishMul) {
        float noSellMul;
        if (adjustedNoSellRefundRate <= noSellPunishThreshold) {
            noSellMul = 1f;
        } else {
            noSellMul = BigDecimal.valueOf((adjustedNoSellRefundRate - noSellPunishThreshold) * noSellPunishMul + 1)
                    .setScale(4, RoundingMode.HALF_UP).floatValue();
        }

        float qualityMul;
        if (adjustedQualityRefundRate <= qualityPunishThreshold) {
            qualityMul = 1f;
        } else {
            qualityMul = BigDecimal.valueOf((adjustedQualityRefundRate - qualityPunishThreshold) * qualityPunishMul + 1)
                    .setScale(4, RoundingMode.HALF_UP).floatValue();
        }

        return BigDecimal.valueOf(maxPrice * noSellMul * qualityMul).setScale(2, RoundingMode.HALF_UP).floatValue();
    }

    private Integer getListingTypeByShopId(Long shopId) {
        List<Long> tagIds = faMerchantsApplyCoreService.queryShopTagByShopId(shopId);
        if (tagIds.contains(146L) || tagIds.contains(507L) || tagIds.contains(514L)) {
            return null;
        } else {
            return 0;
        }
    }

    private List<Integer> getShopTagValuesByShopId(Long shopId) {
        List<Long> tagIds = faMerchantsApplyCoreService.queryShopTagByShopId(shopId);
        if (CollectionUtils.isEmpty(tagIds)) {
            return new ArrayList<>();
        }
        List<String> names = tagIds.stream().filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toList());
        return searchTypeMatchBinaryConfigService.getDecimalValuesByEnumNames(names, 1);
    }

    private List<Integer> getDeliveryTypeValues(FaMerchantsApply faMerchantsApply) {
        if (faMerchantsApply == null) {
            return new ArrayList<>();
        }
        Integer deliveryType = faMerchantsApply.getDeliveryType();
        if (deliveryType == null) {
            return new ArrayList<>();
        }
        List<String> names = Collections.singletonList(String.valueOf(deliveryType));
        return searchTypeMatchBinaryConfigService.getDecimalValuesByEnumNames(names, 2);
    }


    @Override
    public void copyTemplateForWarehouseOverSea(List<Long> listingIds) {
//        LogUtils.info(log, "复制普通listing模板至海外仓模板库 listingIds:{}", listingIds);
//        CheckUtils.isEmpty(listingIds, ProductResultCode.PARAMETER_ERROR);
//
//        List<ListingInfo> originalListingInfoList = listingInfoService.lambdaQuery()
//                .in(ListingInfo::getId, listingIds)
//                .eq(ListingInfo::getIsDel, 0)
//                .ne(ListingInfo::getDeliveryType, DeliveryTypeBinaryEnum.DIRECT.getValue())
//                .list();
//        if (CollectionUtils.isEmpty(originalListingInfoList)) {
//            return;
//        }
//
//        String operator = getUserName();
//        if (operator == null) {
//            operator = "复制listing海外仓模板";
//        }
//
//        List<ListingInfo> newListingInfoList = BeanCopyUtil.transformList(originalListingInfoList, ListingInfo.class);
//        for (ListingInfo listingInfo : newListingInfoList) {
//            Long listingInfoGoodsId = listingInfo.getGoodsId();
//
//            GoodsCopyDto copyDto = new GoodsCopyDto();
//            copyDto.setGoodsId(listingInfoGoodsId);
//            copyDto.setType(2);
//            copyDto.setShopId(CommonConstants.LISTING_GOODS_SHOP_ID);
//            copyDto.setShopName(CommonConstants.LISTING_GOODS_SHOP_NAME);
//            copyDto.setIsShow(GoodsIsShowEnums.TEMPLATE);
//            copyDto.setIsLock(0);
//            copyDto.setNeedTag(true);
//            copyDto.setDeliveryType(DeliveryTypeEnum.DIRECT.getCode());
//            Goods goods = newAddGoodsCoreService.copyAddGoods(copyDto);
//
//            Random random = new Random();
//            Long listingId = (long) (random.nextInt(900000000) + 100000000);
//            ListingInfo existOne = listingInfoService.getById(listingId);
//            while (existOne != null) {
//                listingId = (long) (random.nextInt(900000000) + 100000000);
//                existOne = listingInfoService.getById(listingId);
//            }
//            listingInfo.setSourceGoodsId(listingInfo.getId());
//            listingInfo.setId(listingId);
//
//            listingInfo.setGoodsId(goods.getId());
//            listingInfo.setStatus(0);
//            listingInfo.setDeliveryType(DeliveryTypeBinaryEnum.DIRECT.getValue());
//            listingInfo.setFollowCount(0);
//            listingInfo.setIsDel(0);
//            listingInfo.setCreateTime(LocalDateTime.now());
//            listingInfo.setUpdateTime(LocalDateTime.now());
//            listingInfo.setCreateUser(operator);
//            listingInfo.setUpdateUser(operator);
//            listingInfoService.saveOrUpdate(listingInfo);
//        }
    }

    /**
     * 检测商品是否为listing商品
     *
     * @param goodsId
     * @return
     */
    @Override
    public ListingFollowGoodsCheckVo isListingGoods(Long goodsId) {
        ListingFollowGoodsCheckVo listingFollowGoodsCheckVo = new ListingFollowGoodsCheckVo();
        listingFollowGoodsCheckVo.setGoodsId(goodsId);

        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.getByGoodsId(goodsId);
        if (Objects.nonNull(listingFollowGoods)) {
            listingFollowGoodsCheckVo.setListingId(listingFollowGoods.getListingId());
            listingFollowGoodsCheckVo.setListingGoodsFlag(true);
        }
        return listingFollowGoodsCheckVo;
    }

    @Override
    public Integer countReductionPushByShopId() {
        Long shopId = getShopId();
        LogUtils.info(log, "根据店铺id查询listing降价推送数 shopId:{}", shopId);
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);
        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getShopId, shopId)
                .eq(ListingFollowGoods::getAcceptReductionPush, 1)
                .ne(ListingFollowGoods::getScoreSort, 1)
                .eq(ListingFollowGoods::getIsDel, 0)
                .gt(ListingFollowGoods::getSuggestedPrice, 0)
                .select(ListingFollowGoods::getId, ListingFollowGoods::getGoodsId, ListingFollowGoods::getSuggestedPrice)
                .list();

        List<Long> goodsIds = listingFollowGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsIds)) {
            return 0;
        }

        List<Goods> goodsList = goodsService.lambdaQuery().in(Goods::getId, goodsIds).eq(Goods::getIsDel, 0).list();
        Map<Long, BigDecimal> goodsMaxPriceMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Goods::getMaxPrice, (v1, v2) -> v2));

        List<ListingFollowGoods> cancelList = listingFollowGoodsList.stream()
                .filter(listingFollowGoods -> goodsMaxPriceMap.get(listingFollowGoods.getGoodsId()).compareTo(BigDecimal.valueOf(listingFollowGoods.getSuggestedPrice())) <= 0)
                .peek(listingFollowGoods -> {
                    listingFollowGoods.setSuggestedPrice(0);
                    listingFollowGoods.setUpdateTime(LocalDateTime.now());
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cancelList)) {
            listingFollowGoodsService.updateBatchById(cancelList);
        }

        return goodsIds.size() - cancelList.size();
    }

    @Override
    public PageView<ListingReductionPushVo> queryReductionPushList(PageParam pageParam) {
        LogUtils.info(log, "降价推送列表页 param:{}", pageParam);
        Long shopId = getShopId();
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);

        IPage<ListingFollowGoods> page = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getShopId, shopId)
                .eq(ListingFollowGoods::getAcceptReductionPush, 1)
                .ne(ListingFollowGoods::getScoreSort, 1)
                .eq(ListingFollowGoods::getIsDel, 0)
                .gt(ListingFollowGoods::getSuggestedPrice, 0)
                .page(new Page<>(pageParam.getPageNow(), pageParam.getPageSize()));

        PageView<ListingReductionPushVo> pageView = new PageView<>(Math.toIntExact(pageParam.getPageSize()), Math.toIntExact(pageParam.getPageNow()));
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return pageView;
        }

        List<ListingFollowGoods> listingFollowGoodsList = page.getRecords();
        List<Long> goodsIds = listingFollowGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        List<Goods> goodsList = goodsService.queryGoodsByAllIds(goodsIds);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

        Map<Long, List<GoodsItem>> goodsItemGroupMap = goodsItemService.queryGoodsIdsList(goodsIds).stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

        List<ListingReductionPushVo> voList = listingFollowGoodsList.stream().map(listingFollowGoods -> {
                    Goods goods = goodsMap.get(listingFollowGoods.getGoodsId());
                    if (goods.getMaxPrice().compareTo(BigDecimal.valueOf(listingFollowGoods.getSuggestedPrice())) <= 0) {
                        return null;
                    }

                    List<GoodsItem> goodsItemList = goodsItemGroupMap.get(listingFollowGoods.getGoodsId());

                    ListingReductionPushVo vo = new ListingReductionPushVo();
                    vo.setGoodsId(listingFollowGoods.getGoodsId());
                    vo.setGoodsName(goods.getName());
                    vo.setMainImage(goods.getMainImage());
                    vo.setMinPrice(goods.getMinPrice());
                    vo.setMaxPrice(goods.getMaxPrice());

                    goodsItemList.stream()
                            .filter(goodsItem -> goodsItem.getOrginalPrice().compareTo(BigDecimal.ZERO) > 0)
                            .max(Comparator.comparing(GoodsItem::getPrice))
                            .ifPresent(maxPriceSku -> {
                                BigDecimal percent = BigDecimal.valueOf(listingFollowGoods.getSuggestedPrice()).subtract(maxPriceSku.getDefaultDelivery()).divide(maxPriceSku.getOrginalPrice(), 3, RoundingMode.DOWN);
                                if (percent.compareTo(new BigDecimal("0.97")) > 0) {
                                    percent = new BigDecimal("0.97");
                                }
                                BigDecimal reductionRate = BigDecimal.ONE
                                        .subtract(percent)
                                        .multiply(new BigDecimal("100")).setScale(1, RoundingMode.UP);
                                GoodsItem minPriceSku = goodsItemList.stream().min(Comparator.comparing(GoodsItem::getPrice)).orElse(null);
                                if (minPriceSku != null) {
                                    vo.setReductionRate(reductionRate);
                                    vo.setReductionMinPrice(minPriceSku.getOrginalPrice().multiply(percent).add(minPriceSku.getDefaultDelivery()).setScale(2, RoundingMode.DOWN));
                                    vo.setReductionMaxPrice(maxPriceSku.getOrginalPrice().multiply(percent).add(maxPriceSku.getDefaultDelivery()).setScale(2, RoundingMode.DOWN));
//                                    vo.setReasonType(String.format("竞标组内排名：%s，按建议降价后可超过今日第一", listingFollowGoods.getScoreSort()));
                                    vo.setScoreSort(listingFollowGoods.getScoreSort());
                                }
                            });
                    return vo.getReductionRate() == null ? null : vo;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        pageView.setRecords(voList);
        pageView.setRowCount(page.getTotal());
        return pageView;
    }

    @Override
    public void listingReductionPushNoticeJob(){
        LogUtils.info(log,"定时推送listing降价push ==> start");

        QueryWrapper<ListingFollowGoods> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct shop_id as 'shopId'")
                .eq("is_del", 0)
                .eq("accept_reduction_push", 1)
                .ne("score_sort", 1)
                .gt("suggested_price", 0);
        List<Map<String, Object>> listMaps = listingFollowGoodsService.listMaps(queryWrapper);
        LogUtils.info(log, "定时推送listing降价push ==> listMaps:{}", listMaps);

        if (CollectionUtils.isEmpty(listMaps)) {
            return;
        }

        int num = 1;
        List<Long> allShopIds = listMaps.stream().map(mapO -> Long.parseLong(mapO.get("shopId").toString())).collect(Collectors.toList());
        for (List<Long> batchShopIds : Lists.partition(allShopIds, 10)) {
            LogUtils.info(log, "定时推送listing降价push ==> batch:{}; size:{}", num++, batchShopIds.size());
            batchSendNotice(batchShopIds);
        }
    }

    @Override
    public void listingReductionPushNoticeJob(List<Long> shopIds) {
        batchSendNotice(shopIds);
    }

    private void batchSendNotice(List<Long> shopIds) {
        if (CollectionUtils.isEmpty(shopIds)) {
            LogUtils.info(log,"定时推送listing降价push ==> return1");
            return;
        }

        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getShopId, shopIds)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getAcceptReductionPush, 1)
                .ne(ListingFollowGoods::getScoreSort, 1)
                .gt(ListingFollowGoods::getSuggestedPrice, 0)
                .list();
        if (CollectionUtils.isEmpty(listingFollowGoodsList)) {
            LogUtils.info(log,"定时推送listing降价push ==> return2");
            return;
        }

        List<Long> listingIds = listingFollowGoodsList.stream().map(ListingFollowGoods::getListingId).distinct().collect(Collectors.toList());
        List<ListingInfo> listingInfoList = listingInfoService.lambdaQuery().in(ListingInfo::getId, listingIds).eq(ListingInfo::getStatus, 1).list();
        if (CollectionUtils.isEmpty(listingInfoList)) {
            LogUtils.info(log,"定时推送listing降价push ==> return3");
            return;
        }

        List<Long> validListingIds = listingInfoList.stream().map(ListingInfo::getId).collect(Collectors.toList());
        listingFollowGoodsList = listingFollowGoodsList.stream().filter(listingFollowGoods -> validListingIds.contains(listingFollowGoods.getListingId())).collect(Collectors.toList());

        List<Long> goodsIds = listingFollowGoodsList.stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        List<Goods> goodsList = goodsService.queryGoodsByAllIds(goodsIds);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v2));

        List<GoodsItem> allGoodsItemList = goodsItemService.queryGoodsIdsList(goodsIds);
        Map<Long, List<GoodsItem>> goodsItemGroupMap = allGoodsItemList.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

        List<NoticeInput> noticeInputList = Lists.newArrayList();
        Map<Long, List<ListingFollowGoods>> listingFollowGoodsGroupMap = listingFollowGoodsList.stream().collect(Collectors.groupingBy(ListingFollowGoods::getShopId));
        for (Map.Entry<Long, List<ListingFollowGoods>> entry : listingFollowGoodsGroupMap.entrySet()) {
            Long shopId = entry.getKey();
            List<ListingFollowGoods> shopListingFollowGoodsList = entry.getValue();

            List<String> contentList = Lists.newArrayList();
            for (ListingFollowGoods listingFollowGoods : shopListingFollowGoodsList) {
                Long goodsId = listingFollowGoods.getGoodsId();
                Goods goods = goodsMap.get(goodsId);
                if (goods == null || goods.getMaxPrice().compareTo(BigDecimal.valueOf(listingFollowGoods.getSuggestedPrice())) <= 0) {
                    continue;
                }

                List<GoodsItem> goodsItemList = goodsItemGroupMap.get(goodsId);
                GoodsItem maxPriceSku = goodsItemList.stream()
                        .filter(goodsItem -> goodsItem.getOrginalPrice().compareTo(BigDecimal.ZERO) > 0)
                        .max(Comparator.comparing(GoodsItem::getPrice)).orElse(null);
                if (maxPriceSku != null) {
                    BigDecimal percent = BigDecimal.valueOf(listingFollowGoods.getSuggestedPrice())
                            .subtract(maxPriceSku.getDefaultDelivery())
                            .divide(maxPriceSku.getOrginalPrice(), 3, RoundingMode.DOWN);
                    if (percent.compareTo(new BigDecimal("0.97")) > 0) {
                        percent = new BigDecimal("0.97");
                    }
                    BigDecimal reductionRate = BigDecimal.ONE
                            .subtract(percent)
                            .multiply(new BigDecimal("100")).setScale(1, RoundingMode.UP);

                    contentList.add("<p>"+listingFollowGoods.getGoodsId() + "，组内排名第" + listingFollowGoods.getScoreSort() + "，建议底价降" + reductionRate + " % </p>");
                }
            }

            if (CollectionUtils.isNotEmpty(contentList)) {
                StringBuilder stringBuilder = new StringBuilder("<p>以下竞标商品今日竞标失败，建议您降低售价参与明日的竞价。建议降价仅根据今日竞标成功商品价格反算，不保证次日一定竞标成功，在建议降价基础上继续降价可提高竞标成功概率</p>");
                contentList.forEach(stringBuilder::append);

                NoticeInput notice = new NoticeInput();
                notice.setTitle("竞标失败商品提醒");
                notice.setContent(stringBuilder.toString());
                notice.setSpecifyShopIds(String.valueOf(shopId));
                notice.setStatus(1);
                notice.setIsDialogs(1);
                notice.setFounder("schedule job");
                noticeInputList.add(notice);
            }
        }

        if (CollectionUtils.isNotEmpty(noticeInputList)) {
            LogUtils.info(log, "定时推送listing降价push ==> 发送公告:noticeInputList: {}", noticeInputList);
            noticeClientFactory.noticeRemoteApi(noticeInputList);
        }
    }


    @Override
    public void acceptReduction(ShopAcceptReductionVo vo) {
        LogUtils.info(log, "接受降价: {}", vo);
        Long shopId = getShopId();
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);

        List<ShopAcceptReductionVo.goodsReductionInfo> goodsReductionInfoList = vo.getGoodsReductionInfoList();
        CheckUtils.isEmpty(goodsReductionInfoList, ProductResultCode.PARAMETER_ERROR);
        goodsReductionInfoList.forEach(info -> {
            CheckUtils.notNull(info.getGoodsId(), ProductResultCode.PARAMETER_ID_ERROR);
            CheckUtils.check(info.getReductionRate() == null || info.getReductionRate().compareTo(BigDecimal.ZERO) <= 0 || info.getReductionRate().compareTo(new BigDecimal("100")) > 0, ProductResultCode.GOODS_PRICE_RATIO_ERROR);
        });

        Map<Long, BigDecimal> goodsReductionRateMap = goodsReductionInfoList.stream().collect(Collectors.toMap(ShopAcceptReductionVo.goodsReductionInfo::getGoodsId, ShopAcceptReductionVo.goodsReductionInfo::getReductionRate, (v1, v2) -> v1));
        List<Long> goodsIds = Lists.newArrayList(goodsReductionRateMap.keySet());

        List<Goods> goodsList = goodsService.lambdaQuery()
                .in(Goods::getId, goodsIds)
                .eq(Goods::getIsDel, 0)
                .eq(Goods::getShopId, shopId)
                .list();
        CheckUtils.isEmpty(goodsList, ProductResultCode.GOODS_NOT_EXIST);
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));

        goodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());
        List<GoodsItem> allGoodsItemList = goodsItemService.queryGoodsIdsList(goodsIds);
        Map<Long, List<GoodsItem>> goodsItemGroupMap = allGoodsItemList.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

        List<GoodsSkuPo> allGoodsSkuPoList = goodsSkuService.findListByGoodsIds(goodsIds);
        Map<Long, List<GoodsSkuPo>> goodsSkuGroupMap = allGoodsSkuPoList.stream().collect(Collectors.groupingBy(GoodsSkuPo::getGoodsId));

        List<GoodsLockInfo> goodsLockInfoList = goodsLockInfoService.lambdaQuery()
                .in(GoodsLockInfo::getGoodsId, goodsIds)
                .eq(GoodsLockInfo::getIsDel, 0)
                .list();
        Map<Long, String> goodsLockLabelMap = goodsLockInfoList.stream().collect(Collectors.toMap(GoodsLockInfo::getGoodsId, GoodsLockInfo::getLabel, (v1, v2) -> v2));

        List<GoodsOperationLogDto> goodsOperationLogDtoList = Lists.newArrayList();
        List<GoodsEditInfo> goodsEditInfoList = Lists.newArrayList();

        for (Long goodsId : goodsIds) {
            Goods goods = goodsMap.get(goodsId);
            List<GoodsItem> goodsItemList = goodsItemGroupMap.get(goodsId);
            List<GoodsSkuPo> goodsSkuPoList = goodsSkuGroupMap.get(goodsId);

            BigDecimal reductionRate = goodsReductionRateMap.get(goodsId).divide(new BigDecimal("100"), 4, RoundingMode.UP);

            Map<Long, BigDecimal> oldData = Maps.newHashMap();
            Map<Long, BigDecimal> newData = Maps.newHashMap();
            Map<Long, MutableTriple<Long, BigDecimal, BigDecimal>> changeEditMap = Maps.newHashMap();


            if (CollectionUtils.isNotEmpty(goodsItemList)) {
                Date now = new Date();
                goodsItemList.forEach(goodsItem -> {
                    BigDecimal originalPrice = goodsItem.getOrginalPrice();
                    BigDecimal newOriginalPrice = originalPrice.multiply(BigDecimal.ONE.subtract(reductionRate)).setScale(2, RoundingMode.HALF_UP);

                    goodsItem.setOrginalPrice(newOriginalPrice);
                    goodsItem.setPrice(goodsItem.getOrginalPrice().add(goodsItem.getDefaultDelivery()));
                    goodsItem.setUpdateTime(now);

                    MutableTriple<Long, BigDecimal, BigDecimal> triple = new MutableTriple<>(goodsItem.getId(), originalPrice, newOriginalPrice);
                    changeEditMap.put(goodsItem.getSkuId(), triple);
                    oldData.put(goodsItem.getSkuId(), goodsItem.getOrginalPrice());
                    newData.put(goodsItem.getSkuId(), goodsItem.getOrginalPrice());
                });
            }

            if (CollectionUtils.isNotEmpty(goodsSkuPoList)) {
                goodsSkuPoList.forEach(goodsSkuPo -> {
                    goodsSkuPo.setOriginalPrice(goodsSkuPo.getOriginalPrice().multiply(BigDecimal.ONE.subtract(reductionRate)).setScale(2, RoundingMode.HALF_UP));
                    goodsSkuPo.setPrice(goodsSkuPo.getOriginalPrice().add(goodsSkuPo.getDefaultDelivery()));
                    goodsSkuPo.setUpdateTime(LocalDateTime.now());
                });
            }

            goodsItemList.stream().map(GoodsItem::getPrice).min(BigDecimal::compareTo).ifPresent(goods::setMinPrice);
            goodsItemList.stream().map(GoodsItem::getPrice).max(BigDecimal::compareTo).ifPresent(goods::setMaxPrice);
            goods.setUpdateTime(LocalDateTime.now());

            //log
            String label = goodsLockLabelMap.get(goodsId);
            List<String> specialTagList = StringUtils.isBlank(label) ? Lists.newArrayList() : GoodsLockLabelTypEnums.listNamesByCodes(label);
            specialTagList.add("listing");

            GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                    .goodsId(goodsId)
                    .type(OperationLogTypeEnums.SHOP_ACCEPT_LISTING_REDUCTION)
                    .oldData(JSON.toJSONString(oldData))
                    .newData(JSON.toJSONString(newData))
                    .content(OperationLogTypeEnums.SHOP_ACCEPT_LISTING_REDUCTION.getDesc())
                    .status(1)
                    .user(getUserName());
            goodsOperationLogDtoList.add(goodsOperationLogDto);

            GoodsEditPriceDto goodsEditPriceDto = new GoodsEditPriceDto();
            List<GoodsEditPriceDto.SkuChangeDto> skuChangeDtoList = Lists.newArrayList();
            changeEditMap.forEach((k,v)->{
                skuChangeDtoList.add(new GoodsEditPriceDto.SkuChangeDto(v.getLeft(), k, v.getMiddle(), v.getRight()));
            });
            goodsEditPriceDto.setSkuChangeDtoList(skuChangeDtoList);

            GoodsEditInfo editInfo = new GoodsEditInfo();
            editInfo.setGoodsId(goods.getId());
            editInfo.setCategoryId(goods.getCategoryId());
            editInfo.setShopId(goods.getShopId());
            editInfo.setShopName(goods.getShopName());
            editInfo.setStatus(1);
            editInfo.setAuditUser("system");
            editInfo.setAuditTime(LocalDateTime.now());
            editInfo.setAddPrice(0);
            editInfo.setApplyUser(getUserName());
            editInfo.setApplyTime(LocalDateTime.now());
            editInfo.setApplyReason("商家接受listing降价push");
            editInfo.setIsDel(0);
            editInfo.setType(GoodsEditTypeEnums.PRICE.getCode());
            editInfo.setContent(JSON.toJSONString(goodsEditPriceDto));
            editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
            editInfo.setUpdateTime(LocalDateTime.now());
            goodsEditInfoList.add(editInfo);
        }

        goodsService.updateBatchById(goodsList);
        goodsItemService.updateBatchById(allGoodsItemList);
        if (CollectionUtils.isNotEmpty(allGoodsSkuPoList)) {
            goodsSkuService.updateBatchByIdAndGoodsId(allGoodsSkuPoList);
        }

        goodsEditInfoService.saveBatch(goodsEditInfoList);
        List<GoodsEditInfoDetail> goodsEditInfoDetailList = goodsEditInfoList.stream().map(goodsEditInfo -> {
            GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
            goodsEditInfoDetail.setEditId(goodsEditInfo.getId());
            goodsEditInfoDetail.setGoodsId(goodsEditInfo.getGoodsId());
            goodsEditInfoDetail.setContent(goodsEditInfo.getContent());
            return goodsEditInfoDetail;
        }).collect(Collectors.toList());
        goodsEditInfoDetailService.saveBatch(goodsEditInfoDetailList);

        //建议价以下的清空当天建议价
        Map<Long, BigDecimal> goodsMaxPriceMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Goods::getMaxPrice, (v1, v2) -> v2));
        List<ListingFollowGoods> updateListingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getGoodsId, goodsIds)
                .eq(ListingFollowGoods::getIsDel, 0)
                .list()
                .stream()
                .filter(listingFollowGoods -> {
                    BigDecimal newMaxPrice = goodsMaxPriceMap.get(listingFollowGoods.getGoodsId());
                    boolean autoClearSuggestPrice = newMaxPrice.compareTo(BigDecimal.valueOf(listingFollowGoods.getSuggestedPrice() * 1.01)) <= 0;
                    log.info("acceptReduction 是否清空建议降价 id:{}, newMaxPrice:{}, suggestedPrice:{}, autoClearSuggestPrice:{}", listingFollowGoods.getGoodsId(), newMaxPrice, listingFollowGoods.getSuggestedPrice(), autoClearSuggestPrice);
                    return autoClearSuggestPrice;
                })
                .peek(listingFollowGoods -> {
                    log.info("acceptReduction ==> 清空建议价 id:{}", listingFollowGoods.getGoodsId());
                    listingFollowGoods.setSuggestedPrice(0);
                    listingFollowGoods.setUpdateTime(LocalDateTime.now());
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateListingFollowGoodsList)) {
            listingFollowGoodsService.updateBatchById(updateListingFollowGoodsList);
        }

        goodsOperationLogDtoList.forEach(dto -> mqSender.send("SYNC_GOODS_OPERATION_LOG", dto));

        goodsIds.forEach(goodsId -> {
            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(goodsId);
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("商家接受listing降价");
            goodsSyncModel.setSourceService("vp");
            mqSender.send("SYNC_GOODS_TOPIC_BATCH", goodsSyncModel);
        });
    }

    @Override
    public void cancelPush(GoodsIdsCondition condition) {
        LogUtils.info(log, "不再提醒: {}", condition);
        Long shopId = getShopId();
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);
        CheckUtils.isEmpty(condition.getGoodsIds(), ProductResultCode.PARAMETER_ID_ERROR);


        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getGoodsId, condition.getGoodsIds())
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getShopId, shopId)
                .eq(ListingFollowGoods::getAcceptReductionPush, 1)
                .list();
        if (CollectionUtils.isNotEmpty(listingFollowGoodsList)) {
            listingFollowGoodsList.forEach(listingFollowGoods -> {
                listingFollowGoods.setAcceptReductionPush(0);
                listingFollowGoods.setUpdateTime(LocalDateTime.now());
            });
            listingFollowGoodsService.updateBatchById(listingFollowGoodsList);
        }

    }

    @Override
    public GoodsReductionVO queryGoodsReductionList(Long goodsId) {
        CheckUtils.notNull(goodsId, ProductResultCode.PARAMETER_ID_ERROR);

        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getGoodsId, goodsId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .one();
        AssertsUtils.isTrue(listingFollowGoods != null, "该商品不是有效listing商品");
        AssertsUtils.isTrue(listingFollowGoods.getSuggestedPrice() > 0, "该商品不存在建议降价");


        List<GoodsItem> goodsItemList = goodsItemService.queryByGoodsId(goodsId);
        CheckUtils.isEmpty(goodsItemList, ProductResultCode.GOODS_ITEM_NOT_EXIST);

        GoodsItem maxPriceSku = goodsItemList.stream()
                .filter(goodsItem -> goodsItem.getOrginalPrice().compareTo(BigDecimal.ZERO) > 0)
                .max(Comparator.comparing(GoodsItem::getPrice))
                .orElseThrow(() -> new RuntimeException("。。。排序不出最高价sku"));
        BigDecimal percent = BigDecimal.valueOf(listingFollowGoods.getSuggestedPrice()).subtract(maxPriceSku.getDefaultDelivery()).divide(maxPriceSku.getOrginalPrice(), 3, RoundingMode.DOWN);
        if (percent.compareTo(new BigDecimal("0.97")) > 0) {
            percent = new BigDecimal("0.97");
        }
        BigDecimal reductionRate = BigDecimal.ONE
                .subtract(percent)
                .multiply(new BigDecimal("100")).setScale(1, RoundingMode.UP);

        GoodsReductionVO vo = new GoodsReductionVO();
        vo.setGoodsId(goodsId);
        vo.setListingSort(listingFollowGoods.getScoreSort());
        vo.setAcceptReductionPush(listingFollowGoods.getAcceptReductionPush());

        BigDecimal finalPercent = percent;
        List<SkuReductionPriceVO> res = goodsItemList.stream()
                .map(goodsItem -> {
                            SkuReductionPriceVO skuReductionPriceVO = new SkuReductionPriceVO();
                            skuReductionPriceVO.setGoodsId(goodsId);
                            skuReductionPriceVO.setSkuId(goodsItem.getSkuId());
                            skuReductionPriceVO.setSkuName(goodsItem.getName());
                            skuReductionPriceVO.setPrice(goodsItem.getOrginalPrice().multiply(finalPercent).setScale(2, RoundingMode.DOWN));
                            skuReductionPriceVO.setCurrentPrice(goodsItem.getOrginalPrice());
                            skuReductionPriceVO.setFreight(goodsItem.getDefaultDelivery());
                            skuReductionPriceVO.setCurrentFreight(goodsItem.getDefaultDelivery());
                            return skuReductionPriceVO;
                        }
                ).collect(Collectors.toList());
        vo.setReductionRate(reductionRate);
        vo.setSkuReductionPriceVOList(res);
        return vo;
    }

    @Override
    public void updateAcceptReductionPush(UpdateAcceptReductionPushVo condition) {
        LogUtils.info(log, "订阅/取消订阅建议降价推送: {}", condition);
        Long shopId = getShopId();
        CheckUtils.notNull(shopId, ProductResultCode.SHOP_ID_NULL);
        CheckUtils.isEmpty(condition.getGoodsIds(), ProductResultCode.PARAMETER_ID_ERROR);
        CheckUtils.notNull(condition.getAcceptReductionPush(), ProductResultCode.PARAMETER_ERROR);

        List<ListingFollowGoods> listingFollowGoodsList = listingFollowGoodsService.lambdaQuery()
                .in(ListingFollowGoods::getGoodsId, condition.getGoodsIds())
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getShopId, shopId)
                .list();
        if (CollectionUtils.isNotEmpty(listingFollowGoodsList)) {
            listingFollowGoodsList.forEach(listingFollowGoods -> {
                listingFollowGoods.setAcceptReductionPush(condition.getAcceptReductionPush());
                listingFollowGoods.setUpdateTime(LocalDateTime.now());
            });
            listingFollowGoodsService.updateBatchById(listingFollowGoodsList);
        }
    }
}
