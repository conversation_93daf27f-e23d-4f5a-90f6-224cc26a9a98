package com.voghion.product.core.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.PageView;
import com.colorlight.base.mybatis.utils.PageTransferUtils;
import com.colorlight.base.utils.CheckUtils;
import com.colorlight.base.utils.TransferUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.voghion.product.api.dto.GoodsTagDTO;
import com.voghion.product.api.dto.GoodsTagStyleDTO;
import com.voghion.product.api.dto.ShopTagDTO;
import com.voghion.product.api.input.ShopTagInput;
import com.voghion.product.core.GoodsTagCoreService;
import com.voghion.product.model.enums.GoodsTagResultCode;
import com.voghion.product.model.po.FaMerchantsTag;
import com.voghion.product.model.po.GoodsTag;
import com.voghion.product.model.po.GoodsTagStyle;
import com.voghion.product.model.vo.GoodsTagStyleVO;
import com.voghion.product.model.vo.GoodsTagVO;
import com.voghion.product.model.vo.condition.GoodsTagCondition;
import com.voghion.product.service.FaMerchantsTagService;
import com.voghion.product.service.GoodsTagService;
import com.voghion.product.service.GoodsTagStyleService;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GoodsTagCoreServiceImpl
 *
 * <AUTHOR>
 * @date 2022/12/9
 */
@Service
@Slf4j
public class GoodsTagCoreServiceImpl extends AbstractCommonServiceImpl implements GoodsTagCoreService {

    @Resource
    private GoodsTagService goodsTagService;

    @Resource
    private GoodsTagStyleService goodsTagStyleService;

    @Resource
    private RedisApi redisApi;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private FaMerchantsTagService faMerchantsTagService;

    public static final String COUNTRY_ALL = "ALL";

    @Override
    public void addGoodsTag(GoodsTagVO goodsTagVO) {
        GoodsTag goodsTag = getAddGoodsTag(goodsTagVO);
        boolean insert = goodsTagService.insert(goodsTag);
        if (insert) {
            refreshCache();
        }
    }

    @Override
    public void addNewGoodsTag(GoodsTagVO goodsTagVO) {
        GoodsTag goodsTag = getAddGoodsTag(goodsTagVO);
        List<GoodsTagStyleVO> goodsTagStyleList = goodsTagVO.getGoodsTagStyleList();
        checkStyleParam(goodsTagStyleList);
        List<GoodsTagStyle> goodsTagStyles = TransferUtils.transferList(goodsTagStyleList, GoodsTagStyle.class);
        Boolean flag = transactionTemplate.execute(transactionStatus -> {
            goodsTagService.insert(goodsTag);
            if (CollectionUtils.isNotEmpty(goodsTagStyles)) {
                goodsTagStyles.forEach(goodsTagStyle -> goodsTagStyle.setTagId(goodsTag.getId()));
                goodsTagStyleService.insertBatch(goodsTagStyles);
            }
            return Boolean.TRUE;
        });
        if (Boolean.TRUE.equals(flag)) {
            refreshCache();
        }
    }


    private GoodsTag getAddGoodsTag(GoodsTagVO goodsTagVO) {
        log.info("addGoodsTag|goodsTagVO:{}", JSON.toJSONString(goodsTagVO));
        checkParam(goodsTagVO);
        GoodsTag goodsTag = new GoodsTag();
        TransferUtils.transferBean(goodsTagVO, goodsTag);
        String user = getUserName();
        LocalDateTime time = LocalDateTime.now();
        goodsTag.setCreateUser(user);
        goodsTag.setCreateTime(time);
        goodsTag.setUpdateUser(user);
        goodsTag.setUpdateTime(time);
        log.info("addGoodsTag|goodsTag:{}", JSON.toJSONString(goodsTag));
        return goodsTag;
    }

    @Override
    public void updateGoodsTag(GoodsTagVO goodsTagVO) {
        GoodsTag goodsTag = getUpdateGoodsTag(goodsTagVO);
        boolean update = goodsTagService.updateById(goodsTag);
        if (update) {
            refreshCache();
        }
    }

    private GoodsTag getUpdateGoodsTag(GoodsTagVO goodsTagVO) {
        log.info("updateGoodsTag|goodsTagVO:{}", JSON.toJSONString(goodsTagVO));
        CheckUtils.notNull(goodsTagVO.getId(), GoodsTagResultCode.ID_NULL_ERROR);
        checkParam(goodsTagVO);
        GoodsTag goodsTag = new GoodsTag();
        TransferUtils.transferBean(goodsTagVO, goodsTag);
        String user = getUserName();
        LocalDateTime time = LocalDateTime.now();
        goodsTag.setUpdateUser(user);
        goodsTag.setUpdateTime(time);
        log.info("updateGoodsTag|goodsTag:{}", JSON.toJSONString(goodsTag));
        return goodsTag;
    }

    @Override
    public void updateNewGoodsTag(GoodsTagVO goodsTagVO) {
        GoodsTag goodsTag = getUpdateGoodsTag(goodsTagVO);
        List<GoodsTagStyleVO> goodsTagStyleList = goodsTagVO.getGoodsTagStyleList();
        checkStyleParam(goodsTagStyleList);
        List<GoodsTagStyle> goodsTagStyles = TransferUtils.transferList(goodsTagStyleList, GoodsTagStyle.class);
        Boolean flag = transactionTemplate.execute(transactionStatus -> {
            goodsTagService.updateById(goodsTag);
            goodsTagStyleService.deleteByTagId(goodsTag.getId());
            if (CollectionUtils.isNotEmpty(goodsTagStyles)) {
                goodsTagStyles.forEach(goodsTagStyle -> goodsTagStyle.setTagId(goodsTag.getId()));
                goodsTagStyleService.insertBatch(goodsTagStyles);
            }
            return Boolean.TRUE;
        });
        if (Boolean.TRUE.equals(flag)) {
            refreshCache();
        }
    }

    @Override
    public void updateGoodsTagActivityInfo(GoodsTag goodsTagActivityInfo) {
        log.info("updateGoodsTagActivityInfo|goodsTagActivityInfo:{}", JSON.toJSONString(goodsTagActivityInfo));
        CheckUtils.notNull(goodsTagActivityInfo.getId(), GoodsTagResultCode.ID_NULL_ERROR);
        GoodsTag goodsTag = goodsTagService.selectById(goodsTagActivityInfo.getId());
        if (goodsTag == null || goodsTagActivityInfo.getActivityId().equals(goodsTag.getActivityId())) {
            return;
        }
        boolean update = goodsTagService.updateById(goodsTagActivityInfo);
        if (update) {
            refreshCache();
        }
    }

    @Override
    public void deleteGoodsTag(Long id) {
        log.info("deleteGoodsTag|id:{},username:{}", id, getUserName());
        CheckUtils.notNull(id, GoodsTagResultCode.ID_NULL_ERROR);
        boolean delete = goodsTagService.deleteById(id);
        if (delete) {
            refreshCache();
        }
    }

    @Override
    public PageView<GoodsTagVO> queryPageByCondition(GoodsTagCondition goodsTagCondition) {
        log.info("queryPageByCondition|goodsTagCondition:{}", JSON.toJSONString(goodsTagCondition));
        if (CollectionUtils.isNotEmpty(goodsTagCondition.getCountryList())) {
            List<String> countryList = goodsTagCondition.getCountryList();
            if (countryList.contains(COUNTRY_ALL)) {
                goodsTagCondition.setCountryList(null);
            } else {
                countryList.add(COUNTRY_ALL);
                goodsTagCondition.setCountryList(countryList);
            }
        }
        IPage<GoodsTag> goodsTagPage = goodsTagService.queryPageByCondition(goodsTagCondition);
        PageView<GoodsTag> goodsTagPageView = new PageView<>();
        PageTransferUtils.tranferIpageToPageView(goodsTagPage, goodsTagPageView);
        PageView<GoodsTagVO> resultPage = new PageView<>();
        TransferUtils.transferBean(goodsTagPageView, resultPage);
        if (CollectionUtils.isNotEmpty(goodsTagPage.getRecords())) {
            List<GoodsTagVO> goodsTagVoList = TransferUtils.transferList(goodsTagPage.getRecords(), GoodsTagVO.class);
            goodsTagVoList.forEach(GoodsTagCoreServiceImpl::convertStr);
            resultPage.setRecords(goodsTagVoList);
        }
        return resultPage;
    }

    @Override
    public PageView<GoodsTagVO> queryNewPageByCondition(GoodsTagCondition goodsTagCondition) {
        log.info("queryPageByCondition|goodsTagCondition:{}", JSON.toJSONString(goodsTagCondition));
        if (CollectionUtils.isNotEmpty(goodsTagCondition.getCountryList())) {
            List<String> countryList = goodsTagCondition.getCountryList();
            if (countryList.contains(COUNTRY_ALL)) {
                goodsTagCondition.setCountryList(null);
            } else {
                countryList.add(COUNTRY_ALL);
                goodsTagCondition.setCountryList(countryList);
            }
        }
        IPage<GoodsTag> goodsTagPage = goodsTagService.queryPageByCondition(goodsTagCondition);
        PageView<GoodsTag> goodsTagPageView = new PageView<>();
        PageTransferUtils.tranferIpageToPageView(goodsTagPage, goodsTagPageView);
        PageView<GoodsTagVO> resultPage = new PageView<>();
        TransferUtils.transferBean(goodsTagPageView, resultPage);
        if (CollectionUtils.isNotEmpty(goodsTagPage.getRecords())) {
            List<GoodsTagVO> goodsTagVoList = TransferUtils.transferList(goodsTagPage.getRecords(), GoodsTagVO.class);
            List<Long> tagIds = goodsTagVoList.stream().map(GoodsTagVO::getId).collect(Collectors.toList());
            List<GoodsTagStyle> goodsTagStyleList = goodsTagStyleService.selectByTagIds(tagIds);
            Map<Long, List<GoodsTagStyleVO>> styleMap = new LinkedHashMap<>(16);
            if (CollectionUtils.isNotEmpty(goodsTagStyleList)) {
                List<GoodsTagStyleVO> goodsTagStyleVoList = TransferUtils.transferList(goodsTagStyleList, GoodsTagStyleVO.class);
                styleMap = goodsTagStyleVoList.stream().collect(Collectors.groupingBy(GoodsTagStyleVO::getTagId, LinkedHashMap::new, Collectors.toList()));
            }
            for (GoodsTagVO goodsTagVO : goodsTagVoList) {
                convertStr(goodsTagVO);
                goodsTagVO.setGoodsTagStyleList(styleMap.get(goodsTagVO.getId()));
            }
            resultPage.setRecords(goodsTagVoList);
        }
        return resultPage;
    }

    @Override
    public List<GoodsTagVO> listAll(GoodsTagCondition goodsTagCondition) {
        List<GoodsTag> goodsTags = goodsTagService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(goodsTagCondition.getTypeList()), GoodsTag::getType, goodsTagCondition.getTypeList())
                .eq(GoodsTag::getIsDel, 0)
                .list();
        if(CollectionUtils.isNotEmpty(goodsTags)){
            return TransferUtils.transferList(goodsTags,GoodsTagVO.class);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<ShopTagDTO> queryShopTagsByInput(ShopTagInput shopTagInput) {
        if (Objects.isNull(shopTagInput) ||
                (StringUtils.isBlank(shopTagInput.getName())
                    && CollectionUtils.isEmpty(shopTagInput.getTagIds()))) {
            return Lists.newArrayList();
        }

        List<GoodsTag> goodsTags = goodsTagService.lambdaQuery()
                .in(CollectionUtils.isNotEmpty(shopTagInput.getTagIds()), GoodsTag::getId, shopTagInput.getTagIds())
                .like(StringUtils.isNotBlank(shopTagInput.getName()), GoodsTag::getTagName, shopTagInput.getName()).list();

        return getShopTagDTOS(goodsTags);

    }

    @NotNull
    private List<ShopTagDTO> getShopTagDTOS(List<GoodsTag> goodsTags) {
        Map<Long, List<FaMerchantsTag>> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(goodsTags)) {
            Collection<FaMerchantsTag> faMerchantsTags = faMerchantsTagService.lambdaQuery()
                    .in(FaMerchantsTag::getTagId, goodsTags.stream().map(GoodsTag::getId).collect(Collectors.toList()))
                    .eq(FaMerchantsTag::getStatus, 1) // 未删除的状态
                    .list();
            map = faMerchantsTags.stream().collect(Collectors.groupingBy(FaMerchantsTag::getTagId));
        }

        final Map<Long, List<FaMerchantsTag>> finalMap = map;
        return goodsTags.stream().map(goodsTag -> {
            ShopTagDTO shopTagDTO = BeanUtil.toBean(goodsTag, ShopTagDTO.class);
            shopTagDTO.setShopIdList(finalMap.getOrDefault(shopTagDTO.getId(), Lists.newArrayList()).stream().map(FaMerchantsTag::getShopId).collect(Collectors.toList()));
            return shopTagDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateGoodsTagSort(GoodsTagVO goodsTagVO) {
        log.info("updateGoodsTagSort|goodsTagVO:{}", JSON.toJSONString(goodsTagVO));
        CheckUtils.notNull(goodsTagVO.getId(), GoodsTagResultCode.ID_NULL_ERROR);
        CheckUtils.notNull(goodsTagVO.getSort(), GoodsTagResultCode.SORT_NULL_ERROR);
        GoodsTag goodsTag = new GoodsTag();
        goodsTag.setId(goodsTagVO.getId());
        goodsTag.setSort(goodsTagVO.getSort());
        goodsTag.setUpdateUser(getUserName());
        goodsTag.setUpdateTime(LocalDateTime.now());
        log.info("updateGoodsTagSort|goodsTag:{}", JSON.toJSONString(goodsTag));
        boolean update = goodsTagService.updateById(goodsTag);
        if (update) {
            refreshCache();
        }
    }

    @Override
    public void updateGoodsTagShow(GoodsTagVO goodsTagVO) {
        log.info("updateGoodsTagShow|goodsTagVO:{}", JSON.toJSONString(goodsTagVO));
        CheckUtils.notNull(goodsTagVO.getId(), GoodsTagResultCode.ID_NULL_ERROR);
        Integer isShow = goodsTagVO.getIsShow();
        CheckUtils.check(isShow == null || (isShow != 0 && isShow != 1), GoodsTagResultCode.IS_SHOW_ERROR);
        GoodsTag goodsTag = new GoodsTag();
        goodsTag.setId(goodsTagVO.getId());
        goodsTag.setIsShow(isShow);
        goodsTag.setUpdateUser(getUserName());
        goodsTag.setUpdateTime(LocalDateTime.now());
        log.info("updateGoodsTagShow|goodsTag:{}", JSON.toJSONString(goodsTag));
        boolean update = goodsTagService.updateById(goodsTag);
        if (update) {
            refreshCache();
        }
    }

    @Override
    public GoodsTagVO getGoodsTagById(Long tagId) {
        log.info("getGoodsTagById|tagId:{}", tagId);
        CheckUtils.notNull(tagId, GoodsTagResultCode.ID_NULL_ERROR);
        GoodsTag goodsTag = goodsTagService.getById(tagId);
        if (goodsTag != null) {
            GoodsTagVO goodsTagVO = new GoodsTagVO();
            TransferUtils.transferBean(goodsTag, goodsTagVO);
            convertStr(goodsTagVO);

            goodsTagVO.setGoodsTagStyleList(Lists.newArrayList());
            GoodsTagStyle goodsTagStyle = new GoodsTagStyle();
            goodsTagStyle.setTagId(tagId);
            List<GoodsTagStyle> goodsTagStyles = goodsTagStyleService.selectList(goodsTagStyle);
            if (CollectionUtils.isNotEmpty(goodsTagStyles)) {
                List<GoodsTagStyleVO> goodsTagStyleVoList = TransferUtils.transferList(goodsTagStyles, GoodsTagStyleVO.class);
                goodsTagVO.setGoodsTagStyleList(goodsTagStyleVoList);
            }

            return goodsTagVO;
        }
        return null;
    }

    @Override
    public GoodsTagDTO getGoodsTagInfoById(Long tagId) {
        log.info("getGoodsTagInfoById|tagId:{}", tagId);
        CheckUtils.notNull(tagId, GoodsTagResultCode.ID_NULL_ERROR);
        GoodsTag goodsTag = goodsTagService.getById(tagId);
        if (goodsTag != null) {
            GoodsTagDTO goodsTagDTO = new GoodsTagDTO();
            TransferUtils.transferBean(goodsTag, goodsTagDTO);

            goodsTagDTO.setGoodsTagStyleList(Lists.newArrayList());
            GoodsTagStyle goodsTagStyle = new GoodsTagStyle();
            goodsTagStyle.setTagId(tagId);
            List<GoodsTagStyle> goodsTagStyles = goodsTagStyleService.selectList(goodsTagStyle);
            if (CollectionUtils.isNotEmpty(goodsTagStyles)) {
                List<GoodsTagStyleDTO> goodsTagStyleVoList = TransferUtils.transferList(goodsTagStyles, GoodsTagStyleDTO.class);
                goodsTagDTO.setGoodsTagStyleList(goodsTagStyleVoList);
            }

            return goodsTagDTO;
        }
        return null;
    }

    @Override
    public List<GoodsTagDTO> getGoodsTagInfoByIds(List<Long> tagIds) {
        log.info("getGoodsTagInfoByIds|tagIds:{}", tagIds);
        CheckUtils.isEmpty(tagIds, GoodsTagResultCode.ID_NULL_ERROR);
        Collection<GoodsTag> goodsTags = goodsTagService.selectByIds(tagIds);
        if (CollectionUtils.isNotEmpty(goodsTags)) {
            List<GoodsTagDTO> goodsTagDtoList = TransferUtils.transferList(goodsTags, GoodsTagDTO.class);
            getTagStyleList(goodsTagDtoList, tagIds);
            return goodsTagDtoList;
        }
        return Lists.newArrayList();
    }

    @Override
    public List<GoodsTagDTO> queryGoodsTags() {
        GoodsTag goodsTag = new GoodsTag();
        goodsTag.setIsDel(0);
        List<GoodsTag> goodsTags = goodsTagService.selectList(goodsTag);
        if (CollectionUtils.isNotEmpty(goodsTags)) {
            List<GoodsTagDTO> goodsTagDtoList = TransferUtils.transferList(goodsTags, GoodsTagDTO.class);
            List<Long> tagIds = goodsTagDtoList.stream().map(GoodsTagDTO::getId).collect(Collectors.toList());
            getTagStyleList(goodsTagDtoList, tagIds);
            return goodsTagDtoList;
        }
        return Lists.newArrayList();
    }

    private void getTagStyleList(List<GoodsTagDTO> goodsTagDtoList, List<Long> tagIds) {
        List<GoodsTagStyle> goodsTagStyleList = goodsTagStyleService.selectByTagIds(tagIds);
        Map<Long, List<GoodsTagStyleDTO>> styleMap = new LinkedHashMap<>(16);
        if (CollectionUtils.isNotEmpty(goodsTagStyleList)) {
            List<GoodsTagStyleDTO> goodsTagStyleDtoList = TransferUtils.transferList(goodsTagStyleList, GoodsTagStyleDTO.class);
            styleMap = goodsTagStyleDtoList.stream().collect(Collectors.groupingBy(GoodsTagStyleDTO::getTagId, LinkedHashMap::new, Collectors.toList()));
        }
        for (GoodsTagDTO goodsTagDTO : goodsTagDtoList) {
            goodsTagDTO.setGoodsTagStyleList(styleMap.get(goodsTagDTO.getId()));
        }
    }

    private static void checkParam(GoodsTagVO goodsTagVO) {
        CheckUtils.notNull(goodsTagVO.getTagName(), GoodsTagResultCode.TAG_NAME_NULL_ERROR);
        CheckUtils.notNull(goodsTagVO.getType(), GoodsTagResultCode.TYPE_NULL_ERROR);
        CheckUtils.isEmpty(goodsTagVO.getCountryList(), GoodsTagResultCode.COUNTRY_LIST_EMPTY_ERROR);
        if (goodsTagVO.getCountryList().contains(COUNTRY_ALL)) {
            goodsTagVO.setCountryList(Lists.newArrayList(COUNTRY_ALL));
        }
        goodsTagVO.setCountry(StringUtils.join(goodsTagVO.getCountryList(), ","));
    }

    private static void checkStyleParam(List<GoodsTagStyleVO> goodsTagStyleList) {
        goodsTagStyleList.forEach(goodsTagStyleVO -> {
            CheckUtils.notNull(goodsTagStyleVO.getTagType(), GoodsTagResultCode.TAG_TYPE_NULL_ERROR);
            CheckUtils.notNull(goodsTagStyleVO.getStyleType(), GoodsTagResultCode.STYLE_TYPE_NULL_ERROR);
            if (goodsTagStyleVO.getIsShow() == 1) {
                CheckUtils.notNull(goodsTagStyleVO.getPosition(), GoodsTagResultCode.POSITION_NULL_ERROR);
                if (goodsTagStyleVO.getTagType() == 1) {
                    CheckUtils.notEmpty(goodsTagStyleVO.getImg(), GoodsTagResultCode.IMG_NULL_ERROR);
                } else if (goodsTagStyleVO.getTagType() == 2) {
                    if (StringUtils.isBlank(goodsTagStyleVO.getTitle())) {
                        goodsTagStyleVO.setTitle("");
                    }
                    if (goodsTagStyleVO.getPosition() == 6 || goodsTagStyleVO.getPosition() == 7) {
                        CheckUtils.notEmpty(goodsTagStyleVO.getImg(), GoodsTagResultCode.IMG_NULL_ERROR);
                    }
                }
            }
        });
    }

    private static void convertStr(GoodsTagVO goodsTagVO) {
        goodsTagVO.setCountryList(Lists.newArrayList());
        if (StringUtils.isNotBlank(goodsTagVO.getCountry())) {
            goodsTagVO.setCountryList(Arrays.stream(goodsTagVO.getCountry().split(",")).collect(Collectors.toList()));
        }
    }

    private void refreshCache() {
        redisApi.del("DISTRIBUTE_GOODS_TAG_INFO_V3");
    }
}
