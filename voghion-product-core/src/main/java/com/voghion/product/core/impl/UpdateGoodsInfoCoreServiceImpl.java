package com.voghion.product.core.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.enums.CountryEnums;
import com.colorlight.base.utils.CheckUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.onlest.GoodsSyncModel;
import com.voghion.es.dto.VatCondition;
import com.voghion.es.model.GoodsExtConfigModel;
import com.voghion.es.service.GoodsEsService;
import com.voghion.product.VoghionProductResultCode;
import com.voghion.product.api.dto.*;
import com.voghion.product.api.enums.GoodsEditTypeEnums;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.api.enums.PlatformConfigTypeEnum;
import com.voghion.product.api.enums.SaleableCountryEnum;
import com.voghion.product.api.input.*;
import com.voghion.product.api.service.GoodsEsRemoteService;
import com.voghion.product.biz.AbsBaseGoodsInfo;
import com.voghion.product.biz.AddGoodsInfoBiz;
import com.voghion.product.biz.ListingGoodsBiz;
import com.voghion.product.biz.UpdateGoodsInfoBiz;
import com.voghion.product.client.ActivityRemoteClientFactory;
import com.voghion.product.core.*;
import com.voghion.product.enums.PartsResultCode;
import com.voghion.product.enums.SizeTemplateTypeEnum;
import com.voghion.product.helper.GoodsSkuHelper;
import com.voghion.product.helper.PropertyHelper;
import com.voghion.product.helper.SizeChartTemplateHelper;
import com.voghion.product.listener.BatchUpdateNameVo;
import com.voghion.product.listener.GoodsDiscountImportVO;
import com.voghion.product.model.bo.ItemGoodsInfoExtBo;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.dto.GoodsItemDTO;
import com.voghion.product.model.enums.*;
import com.voghion.product.model.po.*;
import com.voghion.product.model.po.goods.*;
import com.voghion.product.model.vo.*;
import com.voghion.product.model.vo.condition.LockGoodsUpdateCondition;
import com.voghion.product.model.vo.condition.UpdateCountryCondition;
import com.voghion.product.model.vo.condition.UpdateLockLabelCondition;
import com.voghion.product.mq.MqDelayLevel;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.switchcenter.MarketingSwitchCenter;
import com.voghion.product.util.*;
import com.voghion.product.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.jetbrains.annotations.NotNull;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.index.query.TermQueryBuilder;
import org.opensearch.index.reindex.BulkByScrollResponse;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2021/6/8 20:35
 * @describe
 */
@Component
@Slf4j
public class UpdateGoodsInfoCoreServiceImpl extends AbstractCommonServiceImpl implements UpdateGoodsInfoCoreService {

    @Resource
    private GoodsService goodsService;

    @Resource
    private GoodsDetailService goodsDetailService;

    @Resource
    private GoodsExtCategoryService goodsExtCategoryService;

    @Resource
    private GoodsExtDetailService goodsExtDetailService;

    @Resource
    private GoodsExtDetailImgService goodsExtDetailImgService;

    @Resource
    private GoodsImageService goodsImageService;

    @Resource
    private ProductSkuService productSkuService;

    @Resource
    private GoodsItemService goodsItemService;

    @Resource
    private GoodsItemGradientService goodsItemGradientService;

    @Resource
    private ProductService productService;

    @Resource
    private GoodsLabelService goodsLabelService;

    @Resource
    private PropertyInfoService propertyInfoService;

    @Resource
    private PropertyImgDetailService propertyImgDetailService;

    @Resource
    private MqSender mqSender;

    @Resource
    private CountryService countryService;

    @Resource
    public RedisApi redisApi;
    @Resource
    private GoodsFreightService goodsFreightService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Resource
    private CategoryService categoryService;

    @Resource
    private GoodsEsRemoteService goodsEsRemoteService;

    @Resource
    private SensitiveWordsCoreService sensitiveWordsCoreService;

    @Autowired
    private PropertyGoodsInfoCoreService propertyGoodsInfoCoreService;

    @Resource
    private PropertyGoodsInfoService propertyGoodsInfoService;

    @Resource
    private SkuLimitPriceCoreService skuLimitPriceCoreService;

    @Autowired
    private GoodsPriceReductionCoreService goodsPriceReductionCoreService;

    @Resource
    private GoodsCoreService goodsCoreService;

    @Resource
    private FaMerchantsApplyService faMerchantsApplyService;

    @Resource
    private FaMerchantsApplyCoreService faMerchantsApplyCoreService;

    @Resource
    private FaMerchantsTagService faMerchantsTagService;

    @Resource
    private WarehouseOverseasConfigCoreService warehouseOverseasConfigCoreService;

    @Resource
    private MerchantsBrandMarkService merchantsBrandMarkService;

    @Resource
    private MerchantsBrandStoreService merchantsBrandStoreService;

    @Resource
    private GoodsFreightCoreService goodsFreightCoreService;

    @Resource
    private GoodsStandardCoreService goodsStandardCoreService;

    @Resource
    private SizeChartTemplateService sizeChartTemplateService;

    @Resource
    private CategoryMainService categoryMainService;

    @Resource
    private NewAddGoodsCoreService newAddGoodsCoreService;

    @Resource
    private GoodsExtraCoreService goodsExtraCoreService;

    @Resource
    private CategoryCoreService categoryCoreService;

    @Resource
    private GoodsSelfSupportInfoService goodsSelfSupportInfoService;

    @Resource
    private GoodsLockCoreService goodsLockCoreService;

    @Resource(name = "newRedisTemplate")
    private RedisTemplate<String, String> newRedisTemplate;

    @Resource
    private ListingInfoService listingInfoService;

    @Resource
    private ListingFollowGoodsService listingFollowGoodsService;

    @Resource
    private ChanceGoodsTemplateService chanceGoodsTemplateService;

    @Resource
    private GoodsExtConfigService goodsExtConfigService;

    @Resource(name = "goodsInfoPool")
    private Executor executor;

    @Resource
    private GoodsEditInfoService goodsEditInfoService;

    @Resource
    private GoodsEditInfoDetailService goodsEditInfoDetailService;

    @Resource
    private RecommendCoreService recommendCoreService;

    @Resource
    private GoodsLockInfoService goodsLockInfoService;

    @Resource
    private WarehouseStockGoodsService warehouseStockGoodsService;

    @Resource
    private PropertyValueRecordService propertyValueRecordService;

    @Resource
    private GoodsLogisticsCoreService goodsLogisticsCoreService;

    @Resource
    private GoodsVatConfigService goodsVatConfigService;

    @Resource
    private GoodsEsService goodsEsService;

    @Resource
    private LogisticsPropertyConfigCoreService logisticsPropertyConfigCoreService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private TongDunGoodsImageCoreService tongDunGoodsImageCoreService;

    @Resource
    private AliGoodsCategoryPriceConfigService aliGoodsCategoryPriceConfigService;

    @Resource
    private ActivityOriginalPriceService activityOriginalPriceService;

    @Resource
    private ActivityRemoteClientFactory activityRemoteClientFactory;

    @Resource
    private GoodsRealShotConfCoreService goodsRealShotConfCoreService;

    @Resource
    private GoodsRealShotImgCoreService goodsRealShotImgCoreService;

    @Resource
    private GoodsRealShotImgService goodsRealShotImgService;

    @Value("${tongDun.unCheck.realShot.tagList:146}")
    private List<Long> unCheckTagList;

    @Resource
    private GoodsManualService goodsManualService;

    @Resource
    private GoodsPartsService goodsPartsService;

    @Resource
    private CustomTagCoreService customTagCoreService;

    @Resource
    private FilterWordsRecordCoreService filterWordsRecordCoreService;

    @Resource
    private GoodsSkuHelper goodsSkuHelper;

    @Resource
    private GoodsSkuService goodsSkuService;

    @Resource
    private GoodsPropertyRelevantService goodsPropertyRelevantService;

    @Resource
    private GoodsPropertyImgRelevantService goodsPropertyImgRelevantService;

    @Resource
    private GoodsPropertySkuRelevantService goodsPropertySkuRelevantService;

    @Resource
    private UpdateGoodsInfoBiz updateGoodsInfoBiz;

    @Resource
    private AddGoodsInfoBiz addGoodsInfoBiz;

    @Resource
    private SizeChartTemplateHelper sizeChartTemplateHelper;

    @Resource
    private PropertyAssociationService propertyAssociationService;

    @Resource
    private PropertyHelper propertyHelper;

    @Resource
    private MarketingSwitchCenter marketingSwitchCenter;

    @Resource
    private ListingGoodsBiz listingGoodsBiz;

    /**
     * 更新商品信息
     *
     * @param goodsInfoInput
     * @return
     */
    @Override
    public Boolean updateGoods(ProductInfoInput goodsInfoInput) {
        if (goodsInfoInput.getShopId() != null && StringUtils.isNotBlank(goodsInfoInput.getBuyerClientinfo())) {
            //买手新增
            goodsInfoInput.setOperationLogType(OperationLogTypeEnums.UPDATE_GOODS.getCode());
            log.info("当前为买手操作 updateGoods");
            return updateGoodsDetail(goodsInfoInput);
        }

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String voghionToken = request.getHeader("clientInfo");
        // 查询redis
        String vohionTokenStr = redisApi.get(voghionToken);
        // 查看api权限
        JSONObject json = JSONObject.parseObject(vohionTokenStr);
        JSONObject user = JSONObject.parseObject(json.getJSONObject("user").toJSONString());
        Long shopId = user.getLong("shopId");
        String shopName = user.getString("shopName");
        if (shopId != null && shopId > 0) {
            goodsInfoInput.setShopId(shopId);
            goodsInfoInput.setStoreName(shopName);
        }
        goodsInfoInput.setOperationLogType(OperationLogTypeEnums.UPDATE_GOODS.getCode());
        return updateGoodsDetail(goodsInfoInput);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean shopUpdateGoods(ProductInfoInput goodsInfoInput) {
        LogUtils.info(log, "shopUpdateGoods:{}", JSON.toJSONString(goodsInfoInput));
        return updateGoodsDetail(goodsInfoInput);
    }


    private Boolean updateGoodsDetail(ProductInfoInput goodsInfoInput) {
        boolean isTemplateGoods = false;
        if (goodsInfoInput.getShopId().equals(CommonConstants.CHANCE_GOODS_SHOP_ID) || goodsInfoInput.getShopId().equals(CommonConstants.LISTING_GOODS_SHOP_ID)) {
            isTemplateGoods = true;
            goodsInfoInput.setTemplateGoods(true);
        }

        Long categoryId = goodsInfoInput.getCategoryId();
        CheckUtils.notNull(categoryId, ProductResultCode.CATEGORY_ERROR);
        Category category = categoryService.selectById(categoryId);
        CheckUtils.check(category == null || category.getIsDel() == 1, ProductResultCode.CATEGORY_NOT_FIND);
        assert category != null;
        CheckUtils.check(category.getIsLeaf() != 1, CustomResultCode.fill(ProductResultCode.CATEGORY_MUST_BE_LEAF, categoryId.toString()));

        CheckUtils.notNull(goodsInfoInput.getId(), ProductResultCode.PARAMETER_ID_ERROR);
        Goods goods = goodsService.queryGoodsById(goodsInfoInput.getId());

        CheckUtils.check(goodsInfoInput.getShopId() != null && !goodsInfoInput.getShopId().equals(goods.getShopId()), VoghionProductResultCode.GOODS_NOT_ALLOWED_OPERATER);
        CheckUtils.notNull(goodsInfoInput.getProductId(), ProductResultCode.ADD_PRODUCT_ID_ERROR);
        goodsInfoInput.setIsShow(goods.getIsShow());

        if (!isTemplateGoods) {
            //商户品牌校验
            if (goodsInfoInput.getBrandId() == null || goodsInfoInput.getBrandId() == 0) {
                boolean isNeedCheck = categoryCoreService.checkCategoryNeedBrand(goodsInfoInput.getCategoryId());
                CheckUtils.check(isNeedCheck, ProductResultCode.BRAND_NULL_ERROR);
            } else {
                MerchantsBrandStore merchantsBrandStore = merchantsBrandStoreService.selectById(goodsInfoInput.getBrandId());
                if (merchantsBrandStore == null || merchantsBrandStore.getStatus() != 1) {
                    CheckUtils.check(true, ProductResultCode.BRAND_STORE_NOT_EXIST);
                    return Boolean.FALSE;
                }
                List<MerchantsBrandMark> merchantsBrandMarkList = merchantsBrandMarkService.lambdaQuery()
                        .eq(MerchantsBrandMark::getMerchantsBrandStoreId, goodsInfoInput.getBrandId())
                        .eq(Objects.nonNull(category) ,MerchantsBrandMark::getCategoryId, category.getPids().split(",")[0])
                        .eq(MerchantsBrandMark::getShopId, goodsInfoInput.getShopId())
                        .eq(MerchantsBrandMark::getStatus, 10)
                        .eq(MerchantsBrandMark::getIsDel, 0)
                        .list();
                if (CollectionUtils.isEmpty(merchantsBrandMarkList)) {
                    CheckUtils.check(true, ProductResultCode.BRAND_NOT_EXIST);
                    return Boolean.FALSE;
                }
                boolean expireFlag = merchantsBrandMarkList.stream().anyMatch(item -> new Date().before(item.getExpireTime()));
                if (!expireFlag) {
                    CheckUtils.check(true, ProductResultCode.BRAND_IS_EXPIRE);
                    return Boolean.FALSE;
                }
            }

            CheckUtils.check(checkShowStatusForUpdate(Integer.parseInt(goods.getIsShow())), ProductResultCode.GOODS_SHOW_STATUS_CAN_NOT_UPDATE);
        }


        //敏感词校验
        String goodsName = goodsInfoInput.getName();
//        Long firstCategoryId = Long.valueOf(category.getPids().split(",")[0]);
//        Pair<Boolean, Set<String>> sensitiveWordsCheckResult = sensitiveWordsCoreService.checkNew(goodsName, goodsInfoInput.getShopId(), goodsInfoInput.getBrandId(), firstCategoryId);
//        CheckUtils.check(sensitiveWordsCheckResult.getLeft(), CustomResultCode.fill(ProductResultCode.GOODS_NAME_CONTAIN_SENSITIVE_WORDS, String.join(",", sensitiveWordsCheckResult.getRight())));
        if(!goodsName.equals(goods.getName())){
            newRedisTemplate.opsForValue().increment("GOODS_NAME_UPDATE_COUNT:"+LocalDate.now());
        }

        //校验物流属性
        goodsLogisticsCoreService.checkLogisticProperty(goodsInfoInput.getCategoryId(), goodsInfoInput.getLogisticsProperty());

        //商品listing类型
        boolean isListingFirstEdit = false;
        fillListingUseType(goodsInfoInput);
        CheckUtils.check(goodsInfoInput.getUseType() == 2 && OperationLogTypeEnums.ERP_UPDATE_GOODS.getCode().equals(goodsInfoInput.getOperationLogType()),ProductResultCode.LISTING_GOODS_FORBID_UPDATE);
        if (goodsInfoInput.getUseType() == 2 ) {
            AssertsUtils.isTrue(goodsInfoInput.getCountry().contains(SaleableCountryEnum.DE.name()) || goodsInfoInput.getCountry().contains(SaleableCountryEnum.GB.name()), "listing商品可售国家必须包含德国或英国");
            if (GoodsIsShowEnums.WAIT_AUDIT.getType().toString().equals(goodsInfoInput.getIsShow())) {
                //listing首次保存无需审批直接上架
                isListingFirstEdit = true;
                goodsInfoInput.setIsShow(GoodsIsShowEnums.SHELF.getType().toString());
            }
        }

        CheckUtils.check(goodsInfoInput.getUseType() != 1 && StringUtils.isBlank(goodsInfoInput.getItemCode()), ProductResultCode.ITEM_CODE_ERROR);
        CheckUtils.isEmpty(goodsInfoInput.getGoodsImages(), ProductResultCode.MAIN_IMAGE_ERROR);
        CheckUtils.isEmpty(goodsInfoInput.getDetailsImgs(), ProductResultCode.DETAIL_IMAGE_ERROR);

        GoodsInfoUtils.checkPropertyImage(goodsInfoInput.getProperties());
        //图片格式校验
        GoodsInfoUtils.checkImage(goodsInfoInput);

        checkIfContainsChinese(goodsInfoInput);


        //自动补足国家运费,默认取德国运费
        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.selectById(goods.getShopId());
        goodsInfoInput.setFaMerchantsApply(faMerchantsApply);

        //店铺数据冗余 && 海外仓数据兼容
        GoodsInfoUtils.extractedShopInfo(goodsInfoInput, faMerchantsApply);

        //海外仓取配置国家
        if (isTemplateGoods || !DeliveryTypeEnum.DIRECT.getCode().equals(faMerchantsApply.getDeliveryType())) {
            //所有普通商品更新时重新计算国家运费
            if (StringUtils.isNotEmpty(goodsInfoInput.getWeight()) && Double.parseDouble(goodsInfoInput.getWeight()) != 0) {
                CheckUtils.check(Double.parseDouble(goodsInfoInput.getWeight()) < 0, ProductResultCode.WEIGHT_ERROR);
                QueryFreight queryFreight = new QueryFreight();
                queryFreight.setWeight(goodsInfoInput.getWeight());
                queryFreight.setPackageSize(BeanCopyUtil.transform(goodsInfoInput.getPackageSize(), PackageSizeDTO.class));
                queryFreight.setCategoryId(goodsInfoInput.getCategoryId());
                queryFreight.setLogisticsProperty(goodsInfoInput.getLogisticsProperty());
                queryFreight.setShopId(goods.getShopId());
                queryFreight.setType(goods.getType());
                queryFreight.setGoodsId(goodsInfoInput.getId());
                Map<String, BigDecimal> goodsFreightMap = goodsFreightCoreService.countFreight(queryFreight);

                if (null != goodsFreightMap && !goodsFreightMap.isEmpty()) {

                    List<GoodsFreightVO> freightList = goodsFreightMap.entrySet().stream().map(stringBigDecimalEntry -> {
                        GoodsFreightVO freightVO = new GoodsFreightVO();
                        freightVO.setCode(stringBigDecimalEntry.getKey());
                        freightVO.setCurrentFreight(stringBigDecimalEntry.getValue());
                        return freightVO;
                    }).collect(Collectors.toList());
                    goodsInfoInput.setFreightList(freightList);
                    goodsInfoInput.setFreightGradientDto(queryFreight.getFreightGradientDto());
                } else {
                    goodsInfoInput.setFreightList(null);
                }
            } else {
                goodsInfoInput.setFreightList(null);
            }
            //非海外仓必填国家运费
//            CheckUtils.check(CollectionUtils.isEmpty(goodsInfoInput.getFreightList()), ProductResultCode.GOODS_FREIGHT_EMPTY);
            //默认运费取德国
            fillingFreight(goodsInfoInput);
        } else {
            if (CollectionUtils.isNotEmpty(goodsInfoInput.getFreightList())) {
                goodsInfoInput.getFreightList().forEach(goodsFreightVO -> {
                    CheckUtils.notNull(goodsFreightVO.getCurrentFreight(), ProductResultCode.GOODS_FREIGHT_PRICE_NULL);
                });
            }
        }
        //校验店铺主营类目
        if (!isTemplateGoods) {
            if (faMerchantsApply.getCategoryMainId() == null) {
                Object categoryMainSwitch = redisApi.get(PlatformConfigTypeEnum.CATEGORY_MAIN_SWITCH.name());
                CheckUtils.check(categoryMainSwitch != null && "1".equals(categoryMainSwitch.toString()), ProductResultCode.SHOP_MUST_BIND_CATEGORY_MAIN);
            } else {
                CategoryMain categoryMain = categoryMainService.getById(faMerchantsApply.getCategoryMainId());
                LogUtils.info(log, "categoryMain:{}", categoryMain);
                CheckUtils.notNull(categoryMain, ProductResultCode.CATEGORY_MAIN_NOT_EXIST);

                List<Long> allMainCategoryIds = com.google.common.collect.Lists.newArrayList();
                if (StringUtils.isNotBlank(categoryMain.getLeafCategoryIds())) {
                    Arrays.stream(categoryMain.getLeafCategoryIds().split(",")).map(Long::parseLong).forEach(allMainCategoryIds::add);
                }
                if (StringUtils.isNotBlank(categoryMain.getLeafSecondCategoryIds())) {
                    Arrays.stream(categoryMain.getLeafSecondCategoryIds().split(",")).map(Long::parseLong).forEach(allMainCategoryIds::add);
                }
                CheckUtils.check(CollectionUtils.isNotEmpty(allMainCategoryIds) && !allMainCategoryIds.contains(categoryId), ProductResultCode.CATEGORY_MAIN_NOT_INCLUDE);
            }
        }

        //判断属性必须要有属性值
        GoodsInfoUtils.isPropertyValues(goodsInfoInput);

        //检测sku限价信息
        if (faMerchantsApply != null && !faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.SELF_SUPPORT_SCM.getCode())) {
            skuLimitPriceCoreService.checkSkuLimitPrice(goodsInfoInput);
        }

        //判断是否满足建议降价
        fillUpdateReductionPrice(goodsInfoInput);

        List<Long> pathCategoryIds = Lists.newArrayList(categoryId);
        if (StringUtils.isNotBlank(category.getPids())) {
            pathCategoryIds.addAll(Arrays.stream(category.getPids().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }

        //尺码表/图 校验  1尺码表尺码图二选一 2必须维护尺码表
        Integer limitType = categoryCoreService.checkSizeIsNeed(categoryId);
        CheckUtils.check(!isTemplateGoods && limitType == 1 && goodsInfoInput.getSizeChartTemplateId() == null && StringUtils.isBlank(goodsInfoInput.getSizeImage()), ProductResultCode.SIZE_IMAGE_ERROR);
        CheckUtils.check(!isTemplateGoods && limitType == 2 && goodsInfoInput.getSizeChartTemplateId() == null, ProductResultCode.SIZE_CHART_TEMPLATE_MUST);

        //尺码表校验
        checkSizeChartTemplate(goodsInfoInput.getSizeChartTemplateId(), goodsInfoInput.getShopId(), pathCategoryIds);

        BigDecimal virtualDiscount = newAddGoodsCoreService.getVirtualDiscount(pathCategoryIds, goodsInfoInput.getShopId());
        goodsInfoInput.setDiscount(virtualDiscount);

        boolean updateNewProperty = false;

//        List<PropertyDetailInfoVO> propertyDetailInfoVOS = propertyDetailInfoCoreService.queryByCategoryId(categoryId);
        if (CollectionUtils.isNotEmpty(goodsInfoInput.getPropertyDetailInfoVOList())) {
            propertyGoodsInfoCoreService.checkPropertyDetails(goodsInfoInput.getPropertyGoodsInfoVOS(), goodsInfoInput.getPropertyDetailInfoVOList());
            updateNewProperty = true;
        }
        boolean finalUpdateNewProperty = updateNewProperty;

        //校验商品维护规范
        goodsStandardCoreService.checkGoodsStandard(BeanCopyUtil.transform(goodsInfoInput, ProductInfoInput.class));

        boolean categroyIdIsRequired = categoryCoreService.checkCategroyIdIsRequired(pathCategoryIds);
        CheckUtils.check(categroyIdIsRequired && (CollectionUtils.isEmpty(goodsInfoInput.getGoodsManualVOList()) || goodsInfoInput.getGoodsManualVOList().stream().filter(t -> Objects.nonNull(t.getCountryType())).noneMatch(t -> t.getCountryType() == 1)), ProductResultCode.GOODS_COUNTRY_MANUAL_REQUIRED);

//        CheckUtils.check((category.getType() != null && (category.getType() == 2 || category.getType() == 3)) && CollectionUtils.isEmpty(goodsInfoInput.getGoodsPartsVOList()), ProductResultCode.GOODS_PARTS_EMPTY);


        Boolean success = transactionTemplate.execute(transactionStatus -> {
            //更新商品类目表
            updateGoodsCategory(goodsInfoInput);
            //更新商品详情信息表
            updateGoodsExtDetail(goodsInfoInput);
            //更新商品图片
            updateGoodsImgs(goodsInfoInput);
            //规格修改
            insertGoodsPropertyInfo(goodsInfoInput);
            //更新sku相关信息
            updateSkuRelatedInfo(goodsInfoInput);
            //更新规格、sku信息
            updateGoodsPropertyInfo(goodsInfoInput);
            //更新product
            updateProduct(goodsInfoInput);
            // 更新物流标签商品关系
            updateGoodsLabel(goodsInfoInput);
            //更新商品表
            updateGoodsInfo(goodsInfoInput);
            //更新详情表
            updateDetailInfo(goodsInfoInput);
            //更新商品详情图片
            updateDetailImgs(goodsInfoInput);
            //更新商品实拍图
            updateRealShotImgs(goodsInfoInput);
            // updategoodsLogistics(goodsInfoInput);
            //更新商品新属性值
            if (finalUpdateNewProperty) {
                updatePropertyGoodsInfo(goodsInfoInput.getPropertyGoodsInfoVOS(), goodsInfoInput.getId());
            }
            //商品附属信息
            insertGoodsExtraInfo(goodsInfoInput.getGoodsExtraPropertyVOS(), goodsInfoInput.getId());

            //更新各国家商品操作手册&说明视频
            saveOrUpdateCountryGoodsManual(goodsInfoInput.getId(), goodsInfoInput.getGoodsManualVOList(),goodsInfoInput.getGoodsManualVideoList());

            saveOrUpdateGoodsParts(goodsInfoInput.getId(), goodsInfoInput.getGoodsPartsVOList());

            return Boolean.TRUE;
        });

        //如果是精选店铺修改商品详情 要加入精选店铺审核
        if (null != goodsInfoInput.getIsPickedShopGoods() && 1 == goodsInfoInput.getIsPickedShopGoods()) {
            log.info("检测是精选店铺商品修改并申请，商品id是.{}", goodsInfoInput.getId());
            goodsCoreService.syncPickedShopGoods(Collections.singletonList(goodsInfoInput.getId()));
        }

        success = success == null ? false : success;
        log.info("编辑商品信息 成功:{}", success);
        CheckUtils.check(!success, ProductResultCode.ADD_ERROR);

        // 全局规范同步es
        propertyHelper.propertyValueRecordSyncEs(PropertyHelper.specificationsDtoFunction.apply(goodsInfoInput.getProperties()));

        //商品运费关联逻辑
        filterFreight(goodsInfoInput);
        goodsFreightCoreService.saveOrUpdateFreight(goodsInfoInput);

        if (!isTemplateGoods && (DeliveryTypeEnum.SELF_SUPPORT.getCode().equals(faMerchantsApply.getDeliveryType())
                || DeliveryTypeEnum.SELF_SUPPORT_SCM.getCode().equals(faMerchantsApply.getDeliveryType()))) {
            GoodsSelfSupportInfo goodsSelfSupportInfo = goodsSelfSupportInfoService.lambdaQuery().eq(GoodsSelfSupportInfo::getGoodsId, goodsInfoInput.getId()).one();
            if (goodsSelfSupportInfo == null) {
                goodsSelfSupportInfo = new GoodsSelfSupportInfo();
                goodsSelfSupportInfo.setGoodsId(goodsInfoInput.getId());
                goodsSelfSupportInfo.setShopId(goods.getShopId());
                goodsSelfSupportInfo.setShopName(goods.getShopName());
                goodsSelfSupportInfo.setCreateTime(LocalDateTimeUtils.convertToDate(goods.getCreateTime()));
                goodsSelfSupportInfo.setType(0);

                goodsSelfSupportInfo.setGoodsName(goods.getName());
                goodsSelfSupportInfo.setCategoryId(goods.getCategoryId());
                goodsSelfSupportInfo.setMainImage(goods.getMainImage());
                goodsSelfSupportInfo.setApplyUser("system");
                goodsSelfSupportInfo.setPrincipal(faMerchantsApply.getPrincipal());
            }
            if (goodsSelfSupportInfo.getType() == 0) {
                goodsSelfSupportInfo.setArrival(goodsInfoInput.getArrival());
                goodsSelfSupportInfo.setLastEditTime(new Date());
                goodsSelfSupportInfoService.saveOrUpdate(goodsSelfSupportInfo);
            }
        }

        Object o = redisApi.get("sync_goods_image_switch");
        if (o == null || Integer.parseInt(o.toString()) == 0){
            List<UpdateImageVO> updateImageVOS = goodsInfoInput.getUpdateImageVOS();
            for (List<UpdateImageVO> subList : Lists.partition(updateImageVOS, 20)) {
                mqSender.send("UPDATE_IMAGE_IMPORT", JSON.toJSONString(subList));
            }
        }
        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(goodsInfoInput.getId());
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("更新商品信息(新)");
        goodsSyncModel.setSourceService("vp");
        mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.FIVE_SEC);
        log.info("mq发送成功,商品id:{}", goodsSyncModel.getGoodsId());

        log.info("发送操作事件 goodsId:{} type:{}", goodsInfoInput.getId(), OperationLogTypeEnums.getMsgByCode(goodsInfoInput.getOperationLogType()));
        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(goodsInfoInput.getId())
                .type(goodsInfoInput.getOperationLogType())
                .oldData(JSON.toJSONString(goodsInfoInput.getOldOperationData()))
                .newData(JSON.toJSONString(goodsInfoInput.getNewOperationData()))
                .content(OperationLogTypeEnums.getMsgByCode(goodsInfoInput.getOperationLogType()))
                .status(1)
                .user(getUserName());
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);

        //listing首次编辑
        if (isListingFirstEdit) {
            //listing首次保存无需审批直接上架
            UpdateLockLabelCondition updateLockLabelCondition = new UpdateLockLabelCondition();
            updateLockLabelCondition.setGoodsIds(Collections.singletonList(goodsInfoInput.getId()));
            updateLockLabelCondition.setLabels(Collections.singletonList(GoodsLockLabelTypEnums.LISTING_GOODS.getCode()));
            updateLockLabelCondition.setOperator("listing商品首次保存直接上架、加锁");
            goodsLockCoreService.addLockByApi(updateLockLabelCondition);
        }

        LogUtils.info(log, "useType:{}", goodsInfoInput.getUseType());
        if (goodsInfoInput.getUseType() == 1) {
            String traceId = MDC.get("traceId");
            executor.execute(() -> {
                try {
                    MDC.put("traceId", traceId);
                    listingGoodsBiz.syncListingFollowGoods(goodsInfoInput.getListingId());
                } catch (Exception e) {
                    log.error("【listing商品同步】error. ", e);
                } finally {
                    MDC.remove("traceId");
                }
            });
        }
        return Boolean.TRUE;
    }

    private void insertGoodsPropertyInfo(ProductInfoInput goodsInfoInput) {
        List<PropertyDTO> propertyList = goodsInfoInput.getProperties();
        if (CollectionUtils.isNotEmpty(propertyList)) {
            Map<Long, List<Long>> propValueMap = new LinkedHashMap<>();
            Map<Long, String> propValueNameMap = new LinkedHashMap<>();
            propertyList.forEach(propertyBO -> {
                propertyBO.setCategoryId(goodsInfoInput.getCategoryId());
                propertyInfoService.insertProperty(propertyBO);
                propertyInfoService.insertPropertyValue(goodsInfoInput, propertyBO, propValueMap, propValueNameMap);
            });

            goodsInfoInput.getNewOperationData().put("properties", propertyList);
        }
    }

    private boolean checkShowStatusForUpdate(Integer isShow) {
        return GoodsIsShowEnums.TAKE_OFF.getType().equals(isShow)
                || GoodsIsShowEnums.SHELF.getType().equals(isShow)
                || GoodsIsShowEnums.PROHIBIT.getType().equals(isShow)
                || GoodsIsShowEnums.SOLD_OUT.getType().equals(isShow)
                || GoodsIsShowEnums.AUDITING.getType().equals(isShow)
                || GoodsIsShowEnums.FREEZE.getType().equals(isShow);
    }

    private void updateSkuRelatedInfo(ProductInfoInput goodsInfoInput) {
        List<GoodsSkuPo> goodsSkuPos = goodsSkuHelper.changePropertyAndReturnSku(goodsInfoInput.getId(), goodsInfoInput);

        // TODO 暂时这样，不确定要啥
        List<GoodsSkuPropertyDto> skuDto = goodsSkuPos.stream().map(sku -> new GoodsSkuPropertyDto(sku.getId(), sku.getName())).collect(Collectors.toList());
        goodsInfoInput.setGoodsSkuPropertyList(skuDto);
    }

    /**
     * 更新国家商品说明书
     *
     */
    private void saveOrUpdateCountryGoodsManual(Long goodsId, List<GoodsManualVO> goodsManualVOList,List<GoodsManualVO> goodsManualVideoList) {
        List<GoodsManual> goodsManualList = goodsManualService.getGoodsManualListByGoodsId(goodsId);
        if(CollectionUtils.isNotEmpty(goodsManualList)){
            //编辑需要将之前删除
            goodsManualService.deleteByIds(goodsManualList.stream().map(GoodsManual::getId).collect(Collectors.toList()));
        }

        if (CollectionUtils.isEmpty(goodsManualVOList) && CollectionUtils.isEmpty(goodsManualVideoList)) {
            return;
        }

        List<GoodsManual> batchGoodsMaunualList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsManualVOList)) {
            for (GoodsManualVO goodsManualVO : goodsManualVOList) {
                if (Objects.isNull(goodsManualVO.getCountryType()) || Objects.isNull(goodsManualVO.getName()) || StringUtils.isBlank(goodsManualVO.getUrl())) {
                    log.error("修改国家商品说明书参数为空 goodsId:{}", goodsId);
                    continue;
                }
                GoodsManual insertGoodsManual = new GoodsManual();
                insertGoodsManual.setGoodsId(goodsId);
                insertGoodsManual.setType(1);
                insertGoodsManual.setCountryType(goodsManualVO.getCountryType());
                insertGoodsManual.setName(goodsManualVO.getName());
                insertGoodsManual.setUrl(goodsManualVO.getUrl());
                insertGoodsManual.setCreateUser(getUserName());
                insertGoodsManual.setUpdateUser(getUserName());
                insertGoodsManual.setCreateTime(new Date());
                insertGoodsManual.setUpdateTime(new Date());
                batchGoodsMaunualList.add(insertGoodsManual);
            }
        }

        if (CollectionUtils.isNotEmpty(goodsManualVideoList)) {
            List<GoodsManual> saveList = goodsManualVideoList.stream()
                    .filter(goodsManualVO -> StringUtils.isNotBlank(goodsManualVO.getUrl()))
                    .map(goodsManualVO -> {
                        GoodsManual goodsManual = new GoodsManual();
                        goodsManual.setGoodsId(goodsId);
                        goodsManual.setType(2);
                        goodsManual.setCountryType(0);
                        goodsManual.setName("说明视频");
                        goodsManual.setUrl(goodsManualVO.getUrl());
                        goodsManual.setCreateUser(getUserName());
                        goodsManual.setUpdateUser(getUserName());
                        goodsManual.setCreateTime(new Date());
                        goodsManual.setUpdateTime(new Date());
                        return goodsManual;
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(saveList)) {
                batchGoodsMaunualList.addAll(saveList);
            }
        }

        if(CollectionUtils.isNotEmpty(batchGoodsMaunualList)){
            goodsManualService.insertBatch(batchGoodsMaunualList);

            Map<Integer, Map<Long,String>> imageMap = new HashMap<>();
            Map<Long, String> idImgUrl = batchGoodsMaunualList.stream()
                    .filter(goodsManual -> !GoodsImageUtil.isVoghionImage(goodsManual.getUrl()))
                    .collect(Collectors.toMap(GoodsManual::getId, GoodsManual::getUrl, (v1, v2) -> v1));
            if (MapUtils.isNotEmpty(idImgUrl)) {
                imageMap.put(UpdateGoodsImageEnums.MANUAL.getGoodsType(), idImgUrl);
                UpdateGoodsImageVO updateGoodsImageVO = new UpdateGoodsImageVO();
                updateGoodsImageVO.setGoodsId(goodsId);
                updateGoodsImageVO.setSyncTime(new Date());
                updateGoodsImageVO.setGoodsImage(imageMap);
                log.info("NEW_UPDATE_IMAGE body : {}",JSON.toJSONString(updateGoodsImageVO));
                mqSender.send("NEW_UPDATE_IMAGE",JSON.toJSONString(updateGoodsImageVO));
            }
        }
    }


    private void saveOrUpdateGoodsParts(Long goodsId, List<GoodsPartsVO> goodsPartsVOList) {

        goodsPartsService.deleteByGoodsId(goodsId);
        if (CollectionUtils.isEmpty(goodsPartsVOList)) {
            return;
        }

        List<GoodsParts> partsList = goodsPartsVOList.stream().map(goodsPartsVO -> {
            CheckUtils.check(StringUtils.isNotBlank(goodsPartsVO.getNote()) && goodsPartsVO.getNote().length() > 300, PartsResultCode.NOTE_LEN_ERROR);
            GoodsParts goodsParts = new GoodsParts();
            goodsParts.setGoodsId(goodsId);
            goodsParts.setVpid(goodsPartsVO.getVpid());
            goodsParts.setType(goodsPartsVO.getType());
            goodsParts.setNote(goodsPartsVO.getNote());
            goodsParts.setMake(goodsPartsVO.getMake());
            goodsParts.setModel(goodsPartsVO.getModel());
            goodsParts.setSubmodel(goodsPartsVO.getSubmodel());
            goodsParts.setTrim(goodsPartsVO.getTrim());
            goodsParts.setYear(goodsPartsVO.getYear());
            goodsParts.setCountry(goodsPartsVO.getCountry());
            goodsParts.setEngine(goodsPartsVO.getEngine());
            goodsParts.setDriveType(goodsPartsVO.getDriveType());
            goodsParts.setCreateUser(getUserName());
            goodsParts.setUpdateUser(getUserName());
            goodsParts.setCreateTime(LocalDateTime.now());
            goodsParts.setUpdateTime(LocalDateTime.now());
            return goodsParts;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(partsList)) {
            goodsPartsService.insertBatch(partsList);
        }
    }

    private void syncListingFollowGoods(Long listingId, String traceId) {
        LogUtils.info(log, "listing模板更新，同步跟卖商品->syncListingFollowGoods listingId:{}, traceId:{}", listingId, traceId);
        if (listingId == null) {
            return;
        }
        ListingInfo listingInfo = listingInfoService.getById(listingId);
        Long listingGoodsId = listingInfo.getGoodsId();

        Goods listingGoods = goodsService.getById(listingGoodsId);
        CheckUtils.notNull(listingGoods, CustomResultCode.fill(ProductResultCode.GOODS_NOT_EXIST_EXT, listingGoodsId.toString()));

        Product listingProduct = productService.getById(listingGoods.getProductId());
        CheckUtils.notNull(listingProduct, CustomResultCode.fill(ProductResultCode.PRODUCT_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsItem> listingGoodsItems = goodsItemService.queryGoodsItemByGoodsId(listingGoodsId);
        CheckUtils.isEmpty(listingGoodsItems, CustomResultCode.fill(ProductResultCode.GOODS_ITEM_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsSkuPo> listingGoodsSkuPoList = goodsSkuService.findListByGoodsId(listingGoodsId);
//        Asserts.check(CollectionUtils.isNotEmpty(listingGoodsSkuPoList), "listing模板商品 新sku信息为空");

        List<GoodsPropertyRelevantPo> listingGoodsPropertyRelevantPoList = goodsPropertyRelevantService.findListByGoodsId(listingGoodsId);
//        Asserts.check(CollectionUtils.isNotEmpty(listingGoodsPropertyRelevantPoList), "listing模板商品 新规格信息为空");

        List<GoodsPropertyImgRelevantPo> listingGoodsPropertyImgRelevantPoList = goodsPropertyImgRelevantService.findListByGoodsId(listingGoodsId);
//        Asserts.check(CollectionUtils.isNotEmpty(listingGoodsPropertyImgRelevantPoList), "listing模板商品 新规格图片信息为空");

        List<GoodsPropertySkuRelevantPo> listingGoodsPropertySkuRelevantPoList = goodsPropertySkuRelevantService.findListByGoodsId(listingGoodsId);
//        Asserts.check(CollectionUtils.isNotEmpty(listingGoodsPropertySkuRelevantPoList), "listing模板商品 新sku规格关联信息为空");

        List<PropertyAssociationPo> listingGoodsPropertyAssociationPos = propertyAssociationService.findListByGoodsId(listingGoodsId);
//        Asserts.check(CollectionUtils.isNotEmpty(listingGoodsPropertyAssociationPos), "listing模板商品 新sku新老规格关联信息为空");

        List<ProductSku> listingProductSkuList = productSkuService.queryProductSkuByProductId(listingGoods.getProductId());
        CheckUtils.isEmpty(listingProductSkuList, CustomResultCode.fill(ProductResultCode.PRODUCT_SKU_NOT_EXIST, listingGoodsId.toString()));

        GoodsDetail listingGoodsDetail = goodsDetailService.queryGoodsDetailByGoodsId(listingGoodsId);
        CheckUtils.notNull(listingGoodsDetail, CustomResultCode.fill(ProductResultCode.GOODS_DETAIL_NOT_EXIST_EXT, listingGoodsId.toString()));

        GoodsExtDetail listingGoodsExtDetail = goodsExtDetailService.lambdaQuery().eq(GoodsExtDetail::getGoodsId, listingGoodsId).one();
        CheckUtils.notNull(listingGoodsExtDetail, CustomResultCode.fill(ProductResultCode.GOODS_EXT_DETAIL_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsExtDetailImg> listingGoodsExtDetailImgList = goodsExtDetailImgService.queryByGoodsId(listingGoodsId);
        CheckUtils.isEmpty(listingGoodsExtDetailImgList, CustomResultCode.fill(ProductResultCode.GOODS_EXT_DETAIL_IMAGE_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsImage> listingGoodsImages = goodsImageService.queryListByGoodsId(listingGoodsId);
        CheckUtils.isEmpty(listingGoodsImages, CustomResultCode.fill(ProductResultCode.GOODS_IMAGE_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsExtConfig> listingTagList = goodsExtConfigService.lambdaQuery()
                .eq(GoodsExtConfig::getGoodsId, listingGoodsId)
                .in(GoodsExtConfig::getTagId, Arrays.asList(40, 45, 50))
                .eq(GoodsExtConfig::getIsDel, 0)
                .list();

//        List<GoodsFreight> listingGoodsFreightList = goodsFreightService.queryByGoodsId(listingGoodsId);

//        Set<Long> listingPropertyIds = Sets.newHashSet();
        Set<Long> listingPropertyValueIds = Sets.newHashSet();
        List<String> listingPvalueDescList = Lists.newArrayList();
        for (GoodsItem goodsItem : listingGoodsItems) {
            for (String s : goodsItem.getPvalueStr().split(";")) {
                if (StringUtils.isBlank(s)) {
                    continue;
                }
//                listingPropertyIds.add(Long.parseLong(s.split(":")[0]));
                listingPropertyValueIds.add(Long.parseLong(s.split(":")[1]));
            }
            listingPvalueDescList.add(goodsItem.getPvalueDesc());
        }
        List<PropertyImgDetail> listingPropertyImgDetails = propertyInfoService.queryByImgList(listingProduct.getId(), Lists.newArrayList(listingPropertyValueIds));

        List<PropertyGoodsInfoVO> listingPropertyGoodsInfoVOS = propertyGoodsInfoCoreService.queryByGoodsId(listingGoodsId);

        Map<String, String> reversePvalueStrMap = Maps.newHashMap();
        for (GoodsItem goodsItem : listingGoodsItems) {
            List<String> pvalueList = Lists.newArrayList(goodsItem.getPvalueDesc().split("&gt;"));
            Collections.reverse(pvalueList);
            String reversePvalueDesc = StringUtils.join(pvalueList, "&gt;");

            reversePvalueStrMap.put(goodsItem.getPvalueDesc(), reversePvalueDesc);
            reversePvalueStrMap.put(reversePvalueDesc, goodsItem.getPvalueDesc());
        }

        List<Long> followGoodsIds = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getListingId, listingId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .list().stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        LogUtils.info(log, "syncListingFollowGoods followGoodsIds:{},traceId:{}", followGoodsIds, traceId);
        if (CollectionUtils.isEmpty(followGoodsIds)) {
            return;
        }

        List<Goods> goodsList = goodsService.lambdaQuery().in(Goods::getId, followGoodsIds).eq(Goods::getIsDel, 0).list();
        followGoodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());

        List<GoodsItem> allGoodsItemList = goodsItemService.queryGoodsIdsList(followGoodsIds);
        Map<Long, List<GoodsItem>> groupGoodsItemMap = allGoodsItemList.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

        List<GoodsSkuPo> allGoodsSkuPoList = goodsSkuService.lambdaQuery().in(GoodsSkuPo::getGoodsId, followGoodsIds).eq(GoodsSkuPo::getDeleted, false).list();
        Map<Long, List<GoodsSkuPo>> groupGoodsSkuMap = allGoodsSkuPoList.stream().collect(Collectors.groupingBy(GoodsSkuPo::getGoodsId));

        List<GoodsPropertyRelevantPo> allGoodsPropertyRelevantPoList = goodsPropertyRelevantService.lambdaQuery().in(GoodsPropertyRelevantPo::getGoodsId, followGoodsIds).eq(GoodsPropertyRelevantPo::getDeleted, false).list();
        Map<Long, List<GoodsPropertyRelevantPo>> groupGoodsPropertyRelevantMap = allGoodsPropertyRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertyRelevantPo::getGoodsId));

        List<GoodsPropertyImgRelevantPo> allGoodsPropertyImgRelevantPoList = goodsPropertyImgRelevantService.lambdaQuery().in(GoodsPropertyImgRelevantPo::getGoodsId, followGoodsIds).eq(GoodsPropertyImgRelevantPo::getDeleted, false).list();
        Map<Long, List<GoodsPropertyImgRelevantPo>> groupGoodsPropertyImgRelevantMap = allGoodsPropertyImgRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertyImgRelevantPo::getGoodsId));

        List<GoodsPropertySkuRelevantPo> allGoodsPropertySkuRelevantPoList = goodsPropertySkuRelevantService.lambdaQuery().in(GoodsPropertySkuRelevantPo::getGoodsId, followGoodsIds).eq(GoodsPropertySkuRelevantPo::getDeleted, false).list();
        Map<Long, List<GoodsPropertySkuRelevantPo>> groupGoodsPropertySkuRelevantMap = allGoodsPropertySkuRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsId));

        List<PropertyAssociationPo> allGoodsPropertyAssociationPos = propertyAssociationService.findListByGoodsIds(followGoodsIds);
        Map<Long, List<PropertyAssociationPo>> groupGoodsPropertyAssociationMap = allGoodsPropertyAssociationPos.stream().collect(Collectors.groupingBy(PropertyAssociationPo::getGoodsId));

        List<Long> productIds = goodsList.stream().map(Goods::getProductId).collect(Collectors.toList());
        List<Product> productList = Lists.newArrayList(productService.selectByIds(productIds));
        Map<Long, Product> productMap = productList.stream().collect(Collectors.toMap(Product::getId, Function.identity(), (v1, v2) -> v1));

        List<ProductSku> allProductSkuList = productSkuService.lambdaQuery().in(ProductSku::getProductId, productIds).eq(ProductSku::getIsDel, 0).list();
        Map<Long, List<ProductSku>> groupProductSkuMap = allProductSkuList.stream().collect(Collectors.groupingBy(ProductSku::getProductId));

        List<GoodsDetail> goodsDetailList = goodsDetailService.queryGoodsDetailByGoodsIds(followGoodsIds);
        Map<Long, GoodsDetail> goodsDetailMap = goodsDetailList.stream().collect(Collectors.toMap(GoodsDetail::getGoodsId, Function.identity(), (v1, v2) -> v1));

        List<GoodsExtDetail> goodsExtDetailList = goodsExtDetailService.queryGoodsExtDetailByGoodsIds(followGoodsIds);
        Map<Long, GoodsExtDetail> goodsExtDetailMap = goodsExtDetailList.stream().collect(Collectors.toMap(GoodsExtDetail::getGoodsId, Function.identity(), (v1, v2) -> v1));

        List<PropertyImgDetail> allPropertyImgDetailList = propertyImgDetailService.lambdaQuery().in(PropertyImgDetail::getSpuId, productIds).list();
        Map<Long, List<PropertyImgDetail>> groupPropertyImgDetailMap = allPropertyImgDetailList.stream().collect(Collectors.groupingBy(PropertyImgDetail::getSpuId));

        Map<Long, String> goodsLockLabelMap = goodsLockInfoService.lambdaQuery().in(GoodsLockInfo::getGoodsId, followGoodsIds).eq(GoodsLockInfo::getIsDel, 0).list()
                .stream().collect(Collectors.toMap(GoodsLockInfo::getGoodsId, GoodsLockInfo::getLabel, (v1, v2) -> v1));

        Map<Long, List<GoodsFreight>> goodsFreightGroupMap = goodsFreightService.queryByGoodsIds(followGoodsIds).stream().collect(Collectors.groupingBy(GoodsFreight::getGoodsId));

        Map<Long, List<Long>> goodsTagIdGroupMap = goodsExtConfigService.selectByGoodsIds(followGoodsIds).stream().collect(Collectors.groupingBy(GoodsExtConfig::getGoodsId, Collectors.mapping(GoodsExtConfig::getTagId, Collectors.toList())));

        List<Long> shopIds = goodsList.stream().map(Goods::getShopId).distinct().collect(Collectors.toList());
        Map<Long, Integer> shopDeliveryTypeMap = faMerchantsApplyCoreService.queryByShopIds(shopIds).stream().collect(Collectors.toMap(FaMerchantsApply::getId, FaMerchantsApply::getDeliveryType, (v1, v2) -> v1));

        GoodsEditDetailDto goodsEditDetailDto;
        GoodsEditPropertyDto goodsEditPropertyDto;

        //goodsExtConfig
        if (CollectionUtils.isNotEmpty(listingTagList)) {
            for (GoodsExtConfig goodsExtConfig : listingTagList) {
                BindTagDTO bindTagDTO = new BindTagDTO();
                bindTagDTO.setTagId(goodsExtConfig.getTagId());
                bindTagDTO.setGoodsIds(followGoodsIds);
                goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
            }
        }

        for (Goods goods : goodsList) {
            Long goodsId = goods.getId();
            LogUtils.info(log, "syncListingFollowGoods 开始同步商品id:{}, traceId:{}", goodsId, traceId);
            Integer deliveryType = shopDeliveryTypeMap.get(goods.getShopId());
            boolean isWarehouseOversea = deliveryType.equals(DeliveryTypeEnum.DIRECT.getCode());

            goodsEditDetailDto = new GoodsEditDetailDto();
            goodsEditPropertyDto = new GoodsEditPropertyDto();

            if (!goods.getName().equals(listingGoods.getName())) {
                goodsEditDetailDto.setOldName(goods.getName());
                goodsEditDetailDto.setNewName(listingGoods.getName());
            }
            if (!goods.getName().equals(listingGoods.getName())) {
                goodsEditDetailDto.setOldName(goods.getName());
                goodsEditDetailDto.setNewName(listingGoods.getName());
            }

            List<String> countryList = Lists.newArrayList(goods.getCountry().split(","));
            GoodsExtDetail goodsExtDetail = goodsExtDetailMap.get(goodsId);
            BigDecimal defaultDelivery = BigDecimal.ZERO;
            List<GoodsFreight> goodsFreightList = goodsFreightGroupMap.getOrDefault(goodsId, Lists.newArrayList());
            if (!isWarehouseOversea) {
                Map<String, GoodsFreight> goodsFreightMap = goodsFreightList.stream().collect(Collectors.toMap(GoodsFreight::getCode, Function.identity(), (v1, v2) -> v1));
                List<GoodsFreight> newFreightList = goodsFreightCoreService.countAndInitFreight(goods, listingGoodsExtDetail.getWeight(), goodsTagIdGroupMap.get(goodsId));

                if (CollectionUtils.isNotEmpty(newFreightList)) {
                    for (GoodsFreight newFreight : newFreightList) {
                        GoodsFreight goodsFreight = goodsFreightMap.get(newFreight.getCode());
                        if (goodsFreight == null) {
                            goodsFreight = BeanCopyUtil.transform(newFreight, GoodsFreight.class);
                            goodsFreightList.add(goodsFreight);
                        }
                        goodsFreight.setCurrentFreight(newFreight.getCurrentFreight());
                        goodsFreight.setUpdateTime(LocalDateTime.now());
                    }

                    goodsFreightList.forEach(goodsFreight -> {
                        if (!countryList.contains(goodsFreight.getCode())) {
                            goodsFreight.setIsDel(1);
                            goodsFreight.setUpdateTime(LocalDateTime.now());
                        }
                    });
                }else {
                    goodsFreightList.forEach(goodsFreight -> {
                        goodsFreight.setIsDel(1);
                        goodsFreight.setUpdateTime(LocalDateTime.now());
                    });
                }

                if (CollectionUtils.isNotEmpty(goodsFreightList)) {
                    goodsFreightService.saveOrUpdateBatch(goodsFreightList);

                    GoodsFreight deFreight = goodsFreightList.stream()
                            .filter(goodsFreight1 -> goodsFreight1.getIsDel() == 0 && goodsFreight1.getCode().equals(CountryEnums.DE.getCode()))
                            .findAny()
                            .orElse(null);
                    if (deFreight != null) {
                        defaultDelivery = deFreight.getCurrentFreight();
                    } else {
                        defaultDelivery = goodsFreightList.stream()
                                .filter(ex -> ex.getIsDel() == 0)
                                .map(GoodsFreight::getCurrentFreight)
                                .max(BigDecimal::compareTo)
                                .orElse(BigDecimal.ZERO);
                    }
                }
            }

            //product
            Product product = productMap.get(goods.getProductId());
            product.setName(product.getName());
            product.setCategoryId(goods.getCategoryId());
            productService.updateById(product);

            //product sku
            List<ProductSku> originalProductSkuList = groupProductSkuMap.get(product.getId());
            Map<String, ProductSku> pvalueDescProductSkuMap = originalProductSkuList.stream().collect(Collectors.toMap(ProductSku::getPvalueDesc, Function.identity(), (v1, v2) -> v1));


            List<ProductSku> updateProductSkuList = Lists.newArrayList();
            for (ProductSku listingProductSku : listingProductSkuList) {
                ProductSku productSku = pvalueDescProductSkuMap.get(listingProductSku.getPvalueDesc());
                if (productSku == null) {
                    String reversePvalueDesc = reversePvalueStrMap.get(listingProductSku.getPvalueDesc());
                    if (StringUtils.isNotBlank(reversePvalueDesc)) {
                        productSku = pvalueDescProductSkuMap.get(reversePvalueDesc);
                    }
                }
                if (productSku == null) {
                    productSku = new ProductSku();
                    BeanCopyUtil.copyProperties(listingProductSku, productSku);
                    productSku.setId(null);
                    productSku.setProductId(product.getId());
                    productSku.setCreateTime(LocalDateTime.now());
                }
                productSku.setName(listingProductSku.getName());
                productSku.setPvalueStr(listingProductSku.getPvalueStr());
                productSku.setPvalueDesc(listingProductSku.getPvalueDesc());
                updateProductSkuList.add(productSku);
            }

            List<Long> deleteSkuIds = new ArrayList<>();
            for (ProductSku oldProductSku : originalProductSkuList) {
                if (!listingPvalueDescList.contains(oldProductSku.getPvalueDesc())) {
                    String reversePvalueDesc = reversePvalueStrMap.get(oldProductSku.getPvalueDesc());
                    if (reversePvalueDesc == null || !listingPvalueDescList.contains(reversePvalueDesc)) {
                        deleteSkuIds.add(oldProductSku.getId());

                        oldProductSku.setIsDel(1);
                        updateProductSkuList.add(oldProductSku);
                    }
                }
            }
            LogUtils.info(log, "syncListingFollowGoods 同步商品int.1... id:{}, traceId:{}, updateProductSkuList.size:{}", goodsId, traceId, updateProductSkuList.size());
            productSkuService.saveOrUpdateBatch(updateProductSkuList);
            Map<String, Long> skuIdPvalueMap = updateProductSkuList.stream().collect(Collectors.toMap(ProductSku::getPvalueStr, ProductSku::getId, (v1, v2) -> v1));
//            LogUtils.info(log, "skuIdPvalueMap:{}", skuIdPvalueMap);

            //goodsItem
            List<GoodsItem> originalGoodsItemList = groupGoodsItemMap.getOrDefault(goodsId, Lists.newArrayList());
            Map<String, Long> originalSkuNameIdMap = originalGoodsItemList.stream().collect(Collectors.toMap(GoodsItem::getName, GoodsItem::getSkuId, (v1, v2) -> v1));
            log.info("【syncListingFollowGoods 同步商品】goodsId:{} originalSkuNameIdMap:{}", goodsId, JSONObject.toJSONString(originalSkuNameIdMap));

            //todo 同步新规格和新sku
            Map<String, Long> skuNameAndIdMap = syncNewPropertyAndSku(goodsId, originalSkuNameIdMap, groupGoodsSkuMap.getOrDefault(goodsId, Lists.newArrayList()), listingGoodsSkuPoList,
                    groupGoodsPropertyRelevantMap.getOrDefault(goodsId, Lists.newArrayList()), listingGoodsPropertyRelevantPoList,
                    groupGoodsPropertyImgRelevantMap.getOrDefault(goodsId, Lists.newArrayList()), listingGoodsPropertyImgRelevantPoList,
                    groupGoodsPropertySkuRelevantMap.getOrDefault(goodsId, Lists.newArrayList()), listingGoodsPropertySkuRelevantPoList,
                    groupGoodsPropertyAssociationMap.getOrDefault(goodsId, Lists.newArrayList()), listingGoodsPropertyAssociationPos);
            log.info("【syncListingFollowGoods 同步商品】goodsId:{} skuNameAndIdMap:{}", goodsId, JSONObject.toJSONString(skuNameAndIdMap));


            List<GoodsItemDTO> oldSkuList = BeanCopyUtil.transformList(originalGoodsItemList, GoodsItemDTO.class);
            Map<Long, List<Long>> oldPropertyValueMap = Maps.newHashMap();

            for (GoodsItem goodsItem : originalGoodsItemList) {
                for (String s : goodsItem.getPvalueStr().split(";")) {
                    String[] pvalueArr = s.split(":");
                    List<Long> valueList = oldPropertyValueMap.get(Long.parseLong(pvalueArr[0]));
                    if (CollectionUtils.isEmpty(valueList)) {
                        valueList = Lists.newArrayList();
                    }
                    valueList.add(Long.parseLong(pvalueArr[1]));
                    oldPropertyValueMap.put(Long.parseLong(pvalueArr[0]), valueList);
                }
            }

            Map<String, GoodsItem> originalPvalueStrGoodsItemMap = originalGoodsItemList.stream().collect(Collectors.toMap(GoodsItem::getPvalueDesc, Function.identity(), (v1, v2) -> v1));
            boolean changeProperty = false;

            if (CollectionUtils.isNotEmpty(deleteSkuIds)) {
                QueryWrapper<GoodsItem> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("sku_id", deleteSkuIds);
                goodsItemService.remove(queryWrapper);
                LogUtils.info(log, "syncListingFollowGoods 同步商品int.2... id:{}, traceId:{}, deleteSkuIds:{}", goodsId, traceId, deleteSkuIds);
                if (deleteSkuIds.size() == originalGoodsItemList.size()) {
                    List<String> originalGoodsItemPvalueDescList = originalGoodsItemList.stream().map(GoodsItem::getPvalueDesc).collect(Collectors.toList());
                    LogUtils.info(log, "syncListingFollowGoods 同步商品int.2... id:{}, traceId:{}, 删除所有sku ==> listingPvalueDescList:{};", goodsId, traceId, listingPvalueDescList);
                    LogUtils.info(log, "syncListingFollowGoods 同步商品int.2... id:{}, traceId:{}, 删除所有sku ==> originalGoodsItemPvalueDescList:{}", goodsId, traceId, originalGoodsItemPvalueDescList);
                }
            }

            List<GoodsItem> updateGoodsItemList = Lists.newArrayList();
            for (GoodsItem listingGoodsItem : listingGoodsItems) {
                GoodsItem goodsItem = originalPvalueStrGoodsItemMap.get(listingGoodsItem.getPvalueDesc());
                if (goodsItem == null) {
                    String reversePvalueDesc = reversePvalueStrMap.get(listingGoodsItem.getPvalueDesc());
                    if (StringUtils.isNotBlank(reversePvalueDesc)) {
                        goodsItem = originalPvalueStrGoodsItemMap.get(reversePvalueDesc);
                    }
                }
                if (goodsItem == null) {
                    goodsItem = new GoodsItem();
                    BeanCopyUtil.copyProperties(listingGoodsItem, goodsItem);
                    goodsItem.setId(null);
                    goodsItem.setGoodsId(goodsId);
                    goodsItem.setSkuId(MapUtils.isNotEmpty(skuNameAndIdMap) ? skuNameAndIdMap.get(goodsItem.getName()) : skuIdPvalueMap.get(listingGoodsItem.getPvalueStr()));
                    goodsItem.setCreateTime(LocalDateTime.now());
                    goodsItem.setStock(0L);

                    changeProperty = true;
                }

                goodsItem.setWeight(listingGoodsItem.getWeight());
                if (!isWarehouseOversea) {
                    goodsItem.setDefaultDelivery(defaultDelivery);
                    goodsItem.setPrice(goodsItem.getOrginalPrice().add(defaultDelivery));
                }
                goodsItem.setPvalueStr(listingGoodsItem.getPvalueStr());
                goodsItem.setPvalueDesc(listingGoodsItem.getPvalueDesc());
                goodsItem.setPropertyValueRecordSnap(listingGoodsItem.getPropertyValueRecordSnap().replace(String.valueOf(listingGoodsId), String.valueOf(goodsId)));
                goodsItem.setName(listingGoodsItem.getName());
                goodsItem.setSkuImage(listingGoodsItem.getSkuImage());
                goodsItem.setUpdateTime(new Date());
                updateGoodsItemList.add(goodsItem);
            }
//            LogUtils.info(log, "updateGoodsItemList:{}", updateGoodsItemList);
            goodsItemService.saveOrUpdateBatch(updateGoodsItemList);
            LogUtils.info(log, "syncListingFollowGoods 同步商品int.4... id:{}, traceId:{}, updateGoodsItemList.size:{}", goodsId, traceId, updateGoodsItemList.size());

            //goods
            updateGoodsItemList.stream().map(GoodsItem::getPrice).min(BigDecimal::compareTo).ifPresent(goods::setMinPrice);
            updateGoodsItemList.stream().map(GoodsItem::getPrice).max(BigDecimal::compareTo).ifPresent(goods::setMaxPrice);
            goods.setName(listingGoods.getName());
            goods.setMainImage(listingGoods.getMainImage());
            goods.setCategoryId(listingGoods.getCategoryId());
            goods.setLogisticsProperty(listingGoods.getLogisticsProperty());
            goods.setUpdateTime(LocalDateTime.now());
            goodsService.updateById(goods);


            List<GoodsItemDTO> newSkuList = BeanCopyUtil.transformList(updateGoodsItemList, GoodsItemDTO.class);
            Map<Long, List<Long>> newPropertyValueMap = Maps.newHashMap();

            try {
                for (GoodsItem goodsItem : updateGoodsItemList) {
                    for (String s : goodsItem.getPvalueStr().split(";")) {
                        String[] pvalueArr = s.split(":");
                        List<Long> valueList = newPropertyValueMap.get(Long.parseLong(pvalueArr[0]));
                        if (CollectionUtils.isEmpty(valueList)) {
                            valueList = Lists.newArrayList();
                        }
                        valueList.add(Long.parseLong(pvalueArr[1]));
                        newPropertyValueMap.put(Long.parseLong(pvalueArr[0]), valueList);
                    }
                }

                List<Long> oldPropertyIds = Lists.newArrayList(oldPropertyValueMap.keySet());
                List<Long> newPropertyIds = Lists.newArrayList(newPropertyValueMap.keySet());

                List<Long> oldPropertyValueIds = Lists.newArrayList();
                oldPropertyValueMap.values().forEach(oldPropertyValueIds::addAll);

                List<Long> newPropertyValueIds = Lists.newArrayList();
                newPropertyValueMap.values().forEach(newPropertyValueIds::addAll);

                List<Property> oldPropertyList = propertyInfoService.queryPropertyByIds(oldPropertyIds);
                Map<Long, List<PropertyValue>> oldPropertyValueGroupMap = propertyInfoService.queryPropertyValueByIds(oldPropertyValueIds).stream().collect(Collectors.groupingBy(PropertyValue::getPropertyId));
                List<PropertyDTO> oldPropertyData = oldPropertyList.stream().map(property -> {
                            PropertyDTO propertyDTO = BeanCopyUtil.transform(property, PropertyDTO.class);
                            List<PropertyValue> propertyValues = oldPropertyValueGroupMap.get(property.getId());
                            List<PropertyValueDTO> propertyValueDTOList = propertyValues.stream().map(propertyValue -> BeanCopyUtil.transform(propertyValue, PropertyValueDTO.class)).collect(Collectors.toList());
                            propertyDTO.setValues(propertyValueDTOList);
                            return propertyDTO;
                        }
                ).collect(Collectors.toList());

                List<Property> newPropertyList = propertyInfoService.queryPropertyByIds(newPropertyIds);
                Map<Long, List<PropertyValue>> newPropertyValueGroupMap = propertyInfoService.queryPropertyValueByIds(newPropertyValueIds).stream().collect(Collectors.groupingBy(PropertyValue::getPropertyId));
                List<PropertyDTO> newPropertyData = newPropertyList.stream().map(property -> {
                            PropertyDTO propertyDTO = BeanCopyUtil.transform(property, PropertyDTO.class);
                            List<PropertyValue> propertyValues = newPropertyValueGroupMap.get(property.getId());
                            List<PropertyValueDTO> propertyValueDTOList = propertyValues.stream().map(propertyValue -> BeanCopyUtil.transform(propertyValue, PropertyValueDTO.class)).collect(Collectors.toList());
                            propertyDTO.setValues(propertyValueDTOList);
                            return propertyDTO;
                        }
                ).collect(Collectors.toList());

                goodsEditPropertyDto.setOldPropertyList(oldPropertyData);
                goodsEditPropertyDto.setNewPropertyList(newPropertyData);

                goodsEditPropertyDto.setOldSkuList(oldSkuList);
                goodsEditPropertyDto.setNewSkuList(newSkuList);
            } catch (Exception e) {
                LogUtils.info(log, "listing模板更新同步跟卖商品时组装修改快照异常:{},", traceId, e);
            }

            //goodsDetail
            GoodsDetail goodsDetail = goodsDetailMap.get(goodsId);

            if (!goodsDetail.getDescription().equals(listingGoodsDetail.getDescription())) {
                goodsEditDetailDto.setOldDescription(goodsDetail.getDescription());
                goodsEditDetailDto.setNewDescription(listingGoodsDetail.getDescription());
            }

            goodsDetail.setTitle(listingGoodsDetail.getTitle());
            goodsDetail.setDescription(listingGoodsDetail.getDescription());
            goodsDetailService.updateById(goodsDetail);


            //goodsExtDetail
            if (!StringUtils.equals(goodsExtDetail.getItemCode(), listingGoodsExtDetail.getItemCode())) {
                goodsEditDetailDto.setOldItemCode(goodsExtDetail.getItemCode());
                goodsEditDetailDto.setNewItemCode(listingGoodsExtDetail.getItemCode());
            }

            goodsExtDetail.setWeight(listingGoodsExtDetail.getWeight());
            goodsExtDetail.setPackageSize(listingGoodsExtDetail.getPackageSize());
            goodsExtDetail.setItemCode(listingGoodsExtDetail.getItemCode());
//            goodsExtDetail.setSellerMemberId(listingGoodsExtDetail.getSellerMemberId());
//            goodsExtDetail.setSellerUserId(listingGoodsExtDetail.getSellerUserId());
//            goodsExtDetail.setCompanyName(listingGoodsExtDetail.getCompanyName());
            goodsExtDetailService.updateById(goodsExtDetail);


            //goodsExtDetailImg
            List<String> oldDetailImgList = goodsExtDetailImgService.queryByGoodsId(goodsId).stream().map(GoodsExtDetailImg::getImgUrl).collect(Collectors.toList());
            List<String> newDetailImgList = listingGoodsExtDetailImgList.stream().map(GoodsExtDetailImg::getImgUrl).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(CollectionUtils.disjunction(oldDetailImgList, newDetailImgList))) {
                goodsEditDetailDto.setOldDetailsImgs(oldDetailImgList);
                goodsEditDetailDto.setNewDetailsImgs(newDetailImgList);
            }

            goodsExtDetailImgService.deleteByGoodsId(goodsId);
            List<GoodsExtDetailImg> goodsExtDetailImgs = BeanCopyUtil.transformList(listingGoodsExtDetailImgList, GoodsExtDetailImg.class);
            goodsExtDetailImgs.forEach(goodsExtDetailImg -> {
                goodsExtDetailImg.setId(null);
                goodsExtDetailImg.setGoodsId(goodsId);
                goodsExtDetailImg.setCreateTime(LocalDate.now());
            });
            goodsExtDetailImgService.saveBatch(goodsExtDetailImgs);


            //goodsImage
            List<GoodsImage> oldGoodsImageList = goodsImageService.queryListByGoodsId(goodsId);
            List<String> oldGoodsImages = oldGoodsImageList.stream().filter(goodsImage -> goodsImage.getImageType() == 1).map(GoodsImage::getUrl).collect(Collectors.toList());
            List<String> oldGoodsVideos = oldGoodsImageList.stream().filter(goodsImage -> goodsImage.getImageType() == 2).map(GoodsImage::getUrl).collect(Collectors.toList());
            List<String> newGoodsImages = listingGoodsImages.stream().filter(goodsImage -> goodsImage.getImageType() == 1).map(GoodsImage::getUrl).collect(Collectors.toList());
            List<String> newGoodsVideos = listingGoodsImages.stream().filter(goodsImage -> goodsImage.getImageType() == 2).map(GoodsImage::getUrl).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(CollectionUtils.disjunction(oldGoodsImages, newGoodsImages))) {
                goodsEditDetailDto.setOldGoodsImages(oldGoodsImages);
                goodsEditDetailDto.setNewGoodsImages(newGoodsImages);
            }
            if (CollectionUtils.isNotEmpty(CollectionUtils.disjunction(oldGoodsVideos, newGoodsVideos))) {
                goodsEditDetailDto.setOldGoodsVideos(oldGoodsVideos);
                goodsEditDetailDto.setNewGoodsVideos(newGoodsVideos);
            }

            goodsImageService.deleteByGoodsId(goodsId);
            List<GoodsImage> goodsImageList = BeanCopyUtil.transformList(listingGoodsImages, GoodsImage.class);
            goodsImageList.forEach(goodsImage -> {
                goodsImage.setId(null);
                goodsImage.setGoodsId(goodsId);
                goodsImage.setCreateTime(LocalDateTime.now());
            });
            goodsImageService.saveBatch(goodsImageList);

            //propertyImgDetail
            List<PropertyImgDetail> propertyImgDetails = groupPropertyImgDetailMap.get(product.getId());
            if (CollectionUtils.isNotEmpty(propertyImgDetails)) {
                propertyImgDetailService.deleteByIds(propertyImgDetails.stream().map(PropertyImgDetail::getId).collect(Collectors.toList()));
            }

            List<PropertyImgDetail> propertyImgDetailList = BeanCopyUtil.transformList(listingPropertyImgDetails, PropertyImgDetail.class);
            for (PropertyImgDetail propertyImgDetail : propertyImgDetailList) {
                propertyImgDetail.setId(null);
                propertyImgDetail.setSpuId(product.getId());
                propertyImgDetail.setCreateTime(new Date());
            }
            if (CollectionUtils.isNotEmpty(propertyImgDetailList)) {
                propertyImgDetailService.saveBatch(propertyImgDetailList);
            }


            //propertyGoodsInfo
            if (CollectionUtils.isNotEmpty(listingPropertyGoodsInfoVOS)) {
                listingPropertyGoodsInfoVOS.forEach(propertyGoodsInfoVO -> propertyGoodsInfoVO.setGoodsId(goodsId));
                propertyGoodsInfoCoreService.saveOrUpdatePropertyGoods(listingPropertyGoodsInfoVOS);
            }

            GoodsEditInfo editInfo = new GoodsEditInfo();
            editInfo.setGoodsId(goodsId);
            editInfo.setStatus(0);
            editInfo.setCategoryId(goods.getCategoryId());
            editInfo.setShopId(goods.getShopId());
            editInfo.setShopName(goods.getShopName());

            editInfo.setAuditUser("system");
            editInfo.setAuditTime(LocalDateTime.now());
            editInfo.setStatus(1);

            editInfo.setApplyReason("所属listing模板修改,同步商品信息");
            editInfo.setApplyUser("system");
            editInfo.setApplyTime(LocalDateTime.now());
            editInfo.setIsDel(0);
            editInfo.setType(changeProperty ? GoodsEditTypeEnums.PROPERTY.getCode() : GoodsEditTypeEnums.DETAIL.getCode());
            editInfo.setContent(changeProperty ? JSON.toJSONString(goodsEditPropertyDto) : JSON.toJSONString(goodsEditDetailDto));

            List<String> specialTagList = Lists.newArrayList();
            specialTagList.add("listing");
            String lockLabel = goodsLockLabelMap.get(goodsId);
            if (lockLabel != null) {
                specialTagList = GoodsLockLabelTypEnums.listNamesByCodes(lockLabel);
            }
            editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
            editInfo.setUpdateTime(LocalDateTime.now());
            goodsEditInfoService.save(editInfo);

            GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
            goodsEditInfoDetail.setEditId(editInfo.getId());
            goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
            goodsEditInfoDetail.setContent(changeProperty ? JSON.toJSONString(goodsEditPropertyDto) : JSON.toJSONString(goodsEditDetailDto));
            goodsEditInfoDetailService.save(goodsEditInfoDetail);

            LogUtils.info(log, "syncListingFollowGoods 结束同步商品id:{}, traceId:{}", goodsId, traceId);
            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(goodsId);
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("listing模板更新同步跟卖商品");
            goodsSyncModel.setSourceService("vp");
            mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.ONE_MIN);
            log.info("mq发送成功,商品id:{}, traceId:{}", goodsSyncModel.getGoodsId(), traceId);

            GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                    .traceId(traceId)
                    .goodsId(goodsId)
                    .type(OperationLogTypeEnums.UPDATE_GOODS_FROM_SYNC_LISTING)
                    .content(OperationLogTypeEnums.UPDATE_GOODS_FROM_SYNC_LISTING.getDesc())
                    .status(1)
                    .user("system");
            mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
        }
    }

    @NotNull
    private Map<String, Long> syncNewPropertyAndSku(Long goodsId, Map<String, Long> originalSkuNameIdMap,
                                                    List<GoodsSkuPo> originalSkuPoList, List<GoodsSkuPo> listingGoodsSkuPoList,
                                                    List<GoodsPropertyRelevantPo> originalPropertyRelevantPoList, List<GoodsPropertyRelevantPo> listingGoodsPropertyRelevantPoList,
                                                    List<GoodsPropertyImgRelevantPo> originalImgRelevantPoList, List<GoodsPropertyImgRelevantPo> listingGoodsPropertyImgRelevantPoList,
                                                    List<GoodsPropertySkuRelevantPo> originalPropertySkuRelevantPoList, List<GoodsPropertySkuRelevantPo> listingGoodsPropertySkuRelevantPoList,
                                                    List<PropertyAssociationPo> originalPropertyAssociationList, List<PropertyAssociationPo> propertyAssociationList) {
        if (CollectionUtils.isEmpty(listingGoodsSkuPoList) || CollectionUtils.isEmpty(listingGoodsPropertyRelevantPoList) || CollectionUtils.isEmpty(listingGoodsPropertySkuRelevantPoList) || CollectionUtils.isEmpty(propertyAssociationList)) {
            return Maps.newHashMap();
        }

        Map<Long, GoodsPropertyRelevantPo> goodsPropertyRelevantMap = originalPropertyRelevantPoList.stream().collect(Collectors.toMap(GoodsPropertyRelevantPo::getGlobalPropertyValueId, Function.identity(), (v1, v2) -> v1));

        List<GoodsPropertyRelevantPo> updatePropertyRelevantList = Lists.newArrayList();
        for (GoodsPropertyRelevantPo listingGoodsPropertyRelevantPo : listingGoodsPropertyRelevantPoList) {
            GoodsPropertyRelevantPo goodsPropertyRelevantPo = goodsPropertyRelevantMap.get(listingGoodsPropertyRelevantPo.getGlobalPropertyValueId());
            if (goodsPropertyRelevantPo == null) {
                goodsPropertyRelevantPo = BeanCopyUtil.transform(listingGoodsPropertyRelevantPo, GoodsPropertyRelevantPo.class);
                goodsPropertyRelevantPo.setId(null);
                goodsPropertyRelevantPo.setGoodsId(goodsId);
                goodsPropertyRelevantPo.setCreateTime(LocalDateTime.now());
            }

            goodsPropertyRelevantPo.setMainProperty(listingGoodsPropertyRelevantPo.getMainProperty());
            goodsPropertyRelevantPo.setNameSort(listingGoodsPropertyRelevantPo.getNameSort());
            goodsPropertyRelevantPo.setValueSort(listingGoodsPropertyRelevantPo.getValueSort());
            goodsPropertyRelevantPo.setPropertyAliasName(listingGoodsPropertyRelevantPo.getPropertyAliasName());
            goodsPropertyRelevantPo.setPropertyValueAliasName(listingGoodsPropertyRelevantPo.getPropertyValueAliasName());
            goodsPropertyRelevantPo.setUpdateTime(LocalDateTime.now());
            updatePropertyRelevantList.add(goodsPropertyRelevantPo);
        }

        List<Long> existIds = updatePropertyRelevantList.stream().map(GoodsPropertyRelevantPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        originalPropertyRelevantPoList.stream()
                .filter(propertyRelevantPo -> !existIds.contains(propertyRelevantPo.getId()))
                .peek(propertyRelevantPo -> {
                    propertyRelevantPo.setDeleted(true);
                    propertyRelevantPo.setUpdateTime(LocalDateTime.now());
                })
                .forEach(updatePropertyRelevantList::add);
        if (CollectionUtils.isNotEmpty(updatePropertyRelevantList)) {
            goodsPropertyRelevantService.saveOrUpdateBatch(updatePropertyRelevantList);
        }

        Map<Long, Long> goodsPropertyRelevantIdMappingMap = Maps.newHashMap();
        for (GoodsPropertyRelevantPo myProperty : updatePropertyRelevantList) {
            if (myProperty.getDeleted()) {
                continue;
            }
            for (GoodsPropertyRelevantPo listingProperty : listingGoodsPropertyRelevantPoList) {
                if (listingProperty.getGlobalPropertyValueId().equals(myProperty.getGlobalPropertyValueId())) {
                    goodsPropertyRelevantIdMappingMap.put(listingProperty.getId(), myProperty.getId());
                }
            }
        }


        List<GoodsPropertyImgRelevantPo> newImgPoList = Lists.newArrayList();
        Map<Long, List<GoodsPropertyImgRelevantPo>> originalPropertyRelevantImgGroupMap = originalImgRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertyImgRelevantPo::getGoodsPropertyRelevantId));
        Map<Long, List<GoodsPropertyImgRelevantPo>> listingPropertyRelevantImgGroupMap = listingGoodsPropertyImgRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertyImgRelevantPo::getGoodsPropertyRelevantId));
        for (Map.Entry<Long, List<GoodsPropertyImgRelevantPo>> listingEntry : listingPropertyRelevantImgGroupMap.entrySet()) {
            Long listingPropertyId = listingEntry.getKey();
            List<GoodsPropertyImgRelevantPo> listingImgList = listingEntry.getValue();
            Long myPropertyId = goodsPropertyRelevantIdMappingMap.get(listingPropertyId);
            if (myPropertyId == null) {
                continue;
            }
            List<GoodsPropertyImgRelevantPo> originalImgList = originalPropertyRelevantImgGroupMap.get(myPropertyId);
            Map<String, GoodsPropertyImgRelevantPo> imgUrlPoMap = CollectionUtils.isEmpty(originalImgList) ? Maps.newHashMap() : originalImgList.stream().collect(Collectors.toMap(GoodsPropertyImgRelevantPo::getImgUrl, Function.identity(), (v1, v2) -> v1));

            listingImgList.stream().map(listingImgPo -> {
                GoodsPropertyImgRelevantPo myImgPo = imgUrlPoMap.get(listingImgPo.getImgUrl());
                if (myImgPo == null) {
                    myImgPo = BeanCopyUtil.transform(listingImgPo, GoodsPropertyImgRelevantPo.class);
                    myImgPo.setId(null);
                    myImgPo.setGoodsId(goodsId);
                    myImgPo.setCreateTime(LocalDateTime.now());
                    myImgPo.setGoodsPropertyRelevantId(myPropertyId);
                }
                myImgPo.setSort(listingImgPo.getSort());
                myImgPo.setUpdateTime(LocalDateTime.now());
                return myImgPo;
            }).forEach(newImgPoList::add);
        }

        List<Long> existImgIds = newImgPoList.stream().map(GoodsPropertyImgRelevantPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        originalImgRelevantPoList.stream()
                .filter(imgPo -> !existImgIds.contains(imgPo.getId()))
                .peek(imgPo -> {
                    imgPo.setDeleted(true);
                    imgPo.setUpdateTime(LocalDateTime.now());
                })
                .forEach(newImgPoList::add);
        if (CollectionUtils.isNotEmpty(newImgPoList)) {
            goodsPropertyImgRelevantService.saveOrUpdateBatch(newImgPoList);
        }

        List<PropertyAssociationPo> newPropertyAssociationList = Lists.newArrayList();
        Map<Long, List<PropertyAssociationPo>> originalPropertyAssociationGroupMap = originalPropertyAssociationList.stream().collect(Collectors.groupingBy(PropertyAssociationPo::getPropertyRelevantId));
        log.info("【Listing商品创建】originalPropertyAssociationGroupMap：{}", JSONObject.toJSONString(originalPropertyAssociationGroupMap));
        Map<Long, List<PropertyAssociationPo>> listingPropertyAssociationGroupMap = propertyAssociationList.stream().collect(Collectors.groupingBy(PropertyAssociationPo::getPropertyRelevantId));
        log.info("【Listing商品创建】listingPropertyAssociationGroupMap：{}", JSONObject.toJSONString(listingPropertyAssociationGroupMap));
        for (Map.Entry<Long, List<PropertyAssociationPo>> listingEntry : listingPropertyAssociationGroupMap.entrySet()) {
            Long listingPropertyId = listingEntry.getKey();
            List<PropertyAssociationPo> listingPropertyAssociationList = listingEntry.getValue();
            log.info("【Listing商品创建】listingPropertyAssociationList：{}", JSONObject.toJSONString(listingPropertyAssociationList));
            Long myPropertyId = goodsPropertyRelevantIdMappingMap.get(listingPropertyId);
            if (myPropertyId == null) {
                continue;
            }

            List<PropertyAssociationPo> originalPAList = originalPropertyAssociationGroupMap.get(myPropertyId);
            Map<Long, Map<Long, PropertyAssociationPo>> paMap = CollectionUtils.isEmpty(originalPAList) ? Maps.newHashMap() : originalPAList.stream().collect(Collectors.groupingBy(PropertyAssociationPo::getPropertyId, Collectors.mapping(Function.identity(), Collectors.toMap(PropertyAssociationPo::getPropertyValueId, Function.identity()))));;
            log.info("【Listing商品创建】paMap：{}", JSONObject.toJSONString(paMap));

            listingPropertyAssociationList.stream().map(listingPaPo -> {
                PropertyAssociationPo myPaPo = null;
                Map<Long, PropertyAssociationPo> associationPoMap = paMap.get(listingPaPo.getPropertyId());
                if (MapUtils.isNotEmpty(associationPoMap)) {
                    myPaPo = associationPoMap.get(listingPaPo.getPropertyValueId());
                }

                if (myPaPo == null) {
                    myPaPo = BeanCopyUtil.transform(listingPaPo, PropertyAssociationPo.class);
                    myPaPo.setId(null);
                    myPaPo.setGoodsId(goodsId);
                    myPaPo.setCreateTime(LocalDateTime.now());
                    myPaPo.setPropertyRelevantId(myPropertyId);
                }

                myPaPo.setUpdateTime(LocalDateTime.now());
                return myPaPo;
            }).forEach(newPropertyAssociationList::add);
        }

        List<Long> existPropertyAssociationIds = newPropertyAssociationList.stream().map(PropertyAssociationPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        originalPropertyAssociationList.stream()
                .filter(po -> !existPropertyAssociationIds.contains(po.getId()))
                .peek(po -> {
                    po.setDeleted(true);
                    po.setUpdateTime(LocalDateTime.now());
                })
                .forEach(newPropertyAssociationList::add);
        log.info("【Listing商品创建】newPropertyAssociationList：{}", JSONObject.toJSONString(newPropertyAssociationList));
        if (CollectionUtils.isNotEmpty(newPropertyAssociationList)) {
            propertyAssociationService.saveOrUpdateBatch(newPropertyAssociationList);
        }

        List<GoodsSkuPo> updateSkuList = Lists.newArrayList();

        Map<Long, GoodsSkuPo> originalSkuMap = originalSkuPoList.stream().collect(Collectors.toMap(GoodsSkuPo::getId, Function.identity()));
        log.info("【Listing商品创建】originalSkuMap:{}", JSONObject.toJSONString(originalSkuMap));
        for (GoodsSkuPo listingGoodsSkuPo : listingGoodsSkuPoList) {
            Long goodsSkuId = getGoodsSkuId(listingGoodsSkuPo, listingGoodsPropertyRelevantPoList, listingGoodsPropertySkuRelevantPoList, originalPropertyRelevantPoList, originalPropertySkuRelevantPoList);
            log.info("【Listing商品创建】goodsSkuId:{}", goodsSkuId);
            GoodsSkuPo goodsSkuPo = originalSkuMap.get(goodsSkuId);

            if (goodsSkuPo == null) {
                goodsSkuPo = BeanCopyUtil.transform(listingGoodsSkuPo, GoodsSkuPo.class);
                goodsSkuPo.setId(originalSkuNameIdMap.get(listingGoodsSkuPo.getName()));
                goodsSkuPo.setGoodsId(goodsId);
                log.info("new goodsSku ==> name:{}, setId:{}",listingGoodsSkuPo.getName(),goodsSkuPo.getId());

                String skuCode = String.format("%d-%d", goodsId, SnowflakeUtil.generateId());
                goodsSkuPo.setSkuCode(skuCode);

//                StringBuilder builder = new StringBuilder();
//                List<Long> skuPropertyRelevantStrList = Arrays.stream(goodsSkuPo.getSkuPropertyRelevantStr().split("_")).map(Long::parseLong).collect(Collectors.toList());
//                for (int i = 0; i < skuPropertyRelevantStrList.size(); i++) {
//                    if (i == 0) {
//                        builder.append(goodsId);
//                    } else {
//                        builder.append("_").append(goodsPropertyRelevantIdMappingMap.get(skuPropertyRelevantStrList.get(i)));
//                    }
//                }
//                goodsSkuPo.setSkuPropertyRelevantStr(builder.toString());
                goodsSkuPo.setCreateTime(LocalDateTime.now());
                goodsSkuPo.setUpdateTime(LocalDateTime.now());
            }

            // 有可能顺序变了，所以名字变一下
            goodsSkuPo.setName(listingGoodsSkuPo.getName());
            goodsSkuPo.setSkuImage(listingGoodsSkuPo.getSkuImage());

            updateSkuList.add(goodsSkuPo);
        }

        List<Long> existSkuIds = updateSkuList.stream().map(GoodsSkuPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        originalSkuPoList.stream()
                .filter(skuPo -> !existSkuIds.contains(skuPo.getId()))
                .peek(skuPo -> {
                    skuPo.setDeleted(1);
                    skuPo.setUpdateTime(LocalDateTime.now());
                })
                .forEach(updateSkuList::add);
        if (CollectionUtils.isNotEmpty(updateSkuList)) {
            goodsSkuService.saveOrUpdateBatch(updateSkuList);
        }
        Map<String, Long> skuIdPvalueMap = updateSkuList.stream().filter(po -> po.getDeleted() == 0).collect(Collectors.toMap(GoodsSkuPo::getName, GoodsSkuPo::getId, (v1, v2) -> v1));

        Map<Long, Long> goodsSkuIdMappingMap = Maps.newHashMap();
        for (GoodsSkuPo listingSku : listingGoodsSkuPoList) {
            for (GoodsSkuPo mySku : updateSkuList) {
                if (mySku.getDeleted() == 1) {
                    continue;
                }
                if (listingSku.getName().equals(mySku.getName())) {
                    goodsSkuIdMappingMap.put(listingSku.getId(), mySku.getId());
                }
            }
        }

        Map<Long, String> skuIdAndCodeMap = updateSkuList.stream().filter(sku -> sku.getDeleted() == 0).collect(Collectors.toMap(GoodsSkuPo::getId, GoodsSkuPo::getSkuCode, (v1, v2) -> v2));


        List<GoodsPropertySkuRelevantPo> updatePropertySkuRelevantList = Lists.newArrayList();
        Map<Long, List<GoodsPropertySkuRelevantPo>> originalSkuPropertyRelevantGroupMap = originalPropertySkuRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuId));
        for (Map.Entry<Long, List<GoodsPropertySkuRelevantPo>> entry : listingGoodsPropertySkuRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuId)).entrySet()) {
            Long listingSkuId = entry.getKey();
            List<GoodsPropertySkuRelevantPo> listingPropertySkuRelevantList = entry.getValue();

            Long mySkuId = goodsSkuIdMappingMap.get(listingSkuId);
            if (mySkuId == null) {
                continue;
            }

            String mySkuCode = skuIdAndCodeMap.get(mySkuId);

            List<GoodsPropertySkuRelevantPo> myGoodsPropertySkuRelevantPos = originalSkuPropertyRelevantGroupMap.get(mySkuId);
            Map<Long, GoodsPropertySkuRelevantPo> propertySkuRelevantMap = CollectionUtils.isEmpty(myGoodsPropertySkuRelevantPos)
                    ? Maps.newHashMap() : myGoodsPropertySkuRelevantPos.stream().collect(Collectors.toMap(GoodsPropertySkuRelevantPo::getGoodsPropertyRelevantId, Function.identity(), (v1, v2) -> v1));

            for (GoodsPropertySkuRelevantPo listingSkuRelevantPo : listingPropertySkuRelevantList) {
                Long myPropertyRelevantId = goodsPropertyRelevantIdMappingMap.get(listingSkuRelevantPo.getGoodsPropertyRelevantId());
                GoodsPropertySkuRelevantPo myPropertySkuRelevantPo = propertySkuRelevantMap.get(myPropertyRelevantId);
                if (myPropertySkuRelevantPo == null) {
                    myPropertySkuRelevantPo = BeanCopyUtil.transform(listingSkuRelevantPo, GoodsPropertySkuRelevantPo.class);
                    myPropertySkuRelevantPo.setId(null);
                    myPropertySkuRelevantPo.setGoodsId(goodsId);
                    myPropertySkuRelevantPo.setGoodsSkuId(mySkuId);
                    myPropertySkuRelevantPo.setGoodsSkuCode(mySkuCode);
                    myPropertySkuRelevantPo.setGoodsPropertyRelevantId(myPropertyRelevantId);
                    myPropertySkuRelevantPo.setCreateTime(LocalDateTime.now());
                }
                myPropertySkuRelevantPo.setSort(listingSkuRelevantPo.getSort());
                myPropertySkuRelevantPo.setUpdateTime(LocalDateTime.now());
                updatePropertySkuRelevantList.add(myPropertySkuRelevantPo);
            }
        }
        List<Long> existPropertySkuRelevantIds = updatePropertySkuRelevantList.stream().map(GoodsPropertySkuRelevantPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        originalPropertySkuRelevantPoList.stream()
                .filter(po -> !existPropertySkuRelevantIds.contains(po.getId()))
                .peek(po -> {
                    po.setDeleted(true);
                    po.setUpdateTime(LocalDateTime.now());
                }).forEach(updatePropertySkuRelevantList::add);
        if (CollectionUtils.isNotEmpty(updatePropertySkuRelevantList)) {
            goodsPropertySkuRelevantService.saveOrUpdateBatch(updatePropertySkuRelevantList);
        }

        Map<Long, List<GoodsPropertySkuRelevantPo>> skuPropertySkuRelevantGroupMap = updatePropertySkuRelevantList.stream()
                .filter(po -> !po.getDeleted())
                .collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuId));
        for (GoodsSkuPo goodsSkuPo : updateSkuList) {
            if (goodsSkuPo.getDeleted() == 1) {
                continue;
            }
            List<GoodsPropertySkuRelevantPo> goodsPropertySkuRelevantPos = skuPropertySkuRelevantGroupMap.get(goodsSkuPo.getId());
            String skuPropertyRelevantStr = goodsId + "_" +
                    goodsPropertySkuRelevantPos.stream()
                            .sorted((o1, o2) -> o1.getSort() - o2.getSort())
                            .map(po -> po.getId().toString())
                            .collect(Collectors.joining("_"));
            goodsSkuPo.setSkuPropertyRelevantStr(skuPropertyRelevantStr);
        }
        if (CollectionUtils.isNotEmpty(updateSkuList)) {
            goodsSkuService.updateBatchByIdAndGoodsId(updateSkuList);
        }
        return skuIdPvalueMap;
    }

    private Long getGoodsSkuId(GoodsSkuPo listingGoodsSkuPo, List<GoodsPropertyRelevantPo> listingGoodsPropertyRelevantPoList, List<GoodsPropertySkuRelevantPo> listingGoodsPropertySkuRelevantPoList,
                               List<GoodsPropertyRelevantPo> originalPropertyRelevantPoList, List<GoodsPropertySkuRelevantPo> originalPropertySkuRelevantPoList) {
        List<Long> goodsPropertyRelevantIds = listingGoodsPropertySkuRelevantPoList.stream().filter(po -> Objects.equals(listingGoodsSkuPo.getId(), po.getGoodsSkuId())).map(GoodsPropertySkuRelevantPo::getGoodsPropertyRelevantId).distinct().collect(Collectors.toList());
        log.info("【Listing商品创建】goodsPropertyRelevantIds:{}", JSONObject.toJSONString(goodsPropertyRelevantIds));
        if (CollectionUtils.isEmpty(goodsPropertyRelevantIds)) {
            return null;
        }

        List<GoodsPropertyRelevantPo> goodsPropertyRelevantPos = listingGoodsPropertyRelevantPoList.stream().filter(po -> goodsPropertyRelevantIds.contains(po.getId())).collect(Collectors.toList());
        log.info("【Listing商品创建】goodsPropertyRelevantPos:{}", JSONObject.toJSONString(goodsPropertyRelevantPos));
        if (CollectionUtils.isEmpty(goodsPropertyRelevantPos)) {
            return null;
        }

        // 如果遇到全局id正好一致的有可能会有问题，所以需要过滤删除的
        Map<Long, Map<Long, Long>> globalPropertyGroupMap = originalPropertyRelevantPoList.stream().filter(po -> Boolean.FALSE.equals(po.getDeleted())).collect(Collectors.groupingBy(GoodsPropertyRelevantPo::getGlobalPropertyId, Collectors.mapping(Function.identity(), Collectors.toMap(GoodsPropertyRelevantPo::getGlobalPropertyValueId, GoodsPropertyRelevantPo::getId))));
        log.info("【Listing商品创建】globalPropertyGroupMap:{}", JSONObject.toJSONString(globalPropertyGroupMap));
        List<Long> originalPropertyRelevantIdList = goodsPropertyRelevantPos.stream().map(po -> {
            Map<Long, Long> map = globalPropertyGroupMap.get(po.getGlobalPropertyId());
            if (MapUtils.isEmpty(map)) {
                return null;
            }

            return map.get(po.getGlobalPropertyValueId());
        }).filter(Objects::nonNull).collect(Collectors.toList());

        Map<Long, List<Long>> propertySkuRelevantMap = originalPropertySkuRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuId, Collectors.mapping(GoodsPropertySkuRelevantPo::getGoodsPropertyRelevantId, Collectors.toList())));
        log.info("【Listing商品创建】propertySkuRelevantMap:{}", JSONObject.toJSONString(propertySkuRelevantMap));
        for (Map.Entry<Long, List<Long>> entry : propertySkuRelevantMap.entrySet()) {
            if (CollectionUtils.isEqualCollection(entry.getValue(), originalPropertyRelevantIdList)) {
                return entry.getKey();
            }
        }

        return null;
    }

    public void triggerSyncListingFollowGoods(Long listingId) {
        syncListingFollowGoods(listingId, getTraceId());
    }

    private void fillListingUseType(ProductInfoInput goodsInfoInput) {
        Long goodsId = goodsInfoInput.getId();
        ListingInfo listingInfo = getListingInfo(goodsId);
        if (listingInfo != null) {
            goodsInfoInput.setUseType(1);
            goodsInfoInput.setListingId(listingInfo.getId());
            return;
        }

        ListingFollowGoods listingFollowGoods = getListingFollowGoods(goodsId);
        if (listingFollowGoods != null) {
            goodsInfoInput.setUseType(2);
            goodsInfoInput.setListingId(listingFollowGoods.getListingId());
        }
    }

    private void fillListingUseType(ItemGoodsInfoInputDto dto, ItemGoodsInfoExtBo extBo) {
        Long goodsId = dto.getId();
        ListingInfo listingInfo = getListingInfo(goodsId);
        if (listingInfo != null) {
            dto.setUseType(1);
            extBo.setListingId(listingInfo.getId());
            return;
        }

        ListingFollowGoods listingFollowGoods = getListingFollowGoods(goodsId);
        if (listingFollowGoods != null) {
            dto.setUseType(2);
            extBo.setListingId(listingFollowGoods.getListingId());
        }
    }

    private ListingFollowGoods getListingFollowGoods(Long goodsId) {
        return listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getGoodsId, goodsId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .one();
    }

    private ListingInfo getListingInfo(Long goodsId) {
        return listingInfoService.lambdaQuery()
                .eq(ListingInfo::getGoodsId, goodsId)
                .eq(ListingInfo::getIsDel, 0)
                .one();
    }


    /**
     * 尺码表校验
     */
    private void checkSizeChartTemplate(Long sizeChartTemplateId, Long shopId, List<Long> pathCategoryIds) {
        if (sizeChartTemplateId != null) {
            SizeChartTemplate sizeChartTemplate = sizeChartTemplateService.getById(sizeChartTemplateId);
            CheckUtils.check(sizeChartTemplate == null || sizeChartTemplate.getIsDel() == 1 || sizeChartTemplate.getType() == 1, ProductResultCode.SIZE_CHART_TEMPLATE_NOT_EXIST);
            assert sizeChartTemplate != null;
            CheckUtils.check(!pathCategoryIds.contains(sizeChartTemplate.getCategoryId()), ProductResultCode.SIZE_CHART_TEMPLATE_CATEGORY_NOT_BIND_GOODS);

            //校验就近原则
            List<SizeChartTemplate> allShopTemplates = sizeChartTemplateService.lambdaQuery()
                    .eq(SizeChartTemplate::getType, SizeTemplateTypeEnum.SHOP.getCode())
                    .in(SizeChartTemplate::getCategoryId, pathCategoryIds)
                    .eq(SizeChartTemplate::getShopId, shopId)
                    .eq(SizeChartTemplate::getIsDel, 0)
                    .list();
            if (CollectionUtils.isEmpty(allShopTemplates) || !allShopTemplates.stream().map(SizeChartTemplate::getId).collect(Collectors.toList()).contains(sizeChartTemplateId)) {
                CheckUtils.check(true, CustomResultCode.fill(ProductResultCode.SIZE_CHART_TEMPLATE_NOT_BELONG_SHOP, sizeChartTemplateId.toString()));
            }

            Map<Long, List<SizeChartTemplate>> categoryTemplateGroupMap = allShopTemplates.stream().collect(Collectors.groupingBy(SizeChartTemplate::getCategoryId));
            pathCategoryIds.sort((o1, o2) -> (int) (o2 - o1));
            for (Long pathCategoryId : pathCategoryIds) {
                List<SizeChartTemplate> sizeChartTemplateList = categoryTemplateGroupMap.get(pathCategoryId);
                if (CollectionUtils.isNotEmpty(sizeChartTemplateList)) {
                    if (!sizeChartTemplateList.stream().map(SizeChartTemplate::getId).collect(Collectors.toList()).contains(sizeChartTemplateId)) {
                        CheckUtils.check(true, ProductResultCode.SIZE_CHART_TEMPLATE_MUST_BIND_SHOP_LAST_LEVEL_CATEGORY);
                    }
                    break;
                }
            }
        }
    }

    public void insertGoodsExtraInfo(List<GoodsExtraPropertyVO> goodsExtraPropertyVOS, Long goodsId) {
        if (CollectionUtils.isEmpty(goodsExtraPropertyVOS)) {
            return;
        }
        List<GoodsExtraProperty> goodsExtraProperties = JSONObject.parseArray(JSONObject.toJSONString(goodsExtraPropertyVOS), GoodsExtraProperty.class);
        GoodsExtraVO vo = new GoodsExtraVO();
        vo.setGoodsExtraPropertyList(goodsExtraProperties);
        vo.setGoodsIdsStr(String.valueOf(goodsId));
        goodsExtraCoreService.addGoodsExtra(vo);
    }


    /**
     * 过滤不存在的国家的运费
     */
    private void filterFreight(ProductInfoInput goodsInfoInput) {
        if (CollectionUtils.isEmpty(goodsInfoInput.getFreightList())) {
            return;
        }
        List<String> allCountryCodes = countryService.getAllCode();
        List<GoodsFreightVO> filterFreight = goodsInfoInput.getFreightList().stream().filter(goodsFreightVO -> allCountryCodes.contains(goodsFreightVO.getCode())).collect(Collectors.toList());
        goodsInfoInput.setFreightList(filterFreight);

        if (MapUtils.isNotEmpty(goodsInfoInput.getFreightGradientDto())) {
            List<String> freightCountryList = Lists.newArrayList(goodsInfoInput.getFreightGradientDto().keySet());
            freightCountryList.removeAll(allCountryCodes);
            if (CollectionUtils.isNotEmpty(freightCountryList)) {
                for (String code : freightCountryList) {
                    goodsInfoInput.getFreightGradientDto().remove(code);
                }
            }
        }
    }

    private String redisGetUsername() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String voghionToken = request.getHeader("clientInfo");
        // 查询redis
        String vohionTokenStr = redisApi.get(voghionToken);
        // 查看api权限
        JSONObject json = JSONObject.parseObject(vohionTokenStr);
        JSONObject user = JSONObject.parseObject(json.getJSONObject("user").toJSONString());
        Object username = user.get("username");
        return String.valueOf(username);

    }


    /**
     * 更新商品表
     *
     * @param goodsInfoInput
     * @return
     */
    private void updateGoodsInfo(ProductInfoInput goodsInfoInput) {
        Goods originalGoods = goodsService.getById(goodsInfoInput.getId());
        String mainImage = originalGoods.getMainImage();
        //更新商品表
        Goods goods = new Goods();
        goods.setId(goodsInfoInput.getId());

        if (CollectionUtils.isNotEmpty(goodsInfoInput.getGoodsImages())) {
            goods.setMainImage(goodsInfoInput.getGoodsImages().get(0));
        } else if (StringUtils.isNotEmpty(goodsInfoInput.getMainImage())) {
            goods.setMainImage(goodsInfoInput.getMainImage());
        }

        String name = goodsInfoInput.getName();
        //是否更新了价格
        List<GoodsItemDTO> skuList = goodsInfoInput.getSkuInfo();

        Optional<BigDecimal> maxPrice = skuList.stream().map(GoodsItemDTO::getPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
        Optional<BigDecimal> minPrice = skuList.stream().map(GoodsItemDTO::getPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);

//        Optional<BigDecimal> maxMarketPrice = skuList.stream().map(GoodsItemDTO::getMarketPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
//        Optional<BigDecimal> minMarketPrice = skuList.stream().map(GoodsItemDTO::getMarketPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);

        Optional<BigDecimal> maxGrouponPrice = skuList.stream().map(GoodsItemDTO::getGrouponPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
        Optional<BigDecimal> minGrouponPrice = skuList.stream().map(GoodsItemDTO::getGrouponPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);

        Optional<BigDecimal> maxOrginalPrice = skuList.stream().map(GoodsItemDTO::getOrginalPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
        Optional<BigDecimal> minOrginalPrice = skuList.stream().map(GoodsItemDTO::getOrginalPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);

        Optional<BigDecimal> maxOrginalMarketPrice = skuList.stream().map(GoodsItemDTO::getOrginalMarketPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
        Optional<BigDecimal> minOrginalMarketPrice = skuList.stream().map(GoodsItemDTO::getOrginalMarketPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);

//        Optional<BigDecimal> maxOriginalGrouponPrice = skuList.stream().map(GoodsItemDTO::getOriginalGrouponPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
//        Optional<BigDecimal> minOriginalGrouponPrice = skuList.stream().map(GoodsItemDTO::getOriginalGrouponPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);

        Optional<BigDecimal> maxCostPrice = skuList.stream().map(GoodsItemDTO::getCostPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
        Optional<BigDecimal> minCostPrice = skuList.stream().map(GoodsItemDTO::getCostPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);

        maxPrice.ifPresent(goods::setMaxPrice);
        minPrice.ifPresent(goods::setMinPrice);
        maxGrouponPrice.ifPresent(goods::setMaxGrouponPrice);
        minGrouponPrice.ifPresent(goods::setMinGrouponPrice);
//        maxMarketPrice.ifPresent(goods::setMaxMarketPrice);
//        minMarketPrice.ifPresent(goods::setMinMarketPrice);
        goods.setMaxMarketPrice(countGoodsVirtualPrice(goods.getMaxPrice(), goodsInfoInput.getDiscount()));
        goods.setMinMarketPrice(countGoodsVirtualPrice(goods.getMinPrice(), goodsInfoInput.getDiscount()));


        goods.setMinCostPrice(minCostPrice.orElse(BigDecimal.ZERO));
        goods.setMaxCostPrice(maxCostPrice.orElse(BigDecimal.ZERO));

        //goods.setOrginalMaxPrice(maxOrginalPrice.orElse(goods.getMaxPrice()));
        goods.setOrginalMinPrice(minOrginalPrice.orElse(goods.getMinPrice()));
        goods.setOrginalMaxMarketPrice(maxOrginalMarketPrice.orElse(goods.getMaxMarketPrice()));
        goods.setOrginalMinMarketPrice(minOrginalMarketPrice.orElse(goods.getMinMarketPrice()));


        if (StringUtils.isEmpty(name)) {
            goods.setName("");
        } else {
            goods.setName(GoodsInfoUtils.formatGoodsName(name));
        }
        goods.setLabelId(goodsInfoInput.getLabelId());
        goods.setLabelName(goodsInfoInput.getLabelName());
        goods.setFreightTemplateId(goodsInfoInput.getFreightTemplateId());
        goods.setCategoryId(goodsInfoInput.getCategoryId());
        goods.setIsShow(goodsInfoInput.getIsShow());
        goods.setUpdateTime(LocalDateTime.now());
        if (goodsInfoInput.getLogisticsProperty() != null) {
            goods.setLogisticsProperty(goodsInfoInput.getLogisticsProperty());
        }
        if (null != goodsInfoInput.getOrginalSales()) {
            goods.setOrginalSales(goodsInfoInput.getOrginalSales());
        }
//        goods.setSales(goodsInfoInput.getSales());
        if (null != goodsInfoInput.getAppChannel() && goodsInfoInput.getAppChannel() > 0) {
            goods.setAppChannel(goodsInfoInput.getAppChannel());
        }
        if (StringUtils.isNotEmpty(goodsInfoInput.getCountry())) {
            // 获取有效的物流属性国家
            GoodsEffectiveCountryDTO goodsEffectiveCountryDTO = new GoodsEffectiveCountryDTO();
            goodsEffectiveCountryDTO.setGoodsId(goodsInfoInput.getId());
            goodsEffectiveCountryDTO.setShopId(goodsInfoInput.getShopId());
            goodsEffectiveCountryDTO.setLogisticsProperty(goodsInfoInput.getLogisticsProperty());
            goodsEffectiveCountryDTO.setCountry(goodsInfoInput.getCountry());
            goodsEffectiveCountryDTO.setCategoryId(goodsInfoInput.getCategoryId());
            String effectiveCountry = logisticsPropertyConfigCoreService.getGoodsEffectiveCountries(goodsEffectiveCountryDTO);
            goods.setCountry(effectiveCountry);
        }
        goods.setTag(goodsInfoInput.getTag());
        goods.setSortValue(null != goodsInfoInput.getSortValue() && goodsInfoInput.getSortValue() >= 0 ? goodsInfoInput.getSortValue() : 1000);
        goods.setSizeChartTemplateId(goodsInfoInput.getSizeChartTemplateId());

        Long goodsId = goods.getId();

        //只检测上下架商品的图片是否修改
        log.info("当前同盾更新开关的状态是:{}", marketingSwitchCenter.getTongDunUpdateSwitch().equals("1") ? "开始" : "关闭");
        if (!goodsInfoInput.isTemplateGoods() && marketingSwitchCenter.getTongDunUpdateSwitch().equals("1")) {
            FaMerchantsApply faMerchantsApply = null;
            Long shopId = originalGoods.getShopId();
            if (Objects.nonNull(shopId)) {
                faMerchantsApply = faMerchantsApplyService.selectById(shopId);
                CheckUtils.check(Objects.nonNull(faMerchantsApply) && 0 == faMerchantsApply.getIsDisable(), ProductResultCode.SHOP_IS_LOCK);
            }
            Integer isWhite = 0;
            if (faMerchantsApply != null && faMerchantsApply.getTongDunWhiteList() != null) {
                isWhite = faMerchantsApply.getTongDunWhiteList();
            }
            List<GoodsItem> goodsItems = goodsItemService.queryGoodsItemByGoodsId(goodsId);
            if (CollectionUtils.isNotEmpty(goodsItems)) {
                List<String> skuImage = goodsItems.stream().map(GoodsItem::getSkuImage).distinct().collect(Collectors.toList());
                goodsInfoInput.setGoodsItemImages(skuImage);
            }
            log.info("当前店铺shopId是:{},同盾是否白名单:{}", shopId, isWhite == 1 ? "是" : "否");
            if (isWhite != 1) {
                if ("0".equals(originalGoods.getIsShow()) || "1".equals(originalGoods.getIsShow()) || "4".equals(originalGoods.getIsShow())) {
                    //判断是否修改主图
//                if (StringUtils.isNotEmpty(mainImage) && (!mainImage.equals(goods.getMainImage()))) {
                    log.info("原来的主图:" + mainImage);
                    log.info("修改后的主图:" + goods.getMainImage());
                    log.info("更新商品 商品id为:" + goods.getId() + "状态（is_show）是:" + goods.getIsShow() + "修改主图,触发同盾");

                    TongDunGoodsImagesVO tongDunGoodsImagesVO = new TongDunGoodsImagesVO();
                    tongDunGoodsImagesVO.setGoodsId(goodsId);
                    tongDunGoodsImagesVO.setShopId(originalGoods.getShopId());
                    tongDunGoodsImagesVO.setGoodsName(goods.getName());
                    tongDunGoodsImagesVO.setCategoryId(goods.getCategoryId());
                    tongDunGoodsImagesVO.setGoodsImage(getImageUrl(goodsInfoInput));
                    tongDunGoodsImagesVO.setIsType(0L);
                    tongDunGoodsImagesVO.setTdType(1);
                    log.info("发送同盾检测图片Mq" + JSON.toJSONString(tongDunGoodsImagesVO));
                    mqSender.send("ADD_TONG_DUN_GOODS", JSON.toJSONString(tongDunGoodsImagesVO));
//                }
                }
            }
        }

        if (goodsInfoInput.getIsReductionPrice() != null && !goodsInfoInput.getIsReductionPrice()) {
            goods.setIsReduction(0);
            goodsPriceReductionCoreService.updateReductionRecord(goodsInfoInput.getId(), goodsInfoInput.getShopName());
        }

        boolean b = goodsService.lambdaUpdate()
                .set(goods.getMainImage() != null, Goods::getMainImage, goods.getMainImage())
                .set(goods.getMinPrice() != null, Goods::getMinPrice, goods.getMinPrice())
                .set(goods.getMaxPrice() != null, Goods::getMaxPrice, goods.getMaxPrice())
                .set(goods.getMinMarketPrice() != null, Goods::getMinMarketPrice, goods.getMinMarketPrice())
                .set(goods.getMaxMarketPrice() != null, Goods::getMaxMarketPrice, goods.getMaxMarketPrice())
                .set(goods.getMinCostPrice() != null, Goods::getMinCostPrice, goods.getMinCostPrice())
                .set(goods.getMaxCostPrice() != null, Goods::getMaxCostPrice, goods.getMaxCostPrice())
                .set(goods.getOrginalMinPrice() != null, Goods::getOrginalMinPrice, goods.getOrginalMinPrice())
                .set(goods.getOrginalMaxPrice() != null, Goods::getOrginalMaxPrice, goods.getOrginalMaxPrice())
                .set(goods.getOrginalMinMarketPrice() != null, Goods::getOrginalMinMarketPrice, goods.getOrginalMinMarketPrice())
                .set(goods.getOrginalMaxMarketPrice() != null, Goods::getOrginalMaxMarketPrice, goods.getOrginalMaxMarketPrice())
                .set(goods.getName() != null, Goods::getName, goods.getName())
                .set(goods.getLabelId() != null, Goods::getLabelId, goods.getLabelId())
                .set(goods.getLabelName() != null, Goods::getLabelName, goods.getLabelName())
                .set(goods.getFreightTemplateId() != null, Goods::getFreightTemplateId, goods.getFreightTemplateId())
                .set(goods.getCategoryId() != null, Goods::getCategoryId, goods.getCategoryId())
                .set(goods.getIsShow() != null, Goods::getIsShow, goods.getIsShow())
                .set(goods.getLogisticsProperty() != null, Goods::getLogisticsProperty, goods.getLogisticsProperty())
                .set(goods.getOrginalSales() != null, Goods::getOrginalSales, goods.getOrginalSales())
                .set(goods.getAppChannel() != null, Goods::getAppChannel, goods.getAppChannel())
                .set(goods.getCountry() != null, Goods::getCountry, goods.getCountry())
                .set(goods.getTag() != null, Goods::getTag, goods.getTag())
                .set(goods.getSortValue() != null, Goods::getSortValue, goods.getSortValue())
                .set(goods.getIsReduction() != null, Goods::getIsReduction, goods.getIsReduction())
                .set(Goods::getSizeChartTemplateId, goods.getSizeChartTemplateId())
                .set(Goods::getUpdateTime, LocalDateTime.now())
                .eq(Goods::getId, goodsInfoInput.getId())
                .update();
        CheckUtils.check(!b, ProductResultCode.UPDATE_ERROR);
        LogUtils.info(log, "goods表更新结束 isShow:{}, minPrice:{}, maxPrice:{}, logisticsProperty:{}, country:{}", goods.getIsShow(), goods.getMinPrice(), goods.getMaxPrice(), goods.getLogisticsProperty(), goods.getCountry());

        if (goodsInfoInput.getUseType() == 1) {
            LogUtils.info(log, "当前商品为listing模板");
            ListingInfo listingInfo = listingInfoService.lambdaQuery().eq(ListingInfo::getGoodsId, goodsInfoInput.getId()).one();
            if (listingInfo != null) {
                LogUtils.info(log, "当前商品为listing模板，开始更新 listingId:{}", listingInfo.getId());
                listingInfo.setMinPrice(goods.getMinPrice());
                listingInfo.setMaxPrice(goods.getMaxPrice());
                listingInfo.setCategoryId(goods.getCategoryId());
                listingInfo.setName(goods.getName());
                listingInfo.setUpdateTime(LocalDateTime.now());
                listingInfo.setUpdateUser(getUserName());

                //基准价(动态调价)
//                BigDecimal vatRate;
//                GoodsVatConfig goodsVatConfig = goodsVatConfigService.lambdaQuery().eq(GoodsVatConfig::getGoodsId, goodsId).eq(GoodsVatConfig::getEffectStatus, 1).one();
//                if (goodsVatConfig != null) {
//                    vatRate = goodsVatConfig.getVat();
//                    LogUtils.info(log, "存在商品级vat配置 源goodsId:{}, vatRate:{}", goodsId, vatRate);
//                } else {
//                    List<Long> negativeTagIds = goodsExtConfigService.lambdaQuery()
//                            .eq(GoodsExtConfig::getGoodsId, goodsId)
//                            .in(GoodsExtConfig::getTagId, Arrays.asList(40, 45, 50))
//                            .eq(GoodsExtConfig::getIsDel, 0)
//                            .list()
//                            .stream().map(GoodsExtConfig::getTagId).collect(Collectors.toList());
//
//                    GoodsExtConfigModel goodsExtConfigModel = new GoodsExtConfigModel();
//                    goodsExtConfigModel.setTagIds(negativeTagIds);
//                    Integer deliveryType = goodsInfoInput.getFaMerchantsApply() != null ? goodsInfoInput.getFaMerchantsApply().getDeliveryType() : 0;
//                    vatRate = goodsEsService.getVatRate(
//                            new VatCondition().setGoodsId(goodsId)
//                                    .setCountry(SaleableCountryEnum.DE.name())
//                                    .setShopId(goods.getShopId())
//                                    .setGoodsExtConfigModel(goodsExtConfigModel)
//                                    .setDeliveryType(deliveryType)
//                                    .setCategoryId(goods.getCategoryId())
//                    );
//                    LogUtils.info(log, "走默认vat配置 源goodsId:{}, vatRate:{}，含负向标签:{}", goodsId, vatRate, CollectionUtils.isNotEmpty(negativeTagIds));
//                }
                if (listingInfo.getBaseVatRate() != null) {
                    listingInfo.setBasePrice(goodsEsService.getVatPrice(listingInfo.getBaseVatRate(), goods.getMaxPrice()));
                }
                listingInfoService.updateById(listingInfo);
            }
        }

        List<ChanceGoodsTemplate> chanceGoodsTemplateList = chanceGoodsTemplateService.lambdaQuery()
                .eq(ChanceGoodsTemplate::getGoodsId, goodsInfoInput.getId())
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .list();
        if (CollectionUtils.isNotEmpty(chanceGoodsTemplateList)) {
            for (ChanceGoodsTemplate chanceGoodsTemplate : chanceGoodsTemplateList) {
                chanceGoodsTemplate.setMinPrice(goods.getMinPrice());
                chanceGoodsTemplate.setMaxPrice(goods.getMaxPrice());
                chanceGoodsTemplate.setCategoryId(goods.getCategoryId());
                chanceGoodsTemplate.setGoodsName(goods.getName());
                chanceGoodsTemplate.setUpdateTime(new Date());
                chanceGoodsTemplate.setUpdateUser(getUserName());
            }
            chanceGoodsTemplateService.updateBatchById(chanceGoodsTemplateList);
        }

        //待转化图片
        goodsInfoInput.getUpdateImageVOS().add(new UpdateImageVO(goods.getId(), goods.getMainImage(), "goodsService", System.currentTimeMillis()));

        initGoodsOperationLog(goodsInfoInput, originalGoods, goods);

    }

    private String getImageUrl(ProductInfoInput goodsInfoInput) {
        List<String> goodsImages = goodsInfoInput.getGoodsImages();
        List<String> detailsImgs = goodsInfoInput.getDetailsImgs();
        List<String> goodsItemImages = goodsInfoInput.getGoodsItemImages();
        List<String> res = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsImages)) {
            res.addAll(goodsImages);
        }
        if (CollectionUtils.isNotEmpty(detailsImgs)) {
            res.addAll(detailsImgs);
        }
        if (CollectionUtils.isNotEmpty(goodsItemImages)) {
            int n = goodsItemImages.size();
            if (n <= 2) {
                res.addAll(goodsItemImages);
            } else {
                List<String> tmp = new ArrayList<>();
                int index1 = (int) (Math.random() * n);
                int index2;
                do {
                    index2 = (int) (Math.random() * n);
                } while (index1 == index2);
                tmp.add(goodsItemImages.get(index1));
                tmp.add(goodsItemImages.get(index2));
                res.addAll(tmp);
            }
        }
        return JSON.toJSONString(res);
    }

    private void initGoodsOperationLog(ProductInfoInput goodsInfoInput, Goods oldGoods, Goods newGoods) {
        JSONObject operationOldData = goodsInfoInput.getOldOperationData();
        operationOldData.put("id", oldGoods.getId());
        operationOldData.put("name", oldGoods.getName());
        operationOldData.put("categoryId", oldGoods.getCategoryId());
        operationOldData.put("mainImage", oldGoods.getMainImage());
        operationOldData.put("freightTemplateId", oldGoods.getFreightTemplateId());
        operationOldData.put("country", oldGoods.getCountry());
        operationOldData.put("logisticsProperty", oldGoods.getLogisticsProperty());
        operationOldData.put("sizeChartTemplateId", oldGoods.getSizeChartTemplateId());
        operationOldData.put("productId", oldGoods.getProductId());
        operationOldData.put("tag", oldGoods.getTag());
        operationOldData.put("isDel", oldGoods.getIsDel());
        operationOldData.put("isShow", oldGoods.getIsShow());
        operationOldData.put("isLock", oldGoods.getIsLock());
        operationOldData.put("status", oldGoods.getStatus());
        operationOldData.put("minMarketPrice", oldGoods.getMinMarketPrice());
        operationOldData.put("maxMarketPrice", oldGoods.getMaxMarketPrice());
        operationOldData.put("minCostPrice", oldGoods.getMinCostPrice());
        operationOldData.put("maxCostPrice", oldGoods.getMaxCostPrice());
        operationOldData.put("minPrice", oldGoods.getMinPrice());
        operationOldData.put("maxPrice", oldGoods.getMaxPrice());
        operationOldData.put("minGrouponPrice", oldGoods.getMinGrouponPrice());
        operationOldData.put("maxGrouponPrice", oldGoods.getMaxGrouponPrice());

        JSONObject operationNewData = goodsInfoInput.getNewOperationData();
        operationNewData.put("id", newGoods.getId());
        operationNewData.put("name", newGoods.getName());
        operationNewData.put("categoryId", newGoods.getCategoryId());
        operationNewData.put("mainImage", newGoods.getMainImage());
        operationNewData.put("freightTemplateId", newGoods.getFreightTemplateId());
        operationNewData.put("country", newGoods.getCountry());
        operationNewData.put("logisticsProperty", newGoods.getLogisticsProperty());
        operationNewData.put("sizeChartTemplateId", newGoods.getSizeChartTemplateId());
        operationNewData.put("productId", newGoods.getProductId());
        operationNewData.put("tag", newGoods.getTag());
        operationNewData.put("isDel", newGoods.getIsDel());
        operationNewData.put("isShow", newGoods.getIsShow());
        operationNewData.put("isLock", newGoods.getIsLock());
        operationNewData.put("status", newGoods.getStatus());
        operationNewData.put("minMarketPrice", newGoods.getMinMarketPrice());
        operationNewData.put("maxMarketPrice", newGoods.getMaxMarketPrice());
        operationNewData.put("minCostPrice", newGoods.getMinCostPrice());
        operationNewData.put("maxCostPrice", newGoods.getMaxCostPrice());
        operationNewData.put("minPrice", newGoods.getMinPrice());
        operationNewData.put("maxPrice", newGoods.getMaxPrice());
        operationNewData.put("minGrouponPrice", newGoods.getMinGrouponPrice());
        operationNewData.put("maxGrouponPrice", newGoods.getMaxGrouponPrice());
    }


    /**
     * 更新详情表
     *
     * @param goodsInfoInput
     * @return
     */
    private void updateDetailInfo(ProductInfoInput goodsInfoInput) {
        GoodsDetail goodsDetail = goodsDetailService.queryGoodsDetailByGoodsId(goodsInfoInput.getId());
        GoodsDetail originalGoodsDetail = BeanCopyUtil.transform(goodsDetail, GoodsDetail.class);
        goodsInfoInput.getOldOperationData().put("goodsDetail", originalGoodsDetail);

        //更新详情表
        goodsDetail.setTitle(goodsInfoInput.getName());
        goodsDetail.setDescription(goodsInfoInput.getDescription());
        List<String> detailsImgs = goodsInfoInput.getDetailsImgs();
        if (CollectionUtils.isNotEmpty(detailsImgs)) {
            String imgUrl = detailsImgs.get(0);
            if (!"null".equals(imgUrl)) {
                goodsDetail.setDetailUrl(imgUrl);
            }
        }
        //设置尺码表信息
        String sizeImage = goodsInfoInput.getSizeImage();
        List<SizeTableDTO> sizeTables = goodsInfoInput.getSizeTables();
        goodsDetail.setSizeTable(GoodsInfoUtils.sizeTableToString(sizeTables));
        goodsDetail.setSizeImage(StringUtils.isBlank(sizeImage) || "null".equals(sizeImage) ? "" : sizeImage);

        boolean success = goodsDetailService.updateById(goodsDetail);
        CheckUtils.check(!success, ProductResultCode.UPDATE_ERROR);
        goodsInfoInput.getNewOperationData().put("goodsDetail", goodsDetail);

        //待转化图片
        goodsInfoInput.getUpdateImageVOS().add(new UpdateImageVO(goodsDetail.getId(), goodsDetail.getId(), goodsDetail.getDetailUrl(), goodsDetail.getSizeImage(), "goodsDetailService", System.currentTimeMillis()));
    }


    /**
     * 更新商品类目信息
     *
     * @param goodsInfoInput
     * @return
     */
    private void updateGoodsCategory(ProductInfoInput goodsInfoInput) {
        //更新商品类目信息
        GoodsExtCategoryDTO goodsExtCategory = new GoodsExtCategoryDTO();
        goodsExtCategory.setGoodsId(goodsInfoInput.getId());
        goodsExtCategory.setName(goodsInfoInput.getName());
        goodsExtCategory.setSales(goodsInfoInput.getSales());
        goodsExtCategory.setCategoryId(goodsInfoInput.getCategoryId());
        boolean updateGoodsCategory = goodsExtCategoryService.updateGoodsExtCategory(goodsExtCategory);
        CheckUtils.check(!updateGoodsCategory, ProductResultCode.UPDATE_ERROR);
    }


    /**
     * 更新商品详情信息
     *
     * @param goodsInfoInput
     */
    private void updateGoodsExtDetail(ProductInfoInput goodsInfoInput) {
        GoodsExtDetail goodsExtDetail = goodsExtDetailService.lambdaQuery().eq(GoodsExtDetail::getGoodsId, goodsInfoInput.getId()).one();
        if (!goodsInfoInput.getItemCode().equals(goodsExtDetail.getItemCode())) {
            List<GoodsExtDetail> sameItemCodeDetails = goodsExtDetailService.lambdaQuery()
                    .eq(GoodsExtDetail::getItemCode, goodsInfoInput.getItemCode())
                    .eq(GoodsExtDetail::getShopUrl, goodsInfoInput.getShopId().toString())
                    .select(GoodsExtDetail::getGoodsId)
                    .list();
            if (CollectionUtils.isNotEmpty(sameItemCodeDetails)) {
                List<Long> sameItemCodeGoodsIds = sameItemCodeDetails.stream().map(GoodsExtDetail::getGoodsId).collect(Collectors.toList());
                Integer count = goodsService.lambdaQuery().in(Goods::getId, sameItemCodeGoodsIds).eq(Goods::getIsDel, 0).count();
                CheckUtils.check(count > 0, ProductResultCode.GOODS_DUPLICATE);
            }
        }

        GoodsExtDetail originalGoodsExtDetail = BeanCopyUtil.transform(goodsExtDetail, GoodsExtDetail.class);
        goodsInfoInput.getOldOperationData().put("goodsExtDetail", originalGoodsExtDetail);

        goodsExtDetail.setGoodsId(goodsInfoInput.getId());
        goodsExtDetail.setWeight(GoodsExtDetailUtils.getWeight(goodsInfoInput.getWeight()));
        goodsExtDetail.setItemCode(goodsInfoInput.getItemCode());
        goodsExtDetail.setPackageSize(GoodsExtDetailUtils.getPackageSizeString(goodsInfoInput.getPackageSize()));
        goodsExtDetail.setSpecs(GoodsExtDetailUtils.getSpecs(goodsInfoInput.getSpecs()));
        goodsExtDetail.setBrandId(goodsInfoInput.getBrandId() != null ? goodsInfoInput.getBrandId() : 0);
        goodsExtDetail.setPrincipal(goodsInfoInput.getPrincipal());
        goodsExtDetail.setGoodsUrl(goodsInfoInput.getGoodsUrl());
        goodsExtDetail.setCostUrl(goodsInfoInput.getCostUrl());
        goodsExtDetail.setProcureSupplier(goodsInfoInput.getProcureSupplier());
        goodsExtDetail.setOriginalShopName(goodsInfoInput.getOriginalShopName());
        boolean updateGoodsExtDetail = goodsExtDetailService.updateById(goodsExtDetail);
        CheckUtils.check(!updateGoodsExtDetail, ProductResultCode.UPDATE_ERROR);

        goodsInfoInput.getNewOperationData().put("goodsExtDetail", goodsExtDetail);
        //新增处理逻辑，商品绑定的品牌如果关联了自定义标签，该商品也应关联对应的自定义标签
        GoodsRelTagDTO relTagDTO = new GoodsRelTagDTO();
        relTagDTO.setBrandId(goodsExtDetail.getBrandId());
        relTagDTO.setGoodsIds(Lists.newArrayList(goodsExtDetail.getGoodsId()));
        mqSender.send(CommonConstants.REL_TAG_TOPIC, relTagDTO);
    }


    /**
     * 更新商品详情图片
     *
     * @param goodsInfoInput
     * @return
     */
    public void updateDetailImgs(ProductInfoInput goodsInfoInput) {
        List<GoodsExtDetailImg> originalGoodsExtDetailImgList = goodsExtDetailImgService.lambdaQuery().eq(GoodsExtDetailImg::getGoodsId, goodsInfoInput.getId()).list();
        goodsInfoInput.getOldOperationData().put("goodsExtDetailImgs", originalGoodsExtDetailImgList.stream().map(GoodsExtDetailImg::getImgUrl).collect(Collectors.toList()));

        //更新商品详情图片

        List<String> detailsImgs = goodsInfoInput.getDetailsImgs();
        //先删除商品相关的图片
        goodsExtDetailImgService.deleteByGoodsId(goodsInfoInput.getId());
        // 在重新添加图片
        if (CollectionUtils.isNotEmpty(detailsImgs)) {
            //删除第一张图片，不需要存入第一张主图 详情里面已经存入了主图
//                detailsImgs.remove(0);
            if (detailsImgs.size() <= 0) {
                return;
            }
            List<GoodsExtDetailImg> goodsExtDetailImgList = new ArrayList<>();
            Long id = goodsInfoInput.getId();
            detailsImgs.forEach(img -> {
                if (StringUtils.isNotEmpty(img)) {
                    GoodsExtDetailImg goodsExtDetailImg = new GoodsExtDetailImg();
                    goodsExtDetailImg.setGoodsId(id);
                    goodsExtDetailImg.setImgUrl(img);
                    goodsExtDetailImgList.add(goodsExtDetailImg);
                }
            });
            if (CollectionUtils.isNotEmpty(goodsExtDetailImgList)) {
                boolean b = goodsExtDetailImgService.insertBatch(goodsExtDetailImgList);
                CheckUtils.check(!b, ProductResultCode.UPDATE_ERROR);

                //待转化图片
                for (GoodsExtDetailImg goodsExtDetailImg : goodsExtDetailImgList) {
                    goodsInfoInput.getUpdateImageVOS().add(new UpdateImageVO(goodsExtDetailImg.getId(), goodsExtDetailImg.getImgUrl(), "goodsExtDetailImgService", System.currentTimeMillis()));
                }
            }

        }
        goodsInfoInput.getNewOperationData().put("goodsExtDetailImgs", detailsImgs);
    }

    private void updatePropertyGoodsInfo(List<PropertyGoodsInfoVO> propertyGoodsInfoVOS, Long goodsId) {
        if (CollectionUtils.isEmpty(propertyGoodsInfoVOS)) {
            return;
        }
        List<PropertyGoodsInfoVO> collect = propertyGoodsInfoVOS.stream()
                .filter(k -> StringUtils.isNotBlank(k.getInputValue()))
                .peek(v -> v.setGoodsId(goodsId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            propertyGoodsInfoCoreService.saveOrUpdatePropertyGoods(collect);
        }
    }

    private void updatePropertyGoodsInfo(List<PropertyGoodsInfoVO> propertyGoodsInfoVOS, Long goodsId, List<PropertyDetailInfoVO> modelList) {
        if (CollectionUtils.isEmpty(propertyGoodsInfoVOS)) {
            return;
        }
        List<PropertyGoodsInfoVO> collect = propertyGoodsInfoVOS.stream()
                .filter(k -> StringUtils.isNotBlank(k.getInputValue()))
                .peek(v -> v.setGoodsId(goodsId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            propertyGoodsInfoCoreService.saveOrUpdatePropertyGoods(collect, modelList);
        }
    }

    /**
     * 修改商品图片
     *
     * @param goodsInfoInput
     * @return
     */
    public void updateGoodsImgs(ProductInfoInput goodsInfoInput) {
        List<GoodsImage> originalGoodsImages = goodsImageService.lambdaQuery().in(GoodsImage::getGoodsId, goodsInfoInput.getId()).list();
        goodsInfoInput.getOldOperationData().put("goodsImages", originalGoodsImages.stream().map(GoodsImage::getUrl).collect(Collectors.toList()));

        Long goodsId = goodsInfoInput.getId();
        List<String> goodsImages = goodsInfoInput.getGoodsImages();
        List<String> goodsVideos = goodsInfoInput.getGoodsVideos();
        List<GoodsImage> goodsImageList = new ArrayList<>();
        goodsImageService.deleteByGoodsId(goodsId);
        if (!CollectionUtils.isEmpty(goodsImages)) {
            goodsImages.forEach(img -> {
                if (StringUtils.isNotEmpty(img)) {
                    GoodsImage goodsImage = new GoodsImage();
                    goodsImage.setGoodsId(goodsId);
                    goodsImage.setImageType(GoodsImageTypeEnum.IMAGE.getCode());
                    goodsImage.setCreateTime(LocalDateTime.now());
                    goodsImage.setUrl(img);
                    goodsImageList.add(goodsImage);
                }
            });
        }
        if (!CollectionUtils.isEmpty(goodsVideos)) {
            goodsVideos.forEach(video -> {
                if (StringUtils.isNotEmpty(video)) {
                    GoodsImage goodsImage = new GoodsImage();
                    goodsImage.setGoodsId(goodsId);
                    goodsImage.setImageType(GoodsImageTypeEnum.VIDEO.getCode());
                    goodsImage.setCreateTime(LocalDateTime.now());
                    goodsImage.setUrl(video);
                    goodsImageList.add(goodsImage);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(goodsImageList)) {
            boolean b = goodsImageService.insertBatch(goodsImageList);
            CheckUtils.check(!b, ProductResultCode.UPDATE_ERROR);

            //待转化图片
            for (GoodsImage goodsImage : goodsImageList) {
                goodsInfoInput.getUpdateImageVOS().add(new UpdateImageVO(goodsImage.getId(), goodsImage.getUrl(), "goodsImageService", System.currentTimeMillis()));
            }
        }
        goodsInfoInput.getNewOperationData().put("goodsImages", goodsImages);
    }



    /**
     * 运营端修改商品实拍图
     *
     * @param updateGoodsRealShotImgDTO
     * @return
     */
    @Transactional
    public Boolean updateGoodsDetailRealShotImgs(UpdateGoodsRealShotImgDTO updateGoodsRealShotImgDTO) {
        CheckUtils.notNull(updateGoodsRealShotImgDTO,ProductResultCode.PARAMETER_ERROR);
        CheckUtils.notNull(updateGoodsRealShotImgDTO.getId(),ProductResultCode.PARAMETER_GOODS_ID_NULL);
        // 校验图片
        if (CollectionUtils.isNotEmpty(updateGoodsRealShotImgDTO.getRealShotImgs())){
            List<String> goodsRealShotImages = updateGoodsRealShotImgDTO.getRealShotImgs().stream()
                    .filter(StringUtils::isNotBlank)
                    .map(s -> s.replace(" ", "").replace("\\n", ""))
                    .collect(Collectors.toList());
            goodsRealShotImages.forEach(s -> CheckUtils.check(!s.startsWith("http"), ProductResultCode.GOODS_IMAGE_FORMAT_ERROR));
            goodsRealShotImages.forEach(s -> CheckUtils.check(GoodsInfoUtils.verifyNotImage(s), ProductResultCode.GOODS_IMAGE_FORMAT_ERROR_HTML));
            updateGoodsRealShotImgDTO.setRealShotImgs(goodsRealShotImages);
        }
        Goods goods = goodsService.queryGoodsById(updateGoodsRealShotImgDTO.getId());
        CheckUtils.notNull(goods,ProductResultCode.GOODS_NOT_EXIST);
        // 更新商品实拍图
        ProductInfoInput productInfoInput = new ProductInfoInput();
        productInfoInput.setId(updateGoodsRealShotImgDTO.getId());
        productInfoInput.setRealShotImgs(updateGoodsRealShotImgDTO.getRealShotImgs());
        productInfoInput.setShopId(goods.getShopId());
        productInfoInput.setShopName(goods.getShopName());
        productInfoInput.setCategoryId(goods.getCategoryId());
        log.info("实拍图 productInfoInput 入参:{}",JSON.toJSONString(productInfoInput));
        updateRealShotImgs(productInfoInput);
        // 记录修改日志
        GoodsEditRealShotImgDTO goodsEditRealShotImgDTO = new GoodsEditRealShotImgDTO();
        goodsEditRealShotImgDTO.setOldRealShotImgs(productInfoInput.getOldOperationData().getObject("goodsRealShotImgs",ArrayList.class));
        goodsEditRealShotImgDTO.setNewRealShotImgs(productInfoInput.getNewOperationData().getObject("goodsRealShotImgs",ArrayList.class));
        log.info("实拍图 goodsEditRealShotImgDTO 入参:{}",JSON.toJSONString(goodsEditRealShotImgDTO));
        recordRealShotEditInfo(productInfoInput,goodsEditRealShotImgDTO);
        // 发送同盾
        boolean needSeed = true;
        List<FaMerchantsTag> faMerchantsTagList = faMerchantsTagService.lambdaQuery()
                .eq(FaMerchantsTag::getShopId, goods.getShopId())
                .eq(FaMerchantsTag::getStatus, 1)
                .list();
        if (CollectionUtils.isNotEmpty(faMerchantsTagList)){
            List<Long> shopTags = faMerchantsTagList.stream().map(FaMerchantsTag::getTagId).collect(Collectors.toList());
            shopTags.retainAll(unCheckTagList);
            if (CollectionUtils.isNotEmpty(shopTags)){
                needSeed = false;
            }
        }
        if (needSeed){
            tongDunGoodsImageCoreService.addTongDun(Lists.newArrayList(goods),1);
        }
        return true;
    }

    private void updateRealShotImgs(ProductInfoInput goodsInfoInput){
        CheckUtils.notNull(goodsInfoInput,ProductResultCode.PARAMETER_ERROR);
        CheckUtils.notNull(goodsInfoInput.getId(),ProductResultCode.PARAMETER_GOODS_ID_NULL);
        List<String> realShotImgs = goodsInfoInput.getRealShotImgs();
        Long goodsId = goodsInfoInput.getId();
        // 记录商品历史实拍图
        List<GoodsRealShotImg> oldGoodsRealShotImgList = goodsRealShotImgService.queryByGoodsId(goodsId);
        goodsInfoInput.getOldOperationData().put("goodsRealShotImgs", oldGoodsRealShotImgList.stream().map(GoodsRealShotImg::getImgUrl).collect(Collectors.toList()));
        // 在重新添加图片
        Boolean save = goodsRealShotImgCoreService.addGoodsRealShotImg(goodsId, realShotImgs);
        CheckUtils.check(!save, ProductResultCode.ADD_REAL_SHOT_IMAGE_ERROR);
        // 记录商品最新的实拍图
        goodsInfoInput.getNewOperationData().put("goodsRealShotImgs", realShotImgs);
    }


    private void recordRealShotEditInfo(ProductInfoInput goodsInfoInput,GoodsEditRealShotImgDTO goodsEditRealShotImgDTO){
        CheckUtils.notNull(goodsInfoInput,ProductResultCode.PARAMETER_ERROR);
        CheckUtils.notNull(goodsInfoInput.getId(),ProductResultCode.PARAMETER_GOODS_ID_NULL);
        CheckUtils.notNull(goodsEditRealShotImgDTO,ProductResultCode.PARAMETER_ERROR);
        Long goodsId = goodsInfoInput.getId();
        Long shopId = goodsInfoInput.getShopId();
        String shopName = goodsInfoInput.getShopName();
        Long categoryId = goodsInfoInput.getCategoryId();
        if (shopId == null || categoryId == null || StringUtils.isBlank(shopName)){
            Goods goods = goodsService.queryGoodsById(goodsId);
            CheckUtils.notNull(goods,ProductResultCode.GOODS_NOT_EXIST);
            shopId = goods.getShopId();
            categoryId = goods.getCategoryId();
            shopName = goods.getShopName();
        }
        //init specialTag
        List<String> specialTagList = initSpecialTags(goodsId);
        GoodsEditInfo editInfo = new GoodsEditInfo();
        editInfo.setGoodsId(goodsId);
        editInfo.setCategoryId(categoryId);
        editInfo.setShopId(shopId);
        editInfo.setShopName(shopName);
        editInfo.setApplyUser(getUserName());
        editInfo.setApplyTime(LocalDateTime.now());
        editInfo.setStatus(1);
        editInfo.setAuditUser("system");
        editInfo.setAuditTime(LocalDateTime.now());
        editInfo.setIsDel(0);
        editInfo.setType(GoodsEditTypeEnums.REAL_SHOT_IMG.getCode());
        editInfo.setContent(JSON.toJSONString(goodsEditRealShotImgDTO));
        editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
        editInfo.setUpdateTime(LocalDateTime.now());
        goodsEditInfoService.save(editInfo);

        GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
        goodsEditInfoDetail.setEditId(editInfo.getId());
        goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
        goodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditRealShotImgDTO));
        goodsEditInfoDetailService.save(goodsEditInfoDetail);
    }


    /**
     * 更新属性信息
     *
     * @param goodsInfoInput
     */
    private void updateGoodsPropertyInfo(ProductInfoInput goodsInfoInput) {

        //看用户有没有自定义的属性信息 有，根据name查询是否存在，不存在就添加，type 对应的值就是2,有就拿到对应的id
        List<PropertyDTO> propertyListInfo = goodsInfoInput.getProperties();
        if (CollectionUtils.isEmpty(propertyListInfo)) {
            return;
        }

        Set<GoodsItem> goodsItemsList = new HashSet<>();
//        List<Long> propIds = new LinkedList<>();
        // 前置
//        Map<Long, List<Long>> propValueMap = new LinkedHashMap<>();
//        Map<Long, String> propValueNameMap = new LinkedHashMap<>();
//        propertyListInfo.forEach(propertyBO -> {
//            propertyBO.setCategoryId(goodsInfoInput.getCategoryId());
//            propertyInfoService.insertProperty(propertyBO);
//            propertyInfoService.insertPropertyValue(goodsInfoInput, propertyBO, propValueMap, propValueNameMap);
////            propIds.add(propertyBO.getId());
//        });
//        goodsInfoInput.getNewOperationData().put("properties", propertyListInfo);

        List<ProductSku> originalProductSkuList = productSkuService.queryProductSkuByProductId(goodsInfoInput.getProductId());
        List<GoodsItem> originalGoodsItemList = goodsItemService.queryGoodsItemByGoodsId(goodsInfoInput.getId());


        Set<String> propertyValueStrList = Sets.newHashSet();
        for (PropertyDTO property : goodsInfoInput.getProperties()) {
            for (PropertyValueDTO value : property.getValues()) {
                propertyValueStrList.add(value.getValue());
            }
        }

        List<PropertyValueRecord> propertyValueRecordList = propertyValueRecordService.lambdaQuery()
                .in(PropertyValueRecord::getName, propertyValueStrList)
                .list();
        Map<String, Long> propertyValueRecordMap = propertyValueRecordList.stream().collect(Collectors.toMap(PropertyValueRecord::getName, PropertyValueRecord::getId, (v1, v2) -> v1));

        List<PropertyValueRecord> saveList = Lists.newArrayList();
        for (String propertyValue : propertyValueStrList) {
            Long propertyValueRecordId = propertyValueRecordMap.get(propertyValue);
            if (propertyValueRecordId == null) {
                saveList.add(new PropertyValueRecord(propertyValue));
            }
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            propertyValueRecordService.saveBatch(saveList);
            propertyValueRecordList.addAll(saveList);
            propertyValueRecordMap = propertyValueRecordList.stream().collect(Collectors.toMap(PropertyValueRecord::getName, PropertyValueRecord::getId, (v1, v2) -> v1));
        }
        goodsInfoInput.setPropertyValueRecordMap(propertyValueRecordMap);

        //sku规格修改权限校验
        boolean stop = checkUpdatePropertyPermission(goodsInfoInput, originalProductSkuList, originalGoodsItemList);
        if (stop) {
            LogUtils.info(log, "规格大项变动，return");
            return;
        }


        //更具属性生成所有的sku信息
        Map<String, GoodsItemDTO> skuMap = goodsInfoInput.getSkuMap();
        List<ProductSku> newProductSkuList = ProductSkuGenerator.generateSkuData(goodsInfoInput.getProductId(), skuMap, propertyListInfo);
        LogUtils.info(log, "skuMap:{}", skuMap);
        LogUtils.info(log, "newProductSkuList:{}", newProductSkuList);
        CheckUtils.isEmpty(newProductSkuList, ProductResultCode.CREATE_SKU_ERROR);

        Map<String, Long> goodsSkuNameMap = goodsInfoInput.getGoodsSkuPropertyList().stream().collect(Collectors.toMap(GoodsSkuPropertyDto::getName, GoodsSkuPropertyDto::getSkuId, (v1, v2) -> v1));

        //判断两个集合里面是否有重复的  有就不修改，没有就删除旧的，添加新的
        if (CollectionUtils.isNotEmpty(originalProductSkuList)) {
            List<String> oldPvalueDesc = originalProductSkuList.stream().map(ProductSku::getPvalueDesc).distinct().collect(Collectors.toList());
            // 判断原goodsItem与productSku是否相同，若有不同则全部删除，重新添加
            List<String> oldGoodsItemPvalueDescList = originalGoodsItemList.stream().filter(goodsItemDTO -> StringUtils.isNotBlank(goodsItemDTO.getPvalueStr()) && StringUtils.isNotBlank(goodsItemDTO.getPvalueDesc())).map(GoodsItem::getPvalueDesc).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEqualCollection(oldPvalueDesc, oldGoodsItemPvalueDescList)) {
                saveNewProductSkuAndGoodsItem(goodsInfoInput, oldPvalueDesc, oldGoodsItemPvalueDescList, originalProductSkuList, newProductSkuList, skuMap, goodsSkuNameMap, goodsItemsList);
            } else {
                //oldProductSkuList里面name相等的就不需要删除，如果是不相等的就需要删除，
                // newProductSkuList面name相等的就不需要添加，如果是不相等的就需要添加，
                Map<String, ProductSku> oldPValueMap = originalProductSkuList.stream().collect(Collectors.toMap(ProductSku::getPvalueDesc, item -> item, (v1, v2) -> v1));
                Map<String, ProductSku> newPValueMap = newProductSkuList.stream().collect(Collectors.toMap(ProductSku::getPvalueDesc, item -> item, (v1, v2) -> v1));

                Map<String, String> reversePvalueDescMap = Maps.newHashMap();
                for (ProductSku productSku : originalProductSkuList) {
                    List<String> pvalueList = Lists.newArrayList(productSku.getPvalueDesc().split("&gt;"));
                    Collections.reverse(pvalueList);
                    String reversePvalueDesc = StringUtils.join(pvalueList, "&gt;");
//                oldPValueMap.put(reversePvalueDesc, productSku);
                    reversePvalueDescMap.put(productSku.getPvalueDesc(), reversePvalueDesc);
                    reversePvalueDescMap.put(reversePvalueDesc, productSku.getPvalueDesc());
                }

                LogUtils.info(log, "old pvalueStr list:{}", originalProductSkuList.stream().map(ProductSku::getPvalueStr).collect(Collectors.toList()));
                LogUtils.info(log, "new pvalueStr list:{}", newProductSkuList.stream().map(ProductSku::getPvalueStr).collect(Collectors.toList()));

                Map<String, GoodsItem> originalGoodsItemPvalueDescMap = originalGoodsItemList.stream().filter(goodsItemDTO -> StringUtils.isNotBlank(goodsItemDTO.getPvalueStr()))
                        .collect(Collectors.toMap(GoodsItem::getPvalueDesc, Function.identity(), (v1, v2) -> v1));
                List<ProductSku> addSkuList = new ArrayList<>();
                List<ProductSku> updateSkuList = new ArrayList<>();
                List<Long> deleteSkuIds = new ArrayList<>();
                for (ProductSku newProductSku : newProductSkuList) {
                    String newPValueDesc = newProductSku.getPvalueDesc();
                    ProductSku oldProductSku = oldPValueMap.get(newPValueDesc);

                    if (oldProductSku == null) {
                        String reversePvalueDesc = reversePvalueDescMap.get(newPValueDesc);
                        oldProductSku = oldPValueMap.get(reversePvalueDesc);
                    }

                    if (null != oldProductSku) {
                        newProductSku.setId(oldProductSku.getId());
                        updateSkuList.add(newProductSku);
                    } else {
                        addSkuList.add(newProductSku);
                    }
                }

                // goodsItem上的SkuId
                List<Long> delSkuIdList = new ArrayList<>();
                for (ProductSku oldSku : originalProductSkuList) {
                    String pvalueDesc = oldSku.getPvalueDesc();
                    ProductSku productSku = newPValueMap.get(pvalueDesc);
                    if (null == productSku) {
                        String reversePvalueDesc = reversePvalueDescMap.get(pvalueDesc);
                        productSku = newPValueMap.get(reversePvalueDesc);

                        if (null == productSku) {
                            deleteSkuIds.add(oldSku.getId());

                            GoodsItem goodsItem = originalGoodsItemPvalueDescMap.get(pvalueDesc);
                            if (goodsItem == null) {
                                goodsItem = originalGoodsItemPvalueDescMap.get(reversePvalueDescMap.get(pvalueDesc));
                            }
                            LogUtils.info(log, "删除productSku reversePvalueDesc:{}, goodsItem.skuId:{}", reversePvalueDesc, goodsItem == null ? "null" : goodsItem.getSkuId());
                            if (goodsItem != null) {
                                delSkuIdList.add(goodsItem.getSkuId());
                            }
                        }
                    }
                }

                Map<String, String> specMap = Maps.newHashMap();
                if (CollectionUtils.isEmpty(updateSkuList)) {
                    for (GoodsItem goodsItem : originalGoodsItemList) {
                        if (StringUtils.isBlank(goodsItem.getOriginalProductId())) {
                            continue;
                        }
                        specMap.put(goodsItem.getPvalueStr(), goodsItem.getOriginalProductId());
                        List<String> pvalueList = Arrays.stream(goodsItem.getPvalueStr().split(";")).collect(Collectors.toList());
                        if (pvalueList.size() == 2) {
                            specMap.put(pvalueList.get(1) + ";" + pvalueList.get(0), goodsItem.getOriginalProductId());
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(deleteSkuIds)) {
                    //删除不相等的sku
                    ProductSkuDTO skuBO = new ProductSkuDTO();
                    skuBO.setIds(deleteSkuIds);
                    productSkuService.deleteProductSkuByOption(skuBO);
                }


                if (CollectionUtils.isNotEmpty(updateSkuList)) {
                    LogUtils.info(log, "updateSkuList.size:{}", updateSkuList.size());
                    boolean b = productSkuService.updateBatchById(updateSkuList);
                    CheckUtils.check(!b, ProductResultCode.UPDATE_ERROR);
                }


                if (CollectionUtils.isNotEmpty(addSkuList)) {
                    LogUtils.info(log, "addSkuList.size:{}", addSkuList.size());
                    // 添加新修改的sku
                    boolean i = productSkuService.insertBatch(addSkuList);
                    CheckUtils.check(!i, ProductResultCode.UPDATE_ERROR);
                }

                //更新对应的product_sku


                //更新GoodsItem库的价格 价格变动在更新价格
                Long goodsId = goodsInfoInput.getId();


                if (CollectionUtils.isNotEmpty(originalGoodsItemList)) {
                    goodsInfoInput.getOldOperationData().put("itemList", initGoodsItemOperationData(originalGoodsItemList));

                    //判断是否有删除的sku,将goods_item表里面的数据也删除
                    if (CollectionUtils.isNotEmpty(delSkuIdList)) {
                        GoodsItemDTO goodsItemInput = new GoodsItemDTO();
                        goodsItemInput.setSkuIds(delSkuIdList);
                        goodsItemInput.setGoodsId(goodsInfoInput.getId());
                        goodsItemService.deleteGoodsItemByOption(goodsItemInput);
                    }

                    //更新对应的goodsItem信息
                    Map<String, GoodsItemDTO> pValueGoodsItemMap = skuMap.values().stream()
                            .filter(goodsItemDTO -> StringUtils.isNotBlank(goodsItemDTO.getPvalueStr()))
                            .collect(Collectors.toMap(GoodsItem::getPvalueDesc, Function.identity(), (v1, v2) -> v1));
                    LogUtils.info(log, "pValueGoodsItemMap:{}", pValueGoodsItemMap);
                    List<GoodsItem> updateGoodsItemList = new ArrayList<>();
                    for (GoodsItem originalGoodsItem : originalGoodsItemList) {
                        String pvalueDesc = originalGoodsItem.getPvalueDesc();
                        ProductSku newProductSku = newPValueMap.get(pvalueDesc);
                        GoodsItemDTO newGoodsItemDTO = pValueGoodsItemMap.get(pvalueDesc);

                        if (newProductSku == null) {
                            String reversePvalueDesc = reversePvalueDescMap.get(pvalueDesc);
                            newProductSku = newPValueMap.get(reversePvalueDesc);
                        }

                        if (newGoodsItemDTO == null) {
                            String reversePvalueDesc = reversePvalueDescMap.get(pvalueDesc);
                            LogUtils.info(log, "newGoodsItemDTO为空， pvalueDesc:{} --> reversePvalueDesc:{}", pvalueDesc, reversePvalueDesc);
                            newGoodsItemDTO = pValueGoodsItemMap.get(reversePvalueDesc);
                        }

                        if (null != newGoodsItemDTO) {
//                        if (goodsInfoInput.getShopId() != null) {
//                            CheckUtils.notEmpty(newGoodsItemDTO.getOriginalSkuId(), ProductResultCode.SHOP_SKU_ERROR);
//                        }
                            GoodsItem updateGoodsItem = new GoodsItem();
                            updateGoodsItem.setId(originalGoodsItem.getId());
                            updateGoodsItem.setSkuId(Optional.ofNullable(goodsSkuNameMap.get(newGoodsItemDTO.getName())).orElse(originalGoodsItem.getSkuId()));
                            updateGoodsItem.setPrice(newGoodsItemDTO.getPrice());
                            updateGoodsItem.setGrouponPrice(newGoodsItemDTO.getGrouponPrice());
                            updateGoodsItem.setName(newGoodsItemDTO.getName());

                            updateGoodsItem.setOrginalPrice(newGoodsItemDTO.getOrginalPrice() != null ? newGoodsItemDTO.getOrginalPrice() : newGoodsItemDTO.getPrice());
                            updateGoodsItem.setMarketPrice(countGoodsVirtualPrice(updateGoodsItem.getPrice(), goodsInfoInput.getDiscount()));
                            updateGoodsItem.setOrginalMarketPrice(newGoodsItemDTO.getOrginalMarketPrice() != null ? newGoodsItemDTO.getOrginalMarketPrice() : new BigDecimal(0));
                            updateGoodsItem.setOriginalGrouponPrice(newGoodsItemDTO.getOriginalGrouponPrice());
                            updateGoodsItem.setCostPrice(newGoodsItemDTO.getCostPrice());
                            updateGoodsItem.setOriginalProductId(StringUtils.isBlank(newGoodsItemDTO.getOriginalProductId()) ? null : newGoodsItemDTO.getOriginalProductId());
                            updateGoodsItem.setOriginalSkuId(newGoodsItemDTO.getOriginalSkuId());
                            updateGoodsItem.setStock(newGoodsItemDTO.getStock());
                            updateGoodsItem.setSold(newGoodsItemDTO.getSold());
                            updateGoodsItem.setPackageSize(newGoodsItemDTO.getPackageSize());
                            updateGoodsItem.setWeight(newGoodsItemDTO.getWeight());
                            updateGoodsItem.setDefaultDelivery(newGoodsItemDTO.getDefaultDelivery());
                            updateGoodsItem.setMinPurchaseQuantity(newGoodsItemDTO.getMinPurchaseQuantity() != null ? newGoodsItemDTO.getMinPurchaseQuantity() : 1);
                            updateGoodsItem.setUpdateTime(new Date());
                            updateGoodsItem.setSkuStatus(originalGoodsItem.getSkuStatus() == null ? 1 : originalGoodsItem.getSkuStatus());
                            if (null == newGoodsItemDTO.getNum() || newGoodsItemDTO.getNum() <= 0) {
                                updateGoodsItem.setNum(1);
                            } else {
                                updateGoodsItem.setNum(newGoodsItemDTO.getNum());
                            }

                            if (null != newProductSku) {
                                updateGoodsItem.setName(newProductSku.getName());
                                updateGoodsItem.setPvalueStr(newProductSku.getPvalueStr());
                                updateGoodsItem.setPvalueDesc(newProductSku.getPvalueDesc());

                                if (MapUtils.isNotEmpty(propertyValueRecordMap)) {
                                    String propertyValueRecordSnap = goodsId + "-" + Arrays.stream(newProductSku.getPvalueDesc().split("&gt;"))
                                            .map(propertyValueRecordMap::get)
                                            .filter(Objects::nonNull)
                                            .sorted(Long::compareTo)
                                            .map(Object::toString)
                                            .collect(Collectors.joining("-"));
                                    updateGoodsItem.setPropertyValueRecordSnap(propertyValueRecordSnap);
                                }
                            }

                            Map<Long, String> propertyValueImgUrlMap = goodsInfoInput.getPropertyValueImgUrlMap();
                            updateGoodsItem.setSkuImage(StringUtils.isNotBlank(newGoodsItemDTO.getImageUrl()) ? newGoodsItemDTO.getImageUrl() : "");
                            if (StringUtils.isBlank(updateGoodsItem.getSkuImage()) && propertyValueImgUrlMap != null && StringUtils.isNotBlank(updateGoodsItem.getPvalueStr())) {
                                for (String propertyStr : updateGoodsItem.getPvalueStr().split(";")) {
                                    String propertyValueId = propertyStr.split(":")[1];
                                    String imgUrl = propertyValueImgUrlMap.get(Long.parseLong(propertyValueId));
                                    if (StringUtils.isNotBlank(imgUrl)) {
                                        LogUtils.info(log, "item图片替换为规格图片 itemId:{}, imgUrl:{}", originalGoodsItem.getId(), imgUrl);
                                        updateGoodsItem.setSkuImage(imgUrl);
                                        break;
                                    }
                                }
                            }


                            updateGoodsItemList.add(updateGoodsItem);
                        }else {
                            LogUtils.info(log, "无对应newGoodsItemDTO ===> pvalueDesc:{}", pvalueDesc);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(updateGoodsItemList)) {
                        boolean b = goodsItemService.updateBatchById(updateGoodsItemList);
                        goodsItemsList.addAll(updateGoodsItemList);
                        CheckUtils.check(!b, ProductResultCode.PRODUCT_SKU_ERROR);

                        //待转化图片
                        for (GoodsItem goodsItem : updateGoodsItemList) {
                            goodsInfoInput.getUpdateImageVOS().add(new UpdateImageVO(goodsItem.getId(), goodsItem.getSkuImage(), "goodsItemService", System.currentTimeMillis()));
                        }
                    }
                }


                //在判断有没有需要添加的sku数据，有就在重新添加进去
                if (CollectionUtils.isNotEmpty(addSkuList)) {
                    List<GoodsItem> goodsItemList = new ArrayList<>();
                    addSkuList.forEach(productSku -> {
                        String name = productSku.getName();
                        GoodsItemDTO goodsItemDTO = skuMap.get(name);
                        if (null != goodsItemDTO) {
//                        CheckUtils.notEmpty(goodsItemDTO.getOriginalSkuId(), ProductResultCode.SHOP_SKU_ERROR);
                            GoodsItem goodsItem = getGoodsItem(goodsId, productSku, goodsItemDTO, goodsInfoInput, specMap, goodsSkuNameMap.get(goodsItemDTO.getName()));
                            goodsItemList.add(goodsItem);
                        }

                    });
                    if (CollectionUtils.isNotEmpty(goodsItemList)) {
                        boolean b = goodsItemService.insertBatch(goodsItemList);
                        goodsItemsList.addAll(goodsItemList);
                        CheckUtils.check(!b, ProductResultCode.ADD_ERROR);

                        //待转化图片
                        for (GoodsItem goodsItem : goodsItemList) {
                            goodsInfoInput.getUpdateImageVOS().add(new UpdateImageVO(goodsItem.getId(), goodsItem.getSkuImage(), "goodsItemService", System.currentTimeMillis()));
                        }
                    }
                }
            }
        }
//        else {
//            //没有product_sku信息 全部走新增
//            if (CollectionUtils.isNotEmpty(newProductSkuList)) {
//                // 添加新修改的sku
//                boolean i = productSkuService.insertBatch(newProductSkuList);
//                CheckUtils.check(!i, ProductResultCode.UPDATE_ERROR);
//            }
//
//            //先删除所有的goods_item信息
//            GoodsItemDTO goodsItemInput = new GoodsItemDTO();
//            goodsItemInput.setGoodsId(goodsInfoInput.getId());
//            goodsItemService.deleteGoodsItemByOption(goodsItemInput);
//
//
//            List<GoodsItem> goodsItemList = new ArrayList<>();
//            //然后在全部新增
//            for (ProductSku productSku : newProductSkuList) {
//                String name = productSku.getName();
//                GoodsItemDTO goodsItemDTO = skuMap.get(name);
//                if (null != goodsItemDTO) {
//                    GoodsItem goodsItem = getGoodsItem(goodsInfoInput.getId(), productSku, goodsItemDTO, goodsInfoInput, null);
//                    goodsItemList.add(goodsItem);
//                }
//            }
//
//            if (CollectionUtils.isNotEmpty(goodsItemList)) {
//                boolean b = goodsItemService.insertBatch(goodsItemList);
//                goodsItemsList.addAll(goodsItemList);
//                CheckUtils.check(!b, ProductResultCode.ADD_ERROR);
//            }
//        }
        goodsInfoInput.setGoodsItems(Lists.newArrayList(goodsItemsList));

        goodsInfoInput.getNewOperationData().put("itemList", initGoodsItemOperationData(Lists.newArrayList(goodsItemsList)));
    }

    private void saveNewProductSkuAndGoodsItem(ProductInfoInput goodsInfoInput, List<String> oldPvalueDesc, List<String> oldGoodsItemPvalueDescList, List<ProductSku> originalProductSkuList, List<ProductSku> newProductSkuList, Map<String, GoodsItemDTO> skuMap, Map<String, Long> goodsSkuNameMap, Set<GoodsItem> goodsItemsList) {
        LogUtils.info(log, "原goodsItem与productSku的PvalueDesc不相同 oldPvalueDesc:{} oldGoodsItemList:{}", oldPvalueDesc, oldGoodsItemPvalueDescList);
        List<Long> delProductSkuIdList = originalProductSkuList.stream().map(ProductSku::getId).distinct().collect(Collectors.toList());
        // 删除productSku
        ProductSkuDTO skuBO = new ProductSkuDTO();
        skuBO.setIds(delProductSkuIdList);
        AssertsUtils.isValidateTrue(productSkuService.deleteProductSkuByOption(skuBO), "Failed to delete the original product SKU. delProductSkuIdList:" + JSONObject.toJSONString(delProductSkuIdList));

        // 删除goodsItem
        GoodsItemDTO goodsItemInput = new GoodsItemDTO();
        goodsItemInput.setGoodsId(goodsInfoInput.getId());
        AssertsUtils.isValidateTrue(goodsItemService.deleteGoodsItemByOption(goodsItemInput), "Failed to delete the original goods item. goodsId:" + goodsInfoInput.getId());

        // 新增productSku
        AssertsUtils.isValidateTrue(productSkuService.insertBatch(newProductSkuList), "Failed to add new product SKU. newProductSkuList:" + JSONObject.toJSONString(newProductSkuList));

        // 新增goodsItem
        List<GoodsItem> goodsItemList = newProductSkuList.stream().map(productSku -> {
            String name = productSku.getName();
            GoodsItemDTO goodsItemDTO = skuMap.get(name);
            if (null == goodsItemDTO) {
                return null;
            }

            return getGoodsItem(goodsInfoInput.getId(), productSku, goodsItemDTO, goodsInfoInput, null, goodsSkuNameMap.get(goodsItemDTO.getName()));
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(goodsItemList)) {
            AssertsUtils.isValidateTrue(goodsItemService.insertBatch(goodsItemList), "Failed to add new goods item. goodsItemList:" + JSONObject.toJSONString(goodsItemList));
            goodsItemsList.addAll(goodsItemList);

            // 待转化图片
            for (GoodsItem goodsItem : goodsItemList) {
                goodsInfoInput.getUpdateImageVOS().add(new UpdateImageVO(goodsItem.getId(), goodsItem.getSkuImage(), "goodsItemService", System.currentTimeMillis()));
            }
        }
    }

    /**
     * sku规格修改权限校验
     * 2022.8.8 商品编辑禁止修改sku规格信息 - 2023.6.23待确认状态可以编辑 - 2023.10.31 sku编辑权限
     */
    private boolean checkUpdatePropertyPermission(ProductInfoInput goodsInfoInput, List<ProductSku> originalProductSkuList, List<GoodsItem> originalGoodsItemList) {
        boolean canAddProperty = false;
        boolean canDelProperty = false;
        String goodsItemExtWhiteList = newRedisTemplate.opsForValue().get(RedisKeyConstants.GOODS_ITEM_EXT_WHITELIST + goodsInfoInput.getId());
        if (StringUtils.isNotBlank(goodsItemExtWhiteList)) {
            GoodsItemExtWhiteListConfig goodsItemExtWhiteListConfig = JSON.parseObject(goodsItemExtWhiteList, GoodsItemExtWhiteListConfig.class);
            canAddProperty = goodsItemExtWhiteListConfig.getAdd() == 1;
            canDelProperty = goodsItemExtWhiteListConfig.getDelete() == 1;
            LogUtils.info(log, "当前商品sku编辑权限 canAddProperty:{}, canDelProperty:{}", canAddProperty, canDelProperty);
        }
        //如果是备货仓商品不修改属性信息
        WarehouseStockGoods one = warehouseStockGoodsService.lambdaQuery()
                .eq(WarehouseStockGoods::getGoodsId, goodsInfoInput.getId())
                .eq(WarehouseStockGoods::getStatus, 0)
                .and(wrapper -> wrapper.gt(WarehouseStockGoods::getShowNums,0).or().gt(WarehouseStockGoods::getWaitSignNums,0))
                .last("limit 1").one();
        if (one != null){
            return false;
        }
        Map<String, Set<String>> originalPropertyMap = Maps.newHashMap();
        for (GoodsItem originalGoodsItem : originalGoodsItemList) {
            for (String s : originalGoodsItem.getPvalueStr().split(";")) {
                String[] split = s.split(":");
                Set<String> originalPropertyValueIds = originalPropertyMap.get(split[0]);
                if (CollectionUtils.isEmpty(originalPropertyValueIds)) {
                    originalPropertyValueIds = Sets.newHashSet();
                }
                originalPropertyValueIds.add(split[1]);
                originalPropertyMap.put(split[0], originalPropertyValueIds);
            }
        }
        LogUtils.info(log,"originalPropertyMap:{}", JSON.toJSONString(originalPropertyMap));

        //更新前规格组合item数量
        int originalSkuCount = 1;
        for (Set<String> originalPropertyValueIds : originalPropertyMap.values()) {
            originalSkuCount *= originalPropertyValueIds.size();
        }

        List<PropertyDTO> originalPropertyList = propertyInfoService.queryOriginalProperty(originalPropertyMap);
        goodsInfoInput.getOldOperationData().put("properties", originalPropertyList);

        List<PropertyDTO> newProperties = goodsInfoInput.getProperties();

        List<String> originalPropertyNameList = originalPropertyList.stream().map(Property::getName).collect(Collectors.toList());
        List<String> newPropertyNameList = newProperties.stream().map(Property::getName).collect(Collectors.toList());

        Map<String, Long> goodsSkuNameMap = goodsInfoInput.getGoodsSkuPropertyList().stream().collect(Collectors.toMap(GoodsSkuPropertyDto::getName, GoodsSkuPropertyDto::getSkuId, (v1, v2) -> v1));
        Collection disjunction = CollectionUtils.disjunction(originalPropertyNameList, newPropertyNameList);
        if (CollectionUtils.isNotEmpty(disjunction)) {
            LogUtils.info(log, "===>存在规格大类的变更");
            boolean existAddProperty = false;
            boolean existDelProperty = false;
            List<String> originalPropertyNameList2 = Lists.newArrayList(originalPropertyNameList);
            List<String> newPropertyNameList2 = Lists.newArrayList(newPropertyNameList);
            originalPropertyNameList2.removeAll(newPropertyNameList);
            if (CollectionUtils.isNotEmpty(originalPropertyNameList2)) {
                existDelProperty = true;
            }
            newPropertyNameList2.removeAll(originalPropertyNameList);
            if (CollectionUtils.isNotEmpty(newPropertyNameList2)) {
                existAddProperty = true;
            }

            if (getShopId() != null
                    && !goodsInfoInput.getIsShow().equals(GoodsIsShowEnums.WAIT_AUDIT.getType().toString())
                    && !goodsInfoInput.getIsShow().equals(GoodsIsShowEnums.AUDIT_REJECT.getType().toString())
            ) {
                CheckUtils.check(existAddProperty && !canAddProperty, ProductResultCode.GOODS_SKU_PROPERTY_FORBID_ADD);
                CheckUtils.check(existDelProperty && !canDelProperty, ProductResultCode.GOODS_SKU_PROPERTY_FORBID_DEL);
            }


            originalProductSkuList.forEach(productSku -> productSku.setIsDel(1));
            productSkuService.updateBatchById(originalProductSkuList);
            goodsItemService.deleteByIds(originalGoodsItemList.stream().map(GoodsItem::getId).collect(Collectors.toList()));

            List<ProductSku> newProductSkuList = ProductSkuGenerator.generateSkuData(goodsInfoInput.getProductId(), goodsInfoInput.getSkuMap(), newProperties);
            CheckUtils.isEmpty(newProductSkuList, ProductResultCode.CREATE_SKU_ERROR);

            productSkuService.saveBatch(newProductSkuList);
            Map<String, ProductSku> newProductSkuIdMap = newProductSkuList.stream().collect(Collectors.toMap(ProductSku::getName, Function.identity(), (v1, v2) -> v1));

            List<GoodsItem> newGoodsItemList = goodsInfoInput.getSkuMap().values().stream()
                    .map(goodsItemDTO -> getGoodsItem(goodsInfoInput.getId(), newProductSkuIdMap.get(goodsItemDTO.getName()), goodsItemDTO, goodsInfoInput, null, goodsSkuNameMap.get(goodsItemDTO.getName())))
                    .collect(Collectors.toList());
            goodsItemService.saveBatch(newGoodsItemList);
            goodsInfoInput.getNewOperationData().put("itemList", initGoodsItemOperationData(Lists.newArrayList(newGoodsItemList)));
            LogUtils.info(log, "===>规格大类变更-更新成功，return");
            return true;
        }


        LogUtils.info(log, "===>不存在规格大类的变更");
        boolean existAddPropertyValue = false;
        boolean existDelPropertyValue = false;
        Map<String, List<String>> newPropertyNameValueMap = newProperties.stream()
                .collect(Collectors.toMap(PropertyDTO::getName, propertyDTO -> propertyDTO.getValues().stream().map(PropertyValueDTO::getValue).collect(Collectors.toList()), (v1, v2) -> v1));
        for (PropertyDTO originalPropertyDTO : originalPropertyList) {
            List<String> originalValueList = originalPropertyDTO.getValues().stream().map(PropertyValueDTO::getValue).collect(Collectors.toList());
            List<String> newValueList = newPropertyNameValueMap.get(originalPropertyDTO.getName());

            List<String> originalValueList2 = Lists.newArrayList(originalValueList);
            List<String> newValueList2 = Lists.newArrayList(newValueList);

            newValueList2.removeAll(originalValueList);
            if (CollectionUtils.isNotEmpty(newValueList2)) {
                LogUtils.info(log, "存在新增规格值 originalValueList:{}", originalValueList);
                LogUtils.info(log, "存在新增规格值 newValueList:{}", newValueList);
                existAddPropertyValue = true;
            }

            originalValueList2.removeAll(newValueList);
            if (CollectionUtils.isNotEmpty(originalValueList2)) {
                LogUtils.info(log, "存在删除规格值 originalValueList:{}", originalValueList);
                LogUtils.info(log, "存在删除规格值 newValueList:{}", newValueList);
                existDelPropertyValue = true;
            }
        }

        if (getShopId() != null
                && originalProductSkuList.size() == originalSkuCount
                && !goodsInfoInput.getIsShow().equals(GoodsIsShowEnums.WAIT_AUDIT.getType().toString())
                && !goodsInfoInput.getIsShow().equals(GoodsIsShowEnums.AUDIT_REJECT.getType().toString())
        ) {
            CheckUtils.check(existAddPropertyValue && !canAddProperty, ProductResultCode.GOODS_SKU_PROPERTY_FORBID_ADD);
            CheckUtils.check(existDelPropertyValue && !canDelProperty, ProductResultCode.GOODS_SKU_PROPERTY_FORBID_DEL);
        }
        return false;
    }


    private void checkUpdatePropertyPermission2(ProductInfoInput goodsInfoInput, List<GoodsItem> originalGoodsItemList) {
        boolean canAddProperty = false;
        boolean canDelProperty = false;
        String goodsItemExtWhiteList = newRedisTemplate.opsForValue().get(RedisKeyConstants.GOODS_ITEM_EXT_WHITELIST + goodsInfoInput.getId());
        if (StringUtils.isNotBlank(goodsItemExtWhiteList)) {
            GoodsItemExtWhiteListConfig goodsItemExtWhiteListConfig = JSON.parseObject(goodsItemExtWhiteList, GoodsItemExtWhiteListConfig.class);
            canAddProperty = goodsItemExtWhiteListConfig.getAdd() == 1;
            canDelProperty = goodsItemExtWhiteListConfig.getDelete() == 1;
            LogUtils.info(log, "当前商品sku编辑权限 canAddProperty:{}, canDelProperty:{}", canAddProperty, canDelProperty);
        }

        //备货仓商品不允许修改
//        WarehouseStockGoods one = warehouseStockGoodsService.lambdaQuery()
//                .eq(WarehouseStockGoods::getGoodsId, goodsInfoInput.getId())
//                .eq(WarehouseStockGoods::getStatus, 0)
//                .and(wrapper -> wrapper.gt(WarehouseStockGoods::getShowNums,0).or().gt(WarehouseStockGoods::getWaitSignNums,0))
//                .last("limit 1").one();
//        CheckUtils.check(one != null, ProductResultCode.WAREHOUSE_STOCK_ERROR);

        Map<String, Set<String>> originalPropertyMap = Maps.newHashMap();
        for (GoodsItem originalGoodsItem : originalGoodsItemList) {
            for (String s : originalGoodsItem.getPvalueStr().split(";")) {
                String[] split = s.split(":");
                Set<String> originalPropertyValueIds = originalPropertyMap.get(split[0]);
                if (CollectionUtils.isEmpty(originalPropertyValueIds)) {
                    originalPropertyValueIds = Sets.newHashSet();
                }
                originalPropertyValueIds.add(split[1]);
                originalPropertyMap.put(split[0], originalPropertyValueIds);
            }
        }

        //更新前规格组合item数量
        int originalSkuCount = 1;
        for (Set<String> originalPropertyValueIds : originalPropertyMap.values()) {
            originalSkuCount *= originalPropertyValueIds.size();
        }

        List<PropertyDTO> originalPropertyList = propertyInfoService.queryOriginalProperty(originalPropertyMap);
//        goodsInfoInput.getOldOperationData().put("properties", originalPropertyList);

        List<PropertyDTO> newProperties = goodsInfoInput.getProperties();

        List<String> originalPropertyNameList = originalPropertyList.stream().map(Property::getName).collect(Collectors.toList());
        List<String> newPropertyNameList = newProperties.stream().map(Property::getName).collect(Collectors.toList());

        Collection disjunction = CollectionUtils.disjunction(originalPropertyNameList, newPropertyNameList);
        if (CollectionUtils.isNotEmpty(disjunction)) {
            LogUtils.info(log, "===>存在规格大类的变更 originalPropertyNameList:{}, newPropertyNameList:{}", JSON.toJSONString(originalPropertyNameList), JSON.toJSONString(newPropertyNameList));
            boolean existAddProperty = false;
            boolean existDelProperty = false;
            List<String> originalPropertyNameList2 = Lists.newArrayList(originalPropertyNameList);
            List<String> newPropertyNameList2 = Lists.newArrayList(newPropertyNameList);
            originalPropertyNameList2.removeAll(newPropertyNameList);
            if (CollectionUtils.isNotEmpty(originalPropertyNameList2)) {
                existDelProperty = true;
            }
            newPropertyNameList2.removeAll(originalPropertyNameList);
            if (CollectionUtils.isNotEmpty(newPropertyNameList2)) {
                existAddProperty = true;
            }

            if (getShopId() != null
                    && !goodsInfoInput.getIsShow().equals(GoodsIsShowEnums.WAIT_AUDIT.getType().toString())
                    && !goodsInfoInput.getIsShow().equals(GoodsIsShowEnums.AUDIT_REJECT.getType().toString())
            ) {
                CheckUtils.check(existAddProperty && !canAddProperty, ProductResultCode.GOODS_SKU_PROPERTY_FORBID_ADD);
                CheckUtils.check(existDelProperty && !canDelProperty, ProductResultCode.GOODS_SKU_PROPERTY_FORBID_DEL);
            }
        } else {
            LogUtils.info(log, "===>不存在规格大类的变更");
            boolean existAddPropertyValue = false;
            boolean existDelPropertyValue = false;
            Map<String, List<String>> newPropertyNameValueMap = newProperties.stream()
                    .collect(Collectors.toMap(PropertyDTO::getName, propertyDTO -> propertyDTO.getValues().stream().map(PropertyValueDTO::getValue).collect(Collectors.toList()), (v1, v2) -> v1));
            for (PropertyDTO originalPropertyDTO : originalPropertyList) {
                List<String> originalValueList = originalPropertyDTO.getValues().stream().map(PropertyValueDTO::getValue).collect(Collectors.toList());
                List<String> newValueList = newPropertyNameValueMap.get(originalPropertyDTO.getName());

                List<String> originalValueList2 = Lists.newArrayList(originalValueList);
                List<String> newValueList2 = Lists.newArrayList(newValueList);

                newValueList2.removeAll(originalValueList);
                if (CollectionUtils.isNotEmpty(newValueList2)) {
                    LogUtils.info(log, "存在新增规格值 originalValueList:{}", originalValueList);
                    LogUtils.info(log, "存在新增规格值 newValueList:{}", newValueList);
                    existAddPropertyValue = true;
                }

                originalValueList2.removeAll(newValueList);
                if (CollectionUtils.isNotEmpty(originalValueList2)) {
                    LogUtils.info(log, "存在删除规格值 originalValueList:{}", originalValueList);
                    LogUtils.info(log, "存在删除规格值 newValueList:{}", newValueList);
                    existDelPropertyValue = true;
                }
            }

            if (getShopId() != null
                    && originalGoodsItemList.size() == originalSkuCount
                    && !goodsInfoInput.getIsShow().equals(GoodsIsShowEnums.WAIT_AUDIT.getType().toString())
                    && !goodsInfoInput.getIsShow().equals(GoodsIsShowEnums.AUDIT_REJECT.getType().toString())
            ) {
                CheckUtils.check(existAddPropertyValue && !canAddProperty, ProductResultCode.GOODS_SKU_PROPERTY_FORBID_ADD);
                CheckUtils.check(existDelPropertyValue && !canDelProperty, ProductResultCode.GOODS_SKU_PROPERTY_FORBID_DEL);
            }
        }
    }

    private void fillingFreight(ProductInfoInput goodsInfoInput) {
        //默认运费取德国
        BigDecimal defaultFreight = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(goodsInfoInput.getFreightList())) {
            Optional<GoodsFreightVO> any = goodsInfoInput.getFreightList().stream()
                    .filter(goodsFreightVO -> goodsFreightVO.getCode().equals(SaleableCountryEnum.DE.name()))
                    .findAny();
            if (any.isPresent()) {
                GoodsFreightVO goodsFreightVO = any.get();
                if (goodsFreightVO.getCurrentFreight() != null) {
                    defaultFreight = goodsFreightVO.getCurrentFreight();
                }
            } else {
                defaultFreight = goodsInfoInput.getFreightList().stream().map(GoodsFreight::getCurrentFreight).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            }
        }
        for (GoodsItemDTO goodsItemDTO : goodsInfoInput.getSkuInfo()) {
            goodsItemDTO.setDefaultDelivery(defaultFreight);
        }
    }

    private List<JSONObject> initGoodsItemOperationData(List<GoodsItem> goodsItems) {
        return goodsItems.stream().map(goodsItem -> {
            JSONObject newItemData = new JSONObject();
            newItemData.put("id", goodsItem.getId());
            newItemData.put("skuId", goodsItem.getSkuId());
            newItemData.put("name", goodsItem.getName());
            newItemData.put("pvalueStr", goodsItem.getPvalueStr());
            newItemData.put("pvalueDesc", goodsItem.getPvalueDesc());
            newItemData.put("stock", goodsItem.getStock());
            newItemData.put("orginalPrice", goodsItem.getOrginalPrice());
            newItemData.put("defaultDelivery", goodsItem.getDefaultDelivery());
            newItemData.put("price", goodsItem.getPrice());
            newItemData.put("minPurchaseQuantity", goodsItem.getMinPurchaseQuantity());
            newItemData.put("costPrice", goodsItem.getCostPrice());
            newItemData.put("skuImage", goodsItem.getSkuImage());
            newItemData.put("originalProductId", goodsItem.getOriginalProductId());
            newItemData.put("originalSkuId", goodsItem.getOriginalSkuId());
            return newItemData;
        }).collect(Collectors.toList());
    }

    private JSONObject initLockedGoodsItemOperationData(List<GoodsItem> goodsItems, String mainImage) {
        List<JSONObject> itemData = goodsItems.stream().map(goodsItem -> {
            JSONObject newItemData = new JSONObject();
            newItemData.put("id", goodsItem.getId());
            newItemData.put("orginalPrice", goodsItem.getOrginalPrice());
            newItemData.put("defaultDelivery", goodsItem.getDefaultDelivery());
            newItemData.put("price", goodsItem.getPrice());
            newItemData.put("stock", goodsItem.getStock());
            newItemData.put("minPurchaseQuantity", goodsItem.getMinPurchaseQuantity());
            newItemData.put("skuImage", goodsItem.getSkuImage());
            return newItemData;
        }).collect(Collectors.toList());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("itemList", itemData);
        jsonObject.put("mainImage", mainImage);
        return jsonObject;
    }

    private GoodsItem getGoodsItem(Long goodsId, ProductSku productSku, GoodsItemDTO goodsItemDTO, ProductInfoInput goodsInfoInput, Map<String, String> specMap, Long skuId) {
        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setCreateTime(LocalDateTime.now());
        goodsItem.setUpdateTime(new Date());
        goodsItem.setSkuId(Optional.ofNullable(skuId).orElse(productSku.getId()));
        goodsItem.setGoodsId(goodsId);
        goodsItem.setPrice(goodsItemDTO.getPrice());
        goodsItem.setOrginalPrice(goodsItemDTO.getOrginalPrice());
        goodsItem.setMarketPrice(countGoodsVirtualPrice(goodsItem.getPrice(), goodsInfoInput.getDiscount()));
        goodsItem.setOrginalMarketPrice(goodsItem.getMarketPrice());
        goodsItem.setOriginalGrouponPrice(goodsItem.getOrginalPrice().multiply(new BigDecimal("0.8")).setScale(2, BigDecimal.ROUND_HALF_UP));
        goodsItem.setGrouponPrice(goodsItem.getOriginalGrouponPrice().add(goodsItemDTO.getDefaultDelivery()));

        goodsItem.setName(goodsItemDTO.getName());
        goodsItem.setCostPrice(goodsItemDTO.getCostPrice());
        goodsItem.setPvalueDesc(productSku.getPvalueDesc());
        goodsItem.setPvalueStr(productSku.getPvalueStr());
        goodsItem.setStock(goodsItemDTO.getStock());
        goodsItem.setSold(goodsItemDTO.getSold());
        goodsItem.setOriginalSkuId(goodsItemDTO.getOriginalSkuId());
        goodsItem.setOriginalProductId(MapUtils.isEmpty(specMap) ? null : specMap.get(goodsItem.getPvalueStr()));
        goodsItem.setDefaultDelivery(goodsItemDTO.getDefaultDelivery());
        goodsItem.setPackageSize(goodsItemDTO.getPackageSize());
        goodsItem.setWeight(goodsItemDTO.getWeight());
        goodsItem.setMinPurchaseQuantity(goodsItemDTO.getMinPurchaseQuantity());
        goodsItem.setRatio(60);
        goodsItem.setNum(1);
        goodsItem.setMinPurchaseQuantity(goodsItemDTO.getMinPurchaseQuantity() != null ? goodsItemDTO.getMinPurchaseQuantity() : 1);
        goodsItem.setSkuStatus(goodsItemDTO.getSkuStatus() == null ? 1 : goodsItemDTO.getSkuStatus());

        goodsItem.setSkuImage(StringUtils.isNotBlank(goodsItemDTO.getImageUrl()) ? goodsItemDTO.getImageUrl() : "");
        Map<Long, String> propertyValueImgUrlMap = goodsInfoInput.getPropertyValueImgUrlMap();
        if (StringUtils.isBlank(goodsItem.getSkuImage()) && propertyValueImgUrlMap != null) {
            for (String propertyStr : goodsItem.getPvalueStr().split(";")) {
                String propertyValueId = propertyStr.split(":")[1];
                String imgUrl = propertyValueImgUrlMap.get(Long.parseLong(propertyValueId));
                if (StringUtils.isNotBlank(imgUrl)) {
                    goodsItem.setSkuImage(imgUrl);
                    break;
                }
            }
        }

        Map<String, Long> propertyValueRecordMap = goodsInfoInput.getPropertyValueRecordMap();
        if (MapUtils.isNotEmpty(propertyValueRecordMap)) {
            String propertyValueRecordSnap = goodsId + "-" + Arrays.stream(productSku.getPvalueDesc().split("&gt;"))
                    .map(propertyValueRecordMap::get)
                    .filter(Objects::nonNull)
                    .sorted(Long::compareTo)
                    .map(Object::toString)
                    .collect(Collectors.joining("-"));
            goodsItem.setPropertyValueRecordSnap(propertyValueRecordSnap);
        }
        return goodsItem;
    }


    /**
     * 更新product表
     *
     * @param goodsInfoInput
     */
    private void updateProduct(ProductInfoInput goodsInfoInput) {
        Long categoryId = goodsInfoInput.getCategoryId();
        String name = goodsInfoInput.getName();
        Long productId = goodsInfoInput.getProductId();

        Product product = new Product();
        product.setName(name);
        product.setId(productId);
        product.setCategoryId(categoryId);
        if (null != goodsInfoInput.getAppChannel() && goodsInfoInput.getAppChannel() > 0) {
            product.setAppChannel(goodsInfoInput.getAppChannel());
        }
        if (StringUtils.isNotEmpty(goodsInfoInput.getCountry())) {
            product.setCountry(logisticsPropertyConfigCoreService.transformSystemCountry(goodsInfoInput.getCountry()));
        }
        boolean i = productService.updateById(product);
        CheckUtils.check(!i, ProductResultCode.UPDATE_ERROR);
    }


    public void updateGoodsLabel(ProductInfoInput goodsInfoInput) {
        if (null == goodsInfoInput.getLabelId() || goodsInfoInput.getLabelId() <= 0) {
            return;
        }
        Date date = new Date();
        // 1.查询有没有  没有就新增 有就更新
        List<GoodsLabel> goodsLabels = goodsLabelService.queryLabelByGoodsId(goodsInfoInput.getId());
        if (CollectionUtils.isNotEmpty(goodsLabels)) {
            GoodsLabel goodsLabel = goodsLabels.get(0);
            GoodsLabel newLabel = new GoodsLabel();
            newLabel.setId(goodsLabel.getId());
            newLabel.setLabelId(goodsInfoInput.getLabelId());
            newLabel.setUpdateTime(date);
            boolean b = goodsLabelService.updateById(newLabel);
            CheckUtils.check(!b, ProductResultCode.UPDATE_ERROR);
        } else {
            // 2.新增该标签
            GoodsLabel glb = new GoodsLabel();
            glb.setGoodsId(goodsInfoInput.getId());
            glb.setLabelId(goodsInfoInput.getLabelId());
            glb.setCreateTime(date);
            glb.setUpdateTime(date);
            glb.setIsDel(SystemStatusEnum.STATUS_IS_DEL_OK.getStatus());
            boolean insert = goodsLabelService.insert(glb);
            CheckUtils.check(!insert, ProductResultCode.ADD_ERROR);
        }
    }

    public GoodsItemDTO getShopGoodsPrice(GoodsItemDTO goodsItemDTO) {
        BigDecimal price = goodsItemDTO.getOrginalPrice();
        goodsItemDTO.setOrginalPrice(goodsItemDTO.getOrginalPrice());
        goodsItemDTO.setPrice(price.add(goodsItemDTO.getDefaultDelivery()));
        goodsItemDTO.setMarketPrice(price);
        return goodsItemDTO;
    }


    @Override
    public Boolean updateLockedGoodsPrice(LockGoodsUpdateCondition condition) {
        log.info("锁定商品价格下调参数：{}", JSON.toJSONString(condition));

        Long goodsId = condition.getId();
        CheckUtils.notNull(goodsId, ProductResultCode.PARAMETER_ID_ERROR);

        if (StringUtils.isNotBlank(condition.getDescription())) {
            CheckUtils.check(isContainChinese(condition.getDescription()), ProductResultCode.CONTAILS_CHINESE_ERROR);
        }

        Goods goods = goodsService.selectById(goodsId);
        CheckUtils.check(GoodsIsShowEnums.AUDITING.getType().toString().equals(goods.getIsShow()), ProductResultCode.GOODS_IS_AUDITING);

        if (null != condition.getIsPickedShopGoods() && 1 == condition.getIsPickedShopGoods()) {
            condition.setNeedImage(false);
        }
        if (condition.isNeedImage()) {
            CheckUtils.isEmpty(condition.getGoodsImages(), ProductResultCode.MAIN_IMAGE_ERROR);
        }

        List<LockGoodsUpdateCondition.LockGoodsItemUpdateDto> skuList = condition.getSkuList();
        CheckUtils.check(CollectionUtils.isEmpty(skuList), ProductResultCode.PRODUCT_SKU_ERROR);

        Goods goodsLockInfo = goodsService.queryIsLockByGoodsId(goodsId);
        if (0 == goodsLockInfo.getIsLock()) {
            CheckUtils.check(true, ProductResultCode.GOODS_IS_NOT_LOCKED);
        }
        if (skuList.size() <= 0) {
            CheckUtils.check(true, ProductResultCode.PRODUCT_SKU_ERROR);
        }

        List<GoodsItem> originalGoodsItems = goodsItemService.queryGoodsItemByGoodsId(goodsId);
        Set<Long> originalGoodsItemIds = originalGoodsItems.stream().map(GoodsItem::getId).collect(Collectors.toSet());
        if (originalGoodsItemIds.size() != skuList.size()) {
            CheckUtils.check(true, ProductResultCode.PRODUCT_SKU_ERROR);
        }
        Category category = categoryService.selectById(goods.getCategoryId());

        List<Long> pathCategoryIds = Lists.newArrayList();
        pathCategoryIds.add(goods.getCategoryId());
        if (category != null && StringUtils.isNotBlank(category.getPids())) {
            pathCategoryIds.addAll(Arrays.stream(category.getPids().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }
        BigDecimal virtualDiscount = newAddGoodsCoreService.getVirtualDiscount(pathCategoryIds, goods.getShopId());

        List<GoodsFreight> goodsFreights = goodsFreightService.queryByGoodsId(goodsId);
        //默认运费取德国
        BigDecimal defaultFreight = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(goodsFreights)) {
            Optional<GoodsFreight> any = goodsFreights.stream()
                    .filter(goodsFreight -> goodsFreight.getCode().equals(SaleableCountryEnum.DE.name()))
                    .findAny();
            if (any.isPresent()) {
                GoodsFreight goodsFreightVO = any.get();
                if (goodsFreightVO.getCurrentFreight() != null) {
                    defaultFreight = goodsFreightVO.getCurrentFreight();
                }
            } else {
                defaultFreight = goodsFreights.stream().map(GoodsFreight::getCurrentFreight).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            }

            if (BigDecimal.ZERO.compareTo(defaultFreight) == 0) {
                GoodsFreight goodsFreight = goodsFreights.stream().max(Comparator.comparing(GoodsFreight::getCurrentFreight)).get();
                defaultFreight = goodsFreight.getCurrentFreight();
            }
        }

        Set<Long> updateGoodsItemIds = skuList.stream().map(LockGoodsUpdateCondition.LockGoodsItemUpdateDto::getId).collect(Collectors.toSet());
        originalGoodsItemIds.removeAll(updateGoodsItemIds);
        CheckUtils.check(CollectionUtils.isNotEmpty(originalGoodsItemIds), ProductResultCode.PRODUCT_SKU_ERROR);

        String mainImage = goods.getMainImage();
        goods.setUpdateTime(LocalDateTime.now());

        JSONObject oldItemData = initLockedGoodsItemOperationData(originalGoodsItems, goods.getMainImage());

        Map<Long, GoodsItem> originalGoodsItemMap = originalGoodsItems.stream().collect(Collectors.toMap(GoodsItem::getId, Function.identity(), (a, b) -> b));
        List<GoodsItem> updateList = new ArrayList<>();
        for (LockGoodsUpdateCondition.LockGoodsItemUpdateDto itemDTO : skuList) {
            CheckUtils.notNull(itemDTO.getOrginalPrice(), ProductResultCode.GOODS_SKU_PRICE_ERROR);
            CheckUtils.notNull(itemDTO.getStock(), ProductResultCode.SHOW_PRODUCT_STOCK_ERROR);

            GoodsItem originalGoodsItem = originalGoodsItemMap.get(itemDTO.getId());
            if (itemDTO.getOrginalPrice().compareTo(originalGoodsItem.getOrginalPrice()) > 0) {
                CheckUtils.check(true, ProductResultCode.LOCKED_GOODS_PRICE_CAN_ONLY_LESS);
            }
            if (itemDTO.getStock().compareTo(originalGoodsItem.getStock()) < 0) {
                CheckUtils.check(true, ProductResultCode.LOCKED_GOODS_STOCK_CAN_ONLY_MORE);
            }

            GoodsItem ex = new GoodsItem();
            ex.setId(itemDTO.getId());
            ex.setOrginalPrice(itemDTO.getOrginalPrice());
            ex.setPrice(itemDTO.getOrginalPrice().add(originalGoodsItem.getDefaultDelivery()));
            ex.setOriginalGrouponPrice(itemDTO.getOrginalPrice().multiply(new BigDecimal("0.8")).setScale(2, BigDecimal.ROUND_HALF_UP));
            ex.setGrouponPrice(ex.getOriginalGrouponPrice().add(originalGoodsItem.getDefaultDelivery()));
            ex.setMarketPrice(countGoodsVirtualPrice(ex.getPrice(), virtualDiscount));
            ex.setStock(itemDTO.getStock());
            ex.setUpdateTime(new Date());
            ex.setMinPurchaseQuantity(itemDTO.getMinPurchaseQuantity());
            ex.setSkuId(originalGoodsItem.getSkuId());
            //更新sku图片
            ex.setSkuImage(itemDTO.getImageUrl());
            updateList.add(ex);
        }
        goodsItemService.updateBatchById(updateList);

        //修改国家运费
//        List<LockGoodsUpdateCondition.GoodsFreightUpdateDto> freightList = condition.getFreightList();
//        if (CollectionUtil.isNotEmpty(freightList)) {
//            List<GoodsFreight> goodsFreights = goodsFreightService.queryByGoodsId(goodsId);
//            Map<String, LockGoodsUpdateCondition.GoodsFreightUpdateDto> freightMap = freightList.stream().collect(Collectors.toMap(LockGoodsUpdateCondition.GoodsFreightUpdateDto::getCountry, Function.identity(), (a, b) -> b));
//            for (GoodsFreight goodsFreight : goodsFreights) {
//                LockGoodsUpdateCondition.GoodsFreightUpdateDto goodsFreightUpdateDto = freightMap.get(goodsFreight.getCode());
//                if (null != goodsFreightUpdateDto) {
//                    goodsFreight.setCurrentFreight(goodsFreightUpdateDto.getModifyDelivery());
//                }
//            }
//            goodsFreightService.updateBatchById(goodsFreights);
//        }

        //更新商详图片
        if (CollectionUtils.isNotEmpty(condition.getDetailsImgs())) {
            goodsExtDetailImgService.deleteByGoodsId(goodsId);

            List<GoodsExtDetailImg> goodsExtDetailImgList = new ArrayList<>();
            condition.getDetailsImgs().forEach(img -> {
                if (StringUtils.isNotEmpty(img) || !("null".equals(img))) {
                    GoodsExtDetailImg goodsExtDetailImg = new GoodsExtDetailImg();
                    goodsExtDetailImg.setGoodsId(goodsId);
                    goodsExtDetailImg.setImgUrl(img);
                    goodsExtDetailImgList.add(goodsExtDetailImg);
                }
            });
            boolean success = goodsExtDetailImgService.insertBatch(goodsExtDetailImgList);
            CheckUtils.check(!success, ProductResultCode.UPDATE_ERROR);
        }

        //更新商详文案
        if (StringUtils.isNotBlank(condition.getDescription())) {
            GoodsDetail goodsDetail = goodsDetailService.queryGoodsDetailByGoodsId(goodsId);
            if (goodsDetail != null) {
                goodsDetail.setDescription(condition.getDescription());
                goodsDetailService.updateById(goodsDetail);
            }
        }

        //更新属性图片
        if (CollectionUtils.isNotEmpty(condition.getProperties())) {
            HashMap<Long, String> propertyImageMap = Maps.newHashMap();
            for (PropertyDTO property : condition.getProperties()) {
                for (PropertyValueDTO propertyValue : property.getValues()) {
                    propertyImageMap.put(propertyValue.getId(), propertyValue.getImgUrl());
                }
            }
            List<PropertyImgDetail> propertyImgDetails = propertyInfoService.queryByImgList(goods.getProductId(), Lists.newArrayList(propertyImageMap.keySet()));
            if (CollectionUtils.isNotEmpty(propertyImgDetails)) {
                for (PropertyImgDetail propertyImgDetail : propertyImgDetails) {
                    String newPropertyImage = propertyImageMap.get(propertyImgDetail.getPropertyValueId());
                    if (StringUtils.isNotBlank(newPropertyImage)) {
                        propertyImgDetail.setImgUrl(newPropertyImage);
                    }
                }
                propertyImgDetailService.updateBatchById(propertyImgDetails);
            } else {
                propertyImgDetails = Lists.newArrayList();
                for (Map.Entry<Long, String> entry : propertyImageMap.entrySet()) {
                    Long propertyValueId = entry.getKey();
                    String imgUrl = entry.getValue();
                    if (StringUtils.isBlank(imgUrl)) {
                        continue;
                    }

                    PropertyImgDetail propertyImgDetail = new PropertyImgDetail();
                    propertyImgDetail.setPropertyValueId(propertyValueId);
                    propertyImgDetail.setMainUrl(imgUrl);
                    propertyImgDetail.setImgUrl(imgUrl);
                    propertyImgDetail.setCreateTime(new Date());
                    propertyImgDetail.setSpuId(goods.getProductId());
                    propertyImgDetails.add(propertyImgDetail);
                }
                if (CollectionUtils.isNotEmpty(propertyImgDetails)) {
                    propertyImgDetailService.saveBatch(propertyImgDetails);
                }
            }

        }

        //判断是否满足建议降价
        fillLockedReductionPrice(condition, updateList);

        if (condition.isNeedImage()) {
            //修改主图
            ProductInfoInput productInfoInput = new ProductInfoInput();
            productInfoInput.setId(condition.getId());
            productInfoInput.setGoodsImages(condition.getGoodsImages());
            productInfoInput.setGoodsVideos(condition.getGoodsVideos());
            updateGoodsImgs(productInfoInput);

            goods.setMainImage(condition.getGoodsImages().get(0));
        }
        JSONObject newItemData = initLockedGoodsItemOperationData(updateList, goods.getMainImage());

        Optional<BigDecimal> maxPrice = updateList.stream().map(GoodsItem::getPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
        Optional<BigDecimal> minPrice = updateList.stream().map(GoodsItem::getPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);
        maxPrice.ifPresent(goods::setMaxPrice);
        minPrice.ifPresent(goods::setMinPrice);

        Optional<BigDecimal> maxOrginalPrice = updateList.stream().map(GoodsItem::getOrginalPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
        Optional<BigDecimal> minOrginalPrice = updateList.stream().map(GoodsItem::getOrginalPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);
        goods.setOrginalMaxPrice(maxOrginalPrice.orElseGet(goods::getMaxPrice));
        goods.setOrginalMinPrice(minOrginalPrice.orElseGet(goods::getMinPrice));
        Optional<BigDecimal> maxGrouponPrice = updateList.stream().map(GoodsItem::getGrouponPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
        Optional<BigDecimal> minGrouponPrice = updateList.stream().map(GoodsItem::getGrouponPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);
        maxGrouponPrice.ifPresent(goods::setMaxGrouponPrice);
        minGrouponPrice.ifPresent(goods::setMinGrouponPrice);

        goods.setMinMarketPrice(countGoodsVirtualPrice(goods.getMinPrice(), virtualDiscount));
        goods.setMaxMarketPrice(countGoodsVirtualPrice(goods.getMaxPrice(), virtualDiscount));
        if (condition.getIsReductionPrice() != null && !condition.getIsReductionPrice()) {
            goods.setIsReduction(0);
            goodsPriceReductionCoreService.updateReductionRecord(goods.getId(), goods.getShopName());
        }

        //更新主图时添加同盾
        log.info("当前同盾更新开关的状态是:{}", marketingSwitchCenter.getTongDunUpdateSwitch().equals("1") ? "开始" : "关闭");
        if (marketingSwitchCenter.getTongDunUpdateSwitch().equals("1")) {
            FaMerchantsApply faMerchantsApply = null;
            Long shopId = goods.getShopId();
            if (Objects.nonNull(shopId)) {
                faMerchantsApply = faMerchantsApplyService.selectById(shopId);
                CheckUtils.check(Objects.nonNull(faMerchantsApply) && 0 == faMerchantsApply.getIsDisable(), ProductResultCode.SHOP_IS_LOCK);
            }
            Integer isWhite = 0;
            if (faMerchantsApply != null && faMerchantsApply.getTongDunWhiteList() != null) {
                isWhite = faMerchantsApply.getTongDunWhiteList();
            }
            log.info("当前店铺shopId是:{},同盾是否白名单:{}", shopId, isWhite == 1 ? "是" : "否");
            if (isWhite != 1) {
                if ("0".equals(goods.getIsShow()) || "1".equals(goods.getIsShow())) {
                    //判断是否修改主图
//                if (StringUtils.isNotEmpty(mainImage) && (!mainImage.equals(goods.getMainImage()))) {
                    log.info("原来的主图:" + mainImage);
                    log.info("修改后的主图:" + goods.getMainImage());
                    log.info("锁定中的商品 商品id为:" + goods.getId() + "状态（isShow）是:" + goods.getIsShow() + "修改主图,触发同盾");
                    TongDunGoodsImagesVO tongDunGoodsImagesVO = new TongDunGoodsImagesVO();
                    tongDunGoodsImagesVO.setGoodsId(goods.getId());
                    tongDunGoodsImagesVO.setShopId(goods.getShopId());
                    tongDunGoodsImagesVO.setGoodsName(goods.getName());
                    tongDunGoodsImagesVO.setCategoryId(goods.getCategoryId());
                    tongDunGoodsImagesVO.setGoodsImage(getImageUrl(condition));
                    tongDunGoodsImagesVO.setIsType(0L);
                    tongDunGoodsImagesVO.setTdType(1);
                    log.info("发送同盾检测图片Mq" + JSON.toJSONString(tongDunGoodsImagesVO));
                    mqSender.send("ADD_TONG_DUN_GOODS", JSON.toJSONString(tongDunGoodsImagesVO));
//                }
                }
            }
        }


        boolean success = goodsService.updateById(goods);
        CheckUtils.check(!success, ProductResultCode.UPDATE_ERROR);
        log.info("锁定商品价格下调参数成功");

        log.info("condition.isPickedShopGoods:" + condition.getIsPickedShopGoods());
        if (null != condition.getIsPickedShopGoods() && 1 == condition.getIsPickedShopGoods()) {
            log.info("检测是精选店铺商品申请降价，商品id是.{}", goodsId);
            if (null != condition.getAuditType() && 3 == condition.getAuditType()) {
                goodsCoreService.syncPickedShopPassGoods(Collections.singletonList(goodsId));
            } else if (null != condition.getAuditType() && 4 == condition.getAuditType()) {
                goodsCoreService.syncPickedShopGoods(Collections.singletonList(goodsId));
            }
        }

        //刷新商品缓存
        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(goodsId);
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("锁定商品价格下调");
        goodsSyncModel.setSourceService("vp");
        mqSender.sendDelay("SYNC_GOODS_TOPIC_BATCH", goodsSyncModel, MqDelayLevel.FIVE_SEC);

        //修改活动商品表minPrice maxPrice
//        mqSender.send("ADJUST_GOODS_ACTIVITY", goodsId);

        log.info("发送操作事件 goodsId:{} type:{}", goodsId, OperationLogTypeEnums.REDUCE_LOCKED_GOODS_PRICE.getDesc());
        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(goodsId)
                .type(OperationLogTypeEnums.REDUCE_LOCKED_GOODS_PRICE)
                .content(OperationLogTypeEnums.REDUCE_LOCKED_GOODS_PRICE.getDesc())
                .oldData(JSON.toJSONString(oldItemData))
                .newData(JSON.toJSONString(newItemData))
                .status(1)
                .user(getUserName());
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
        return Boolean.TRUE;
    }

    @Override
    public void settingDiscount(List<GoodsDiscountImportVO> list) {
        log.info("批量设置划线价折扣参数：{}", JSON.toJSONString(list));
        List<Long> goodsIds = list.stream().map(GoodsDiscountImportVO::getGoodsId).collect(Collectors.toList());
        List<Goods> goodsList = (List<Goods>) goodsService.selectByIds(goodsIds);
        if (goodsIds.size() != list.size()) {
            CheckUtils.check(true, ProductResultCode.GOODS_NOT_EXIST);
        }

        List<GoodsItem> updateGoodsItemList = new ArrayList<>();
        Map<Long, Goods> goodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (a, b) -> b));
        for (GoodsDiscountImportVO vo : list) {
            Goods goods = goodsMap.get(vo.getGoodsId());
            List<GoodsItem> goodsItems = goodsItemService.queryGoodsItemByGoodsId(vo.getGoodsId());
            for (GoodsItem goodsItem : goodsItems) {
                BigDecimal beforeDiscountPrice = goodsItem.getPrice().divide(vo.getDiscount(), 2);
                goodsItem.setMarketPrice(beforeDiscountPrice);
                goodsItem.setOrginalMarketPrice(beforeDiscountPrice);
                goodsItem.setUpdateTime(new Date());
                updateGoodsItemList.add(goodsItem);
            }

            Optional<BigDecimal> maxMarketPrice = goodsItems.stream().map(GoodsItem::getMarketPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
            Optional<BigDecimal> minMarketPrice = goodsItems.stream().map(GoodsItem::getMarketPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);
            maxMarketPrice.ifPresent(goods::setMaxMarketPrice);
            minMarketPrice.ifPresent(goods::setMinMarketPrice);

            Optional<BigDecimal> maxOrginalMarketPrice = goodsItems.stream().map(GoodsItem::getOrginalMarketPrice).filter(Objects::nonNull).max(BigDecimal::compareTo);
            Optional<BigDecimal> minOrginalMarketPrice = goodsItems.stream().map(GoodsItem::getOrginalMarketPrice).filter(Objects::nonNull).min(BigDecimal::compareTo);
            goods.setOrginalMaxMarketPrice(maxOrginalMarketPrice.orElseGet(goods::getMaxMarketPrice));
            goods.setOrginalMinMarketPrice(minOrginalMarketPrice.orElseGet(goods::getMinMarketPrice));
        }

        boolean success = goodsService.updateBatchById(goodsList);
        CheckUtils.check(!success, ProductResultCode.UPDATE_ERROR);
        success = goodsItemService.updateBatchById(updateGoodsItemList);
        CheckUtils.check(!success, ProductResultCode.UPDATE_ERROR);

        // 更新goods_tmp表
        for (GoodsItem goodsTmp : updateGoodsItemList) {
            //刷新商品缓存
            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(goodsTmp.getGoodsId());
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("批量设置划线价");
            goodsSyncModel.setSourceService("vp");
            mqSender.sendDelay("SYNC_GOODS_TOPIC_BATCH", goodsSyncModel,MqDelayLevel.TEN_SEC);

        }
    }

    @Override
    @Transactional
    public Boolean deleteShopGoods(Long shopId) {
        List<Goods> goods = goodsService.queryGoodsByShopId(shopId);
        List<Goods> updateList = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        for (Goods gg : goods) {
            Goods update = new Goods();
            update.setId(gg.getId());
            update.setIsShow("0");
            update.setIsDel(1);
            update.setUpdateTime(LocalDateTime.now());
            updateList.add(update);
            ids.add(gg.getId());
        }
        final boolean b = goodsService.updateBatchById(updateList);
        if (!b) {
            log.info("deleteShopGoods error");
        }
        Boolean success = goodsEsRemoteService.deleteGoodsInES(ids);
        if (success) {
            goods.forEach(g -> {
                log.info("发送操作事件 goodsId:{} type:{}", g.getId(), OperationLogTypeEnums.DELETE_GOODS.getDesc());
                GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                        .goodsId(g.getId())
                        .type(OperationLogTypeEnums.DELETE_GOODS)
                        .content(OperationLogTypeEnums.DELETE_GOODS.getDesc())
                        .status(1)
                        .user(getUserName());
                mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
            });
        }
        return success;

    }

    @Override
    public void updateOffShelfProducts(List<Long> shopIds) {

    }

    @Override
    public UpdateGoodsCountryVO updateCountry(UpdateCountryCondition condition) {
        CheckUtils.notNull(condition, ProductResultCode.PARAMETER_ERROR);
        CheckUtils.notNull(condition.getId(), ProductResultCode.PARAMETER_ID_ERROR);
        CheckUtils.notEmpty(condition.getCountry(), ProductResultCode.PARAMETER_COUNTRY_NULL);

        UpdateGoodsCountryVO updateGoodsCountryVO = new UpdateGoodsCountryVO();
        BaseTokenUserInfo userInfo = getUserInfo();
        if (StringUtils.isBlank(condition.getBuyerClientinfo())) {
            if (userInfo.getSelfType() != null && userInfo.getSelfType() == 1) {
//                Set<Long> members = newRedisTemplate.opsForSet().members(RedisKeyConstants.SELF_ACCOUNT_BIND_GOODS_REDIS + userInfo.getId());
                //上新人
                Boolean isBelongArrival = newRedisTemplate.opsForSet().isMember(RedisKeyConstants.SELF_ACCOUNT_BIND_GOODS_REDIS + userInfo.getId(), condition.getId().toString());
                CheckUtils.check(isBelongArrival == null || !isBelongArrival, CustomResultCode.fill(ProductResultCode.GOODS_NOT_BELONG_ARRIVAL, condition.getId().toString()));
            } else {
                //商家
                Long shopId = getShopId();
                if (shopId != null){
                    FaMerchantsApply faMerchantsApply = faMerchantsApplyService.selectById(shopId);
                    CheckUtils.notNull(faMerchantsApply, ProductResultCode.SHOP_ID_NULL);

                    if (GoodsTransferUtils.AvailableCountryTypeEnums.ALL.getCode().equals(condition.getCountryShowType())
                            && !DeliveryTypeEnum.DIRECT.getCode().equals(faMerchantsApply.getDeliveryType())) {
                        condition.setCountry(GoodsTransferUtils.AvailableCountryTypeEnums.ALL.getDetail());
                    }
                    if (DeliveryTypeEnum.DIRECT.getCode().equals(faMerchantsApply.getDeliveryType())) {
                        //海外仓店铺：校验更新国家是否属于已配置销售国家
                        List<String> countryList = warehouseOverseasConfigCoreService.queryCountryByShopId(shopId);
                        CheckUtils.check(Arrays.stream(condition.getCountry().split(",")).anyMatch(s -> !countryList.contains(s)), ProductResultCode.WAREHOUSE_OVERSEA_NOT_BELONG_COUNTRY);
                    }
                }else {
                    // 运营后台修改国家（仅限自营1688商品）
                    Goods goods = goodsService.queryGoodsById(condition.getId());
                    CheckUtils.notNull(goods, ProductResultCode.GOODS_NOT_EXIST_EXT);
                    CheckUtils.notNull(goods.getShopId(), ProductResultCode.SHOP_ID_NULL);
                    FaMerchantsApply faMerchantsApply = faMerchantsApplyService.selectById(goods.getShopId());
                    CheckUtils.notNull(faMerchantsApply, ProductResultCode.SHOP_ID_NULL);
                    if (!DeliveryTypeEnum.SELF_SUPPORT.getCode().equals(faMerchantsApply.getDeliveryType())){
                        CheckUtils.check(true,ProductResultCode.OPERATION_UPDATE_COUNTRY_ERROR);
                    }
                }

            }
        }

        Goods goods = goodsService.selectById(condition.getId());
//        if (1 == goods.getIsLock()) {
//            CheckUtils.check(true, ProductResultCode.GOODS_IS_LOCKED);
//        }
        String oldCountry = goods.getCountry();
        // 获取有效的物流属性国家
        GoodsEffectiveCountryDTO goodsEffectiveCountryDTO = new GoodsEffectiveCountryDTO();
        goodsEffectiveCountryDTO.setGoodsId(goods.getId());
        goodsEffectiveCountryDTO.setShopId(goods.getShopId());
        goodsEffectiveCountryDTO.setLogisticsProperty(goods.getLogisticsProperty());
        goodsEffectiveCountryDTO.setCountry(condition.getCountry());
        goodsEffectiveCountryDTO.setCategoryId(goods.getCategoryId());
        Map<String, String> distinguishCountryMap = logisticsPropertyConfigCoreService.distinguishGoodsCountries(goodsEffectiveCountryDTO);
        String effectiveCountry = distinguishCountryMap.get(logisticsPropertyConfigCoreService.EFFECTIVE);
        goods.setCountry(effectiveCountry);
        boolean b = goodsService.updateById(goods);
        CheckUtils.check(!b, ProductResultCode.UPDATE_ERROR);
        log.info("更新商品可售地区成功");
        goodsCoreService.refreshGoodsFreight(goods.getId());
        updateGoodsCountryVO.setEffectiveCountry(effectiveCountry);
        updateGoodsCountryVO.setIllegalCountry(distinguishCountryMap.get(logisticsPropertyConfigCoreService.ILLEGAL));

        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(condition.getId());
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("更新商品可售地区");
        goodsSyncModel.setSourceService("vp");
        mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.TEN_SEC);

        log.info("mq发送成功,商品id:{}", goodsSyncModel.getGoodsId());
        log.info("发送操作事件 goodsId:{} type:{}", condition.getId(), OperationLogTypeEnums.UPDATE_GOODS_COUNTRY.getDesc());
        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(condition.getId())
                .type(OperationLogTypeEnums.UPDATE_GOODS_COUNTRY)
                .content(OperationLogTypeEnums.UPDATE_GOODS_COUNTRY.getDesc())
                .oldData(oldCountry)
                .newData(effectiveCountry)
                .status(1)
                .user(getUserName());
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
        //校验是否需要发送同盾
        LogUtils.info(log, "updateCountry--> goodsId:{}, oldCountry:{}, newCountry:{}", goods.getId(),oldCountry,effectiveCountry);
        if (logisticsPropertyConfigCoreService.checkIsSendTongDun(oldCountry,effectiveCountry,goods.getIsShow())){
            tongDunGoodsImageCoreService.addTongDun(Lists.newArrayList(goods),1);
        }
        return updateGoodsCountryVO;
    }

    @Override
    public Boolean saveOrUpdatePropertyGoods(ProductInfoInput goodsInfoInput) {
        CheckUtils.check(StringUtils.isEmpty(goodsInfoInput.getGoodsIdsStr()), ProductResultCode.PARAMETER_ERROR);
        String goodsIdsStr = goodsInfoInput.getGoodsIdsStr();
        List<String> goodsIds = Arrays.stream(goodsIdsStr.trim().split("\\n")).collect(Collectors.toList());
        List<Long> goodsIdList = goodsIds.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(goodsIdList)) {
            for (Long goodsId : goodsIdList) {
                log.info("saveOrUpdatePropertyGoods goodsId{}", goodsId);
                updatePropertyGoodsInfo(goodsInfoInput.getPropertyGoodsInfoVOS(), goodsId, goodsInfoInput.getModelList());
                // 同步es
                GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                goodsSyncModel.setGoodsId(goodsId);
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("更新商品属性详情");
                goodsSyncModel.setSourceService("vp");
                mqSender.send("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel));
                log.info("saveOrUpdatePropertyGoods mq发送成功,商品id:{}", goodsSyncModel.getGoodsId());
            }
        }
        return true;
    }


    private String getImageUrl(LockGoodsUpdateCondition condition) {
        List<String> goodsImages = condition.getGoodsImages();
        List<String> detailsImgs = condition.getDetailsImgs();
        List<String> res = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsImages)) {
            res.addAll(goodsImages);
        }
        if (CollectionUtils.isNotEmpty(detailsImgs)) {
            res.addAll(detailsImgs);
        }
        return JSON.toJSONString(res);
    }

    private void fillUpdateReductionPrice(ProductInfoInput input) {
        if (input.getIsReductionPrice() == null || !input.getIsReductionPrice()) {
            return;
        }
        Boolean skuPrice = false;
        Boolean freightPrice = false;
        GoodsReductionVO goodsReductionVO = goodsPriceReductionCoreService.queryGoodsReductionList(input.getId(), 1);
        if (goodsReductionVO != null) {
            if (CollectionUtils.isNotEmpty(goodsReductionVO.getSkuReductionPriceVOList())) {
                Map<Long, SkuReductionPriceVO> skuMap = goodsReductionVO.getSkuReductionPriceVOList().stream().collect(Collectors.toMap(SkuReductionPriceVO::getSkuId, Function.identity(), (v1, v2) -> v1));
                if (CollectionUtils.isNotEmpty(input.getSkuInfo())) {
                    for (GoodsItemDTO goodsItem : input.getSkuInfo()) {
                        if (skuMap.containsKey(goodsItem.getSkuId())) {
                            SkuReductionPriceVO value = skuMap.get(goodsItem.getSkuId());
                            if (value.getPrice().compareTo(goodsItem.getOrginalPrice()) < 0 || value.getFreight().compareTo(goodsItem.getDefaultDelivery()) < 0) {
                                skuPrice = true;
                            }
                        }

                    }
                }
            }
            if (CollectionUtils.isNotEmpty(goodsReductionVO.getGoodsFreightVOList())) {
                Map<String, GoodsFreightReductionVO> freightMap = goodsReductionVO.getGoodsFreightVOList()
                        .stream()
                        .collect(Collectors.toMap(GoodsFreightReductionVO::getCountryCode, Function.identity(), (v1, v2) -> v1));
                if (CollectionUtils.isNotEmpty(input.getFreightList())) {
                    for (GoodsFreightVO goodsFreight : input.getFreightList()) {
                        if (freightMap.containsKey(goodsFreight.getCode())) {
                            GoodsFreightReductionVO value = freightMap.get(goodsFreight.getCode());
                            if (value.getCountryFreight().compareTo(goodsFreight.getCurrentFreight()) < 0) {
                                freightPrice = true;
                            }
                        }

                    }
                }
            }

            if (freightPrice || skuPrice) {
                input.setIsReductionPrice(true);
            } else {
                input.setIsReductionPrice(false);
            }
        } else {
            input.setIsReductionPrice(false);
        }
    }

    private void fillLockedReductionPrice(LockGoodsUpdateCondition input, List<GoodsItem> updateList) {
        if (input.getIsReductionPrice() == null || !input.getIsReductionPrice()) {
            return;
        }
        Boolean skuPrice = false;
        GoodsReductionVO goodsReductionVO = goodsPriceReductionCoreService.queryGoodsReductionList(input.getId(), 1);
        if (goodsReductionVO != null) {
            if (CollectionUtils.isNotEmpty(goodsReductionVO.getSkuReductionPriceVOList())) {
                Map<Long, SkuReductionPriceVO> skuMap = goodsReductionVO.getSkuReductionPriceVOList().stream().collect(Collectors.toMap(SkuReductionPriceVO::getSkuId, Function.identity(), (v1, v2) -> v1));
                if (CollectionUtils.isNotEmpty(updateList)) {
                    for (GoodsItem goodsItem : updateList) {
                        if (skuMap.containsKey(goodsItem.getId())) {
                            SkuReductionPriceVO value = skuMap.get(goodsItem.getSkuId());
                            if (value.getPrice().compareTo(goodsItem.getOrginalPrice()) < 0 || value.getFreight().compareTo(goodsItem.getDefaultDelivery()) < 0) {
                                skuPrice = true;
                            }
                        }

                    }
                }
            }
            if (skuPrice) {
                input.setIsReductionPrice(true);
            } else {
                input.setIsReductionPrice(false);
            }
        } else {
            input.setIsReductionPrice(false);
        }
    }

    //根据虚拟折扣计算price
    private BigDecimal countVirtualPrice(BigDecimal price, BigDecimal maxGoodsFreight, BigDecimal virtualDiscount) {
        if (maxGoodsFreight == null) {
            maxGoodsFreight = BigDecimal.ZERO;
        }
        BigDecimal divide = price.add(maxGoodsFreight).divide(virtualDiscount, 2, BigDecimal.ROUND_HALF_UP);
        return divide;
    }

    private BigDecimal countGoodsVirtualPrice(BigDecimal price, BigDecimal virtualDiscount) {
//        log.info("计算划线价price {}, virtualDiscount{}", price, virtualDiscount);
        BigDecimal divide = price.divide(virtualDiscount, 2, BigDecimal.ROUND_HALF_UP);
        return divide;
    }

    @Override
    public void updatePriceAndStock(UpdatePriceAndStockVo vo) {
        LogUtils.info(log, "updatePriceAndStock vo:{}", vo);

        CheckUtils.notNull(vo.getGoodsId(), ProductResultCode.PARAMETER_ID_ERROR);
//        CheckUtils.notNull(vo.getLogisticsProperty(), ProductResultCode.LOGISTICS_PROPERTY_NULL);
//        CheckUtils.isEmpty(vo.getSkuUpdateList(), ProductResultCode.PARAMETER_ERROR);
//        vo.setSkuUpdateList(vo.getSkuUpdateList().stream().filter(skuUpdateDto -> skuUpdateDto.getGoodsItemId() != null).collect(Collectors.toList()));
//
//        for (UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto : vo.getSkuUpdateList()) {
//            CheckUtils.notNull(skuUpdateDto.getGoodsItemId(), ProductResultCode.PARAMETER_ID_ERROR);
//            CheckUtils.notNull(skuUpdateDto.getOriginalPrice(), ProductResultCode.GOODS_SKU_PRICE_ERROR);
//            CheckUtils.notNull(skuUpdateDto.getStock(), ProductResultCode.GOODS_SKU_STORK_ERROR);
//            if (skuUpdateDto.getSkuStatus() == null) {
//                skuUpdateDto.setSkuStatus(1);
//            }
//        }

        Long goodsId = vo.getGoodsId();
        Goods goods = goodsService.getById(goodsId);
        CheckUtils.notNull(goods, ProductResultCode.GOODS_NOT_EXIST);

        //是否批发
        boolean isWholeSale = goods.getType() == 3;
//        if (isWholeSale) {
//            vo.getSkuUpdateList().forEach(skuUpdateDto -> CheckUtils.notNull(skuUpdateDto.getCostPrice(), ProductResultCode.GOODS_SKU_PRICE_ERROR));
//        }

        Long shopId = vo.getShopId();
        if (vo.getShopId() == null) {
            shopId = getShopId();
        }
        if (shopId != null) {
            CheckUtils.check(!shopId.equals(goods.getShopId()), ProductResultCode.EXIST_GOODS_NOT_BELONG_SHOP_EXT);
        }


        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(goods.getShopId());
        CheckUtils.check(faMerchantsApply.getIsSale() == 2, CustomResultCode.fill(ProductResultCode.HAS_LEAVE_EXIST, goods.getShopId().toString()));
        //是否海外仓
        boolean isWarehouseOverseas = faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.DIRECT.getCode());
        boolean isSelfSupport = faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.SELF_SUPPORT.getCode()) || faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.SELF_SUPPORT_SCM.getCode());

        if (vo.getWeight() == null || isWarehouseOverseas) {
            vo.getSkuUpdateList().stream().map(UpdatePriceAndStockVo.SkuUpdateDto::getWeight).filter(Objects::nonNull).findFirst().ifPresent(vo::setWeight);
        }
        if (vo.getWeight() != null) {
            for (UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto : vo.getSkuUpdateList()) {
                if (skuUpdateDto.getWeight() == null || !isWarehouseOverseas) {
                    skuUpdateDto.setWeight(vo.getWeight());
                }
            }
        }

        //海外仓/非海外仓重量校验
//        if (isWarehouseOverseas) {
//            for (UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto : vo.getSkuUpdateList()) {
//                if (skuUpdateDto.getWeight() == null || skuUpdateDto.getWeight() < 0) {
//                    CheckUtils.check(vo.getWeight() == null || vo.getWeight() < 0, ProductResultCode.WEIGHT_ERROR);
//                    skuUpdateDto.setWeight(vo.getWeight());
//                }
//                if (skuUpdateDto.getDefaultDelivery() == null) {
//                    skuUpdateDto.setDefaultDelivery(BigDecimal.ZERO);
//                }
//                CheckUtils.check(skuUpdateDto.getDefaultDelivery() == null || skuUpdateDto.getDefaultDelivery().compareTo(BigDecimal.ZERO) < 0, ProductResultCode.DEFAULT_DELIVERY_PRICE_ERROR);
//            }
//            vo.setWeight(vo.getSkuUpdateList().get(0).getWeight());
//            CheckUtils.isEmpty(vo.getFreightList(), ProductResultCode.GOODS_FREIGHT_EMPTY);
//        } else {
//            CheckUtils.check(vo.getWeight() == null || vo.getWeight() < 0, ProductResultCode.WEIGHT_ERROR);
//        }

        //校验物流属性
        if (vo.getLogisticsProperty() != null) {
            goodsLogisticsCoreService.checkLogisticProperty(goods.getCategoryId(), vo.getLogisticsProperty());
        }

        // 校验并获取可售国家
        String goodsEffectiveCountries = goodsEffectiveCountry(vo, goods);

        List<GoodsFreight> oldGoodsFreights = goodsFreightService.queryByGoodsId(goodsId);
        List<GoodsItem> goodsItemList = goodsItemService.queryGoodsItemByGoodsId(goodsId);
        GoodsExtDetail goodsExtDetail = goodsExtDetailService.lambdaQuery().eq(GoodsExtDetail::getGoodsId, goodsId).one();
        Double oldWeight = Double.parseDouble(GoodsExtDetailUtils.getWeightNumber(goodsExtDetail.getWeight()));

        checkSkuUpdateList(vo, goodsItemList);
        Map<Long, UpdatePriceAndStockVo.SkuUpdateDto> skuUpdateDtoMap = !CollectionUtils.isEmpty(vo.getSkuUpdateList()) ?
                vo.getSkuUpdateList().stream().collect(Collectors.toMap(UpdatePriceAndStockVo.SkuUpdateDto::getGoodsItemId, Function.identity(), (v1, v2) -> v1)) : Maps.newHashMap();

        //计算国家运费
        Map<String, BigDecimal> goodsFreightMap = Maps.newHashMap();
        QueryFreight queryFreight = new QueryFreight();
        boolean isNewFreight = checkIsFreightChangeAndInitFreightMap(isWarehouseOverseas, vo, oldGoodsFreights, queryFreight, goodsFreightMap, goods, shopId, goodsEffectiveCountries, oldWeight);
        BigDecimal defaultDelivery = goodsFreightMap.getOrDefault(CountryEnums.DE.getCode(), goodsFreightMap.values().stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO));


        //检测sku限价信息
        ProductInfoInput goodsInfoInput = buildProductInfoInput(vo, goods, goodsFreightMap, queryFreight);
        if (!faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.SELF_SUPPORT_SCM.getCode())
                && !faMerchantsApply.getDeliveryType().equals(DeliveryTypeEnum.SELF_SUPPORT.getCode())
                && !isWholeSale && CollectionUtils.isNotEmpty(goodsInfoInput.getSkuInfo())) {
            if (!isNewFreight && CollectionUtils.isNotEmpty(oldGoodsFreights)) {
                goodsInfoInput.setFreightList(oldGoodsFreights.stream().map(goodsFreight -> {
                    GoodsFreightVO freightVO = new GoodsFreightVO();
                    freightVO.setCurrentFreight(goodsFreight.getCurrentFreight());
                    freightVO.setCode(goodsFreight.getCode());
                    return freightVO;
                }).collect(Collectors.toList()));
            }
            skuLimitPriceCoreService.checkSkuLimitPrice(goodsInfoInput);
        }

        //有效sku (stock>0, status=1)
        boolean existValidSku = false;
        for (GoodsItem goodsItem : goodsItemList) {
            UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto = skuUpdateDtoMap.get(goodsItem.getId());
            Long stock = (skuUpdateDto != null && skuUpdateDto.getStock() != null) ? skuUpdateDto.getStock() : goodsItem.getStock();
            Integer skuStatus = (skuUpdateDto != null && skuUpdateDto.getSkuStatus() != null) ? skuUpdateDto.getSkuStatus() : goodsItem.getSkuStatus() == null ? 1 : goodsItem.getSkuStatus();
            if (skuStatus == 1 && stock > 0) {
                existValidSku = true;
            }
        }

        //上下架校验
        if (vo.getIsShow() != null && !vo.getIsShow().toString().equals(goods.getIsShow())) {
            if (vo.getIsShow().equals(GoodsIsShowEnums.SHELF.getType())) {
                CheckUtils.check(!GoodsIsShowEnums.TAKE_OFF.getType().toString().equals(goods.getIsShow()), ProductResultCode.GOODS_CANT_SHOW_FOR_NOT_OFF_SHELF);
//                if (goodsItems.stream().noneMatch(goodsItem -> goodsItem.getStock() > 0)) {
//                    if (CollectionUtils.isEmpty(vo.getSkuUpdateList()) || vo.getSkuUpdateList().stream().noneMatch(skuUpdateDto -> skuUpdateDto.getStock() > 0)) {
//                        CheckUtils.check(true, ProductResultCode.SHOW_PRODUCT_STOCK_ERROR);
//                    }
//                }
                CheckUtils.check(!existValidSku, ProductResultCode.SHOW_PRODUCT_STOCK_ERROR);
            } else if (vo.getIsShow().equals(GoodsIsShowEnums.TAKE_OFF.getType())) {
                CheckUtils.check(!GoodsIsShowEnums.SHELF.getType().toString().equals(goods.getIsShow()) && !GoodsIsShowEnums.SOLD_OUT.getType().toString().equals(goods.getIsShow()), ProductResultCode.GOODS_CANT_OFF_SHELF_FOR_NOT_ON_SHOW);
            }
        }

//        boolean existShelf = false;
//        boolean changeSkuStatus = false;
//        for (GoodsItem goodsItem : goodsItems) {
//            UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto = skuUpdateDtoMap.get(goodsItem.getId());
//            if (skuUpdateDto != null && skuUpdateDto.getSkuStatus() != null && !skuUpdateDto.getSkuStatus().equals(goodsItem.getSkuStatus())) {
//                changeSkuStatus = true;
//            }
//        }
        //无有效sku，自动下架
        boolean autoOffShelf = !existValidSku
                && goods.getIsShow().equals(GoodsIsShowEnums.SHELF.getType().toString())
                && (vo.getIsShow() == null || vo.getIsShow().equals(GoodsIsShowEnums.SHELF.getType()));


        //build修改内容类型
        Set<Integer> types = buildEditTypes(vo, goods, skuUpdateDtoMap, isWarehouseOverseas, oldGoodsFreights, oldWeight, autoOffShelf, goodsItemList, isNewFreight);
        CheckUtils.isEmpty(types, ProductResultCode.GOODS_EDIT_CHANGE_NOT_FIND);
        LogUtils.info(log, "updatePriceAndStock goodsId:{}, 最终改动内容:{}", goodsId, GoodsEditTypeEnums.listAllTypeNameByCodes(types));


        //检测是否存在待审核修改审批
        Integer existApply = goodsEditInfoService.lambdaQuery()
                .eq(GoodsEditInfo::getGoodsId, goodsId)
                .eq(GoodsEditInfo::getStatus, 0)
                .count();
        CheckUtils.check(existApply > 0, ProductResultCode.GOODS_EDIT_APPLY_EXIST);

        //获取类目价格类配置
        AliGoodsCategoryPriceConfig aliGoodsCategoryPriceConfig = getAliGoodsCategoryPriceConfig(vo, goods, isWholeSale);
        //build变更内容
        GoodsEditPriceDto goodsEditPriceDto = buildGoodsEditPriceDto(vo, goods, skuUpdateDtoMap, goodsEffectiveCountries,
                goodsFreightMap, queryFreight, defaultDelivery, aliGoodsCategoryPriceConfig, oldGoodsFreights, goodsItemList, oldWeight, autoOffShelf, isNewFreight);


        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getGoodsId, goodsId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .one();
        boolean isListing = listingFollowGoods != null;


        //是否需要审核
        boolean needAudit = isNeedAudit(vo, goods, skuUpdateDtoMap, isSelfSupport, goodsFreightMap, oldGoodsFreights, goodsItemList, oldWeight, types, isListing);

        //价格变动类型
        Integer addPrice = buildAddPrice(isWholeSale, skuUpdateDtoMap, goodsItemList);

        //所有sku下架 + 商品状态上架 => 则商品下架
        String extraInfo = "";
        if (autoOffShelf) {
            LogUtils.info(log,"所有sku下架===>商品下架");
            vo.setIsShow(GoodsIsShowEnums.TAKE_OFF.getType());
            extraInfo = extraInfo + "【所有sku下架触发商品自动下架】";
        }

        //无需审核，直接更新
        if (!needAudit) {
            String weight = vo.getWeight() != null ? GoodsExtDetailUtils.getWeight(vo.getWeight().toString()) : oldWeight.toString();
            extraInfo = skipAuditAndUpdate(vo, goods, skuUpdateDtoMap, weight, isWarehouseOverseas, goodsEffectiveCountries, goodsFreightMap, defaultDelivery,
                    goodsInfoInput, aliGoodsCategoryPriceConfig, oldGoodsFreights, goodsItemList, goodsExtDetail, types, extraInfo);
        }

        //listing跟新降价推送push
        if (vo.getAcceptReductionPush() != null && listingFollowGoods != null && !Objects.equals(vo.getAcceptReductionPush(), listingFollowGoods.getAcceptReductionPush())) {
            listingFollowGoods.setAcceptReductionPush(vo.getAcceptReductionPush());
            listingFollowGoods.setUpdateTime(LocalDateTime.now());
            listingFollowGoodsService.updateById(listingFollowGoods);
        }


        List<String> specialTagList = initSpecialTags(goodsId);

        GoodsEditInfo editInfo = new GoodsEditInfo();
        editInfo.setStatus(0);
        editInfo.setGoodsId(goods.getId());
        editInfo.setCategoryId(goods.getCategoryId());
        editInfo.setShopId(goods.getShopId());
        editInfo.setShopName(goods.getShopName());
        if (!needAudit) {
            editInfo.setAuditUser("system");
            editInfo.setAuditTime(LocalDateTime.now());
            editInfo.setStatus(1);
        }
        editInfo.setAddPrice(addPrice);
        editInfo.setApplyUser(getUserName());
        editInfo.setApplyTime(LocalDateTime.now());
        editInfo.setApplyReason((StringUtils.isBlank(vo.getApplyReason()) ? "" : vo.getApplyReason()) + extraInfo);
        editInfo.setIsDel(0);
        editInfo.setType(MathUtils.sum(Lists.newArrayList(types)));
        editInfo.setContent(JSON.toJSONString(goodsEditPriceDto));
        editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
        editInfo.setUpdateTime(LocalDateTime.now());
        goodsEditInfoService.save(editInfo);

        GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
        goodsEditInfoDetail.setEditId(editInfo.getId());
        goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
        goodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditPriceDto));
        goodsEditInfoDetailService.save(goodsEditInfoDetail);

        // 写入推荐算法日志
        recommendCoreService.saveRecommendGoodsChangeLog(editInfo);

        LogUtils.info(log, "修改商品价格和库存 finish goodsId:{}", goodsId);
    }

    private boolean checkIsFreightChangeAndInitFreightMap(boolean isWarehouseOverseas, UpdatePriceAndStockVo vo, List<GoodsFreight> oldGoodsFreights, QueryFreight queryFreight,
                                                          Map<String, BigDecimal> goodsFreightMap, Goods goods, Long shopId, String goodsEffectiveCountries, Double oldWeight) {
        boolean isNewFreight = false;
        Map<String, BigDecimal> newFreightMap = Maps.newHashMap();
        if (isWarehouseOverseas) {
            if (CollectionUtils.isEmpty(vo.getFreightList())) {
                newFreightMap = oldGoodsFreights.stream().collect(Collectors.toMap(GoodsFreight::getCode, GoodsFreight::getCurrentFreight, (v1, v2) -> v1));
            }
            if (CollectionUtils.isNotEmpty(vo.getFreightList())) {
                newFreightMap = vo.getFreightList().stream().collect(Collectors.toMap(GoodsFreightVO::getCode, GoodsFreightVO::getCurrentFreight, (v1, v2) -> v1));
                if (CollectionUtils.isEmpty(oldGoodsFreights)) {
                    isNewFreight = true;
                } else {
                    if (oldGoodsFreights.size() != vo.getFreightList().size()) {
                        isNewFreight = true;
                    } else {
                        Set<String> oldFreightCountryList = oldGoodsFreights.stream().map(GoodsFreight::getCode).collect(Collectors.toSet());
                        Set<String> newFreightCountryList = vo.getFreightList().stream().map(GoodsFreightVO::getCode).collect(Collectors.toSet());
                        if (!newFreightCountryList.containsAll(oldFreightCountryList)) {
                            isNewFreight = true;
                        } else {
                            for (GoodsFreight oldGoodsFreight : oldGoodsFreights) {
                                BigDecimal newFreight = newFreightMap.get(oldGoodsFreight.getCode());
                                if (!oldGoodsFreight.getCurrentFreight().equals(newFreight)) {
                                    isNewFreight = true;
                                }
                            }
                        }
                    }

                }
            }
        } else if (vo.getLogisticsProperty() != null && !vo.getLogisticsProperty().equals(goods.getLogisticsProperty()) || vo.getWeight() != null && !vo.getWeight().equals(oldWeight)) {
            newFreightMap = countFreight(goods, shopId, goodsEffectiveCountries, queryFreight, vo.getWeight() != null ? vo.getWeight() : oldWeight,
                    vo.getLogisticsProperty() != null ? vo.getLogisticsProperty() : goods.getLogisticsProperty());
            isNewFreight = true;
        } else {
            newFreightMap = oldGoodsFreights.stream().collect(Collectors.toMap(GoodsFreight::getCode, GoodsFreight::getCurrentFreight, (v1, v2) -> v1));
        }
        goodsFreightMap.putAll(newFreightMap);
        return isNewFreight;
    }

    private static void checkSkuUpdateList(UpdatePriceAndStockVo vo, List<GoodsItem> goodsItemList) {
        if (CollectionUtils.isNotEmpty(vo.getSkuUpdateList())) {
            Map<Long, GoodsItem> goodsItemMap = goodsItemList.stream().collect(Collectors.toMap(GoodsItem::getId, Function.identity(), (v1, v2) -> v1));
            for (UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto : vo.getSkuUpdateList()) {
                GoodsItem goodsItem = goodsItemMap.get(skuUpdateDto.getGoodsItemId());
                if (goodsItem == null) {
                    continue;
                }
                if (skuUpdateDto.getSkuStatus() == null) {
                    skuUpdateDto.setSkuStatus(goodsItem.getSkuStatus());
                }
                if (skuUpdateDto.getCostPrice() == null) {
                    skuUpdateDto.setCostPrice(goodsItem.getCostPrice());
                }
                if (skuUpdateDto.getOriginalPrice() == null) {
                    skuUpdateDto.setOriginalPrice(goodsItem.getOrginalPrice());
                }
                if (skuUpdateDto.getStock() == null) {
                    skuUpdateDto.setStock(goodsItem.getStock());
                }
                if (skuUpdateDto.getDefaultDelivery() == null) {
                    skuUpdateDto.setDefaultDelivery(goodsItem.getDefaultDelivery());
                }
                if (skuUpdateDto.getWeight() == null) {
                    if (vo.getWeight() != null) {
                        skuUpdateDto.setWeight(vo.getWeight());
                    } else {
                        skuUpdateDto.setWeight(Double.parseDouble(GoodsExtDetailUtils.getWeightNumber(goodsItem.getWeight())));
                    }
                }
            }
        }
    }

    private boolean isNeedAudit(UpdatePriceAndStockVo vo, Goods goods, Map<Long, UpdatePriceAndStockVo.SkuUpdateDto> skuUpdateDtoMap,
                                boolean isSelfSupport, Map<String, BigDecimal> goodsFreightMap, List<GoodsFreight> oldGoodsFreights,
                                List<GoodsItem> goodsItems, Double oldWeight, Set<Integer> types, boolean isListing) {
        boolean isWholeSale = goods.getType() == 3;
        boolean isLock = goods.getIsLock() == 1 || isListing;

        boolean needAudit = false;
        if (isLock) {
            for (GoodsItem goodsItem : goodsItems) {
                UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto = skuUpdateDtoMap.get(goodsItem.getId());
                if (skuUpdateDto != null) {
                    if (isWholeSale && skuUpdateDto.getCostPrice() != null && skuUpdateDto.getCostPrice().compareTo(goodsItem.getCostPrice()) > 0) {
                        needAudit = true;
                    }
                    if (!isWholeSale && skuUpdateDto.getOriginalPrice() != null && skuUpdateDto.getOriginalPrice().compareTo(goodsItem.getOrginalPrice()) > 0) {
                        needAudit = true;
                    }
                    if (!isWholeSale && skuUpdateDto.getDefaultDelivery() != null && skuUpdateDto.getDefaultDelivery().compareTo(goodsItem.getDefaultDelivery()) > 0) {
                        needAudit = true;
                    }
                }
            }
        }

        if (vo.getLogisticsProperty() != null && !vo.getLogisticsProperty().equals(goods.getLogisticsProperty()) || vo.getWeight() != null && !vo.getWeight().equals(oldWeight)) {
            if (isLock && vo.getWeight() != null && vo.getWeight().compareTo(oldWeight) > 0) {
                needAudit = true;
            }

            if (MapUtils.isNotEmpty(goodsFreightMap)) {
                if (isLock && CollectionUtils.isEmpty(oldGoodsFreights)) {
                    needAudit = true;
                } else if (CollectionUtils.isNotEmpty(oldGoodsFreights)){
                    BigDecimal oldDefaultDelivery = oldGoodsFreights.stream().filter(goodsFreight -> goodsFreight.getCode().equals(SaleableCountryEnum.DE.name())).findAny().map(GoodsFreight::getCurrentFreight).orElse(BigDecimal.ZERO);
                    BigDecimal newDefaultDelivery = goodsFreightMap.getOrDefault(SaleableCountryEnum.DE.name(), BigDecimal.ZERO);
                    if (isLock && newDefaultDelivery.compareTo(oldDefaultDelivery) > 0) {
                        needAudit = true;
                    }
                }
            }
        }

        if (isListing && types.size() == 1 && types.contains(GoodsEditTypeEnums.OFF_SHELF.getCode())) {
            //listing商品下架不走审核
            needAudit = false;
        }
        if (vo.getSkipAudit() != null && vo.getSkipAudit() == 1 || isSelfSupport) {
            needAudit = false;
        }
        LogUtils.info(log, "updatePriceAndStock --> isLock:{}, needAudit:{}", isLock, needAudit);
        return needAudit;
    }

    private String skipAuditAndUpdate(UpdatePriceAndStockVo vo, Goods goods, Map<Long, UpdatePriceAndStockVo.SkuUpdateDto> skuUpdateDtoMap,
                                      String weight, boolean isWarehouseOverseas, String goodsEffectiveCountries, Map<String, BigDecimal> goodsFreightMap,
                                      BigDecimal defaultDelivery, ProductInfoInput goodsInfoInput, AliGoodsCategoryPriceConfig aliGoodsCategoryPriceConfig,
                                      List<GoodsFreight> oldGoodsFreights, List<GoodsItem> goodsItems, GoodsExtDetail goodsExtDetail, Set<Integer> types, String extraInfo) {
        Long goodsId = goods.getId();
        boolean isWholeSale = goods.getType() == 3;
        List<GoodsItemGradient> goodsItemGradientList = Lists.newArrayList();

        List<GoodsSkuPo> goodsSkuPos = goodsSkuService.findListByGoodsId(goodsId);
        Map<Long, GoodsSkuPo> skuMap = goodsSkuPos.stream().collect(Collectors.toMap(GoodsSkuPo::getId, Function.identity()));
        for (GoodsItem goodsItem : goodsItems) {
            GoodsSkuPo skuPo = skuMap.get(goodsItem.getSkuId());
            UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto = skuUpdateDtoMap.get(goodsItem.getId());
            String skuWeight = null;
            if (skuUpdateDto != null) {
                if (isWholeSale) {
                    fillInfo(skuPo, GoodsSkuPo::setCostPrice, skuUpdateDto.getCostPrice());
                    goodsItem.setCostPrice(skuUpdateDto.getCostPrice());
                }
                goodsItem.setOrginalPrice(skuUpdateDto.getOriginalPrice());
                goodsItem.setStock(skuUpdateDto.getStock());
                goodsItem.setSkuStatus(skuUpdateDto.getSkuStatus());
                fillInfo(skuPo, GoodsSkuPo::setOriginalPrice, skuUpdateDto.getOriginalPrice());
                fillInfo(skuPo, GoodsSkuPo::setStock, skuUpdateDto.getStock());
                fillInfo(skuPo, GoodsSkuPo::setSkuStatus, skuUpdateDto.getSkuStatus());
                if (isWarehouseOverseas) {
                    defaultDelivery = skuUpdateDto.getDefaultDelivery() != null ? skuUpdateDto.getDefaultDelivery() : goodsItem.getDefaultDelivery();
                    if (skuUpdateDto.getWeight() != null) {
                        skuWeight = skuUpdateDto.getWeight().toString();
                    }
                }
            } else if (isWarehouseOverseas) {
                defaultDelivery = goodsItem.getDefaultDelivery();
                skuWeight = vo.getWeight() != null ? vo.getWeight().toString() : null;
            }

            if (!isWarehouseOverseas) {
                skuWeight = weight;
            }

            //批发商品 默认运费
            if (isWholeSale) {
                defaultDelivery = BigDecimal.ZERO;
            }

            BigDecimal price = goodsItem.getOrginalPrice().add(defaultDelivery);
            goodsItem.setDefaultDelivery(defaultDelivery);
            goodsItem.setPrice(price);
            goodsItem.setWeight(skuWeight);
            goodsItem.setUpdateTime(new Date());

            fillInfo(skuPo, GoodsSkuPo::setDefaultDelivery, defaultDelivery);
            fillInfo(skuPo, GoodsSkuPo::setPrice, price);
            fillInfo(skuPo, GoodsSkuPo::setWeight, skuWeight);

            if (isWholeSale && aliGoodsCategoryPriceConfig != null) {
                BigDecimal price2 = goodsItem.getCostPrice()
                        .divide(new BigDecimal("7.5"), 4, RoundingMode.HALF_UP)
                        .add(defaultDelivery)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal("100").subtract(aliGoodsCategoryPriceConfig.getProfitRate().subtract(new BigDecimal("5"))).subtract(aliGoodsCategoryPriceConfig.getRefundRate()), 2, RoundingMode.HALF_UP)
                        .add(aliGoodsCategoryPriceConfig.getFreight())
                        .add(aliGoodsCategoryPriceConfig.getProfit())
                        .subtract(defaultDelivery)
                        .setScale(2, RoundingMode.HALF_UP);

                BigDecimal price3 = goodsItem.getCostPrice()
                        .divide(new BigDecimal("7.5"), 4, RoundingMode.HALF_UP)
                        .add(defaultDelivery)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal("100").subtract(aliGoodsCategoryPriceConfig.getProfitRate().subtract(new BigDecimal("10"))).subtract(aliGoodsCategoryPriceConfig.getRefundRate()), 2, RoundingMode.HALF_UP)
                        .add(aliGoodsCategoryPriceConfig.getFreight())
                        .add(aliGoodsCategoryPriceConfig.getProfit())
                        .subtract(defaultDelivery)
                        .setScale(2, RoundingMode.HALF_UP);

                BigDecimal price4 = goodsItem.getCostPrice()
                        .divide(new BigDecimal("7.5"), 4, RoundingMode.HALF_UP)
                        .add(defaultDelivery)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal("100").subtract(aliGoodsCategoryPriceConfig.getProfitRate().subtract(new BigDecimal("15"))).subtract(aliGoodsCategoryPriceConfig.getRefundRate()), 2, RoundingMode.HALF_UP)
                        .add(aliGoodsCategoryPriceConfig.getFreight())
                        .add(aliGoodsCategoryPriceConfig.getProfit())
                        .subtract(defaultDelivery)
                        .setScale(2, RoundingMode.HALF_UP);
//
                goodsItemGradientList.add(new GoodsItemGradient(goodsId, goodsItem.getId(), goodsItem.getSkuId(), 1, goodsItem.getPrice(), 1, 2));
                goodsItemGradientList.add(new GoodsItemGradient(goodsId, goodsItem.getId(), goodsItem.getSkuId(), 2, price2, 3, 5));
                goodsItemGradientList.add(new GoodsItemGradient(goodsId, goodsItem.getId(), goodsItem.getSkuId(), 3, price3, 6, 17));
                goodsItemGradientList.add(new GoodsItemGradient(goodsId, goodsItem.getId(), goodsItem.getSkuId(), 4, price4, 18, 999));
            }
        }
        goodsItemService.updateBatchById(goodsItems);
        if (CollectionUtils.isNotEmpty(goodsSkuPos)) {
            goodsSkuService.updateBatchByIdAndGoodsId(goodsSkuPos);
        }

        if (isWholeSale && CollectionUtils.isNotEmpty(goodsItemGradientList)) {
            List<GoodsItemGradient> oldGoodsItemGradientList = goodsItemGradientService.lambdaQuery()
                    .eq(GoodsItemGradient::getGoodsId, goodsId)
                    .eq(GoodsItemGradient::getIsDel, 0)
                    .list();
            if (CollectionUtils.isNotEmpty(oldGoodsItemGradientList)) {
                oldGoodsItemGradientList.forEach(goodsItemGradient -> {
                    goodsItemGradient.setIsDel(1);
                    goodsItemGradient.setUpdateTime(LocalDateTime.now());
                });
                goodsItemGradientList.addAll(oldGoodsItemGradientList);
            }

            if (CollectionUtils.isNotEmpty(goodsItemGradientList)) {
                goodsItemGradientService.saveOrUpdateBatch(goodsItemGradientList);
            }
        }

        if (vo.getIsShow() != null && !vo.getIsShow().toString().equals(goods.getIsShow())) {
            if (Objects.equals(vo.getIsShow(), GoodsIsShowEnums.SHELF.getType()) || Objects.equals(vo.getIsShow(), GoodsIsShowEnums.TAKE_OFF.getType())) {
                goods.setIsShow(vo.getIsShow().toString());
            }
        }

        List<BigDecimal> priceList = goodsItems.stream().map(GoodsItem::getPrice).collect(Collectors.toList());
        priceList.stream().max(BigDecimal::compareTo).ifPresent(goods::setMaxPrice);
        priceList.stream().min(BigDecimal::compareTo).ifPresent(goods::setMinPrice);
        if (isWholeSale) {
            List<BigDecimal> costPriceList = goodsItems.stream().map(GoodsItem::getCostPrice).filter(Objects::nonNull).collect(Collectors.toList());
            costPriceList.stream().max(BigDecimal::compareTo).ifPresent(goods::setMaxCostPrice);
            costPriceList.stream().min(BigDecimal::compareTo).ifPresent(goods::setMinCostPrice);
        }
        goods.setLogisticsProperty(vo.getLogisticsProperty());
        goods.setUpdateTime(LocalDateTime.now());
        goods.setCountry(goodsEffectiveCountries);
        goodsService.updateById(goods);

        goodsExtDetail.setWeight(weight);
        goodsExtDetailService.updateById(goodsExtDetail);

        if (MapUtils.isEmpty(goodsFreightMap)) {
            if (CollectionUtils.isNotEmpty(oldGoodsFreights)) {
                oldGoodsFreights.forEach(goodsFreight -> {
                    goodsFreight.setIsDel(1);
                    goodsFreight.setUpdateTime(LocalDateTime.now());
                });
                goodsFreightService.updateBatchById(oldGoodsFreights);
            }
        } else {
            filterFreight(goodsInfoInput);
            goodsFreightCoreService.saveOrUpdateFreight(goodsInfoInput);
        }

        //改价 --> 自动退出flashDeal
        boolean inFlashDeal = CollectionUtils.isNotEmpty(goodsExtConfigService.selectGoodsTagConfig(Collections.singletonList(vo.getGoodsId()), CommonConstants.FLASH_DEAL_TAG_ID));
        if (inFlashDeal && types.contains(GoodsEditTypeEnums.PRICE.getCode())) {
            LogUtils.info(log, "updatePriceAndStock --> 自动退出flashDeal goodsId:{}", goodsId);
            extraInfo = extraInfo + "【商家主动调价自动退出flashDeal】";

            //解除锁定标签
            UpdateLockLabelCondition condition = new UpdateLockLabelCondition(Collections.singletonList(goodsId), Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()), "自动解标签(改价自动退出flashDeal)", OperationLogTypeEnums.UNLOCK_AUTO.getCode());
            goodsLockCoreService.removeLockByApi(condition);
            LogUtils.info(log, "updatePriceAndStock --> 自动退出flashDeal,解除锁定标签 goodsId:{}", goodsId);

            //删除活动标签
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setGoodsIds(Collections.singletonList(goodsId));
            bindTagDTO.setTagId(CommonConstants.FLASH_DEAL_TAG_ID);
            goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
            LogUtils.info(log, "updatePriceAndStock --> 自动退出flashDeal,删除活动标签 goodsId:{}", goodsId);

            //退出活动
            Long activityId = activityRemoteClientFactory.exitFlashDeal(goodsId);
            LogUtils.info(log, "updatePriceAndStock --> 自动退出flashDeal,退出活动 goodsId:{}, activityId:{}", goodsId, activityId);

            //删除活动价格备份信息
            if (activityId != null) {
                List<ActivityOriginalPrice> activityOriginalPriceList = activityOriginalPriceService.queryPriceByActivityIdAndGoodsId(activityId, goodsId);
                if (CollectionUtils.isNotEmpty(activityOriginalPriceList)) {
                    activityOriginalPriceService.deleteByIds(activityOriginalPriceList.stream().map(ActivityOriginalPrice::getId).collect(Collectors.toList()));
                    LogUtils.info(log, "updatePriceAndStock --> 自动退出flashDeal,删除活动价格备份信息 goodsId:{}, size:{}", goodsId, activityOriginalPriceList.size());
                }
            }
        }

        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(goodsId);
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("修改商品价格和库存");
        goodsSyncModel.setSourceService("vp");
        mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.FIVE_SEC);

        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(goodsId)
                .type(OperationLogTypeEnums.UPDATE_PRICE_AND_STOCK)
                .newData(JSON.toJSONString(vo))
                .content(OperationLogTypeEnums.UPDATE_PRICE_AND_STOCK.getDesc() + extraInfo)
                .status(1)
                .user(getUserName());
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
        return extraInfo;
    }

    private <E, T> void fillInfo(E entity, BiConsumer<E, T> consumer, T value) {
        if (entity == null) {
            return;
        }

        consumer.accept(entity, value);
    }

    private Integer buildAddPrice(boolean isWholeSale, Map<Long, UpdatePriceAndStockVo.SkuUpdateDto> skuUpdateDtoMap, List<GoodsItem> goodsItems) {
        Set<Integer> changePrice = new HashSet<>();
        for (GoodsItem goodsItem : goodsItems) {
            UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto = skuUpdateDtoMap.get(goodsItem.getId());
            if (skuUpdateDto == null) {
                continue;
            }
            if (skuUpdateDto.getOriginalPrice() != null && skuUpdateDto.getOriginalPrice().compareTo(goodsItem.getOrginalPrice()) != 0
                    || (skuUpdateDto.getCostPrice() != null && (goodsItem.getCostPrice() == null || skuUpdateDto.getCostPrice().compareTo(goodsItem.getCostPrice()) != 0))) {
                if (isWholeSale && (skuUpdateDto.getCostPrice() != null && (goodsItem.getCostPrice() == null || skuUpdateDto.getCostPrice().compareTo(goodsItem.getCostPrice()) != 0))) {
                    if (skuUpdateDto.getCostPrice() != null && goodsItem.getCostPrice() != null && skuUpdateDto.getCostPrice().compareTo(goodsItem.getCostPrice()) < 0) {
                        changePrice.add(0);
                    } else {
                        changePrice.add(1);
                    }
                } else if (!isWholeSale && skuUpdateDto.getOriginalPrice() != null && skuUpdateDto.getOriginalPrice().compareTo(goodsItem.getOrginalPrice()) != 0) {
                    if (goodsItem.getOrginalPrice().compareTo(skuUpdateDto.getOriginalPrice()) > 0) {
                        changePrice.add(0);
                    } else {
                        changePrice.add(1);
                    }
                }
            }
        }
        if (changePrice.size() > 1) {
            return 2;
        } else if (!changePrice.isEmpty()) {
            return changePrice.contains(0) ? 0 : 1;
        }
        return null;
    }

    @NotNull
    private GoodsEditPriceDto buildGoodsEditPriceDto(UpdatePriceAndStockVo vo, Goods goods, Map<Long, UpdatePriceAndStockVo.SkuUpdateDto> skuUpdateDtoMap,
                                                     String goodsEffectiveCountries, Map<String, BigDecimal> goodsFreightMap, QueryFreight queryFreight,
                                                     BigDecimal defaultDelivery, AliGoodsCategoryPriceConfig aliGoodsCategoryPriceConfig,
                                                     List<GoodsFreight> oldGoodsFreights, List<GoodsItem> goodsItems, Double oldWeight, boolean autoOffShelf, boolean isNewFreight) {
        boolean isWholeSale = goods.getType() == 3;
        GoodsEditPriceDto goodsEditPriceDto = new GoodsEditPriceDto();
        goodsEditPriceDto.setOldIsShow(Integer.parseInt(goods.getIsShow()));
        goodsEditPriceDto.setNewIsShow(vo.getIsShow() != null ? vo.getIsShow() : Integer.parseInt(goods.getIsShow()));

        goodsEditPriceDto.setOldWeight(oldWeight);
        goodsEditPriceDto.setNewWeight(vo.getWeight() != null ? vo.getWeight() : oldWeight);

        goodsEditPriceDto.setOldLogisticProperty(goods.getLogisticsProperty());
        goodsEditPriceDto.setNewLogisticProperty(vo.getLogisticsProperty() != null ? vo.getLogisticsProperty() : goods.getLogisticsProperty());

        goodsEditPriceDto.setOldCountry(goods.getCountry());
        goodsEditPriceDto.setNewCountry(goodsEffectiveCountries);

        if (isNewFreight) {
            List<GoodsEditPriceDto.FreightChangeDto> freightChangeList = Lists.newArrayList();

//            if (CollectionUtils.isEmpty(oldGoodsFreights) && MapUtils.isNotEmpty(goodsFreightMap)) {
//                for (Map.Entry<String, BigDecimal> entry : goodsFreightMap.entrySet()) {
//                    freightChangeList.add(new GoodsEditPriceDto.FreightChangeDto(entry.getKey(), BigDecimal.ZERO, entry.getValue()));
//                }
//            }

            Set<String> allCountries = Sets.newHashSet();
            Map<String, BigDecimal> oldFreightMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(oldGoodsFreights)) {
                oldFreightMap = oldGoodsFreights.stream().collect(Collectors.toMap(GoodsFreight::getCode, GoodsFreight::getCurrentFreight, (v1, v2) -> v1));
                oldGoodsFreights.stream().map(GoodsFreight::getCode).forEach(allCountries::add);
            }
            if (MapUtils.isNotEmpty(goodsFreightMap)) {
                goodsFreightMap.forEach((k, v) -> allCountries.add(k));
            }
            if (CollectionUtils.isNotEmpty(allCountries)) {
                for (String country : allCountries) {
                    BigDecimal oldFreight = oldFreightMap.getOrDefault(country, BigDecimal.ZERO);
                    BigDecimal newFreight = goodsFreightMap.getOrDefault(country, BigDecimal.ZERO);
                    freightChangeList.add(new GoodsEditPriceDto.FreightChangeDto(country, oldFreight, newFreight));
                }
            }

            goodsEditPriceDto.setFreightChangeDtoList(freightChangeList);
            goodsEditPriceDto.setFreightGradientDto(queryFreight.getFreightGradientDto());
        }

        List<GoodsEditPriceDto.SkuChangeDto> skuChangeList = Lists.newArrayList();
        for (GoodsItem goodsItem : goodsItems) {
            UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto = skuUpdateDtoMap.get(goodsItem.getId());
            if (skuUpdateDto == null) {
                continue;
            }

            if (skuUpdateDto.getOriginalPrice() != null && skuUpdateDto.getOriginalPrice().compareTo(goodsItem.getOrginalPrice()) != 0
                    || (skuUpdateDto.getCostPrice() != null && (goodsItem.getCostPrice() == null || skuUpdateDto.getCostPrice().compareTo(goodsItem.getCostPrice()) != 0))
                    || (skuUpdateDto.getDefaultDelivery() != null && skuUpdateDto.getDefaultDelivery().compareTo(goodsItem.getDefaultDelivery()) != 0)
                    || skuUpdateDto.getStock() != null && !skuUpdateDto.getStock().equals(goodsItem.getStock())
                    || skuUpdateDto.getSkuStatus() != null && !skuUpdateDto.getSkuStatus().equals(goodsItem.getSkuStatus())) {
                if (isWholeSale && skuUpdateDto.getCostPrice() != null) {
                    BigDecimal newPrice = skuUpdateDto.getCostPrice()
                            .divide(new BigDecimal("7.5"), 4, RoundingMode.HALF_UP)
                            .add(defaultDelivery)
                            .multiply(new BigDecimal("100"))
                            .divide(new BigDecimal("100").subtract(aliGoodsCategoryPriceConfig.getProfitRate()).subtract(aliGoodsCategoryPriceConfig.getRefundRate()), 2, RoundingMode.HALF_UP)
                            .add(aliGoodsCategoryPriceConfig.getFreight())
                            .add(aliGoodsCategoryPriceConfig.getProfit())
                            .subtract(defaultDelivery)
                            .setScale(2, RoundingMode.HALF_UP);
                    log.info("updatePriceAndStock批发商品==>根据采购价生成销售价 goodsItemId:{}, oldCostPrice:{}, newCostPrice:{}, oldPrice:{}, newPrice:{}", goodsItem.getId(), goodsItem.getCostPrice(), skuUpdateDto.getCostPrice(), goodsItem.getOrginalPrice(), newPrice);
                    skuUpdateDto.setOriginalPrice(newPrice);
                }
                skuChangeList.add(new GoodsEditPriceDto.SkuChangeDto(skuUpdateDto.getGoodsItemId(), skuUpdateDto.getSkuId(), goodsItem.getOrginalPrice(), skuUpdateDto.getOriginalPrice(), goodsItem.getCostPrice(), skuUpdateDto.getCostPrice(), goodsItem.getStock(), skuUpdateDto.getStock(), goodsItem.getSkuStatus(), skuUpdateDto.getSkuStatus(), goodsItem.getDefaultDelivery(), skuUpdateDto.getDefaultDelivery()));
            }
        }
        if (CollectionUtils.isNotEmpty(skuChangeList)) {
            goodsEditPriceDto.setSkuChangeDtoList(skuChangeList);
        }

        if (autoOffShelf) {
            LogUtils.info(log, "所有sku下架===>商品下架111");
            goodsEditPriceDto.setOldIsShow(Integer.parseInt(goods.getIsShow()));
            goodsEditPriceDto.setNewIsShow(vo.getIsShow());
        }
        return goodsEditPriceDto;
    }

    private Set<Integer> buildEditTypes(UpdatePriceAndStockVo vo, Goods goods, Map<Long, UpdatePriceAndStockVo.SkuUpdateDto> skuUpdateDtoMap, boolean isWarehouseOverseas, List<GoodsFreight> oldGoodsFreights, Double oldWeight, boolean autoOffShelf, List<GoodsItem> goodsItems, boolean isNewFreight) {
        boolean isWholeSale = goods.getType() == 3;
        Set<Integer> types = Sets.newHashSet();
        if (vo.getIsShow() != null && !vo.getIsShow().toString().equals(goods.getIsShow())) {
            types.add(vo.getIsShow().equals(GoodsIsShowEnums.SHELF.getType()) ? GoodsEditTypeEnums.ON_SHELF.getCode() : GoodsEditTypeEnums.OFF_SHELF.getCode());
        }
        if ((vo.getLogisticsProperty() != null && !vo.getLogisticsProperty().equals(goods.getLogisticsProperty()))
                || (vo.getWeight() != null && !vo.getWeight().equals(oldWeight))) {
            types.add(GoodsEditTypeEnums.WEIGHT.getCode());
        } else if (isWarehouseOverseas && isNewFreight) {
            types.add(GoodsEditTypeEnums.WEIGHT.getCode());
        }

        for (GoodsItem goodsItem : goodsItems) {
            UpdatePriceAndStockVo.SkuUpdateDto skuUpdateDto = skuUpdateDtoMap.get(goodsItem.getId());
            if (skuUpdateDto == null) {
                continue;
            }
            if (skuUpdateDto.getOriginalPrice() != null && skuUpdateDto.getOriginalPrice().compareTo(goodsItem.getOrginalPrice()) != 0
                    || (skuUpdateDto.getCostPrice() != null && (goodsItem.getCostPrice() == null || skuUpdateDto.getCostPrice().compareTo(goodsItem.getCostPrice()) != 0))
                    || (skuUpdateDto.getDefaultDelivery() != null && goodsItem.getDefaultDelivery() != null && skuUpdateDto.getDefaultDelivery().compareTo(goodsItem.getDefaultDelivery()) != 0)
                    || skuUpdateDto.getStock() != null && !skuUpdateDto.getStock().equals(goodsItem.getStock())
                    || skuUpdateDto.getSkuStatus() != null && !skuUpdateDto.getSkuStatus().equals(goodsItem.getSkuStatus())) {
                if (isWholeSale && (skuUpdateDto.getCostPrice() != null && (goodsItem.getCostPrice() == null || skuUpdateDto.getCostPrice().compareTo(goodsItem.getCostPrice()) != 0))) {
                    types.add(GoodsEditTypeEnums.PRICE.getCode());
                } else if (!isWholeSale && skuUpdateDto.getOriginalPrice() != null && skuUpdateDto.getOriginalPrice().compareTo(goodsItem.getOrginalPrice()) != 0) {
                    types.add(GoodsEditTypeEnums.PRICE.getCode());
                }
                if (isWarehouseOverseas && skuUpdateDto.getDefaultDelivery() != null && skuUpdateDto.getDefaultDelivery().compareTo(goodsItem.getDefaultDelivery()) != 0) {
                    types.add(GoodsEditTypeEnums.PRICE.getCode());
                }
                if (skuUpdateDto.getStock() != null && !skuUpdateDto.getStock().equals(goodsItem.getStock())) {
                    types.add(GoodsEditTypeEnums.STOCK.getCode());
                }
                if (skuUpdateDto.getSkuStatus() != null && !skuUpdateDto.getSkuStatus().equals(goodsItem.getSkuStatus())) {
                    types.add(GoodsEditTypeEnums.SKU_STATUS.getCode());
                }
            }
        }

        if (autoOffShelf) {
            LogUtils.info(log, "所有sku下架===>商品下架-0");
            types.add(GoodsEditTypeEnums.OFF_SHELF.getCode());
        }
        return types;
    }

    private ProductInfoInput buildProductInfoInput(UpdatePriceAndStockVo vo, Goods goods, Map<String, BigDecimal> goodsFreightMap, QueryFreight queryFreight) {
        ProductInfoInput goodsInfoInput = new ProductInfoInput();
        goodsInfoInput.setCategoryId(goods.getCategoryId());
        goodsInfoInput.setId(goods.getId());
        goodsInfoInput.setType(goods.getType());
        if (MapUtils.isNotEmpty(goodsFreightMap)) {
            List<GoodsFreightVO> freightList = goodsFreightMap.entrySet().stream().map(stringBigDecimalEntry -> {
                GoodsFreightVO freightVO = new GoodsFreightVO();
                freightVO.setCode(stringBigDecimalEntry.getKey());
                freightVO.setCurrentFreight(stringBigDecimalEntry.getValue());
                return freightVO;
            }).collect(Collectors.toList());
            goodsInfoInput.setFreightList(freightList);
            goodsInfoInput.setFreightGradientDto(queryFreight.getFreightGradientDto());
        }

        if (CollectionUtils.isNotEmpty(vo.getSkuUpdateList())) {
            List<GoodsItemDTO> itemDTOList = vo.getSkuUpdateList().stream()
                    .filter(dto -> dto.getOriginalPrice() != null)
                    .map(dto -> {
                        GoodsItemDTO itemDTO = new GoodsItemDTO();
                        itemDTO.setOrginalPrice(dto.getOriginalPrice());
                        return itemDTO;
                    }).collect(Collectors.toList());
            goodsInfoInput.setSkuInfo(itemDTOList);
        }
        return goodsInfoInput;
    }

    private AliGoodsCategoryPriceConfig getAliGoodsCategoryPriceConfig(UpdatePriceAndStockVo vo, Goods goods, boolean isWholeSale) {
        AliGoodsCategoryPriceConfig aliGoodsCategoryPriceConfig = null;
        if (isWholeSale && CollectionUtils.isNotEmpty(vo.getSkuUpdateList()) && vo.getSkuUpdateList().stream().allMatch(skuUpdateDto -> skuUpdateDto.getCostPrice() != null)) {
            double avgPrice = vo.getSkuUpdateList().stream()
                    .filter(skuUpdateDto -> skuUpdateDto.getCostPrice() != null)
                    .mapToDouble(skuUpdateDto -> skuUpdateDto.getCostPrice().doubleValue()).average().orElse(0d);
            LogUtils.info(log, "updatePriceAndStock批发商品 avgPrice:{}", avgPrice);
            aliGoodsCategoryPriceConfig = aliGoodsCategoryPriceConfigService.lambdaQuery()
                    .eq(AliGoodsCategoryPriceConfig::getLastCategoryId, goods.getCategoryId())
                    .le(AliGoodsCategoryPriceConfig::getStartPrice, avgPrice)
                    .gt(AliGoodsCategoryPriceConfig::getEndPrice, avgPrice)
                    .one();
            LogUtils.info(log, "updatePriceAndStock批发商品 aliGoodsCategoryPriceConfig.1:{}", aliGoodsCategoryPriceConfig);
            if (aliGoodsCategoryPriceConfig == null) {
                aliGoodsCategoryPriceConfig = aliGoodsCategoryPriceConfigService.lambdaQuery()
                        .eq(AliGoodsCategoryPriceConfig::getLastCategoryId, 0)
                        .le(AliGoodsCategoryPriceConfig::getStartPrice, avgPrice)
                        .gt(AliGoodsCategoryPriceConfig::getEndPrice, avgPrice)
                        .one();
                LogUtils.info(log, "updatePriceAndStock批发商品 aliGoodsCategoryPriceConfig.2:{}", aliGoodsCategoryPriceConfig);
            }
            CheckUtils.notNull(aliGoodsCategoryPriceConfig, ProductResultCode.SPIDER_1688_NOT_HIT_PRICE_MODEL);
        }
        return aliGoodsCategoryPriceConfig;
    }

    private String goodsEffectiveCountry(UpdatePriceAndStockVo vo, Goods goods) {
        GoodsEffectiveCountryDTO goodsEffectiveCountryDTO = new GoodsEffectiveCountryDTO();
        goodsEffectiveCountryDTO.setShopId(goods.getShopId());
        goodsEffectiveCountryDTO.setLogisticsProperty(vo.getLogisticsProperty() == null ? goods.getLogisticsProperty() : vo.getLogisticsProperty());
        goodsEffectiveCountryDTO.setCountry(goods.getCountry());
        goodsEffectiveCountryDTO.setCategoryId(goods.getCategoryId());
        return logisticsPropertyConfigCoreService.getGoodsEffectiveCountries(goodsEffectiveCountryDTO);
    }

    private Map<String, BigDecimal> countFreight(Goods goods, Long shopId, String goodsEffectiveCountries, QueryFreight queryFreight, Double weight, Integer logisticsProperty) {
        Map<String, BigDecimal> goodsFreightMap;
        queryFreight.setWeight(weight.toString());
        queryFreight.setCategoryId(goods.getCategoryId());
        queryFreight.setLogisticsProperty(logisticsProperty);
        queryFreight.setShopId(shopId);
        queryFreight.setType(goods.getType());
        queryFreight.setGoodsId(goods.getId());
        queryFreight.setCountryCodes(logisticsPropertyConfigCoreService.transformSystemCountryList(goodsEffectiveCountries));
        goodsFreightMap = goodsFreightCoreService.countFreight(queryFreight);
        return goodsFreightMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProperty(UpdatePropertyVo vo) {
        LogUtils.info(log, "updateProperty vo:{}", vo);
        CheckUtils.notNull(vo.getGoodsId(), ProductResultCode.PARAMETER_ID_ERROR);
        CheckUtils.check(CollectionUtils.isEmpty(vo.getProperties()) || CollectionUtils.isEmpty(vo.getSkuInfo()), ProductResultCode.PARAMETER_ERROR);

        Long goodsId = vo.getGoodsId();
        Goods goods = goodsService.getById(goodsId);
        CheckUtils.notNull(goods, ProductResultCode.GOODS_NOT_EXIST);

        Long shopId = vo.getShopId();
        if (vo.getShopId() == null) {
            shopId = getShopId();
        }
        if (shopId != null) {
            CheckUtils.check(!shopId.equals(goods.getShopId()), ProductResultCode.EXIST_GOODS_NOT_BELONG_SHOP_EXT);
        }

        //批发商品暂不支持修改
//        CheckUtils.check(goods.getType() == 3, ProductResultCode.WHOLESALE_GOODS_CANNOT_UPDATE);

        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(goods.getShopId());
        CheckUtils.check(faMerchantsApply.getIsSale() == 2, CustomResultCode.fill(ProductResultCode.HAS_LEAVE_EXIST, goods.getShopId().toString()));

//        WarehouseStockGoods one = warehouseStockGoodsService.lambdaQuery()
//                .eq(WarehouseStockGoods::getGoodsId, goodsId)
//                .eq(WarehouseStockGoods::getStatus, 0)
//                .and(wrapper -> wrapper.gt(WarehouseStockGoods::getShowNums,0).or().gt(WarehouseStockGoods::getWaitSignNums,0))
//                .last("limit 1").one();
//        CheckUtils.check(one != null, ProductResultCode.WAREHOUSE_STOCK_ERROR);
        GoodsInfoUtils.checkPropertyImage(vo.getProperties());
        //校验图片格式
        GoodsInfoUtils.checkImage(vo);

        //校验是否包含中文
        checkIfContainsChinese(vo);

        GoodsFreight deFreight = goodsFreightService.lambdaQuery()
                .eq(GoodsFreight::getGoodsId, vo.getGoodsId())
                .eq(GoodsFreight::getIsDel, 0)
                .eq(GoodsFreight::getCode, SaleableCountryEnum.DE.name())
                .and(wrapper ->
                        wrapper.eq(GoodsFreight::getGradientNum, 1)
                                .or()
                                .isNull(GoodsFreight::getGradientNum))
                .one();
        //批发商品 默认运费
        vo.getSkuInfo().forEach(goodsItemDTO -> goodsItemDTO.setDefaultDelivery(deFreight == null || goods.getType() == 3 ? BigDecimal.ZERO : deFreight.getCurrentFreight()));

        //property、sku、skuProperty、skuMap
        GoodsInfoUtils.isPropertyValues(vo);

        //sku编辑权限校验
        List<GoodsItem> oldGoodsItems = goodsItemService.queryGoodsItemByGoodsId(vo.getGoodsId());
        ProductInfoInput goodsInfoInput = BeanCopyUtil.transform(vo, ProductInfoInput.class);
        goodsInfoInput.setId(vo.getGoodsId());
        goodsInfoInput.setProductId(goods.getProductId());
        goodsInfoInput.setIsShow(goods.getIsShow());
        checkUpdatePropertyPermission2(goodsInfoInput, oldGoodsItems);

        //校验商品维护规范
//        goodsStandardCoreService.checkGoodsStandard(BeanCopyUtil.transform(goodsInfoInput, ProductInfoInput.class));

        //检测是否存在待审核修改审批
        Integer existApply = goodsEditInfoService.lambdaQuery()
                .eq(GoodsEditInfo::getGoodsId, goodsId)
                .eq(GoodsEditInfo::getStatus, 0)
                .count();
        CheckUtils.check(existApply > 0, ProductResultCode.GOODS_EDIT_APPLY_EXIST);

//        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery().eq(ListingFollowGoods::getGoodsId, goodsId)
//                .eq(ListingFollowGoods::getIsDel, 0)
//                .eq(ListingFollowGoods::getStatus, 1)
//                .one();
//        boolean needAudit = goods.getIsLock() == 1 || listingFollowGoods != null;
        boolean needAudit = true;

//        判断是否需要更新
//        boolean needUpdate = false;
//        List<ProductSku> oldProductSkuList = productSkuService.queryProductSkuByProductId(goods.getProductId());

        Map<Long, List<Long>> oldPropertyIdMap = Maps.newHashMap();
        List<Long> propertyIds = Lists.newArrayList();
        oldGoodsItems.stream().map(GoodsItem::getPvalueStr)
                .forEach(s -> Arrays.stream(s.split(";"))
                        .forEach(s2 -> {
                            String[] kvArr = s2.split(":");
                            Long propertyId = Long.parseLong(kvArr[0]);
                            Long propertyValueId = Long.parseLong(kvArr[1]);
                            if (!propertyIds.contains(propertyId)) {
                                propertyIds.add(propertyId);
                            }
                            List<Long> propertyValueIds = oldPropertyIdMap.get(propertyId);
                            if (CollectionUtils.isEmpty(propertyValueIds)) {
                                propertyValueIds = Lists.newArrayList();
                            }
                            if (!propertyValueIds.contains(propertyValueId)) {
                                propertyValueIds.add(propertyValueId);
                                oldPropertyIdMap.put(propertyId, propertyValueIds);
                            }
                        }));
        List<Long> oldPropertyValueIds = Lists.newArrayList();
        oldPropertyIdMap.values().forEach(oldPropertyValueIds::addAll);


        List<PropertyImgDetail> propertyImgDetails = propertyInfoService.queryByImgList(goods.getProductId(), oldPropertyValueIds);
        Map<Long, String> oldPropertyValueImgMap = propertyImgDetails.stream()
                .filter(propertyImgDetail -> StringUtils.isNotBlank(propertyImgDetail.getImgUrl()))
                .collect(Collectors.toMap(PropertyImgDetail::getPropertyValueId, PropertyImgDetail::getImgUrl, (v1, v2) -> v1));
        List<PropertyValue> oldPropertyValues = propertyInfoService.queryPropertyValueByIds(oldPropertyValueIds);
        Map<Long, List<PropertyValue>> oldPropertyValueGroupMap = oldPropertyValues.stream()
                .peek(propertyValue -> propertyValue.setImgUrl(oldPropertyValueImgMap.get(propertyValue.getId())))
                .collect(Collectors.groupingBy(PropertyValue::getPropertyId));

        List<Property> oldPropertyList = propertyInfoService.queryPropertyByIds(Lists.newArrayList(oldPropertyIdMap.keySet()));
        List<PropertyDTO> oldProperties = oldPropertyList.stream()
                .map(property -> {
                    PropertyDTO propertyDTO = BeanCopyUtil.transform(property, PropertyDTO.class);
                    List<PropertyValue> propertyValues = oldPropertyValueGroupMap.get(property.getId());
                    List<Long> propertyValueIds = oldPropertyIdMap.get(property.getId());
                    propertyValues.sort(Comparator.comparingInt(o -> propertyValueIds.indexOf(o.getId())));
                    propertyDTO.setValues(BeanCopyUtil.transformList(propertyValues, PropertyValueDTO.class));
                    return propertyDTO;
                })
                .sorted(Comparator.comparingInt(o -> propertyIds.indexOf(o.getId())))
                .collect(Collectors.toList());

        List<GoodsItemDTO> oldSkuList = oldGoodsItems.stream()
                .map(goodsItem -> BeanCopyUtil.transform(goodsItem, GoodsItemDTO.class))
                .collect(Collectors.toList());

        // 校验是否有变更
        boolean needUpdate = false;
        List<PropertyDTO> newProperties = vo.getProperties();
        List<GoodsItemDTO> newSkuList = vo.getSkuInfo();
        Map<Long, List<PropertyValue>> propertyValueGroupMap = oldPropertyValues.stream().collect(Collectors.groupingBy(PropertyValue::getPropertyId));
        if (newProperties.size() != oldProperties.size()) {
            needUpdate = true;
        } else {
            List<String> newPropertyNames = newProperties.stream().map(Property::getName).collect(Collectors.toList());
            List<String> oldPropertyNames = oldProperties.stream().map(Property::getName).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(CollectionUtils.disjunction(oldPropertyNames, newPropertyNames))) {
                needUpdate = true;
            }
        }
        if (!needUpdate) {
            for (PropertyDTO newProperty : newProperties) {
                List<PropertyValueDTO> newValues = newProperty.getValues();
                List<PropertyValue> oldValues = propertyValueGroupMap.get(newProperty.getId());
                if (CollectionUtils.isEmpty(oldValues) || newValues.size() != oldValues.size()) {
                    needUpdate = true;
                    break;
                } else {
                    List<String> oldValueNames = oldValues.stream().map(PropertyValue::getValue).distinct().collect(Collectors.toList());
                    List<String> newValueNames = newValues.stream().map(PropertyValueDTO::getValue).distinct().collect(Collectors.toList());
                    Collection disjunction = CollectionUtils.disjunction(oldValueNames, newValueNames);
                    if (!disjunction.isEmpty()) {
                        needUpdate = true;
                        break;
                    } else {
                        for (PropertyValue oldValue : oldValues) {
                            for (PropertyValueDTO newValue : newValues) {
                                if (oldValue.getValue().equals(newValue.getValue()) && !StringUtils.equals(oldValue.getImgUrl(), newValue.getImgUrl())) {
                                    needUpdate = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
        if (!needUpdate) {
            List<String> newSkuName = newSkuList.stream().map(GoodsItem::getName).collect(Collectors.toList());
            List<String> oldSkuName = oldGoodsItems.stream().map(GoodsItem::getName).collect(Collectors.toList());
            if (oldSkuName.size() != newSkuName.size() || CollectionUtils.isNotEmpty(CollectionUtils.disjunction(oldSkuName, newSkuName))) {
                needUpdate = true;
            } else {
                for (GoodsItemDTO newSku : newSkuList) {
                    for (GoodsItem oldSku : oldGoodsItems) {
                        if (oldSku.getName().equals(newSku.getName()) && (!StringUtils.equals(oldSku.getSkuImage(), newSku.getImageUrl()) || !StringUtils.equals(oldSku.getOriginalSkuId(), newSku.getOriginalSkuId()))) {
                            needUpdate = true;
                            break;
                        }
                    }
                }
            }
        }
        CheckUtils.check(!needUpdate, ProductResultCode.GOODS_EDIT_CHANGE_NOT_FIND);

        Set<String> newImages = Sets.newHashSet();
        Set<String> oldImages = Sets.newHashSet();
        for (PropertyDTO propertyDTO : newProperties) {
            for (PropertyValueDTO value : propertyDTO.getValues()) {
                if (StringUtils.isNotBlank(value.getImgUrl())) {
                    newImages.add(value.getImgUrl());
                }
            }
        }
        newSkuList.stream().filter(goodsItemDTO -> StringUtils.isNotBlank(goodsItemDTO.getImageUrl())).forEach(goodsItemDTO -> newImages.add(goodsItemDTO.getImageUrl()));

        oldImages.addAll(oldPropertyValueImgMap.values());
        oldGoodsItems.stream().filter(goodsItem -> StringUtils.isNotBlank(goodsItem.getSkuImage())).forEach(goodsItem -> oldImages.add(goodsItem.getSkuImage()));

        newImages.removeAll(oldImages);
        Integer isWhite = 0;
        if (faMerchantsApply != null && faMerchantsApply.getTongDunWhiteList() != null) {
            isWhite = faMerchantsApply.getTongDunWhiteList();
        }
        if (!needAudit && CollectionUtils.isNotEmpty(newImages) && isWhite != 1) {
            TongDunGoodsImagesVO tongDunGoodsImagesVO = new TongDunGoodsImagesVO();
            tongDunGoodsImagesVO.setGoodsId(goodsId);
            tongDunGoodsImagesVO.setShopId(goods.getShopId());
            tongDunGoodsImagesVO.setGoodsName(goods.getName());
            tongDunGoodsImagesVO.setCategoryId(goods.getCategoryId());
            tongDunGoodsImagesVO.setGoodsImage(JSON.toJSONString(Lists.newArrayList(newImages)));
            tongDunGoodsImagesVO.setIsType(0L);
            tongDunGoodsImagesVO.setTdType(1);
             mqSender.send("ADD_TONG_DUN_GOODS", JSON.toJSONString(tongDunGoodsImagesVO));
        }



        oldSkuList.sort(Comparator.comparing(GoodsItem::getId));
        newSkuList.sort((o1, o2) -> (int) ((o1.getId() == null ? 0 : o1.getId()) - (o2.getId() == null ? 0 : o2.getId())));


        List<String> specialTagList = initSpecialTags(goodsId);

        //组装editInfo数据
        GoodsEditPropertyDto goodsEditPropertyDto = new GoodsEditPropertyDto();
        goodsEditPropertyDto.setOldPropertyList(oldProperties);
        goodsEditPropertyDto.setNewPropertyList(newProperties);
        goodsEditPropertyDto.setOldSkuList(oldSkuList);
        goodsEditPropertyDto.setNewSkuList(newSkuList);


        GoodsEditInfo editInfo = new GoodsEditInfo();
        editInfo.setGoodsId(goods.getId());
        editInfo.setCategoryId(goods.getCategoryId());
        editInfo.setShopId(goods.getShopId());
        editInfo.setShopName(goods.getShopName());
        editInfo.setApplyUser(getUserName());
        editInfo.setApplyTime(LocalDateTime.now());
        editInfo.setStatus(0);
        if (!needAudit) {
            editInfo.setStatus(1);
            editInfo.setAuditUser("system");
            editInfo.setAuditTime(LocalDateTime.now());
        }
        editInfo.setIsDel(0);
        editInfo.setType(GoodsEditTypeEnums.PROPERTY.getCode());
        editInfo.setContent(JSON.toJSONString(goodsEditPropertyDto));
        editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
        editInfo.setUpdateTime(LocalDateTime.now());
        goodsEditInfoService.save(editInfo);

        GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
        goodsEditInfoDetail.setEditId(editInfo.getId());
        goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
        goodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditPropertyDto));
        goodsEditInfoDetailService.save(goodsEditInfoDetail);

        LogUtils.info(log, "修改商品规格 finish");

        if (!needAudit) {
            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(goodsId);
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("修改商品规格");
            goodsSyncModel.setSourceService("vp");
            mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.FIVE_SEC);

            GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                    .goodsId(goodsId)
                    .type(OperationLogTypeEnums.UPDATE_GOODS_PROPERTY)
                    .newData(JSON.toJSONString(vo))
                    .content(OperationLogTypeEnums.UPDATE_GOODS_PROPERTY.getDesc())
                    .status(1)
                    .user(getUserName());
            mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDetail(UpdateDetailVo vo) {
        LogUtils.info(log, "updateDetail vo:{}", vo);
        CheckUtils.notNull(vo.getGoodsId(), ProductResultCode.PARAMETER_ID_ERROR);
        CheckUtils.check(StringUtils.isBlank(vo.getName()) || StringUtils.isBlank(vo.getItemCode()) || vo.getCategoryId() == null, ProductResultCode.PARAMETER_ERROR);
        CheckUtils.check(CollectionUtils.isEmpty(vo.getGoodsImages()) || CollectionUtils.isEmpty(vo.getDetailsImgs()), CustomResultCode.fill(ProductResultCode.PARAMETER_IS_NULL_EXT, "图片"));
//        CheckUtils.check(CollectionUtils.isEmpty(vo.getPropertyGoodsInfoVOS()) || CollectionUtils.isEmpty(vo.getPropertyDetailInfoVOList()), CustomResultCode.fill(ProductResultCode.PARAMETER_IS_NULL_EXT, "属性"));

        Long goodsId = vo.getGoodsId();
        Goods goods = goodsService.getById(goodsId);
        CheckUtils.notNull(goods, ProductResultCode.GOODS_NOT_EXIST);
        CheckUtils.check(GoodsIsShowEnums.FREEZE.getType().toString().equals(goods.getIsShow()), ProductResultCode.GOODS_IS_FREEZE);
        Long shopId = vo.getShopId();
        if (vo.getShopId() == null) {
            shopId = getShopId();
        }
        if (shopId != null) {
            CheckUtils.check(!shopId.equals(goods.getShopId()), ProductResultCode.EXIST_GOODS_NOT_BELONG_SHOP_EXT);
        }

        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(goods.getShopId());
        CheckUtils.check(faMerchantsApply.getIsSale() == 2, CustomResultCode.fill(ProductResultCode.HAS_LEAVE_EXIST, goods.getShopId().toString()));

        Category category = categoryService.selectById(vo.getCategoryId());
        GoodsExtDetail goodsExtDetail = goodsExtDetailService.queryGoodsExtDetailByGoodsId(goodsId);

//        if (!vo.getName().equals(goods.getName())) {
//            String goodsName = vo.getName();
//            Long firstCategoryId = Long.valueOf(category.getPids().split(",")[0]);
//            Pair<Boolean, Set<String>> sensitiveWordsCheckResult = sensitiveWordsCoreService.checkNew(goodsName, shopId, vo.getBrandId(), firstCategoryId);
//            CheckUtils.check(sensitiveWordsCheckResult.getLeft(), CustomResultCode.fill(ProductResultCode.GOODS_NAME_CONTAIN_SENSITIVE_WORDS, String.join(",", sensitiveWordsCheckResult.getRight())));
//            newRedisTemplate.opsForValue().increment("GOODS_NAME_UPDATE_COUNT:"+LocalDate.now());
//        }

        boolean isTemplateGoods = false;
        if (shopId.equals(CommonConstants.CHANCE_GOODS_SHOP_ID) || shopId.equals(CommonConstants.LISTING_GOODS_SHOP_ID)) {
            isTemplateGoods = true;
        }

        //商户品牌校验
        if (!isTemplateGoods) {
            if (vo.getBrandId() == null || vo.getBrandId() == 0) {
                CheckUtils.check(categoryCoreService.checkCategoryNeedBrand(vo.getCategoryId()), ProductResultCode.BRAND_NULL_ERROR);
            } else {
                MerchantsBrandStore merchantsBrandStore = merchantsBrandStoreService.selectById(vo.getBrandId());
                if (merchantsBrandStore == null || merchantsBrandStore.getStatus() != 1) {
                    CheckUtils.check(true, ProductResultCode.BRAND_STORE_NOT_EXIST);
                }
                List<MerchantsBrandMark> merchantsBrandMarkList = merchantsBrandMarkService.lambdaQuery()
                        .eq(MerchantsBrandMark::getMerchantsBrandStoreId, vo.getBrandId())
                        .eq(Objects.nonNull(category) ,MerchantsBrandMark::getCategoryId, category.getPids().split(",")[0])
                        .eq(MerchantsBrandMark::getShopId, shopId)
                        .eq(MerchantsBrandMark::getStatus, 10)
                        .eq(MerchantsBrandMark::getIsDel, 0)
                        .list();
                if (CollectionUtils.isEmpty(merchantsBrandMarkList)) {
                    CheckUtils.check(true, ProductResultCode.BRAND_NOT_EXIST);
                }
                boolean expireFlag = merchantsBrandMarkList.stream().anyMatch(item -> new Date().before(item.getExpireTime()));
                if (!expireFlag) {
                    CheckUtils.check(true, ProductResultCode.BRAND_IS_EXPIRE);
                }
            }
        }

        CheckUtils.check(category == null || category.getIsDel() == 1, ProductResultCode.CATEGORY_NOT_FIND);
        assert category != null;
        CheckUtils.check(category.getIsLeaf() != 1, CustomResultCode.fill(ProductResultCode.CATEGORY_MUST_BE_LEAF, vo.getCategoryId().toString()));

        //校验图片格式
        GoodsInfoUtils.checkImage(vo);

        //校验是否包含中文
        checkIfContainsChinese(vo);

        //校验主营类目
        checkCategoryMain(vo.getCategoryId(), shopId);

        List<Long> pathCategoryIds = Lists.newArrayList(vo.getCategoryId());
        if (StringUtils.isNotBlank(category.getPids())) {
            pathCategoryIds.addAll(Arrays.stream(category.getPids().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }

        //校验尺码表和尺码图   1尺码表尺码图二选一 2必须维护尺码表
        Integer limitType = categoryCoreService.checkSizeIsNeed(vo.getCategoryId());
        CheckUtils.check(limitType == 1 && vo.getSizeChartTemplateId() == null && StringUtils.isBlank(vo.getSizeImage()), ProductResultCode.SIZE_IMAGE_ERROR);
        CheckUtils.check(limitType == 2 && vo.getSizeChartTemplateId() == null, ProductResultCode.SIZE_CHART_TEMPLATE_MUST);

        //尺码表校验
        checkSizeChartTemplate(vo.getSizeChartTemplateId(), shopId, pathCategoryIds);

        if (vo.getSizeChartTemplateId() != null) {
            SizeChartTemplate sizeChartTemplate = sizeChartTemplateService.getById(vo.getSizeChartTemplateId());
            CheckUtils.check(sizeChartTemplate == null || sizeChartTemplate.getIsDel() == 1 || sizeChartTemplate.getType() == 1, ProductResultCode.SIZE_CHART_TEMPLATE_NOT_EXIST);
            assert sizeChartTemplate != null;
            CheckUtils.check(!pathCategoryIds.contains(sizeChartTemplate.getCategoryId()), ProductResultCode.SIZE_CHART_TEMPLATE_CATEGORY_NOT_BIND_GOODS);
        }

        //检测是否存在待审核修改审批
        Integer existApply = goodsEditInfoService.lambdaQuery()
                .eq(GoodsEditInfo::getGoodsId, goodsId)
                .eq(GoodsEditInfo::getStatus, 0)
                .count();
        CheckUtils.check(existApply > 0, ProductResultCode.GOODS_EDIT_APPLY_EXIST);

        //检查说明书是否必须上传
        boolean categroyIdIsRequired = categoryCoreService.checkCategroyIdIsRequired(pathCategoryIds);
        CheckUtils.check(categroyIdIsRequired && (CollectionUtils.isEmpty(vo.getGoodsManualVOList()) || vo.getGoodsManualVOList().stream().filter(t -> Objects.nonNull(t.getCountryType())).noneMatch(t -> t.getCountryType() == 1)), ProductResultCode.GOODS_COUNTRY_MANUAL_REQUIRED);

//        CheckUtils.check((category.getType() != null && (category.getType() == 2 || category.getType() == 3)) && CollectionUtils.isEmpty(vo.getGoodsPartsVOList()), ProductResultCode.GOODS_PARTS_EMPTY);

        ListingFollowGoods listingFollowGoods = getListingFollowGoods(goodsId);
        boolean isListingGoods = listingFollowGoods != null;
        boolean needAudit = isListingGoods || !vo.getCategoryId().equals(goods.getCategoryId());

        if (CollectionUtils.isNotEmpty(vo.getPropertyGoodsInfoVOS())) {
            vo.setPropertyGoodsInfoVOS(vo.getPropertyGoodsInfoVOS().stream()
                    .filter(k -> StringUtils.isNotBlank(k.getInputValue()))
                    .peek(v -> v.setGoodsId(vo.getGoodsId()))
                    .collect(Collectors.toList()));
        }

        if (!vo.getItemCode().equals(goodsExtDetail.getItemCode())) {
            List<GoodsExtDetail> sameItemCodeDetails = goodsExtDetailService.lambdaQuery()
                    .eq(GoodsExtDetail::getItemCode, vo.getItemCode())
                    .eq(GoodsExtDetail::getShopUrl, shopId.toString())
                    .select(GoodsExtDetail::getGoodsId)
                    .list();
            if (CollectionUtils.isNotEmpty(sameItemCodeDetails)) {
                List<Long> sameItemCodeGoodsIds = sameItemCodeDetails.stream().map(GoodsExtDetail::getGoodsId).collect(Collectors.toList());
                Integer count = goodsService.lambdaQuery().in(Goods::getId, sameItemCodeGoodsIds).eq(Goods::getIsDel, 0).count();
                CheckUtils.check(count > 0, ProductResultCode.GOODS_DUPLICATE);
            }
        }

        GoodsDetail goodsDetail = goodsDetailService.queryGoodsDetailByGoodsId(goodsId);

        List<GoodsExtDetailImg> goodsExtDetailImgs = goodsExtDetailImgService.queryByGoodsId(goodsId);
        List<String> oldDetailsImgs = goodsExtDetailImgs.stream().map(GoodsExtDetailImg::getImgUrl).collect(Collectors.toList());


        List<GoodsImage> goodsImages = goodsImageService.queryListByGoodsId(goodsId);
        List<String> oldGoodsImageList = Lists.newArrayList();
        List<String> oldGoodsVideos = Lists.newArrayList();
        goodsImages.forEach(goodsImg -> {
            if (goodsImg.getImageType().equals(GoodsImageTypeEnum.IMAGE.getCode())) {
                oldGoodsImageList.add(goodsImg.getUrl());
            }
            if (goodsImg.getImageType().equals(GoodsImageTypeEnum.VIDEO.getCode())) {
                oldGoodsVideos.add(goodsImg.getUrl());
            }
        });

        List<PropertyGoodsInfoVO> propertyGoodsInfoVOS = propertyGoodsInfoCoreService.queryByGoodsId(goodsId);

        GoodsEditDetailDto.ExtraSnap oldExtraSnap = null;
        GoodsExtra goodsExtra = goodsExtraCoreService.queryByGoodsId(goodsId);
        if (goodsExtra != null) {
            oldExtraSnap = new GoodsEditDetailDto.ExtraSnap(goodsExtra.getPropertyJson());
        }

        List<GoodsManual> goodsManualList = goodsManualService.getGoodsManualListByGoodsId(goodsId);
        List<GoodsManualVO> oldGoodsManualVOList = null;
        List<GoodsManualVO> oldGoodsManualVideoList= null;
        if(CollectionUtils.isNotEmpty(goodsManualList)){
            oldGoodsManualVOList = goodsManualList.stream()
                    .filter(goodsManual -> goodsManual.getType() == 1)
                    .map(goodsManual-> BeanCopyUtil.transform(goodsManual, GoodsManualVO.class))
                    .collect(Collectors.toList());
            oldGoodsManualVideoList = goodsManualList.stream()
                    .filter(goodsManual -> goodsManual.getType() == 2)
                    .map(goodsManual-> BeanCopyUtil.transform(goodsManual, GoodsManualVO.class))
                    .collect(Collectors.toList());
        }

        boolean goodsManualFlag = (goodsManualList != null && CollectionUtils.isEmpty(vo.getGoodsManualVOList()) && CollectionUtils.isEmpty(vo.getGoodsManualVideoList())) ||
                CollectionUtils.isNotEmpty(vo.getGoodsManualVOList()) || CollectionUtils.isNotEmpty(vo.getGoodsManualVideoList());


        List<GoodsParts> goodsPartsList = goodsPartsService.lambdaQuery().eq(GoodsParts::getGoodsId,goodsId).list();
        List<GoodsPartsVO> oldGoodsPartsVOList = null;
        if(CollectionUtils.isNotEmpty(goodsPartsList)){
            oldGoodsPartsVOList = BeanCopyUtil.transformList(goodsPartsList, GoodsPartsVO.class);
        }

        boolean goodsPartsFlag = (goodsPartsList != null && CollectionUtils.isEmpty(vo.getGoodsPartsVOList())) ||
                (CollectionUtils.isEmpty(goodsPartsList) && CollectionUtils.isNotEmpty(vo.getGoodsPartsVOList())) ||
                (CollectionUtils.isNotEmpty(goodsPartsList) && CollectionUtils.isNotEmpty(vo.getGoodsPartsVOList()));



        //自动通过的改动内容： listing商品修改尺码表、尺码图、属性信息、itemCode、自定义skuId ===> 自动通过
        boolean autoPassChange = !StringUtils.equals(vo.getSizeImage(), goodsDetail.getSizeImage())
                || !Objects.equals(vo.getSizeChartTemplateId(), goods.getSizeChartTemplateId())
                || !vo.getItemCode().equals(goodsExtDetail.getItemCode()) || goodsManualFlag || goodsPartsFlag;

        if (!autoPassChange) {
            if (CollectionUtils.isEmpty(propertyGoodsInfoVOS) && CollectionUtils.isNotEmpty(vo.getPropertyGoodsInfoVOS())) {
                autoPassChange = true;
            } else if (CollectionUtils.isNotEmpty(propertyGoodsInfoVOS) && CollectionUtils.isNotEmpty(vo.getPropertyGoodsInfoVOS())) {
                List<Long> oldPropertyDetailIdList = propertyGoodsInfoVOS.stream().map(PropertyGoodsInfoVO::getPropertyDetailId).collect(Collectors.toList());
                List<Long> newPropertyDetailIdList = vo.getPropertyGoodsInfoVOS().stream().map(PropertyGoodsInfoVO::getPropertyDetailId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(CollectionUtils.disjunction(oldPropertyDetailIdList, newPropertyDetailIdList))) {
                    autoPassChange = true;
                } else {
                    Map<Long, PropertyGoodsInfoVO> oldPropertyInfoMap = propertyGoodsInfoVOS.stream().collect(Collectors.toMap(PropertyGoodsInfoVO::getPropertyDetailId, Function.identity(), (v1, v2) -> v1));
                    for (PropertyGoodsInfoVO propertyGoodsInfoVO : vo.getPropertyGoodsInfoVOS()) {
                        PropertyGoodsInfoVO oldPropertyInfo = oldPropertyInfoMap.get(propertyGoodsInfoVO.getPropertyDetailId());
                        if (!StringUtils.equals(oldPropertyInfo.getInputValue(), propertyGoodsInfoVO.getInputValue())) {
                            autoPassChange = true;
                            break;
                        }
                    }
                }
            }
        }

        boolean otherChange = !vo.getName().equals(goods.getName())
                || !vo.getCategoryId().equals(goods.getCategoryId())
                || !Objects.equals(vo.getBrandId(), goodsExtDetail.getBrandId())
                || !StringUtils.equals(vo.getDescription(), goodsDetail.getDescription())
                || CollectionUtils.isNotEmpty(CollectionUtils.disjunction(vo.getGoodsImages(), oldGoodsImageList))
                || CollectionUtils.isNotEmpty(CollectionUtils.disjunction(vo.getGoodsVideos(), oldGoodsVideos))
                || CollectionUtils.isNotEmpty(CollectionUtils.disjunction(vo.getDetailsImgs(), oldDetailsImgs));
        if (!otherChange) {
            for (int i = 0; i < vo.getGoodsImages().size(); i++) {
                if (!vo.getGoodsImages().get(i).equals(oldGoodsImageList.get(i))) {
                    otherChange = true;
                    break;
                }
            }
        }
        if (!otherChange) {
            for (int i = 0; i < vo.getDetailsImgs().size(); i++) {
                if (!vo.getDetailsImgs().get(i).equals(oldDetailsImgs.get(i))) {
                    otherChange = true;
                    break;
                }
            }
        }
        boolean needSave = autoPassChange || otherChange;

        if (StringUtils.isNotBlank(vo.getOriginalShopName()) || StringUtils.isNotBlank(vo.getGoodsUrl()) || StringUtils.isNotBlank(vo.getProcureSupplier()) || StringUtils.isNotBlank(vo.getCostUrl())) {
            goodsExtDetail.setGoodsUrl(vo.getGoodsUrl());
            goodsExtDetail.setCostUrl(vo.getCostUrl());
            goodsExtDetail.setProcureSupplier(vo.getProcureSupplier());
            goodsExtDetail.setOriginalShopName(vo.getOriginalShopName());
            goodsExtDetailService.updateById(goodsExtDetail);
            if (!needSave) {
                return;
            }
        }

        CheckUtils.check(!needSave, ProductResultCode.GOODS_EDIT_CHANGE_NOT_FIND);

        if (needAudit && isListingGoods && autoPassChange && !otherChange) {
            needAudit = false;
        }

        GoodsEditDetailDto goodsEditDetailDto = new GoodsEditDetailDto();
        goodsEditDetailDto.setOldName(goods.getName());
        goodsEditDetailDto.setOldCategoryId(goods.getCategoryId());
        goodsEditDetailDto.setOldSizeChartTemplateId(goods.getSizeChartTemplateId());
        goodsEditDetailDto.setOldItemCode(goodsExtDetail.getItemCode());
        goodsEditDetailDto.setOldBrandId(goodsExtDetail.getBrandId());
        goodsEditDetailDto.setOldDescription(goodsDetail.getDescription());
        goodsEditDetailDto.setOldSizeImage(goodsDetail.getSizeImage());
        goodsEditDetailDto.setOldGoodsImages(oldGoodsImageList);
        goodsEditDetailDto.setOldGoodsVideos(oldGoodsVideos);
        goodsEditDetailDto.setOldDetailsImgs(oldDetailsImgs);
        goodsEditDetailDto.setOldPropertyGoodsInfoVOS(propertyGoodsInfoVOS);
        goodsEditDetailDto.setOldGoodsExtra(oldExtraSnap);
        goodsEditDetailDto.setOldGoodsManualVOS(oldGoodsManualVOList);
        goodsEditDetailDto.setOldGoodsManualVideoList(oldGoodsManualVideoList);
        goodsEditDetailDto.setOldGoodsPartsVOS(oldGoodsPartsVOList);


        if (!needAudit) {
            goodsService.lambdaUpdate()
                    .set(vo.getName() != null, Goods::getName, GoodsInfoUtils.formatGoodsName(vo.getName()))
                    .set(vo.getCategoryId() != null, Goods::getCategoryId, vo.getCategoryId())
                    .set(CollectionUtils.isNotEmpty(vo.getGoodsImages()), Goods::getMainImage, vo.getGoodsImages().get(0))
                    .set(Goods::getSizeChartTemplateId, vo.getSizeChartTemplateId())
                    .set(Goods::getUpdateTime, LocalDateTime.now())
                    .eq(Goods::getId, goodsId)
                    .update();


            goodsExtDetail.setItemCode(vo.getItemCode());
            goodsExtDetail.setBrandId(vo.getBrandId());
            goodsExtDetailService.updateById(goodsExtDetail);
            //新增处理逻辑，商品绑定的品牌如果关联了自定义标签，该商品也应关联对应的自定义标签
            GoodsRelTagDTO relTagDTO = new GoodsRelTagDTO();
            relTagDTO.setBrandId(goodsExtDetail.getBrandId());
            relTagDTO.setGoodsIds(Lists.newArrayList(goodsExtDetail.getGoodsId()));
            mqSender.send(CommonConstants.REL_TAG_TOPIC, relTagDTO);

            goodsDetail.setDescription(vo.getDescription());
            goodsDetail.setSizeImage(vo.getSizeImage());
            goodsDetailService.updateById(goodsDetail);

            ProductInfoInput productInfoInput = new ProductInfoInput();
            productInfoInput.setId(goodsId);
            productInfoInput.setGoodsImages(vo.getGoodsImages());
            productInfoInput.setGoodsVideos(vo.getGoodsVideos());
            productInfoInput.setDetailsImgs(vo.getDetailsImgs());
            updateGoodsImgs(productInfoInput);
            updateDetailImgs(productInfoInput);

            if (CollectionUtils.isNotEmpty(vo.getPropertyGoodsInfoVOS())) {
                propertyGoodsInfoCoreService.saveOrUpdatePropertyGoods(vo.getPropertyGoodsInfoVOS());
            }

            if (CollectionUtils.isNotEmpty(vo.getGoodsExtraPropertyVOS())) {
                insertGoodsExtraInfo(vo.getGoodsExtraPropertyVOS(), goodsId);
            }

            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(goodsId);
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("修改商品详情");
            goodsSyncModel.setSourceService("vp");
            mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.FIVE_SEC);

            GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                    .goodsId(goodsId)
                    .type(OperationLogTypeEnums.UPDATE_GOODS_DETAIL)
                    .newData(JSON.toJSONString(vo))
                    .content(OperationLogTypeEnums.UPDATE_GOODS_DETAIL.getDesc())
                    .status(1)
                    .user(getUserName());
            mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);

            //机会商品店铺和Listing商品店铺修改详情,不需要发送同盾审核
            //同盾白名单店铺,不需要发送同盾审核
            Integer isWhite = 0;
            if (faMerchantsApply != null && faMerchantsApply.getTongDunWhiteList() != null) {
                isWhite = faMerchantsApply.getTongDunWhiteList();
            }
            if(!isTemplateGoods && isWhite != 1){
                Set<String> tongDunImages = Sets.newHashSet(vo.getGoodsImages());
                tongDunImages.addAll(vo.getDetailsImgs());
                tongDunImages.addAll(vo.getGoodsVideos());
                tongDunImages.removeAll(oldGoodsImageList);
                tongDunImages.removeAll(oldDetailsImgs);
                tongDunImages.removeAll(oldGoodsVideos);
                if (CollectionUtils.isNotEmpty(tongDunImages) || !vo.getName().equals(goods.getName())) {
                    TongDunGoodsImagesVO tongDunGoodsImagesVO = new TongDunGoodsImagesVO();
                    tongDunGoodsImagesVO.setGoodsId(goodsId);
                    tongDunGoodsImagesVO.setShopId(goods.getShopId());
                    tongDunGoodsImagesVO.setGoodsName(vo.getName());
                    tongDunGoodsImagesVO.setCategoryId(goods.getCategoryId());
                    tongDunGoodsImagesVO.setGoodsImage(JSON.toJSONString(Lists.newArrayList(tongDunImages)));
                    tongDunGoodsImagesVO.setIsType(0L);
                    tongDunGoodsImagesVO.setTdType(1);
                    mqSender.send("ADD_TONG_DUN_GOODS", JSON.toJSONString(tongDunGoodsImagesVO));
                    LogUtils.info(log, "图片变更，添加同盾");
                }
            }
        }

        GoodsEditDetailDto.ExtraSnap newExtraSnap = null;
        if (CollectionUtils.isNotEmpty(vo.getGoodsExtraPropertyVOS())) {
            GoodsExtraPropertyVO goodsExtraPropertyVO = vo.getGoodsExtraPropertyVOS().get(0);
            newExtraSnap = new GoodsEditDetailDto.ExtraSnap(JSON.toJSONString(goodsExtraPropertyVO));
        }

        //更新各国家说明手册
        saveOrUpdateCountryGoodsManual(vo.getGoodsId(),vo.getGoodsManualVOList(),vo.getGoodsManualVideoList());

        saveOrUpdateGoodsParts(vo.getGoodsId(), vo.getGoodsPartsVOList());

        goodsEditDetailDto.setNewName(vo.getName());
        goodsEditDetailDto.setNewCategoryId(vo.getCategoryId());
        goodsEditDetailDto.setNewSizeChartTemplateId(vo.getSizeChartTemplateId());
        goodsEditDetailDto.setNewItemCode(vo.getItemCode());
        goodsEditDetailDto.setNewBrandId(vo.getBrandId());
        goodsEditDetailDto.setNewDescription(vo.getDescription());
        goodsEditDetailDto.setNewSizeImage(vo.getSizeImage());
        goodsEditDetailDto.setNewGoodsImages(vo.getGoodsImages());
        goodsEditDetailDto.setNewGoodsVideos(vo.getGoodsVideos());
        goodsEditDetailDto.setNewDetailsImgs(vo.getDetailsImgs());
        goodsEditDetailDto.setNewPropertyGoodsInfoVOS(vo.getPropertyGoodsInfoVOS());
        goodsEditDetailDto.setNewGoodsExtra(newExtraSnap);
        goodsEditDetailDto.setNewGoodsManualVOS(vo.getGoodsManualVOList());
        goodsEditDetailDto.setNewGoodsManualVideoList(vo.getGoodsManualVideoList());
        goodsEditDetailDto.setNewGoodsPartsVOS(vo.getGoodsPartsVOList());


        //init specialTag
        List<String> specialTagList = initSpecialTags(goodsId);

        GoodsEditInfo editInfo = new GoodsEditInfo();
        editInfo.setGoodsId(goods.getId());
        editInfo.setCategoryId(goods.getCategoryId());
        editInfo.setShopId(goods.getShopId());
        editInfo.setShopName(goods.getShopName());
        editInfo.setApplyUser(getUserName());
        editInfo.setApplyTime(LocalDateTime.now());
        editInfo.setStatus(0);
        if (!needAudit) {
            editInfo.setStatus(1);
            editInfo.setAuditUser("system");
            editInfo.setAuditTime(LocalDateTime.now());
        }
        editInfo.setIsDel(0);
        editInfo.setType(GoodsEditTypeEnums.DETAIL.getCode());
        editInfo.setContent(JSON.toJSONString(goodsEditDetailDto));
        editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
        editInfo.setUpdateTime(LocalDateTime.now());
        goodsEditInfoService.save(editInfo);

        GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
        goodsEditInfoDetail.setEditId(editInfo.getId());
        goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
        goodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditDetailDto));
        goodsEditInfoDetailService.save(goodsEditInfoDetail);

        LogUtils.info(log, "修改商品详情 finish");
    }

    private void checkCategoryMain(Long categoryId, Long shopId) {
        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.getById(shopId);
        if (faMerchantsApply.getCategoryMainId() == null) {
            Object categoryMainSwitch = redisApi.get(PlatformConfigTypeEnum.CATEGORY_MAIN_SWITCH.name());
            CheckUtils.check(categoryMainSwitch != null && "1".equals(categoryMainSwitch.toString()), ProductResultCode.SHOP_MUST_BIND_CATEGORY_MAIN);
        } else {
            CategoryMain categoryMain = categoryMainService.getById(faMerchantsApply.getCategoryMainId());
            LogUtils.info(log, "categoryMain:{}", categoryMain);
            CheckUtils.notNull(categoryMain, ProductResultCode.CATEGORY_MAIN_NOT_EXIST);

            List<Long> allMainCategoryIds = Lists.newArrayList();
            if (StringUtils.isNotBlank(categoryMain.getLeafCategoryIds())) {
                Arrays.stream(categoryMain.getLeafCategoryIds().split(",")).map(Long::parseLong).forEach(allMainCategoryIds::add);
            }
            if (StringUtils.isNotBlank(categoryMain.getLeafSecondCategoryIds())) {
                Arrays.stream(categoryMain.getLeafSecondCategoryIds().split(",")).map(Long::parseLong).forEach(allMainCategoryIds::add);
            }
            CheckUtils.check(CollectionUtils.isNotEmpty(allMainCategoryIds) && !allMainCategoryIds.contains(categoryId), ProductResultCode.CATEGORY_MAIN_NOT_INCLUDE);
        }
    }

    private List<String> initSpecialTags(Long goodsId) {
        List<String> specialTagList = Lists.newArrayList();
        List<GoodsLockInfo> goodsLockInfoList = goodsLockInfoService.lambdaQuery()
                .eq(GoodsLockInfo::getGoodsId, goodsId)
                .eq(GoodsLockInfo::getIsDel, 0)
                .list();
        if (CollectionUtils.isNotEmpty(goodsLockInfoList)) {
            specialTagList = GoodsLockLabelTypEnums.listNamesByCodes(goodsLockInfoList.get(goodsLockInfoList.size() - 1).getLabel());
        }

        ListingFollowGoods listingFollowGoods = getListingFollowGoods(goodsId);
        if (listingFollowGoods != null) {
            specialTagList.add("listing");
        }
        return specialTagList;
    }


    @Override
    public void updateSkuOriginalInfo(UpdateSkuOriginalInfoVo vo) {
        LogUtils.info(log, "开始更新sku的供应商信息 vo:{}", vo);

        CheckUtils.notNull(vo.getGoodsId(), ProductResultCode.PARAMETER_ID_ERROR);
        CheckUtils.isEmpty(vo.getSkuInfos(), ProductResultCode.PARAMETER_ERROR);

        Goods goods = goodsService.getById(vo.getGoodsId());
        CheckUtils.notNull(goods, ProductResultCode.GOODS_NOT_EXIST);

        Map<Long, UpdateSkuOriginalInfoVo.SkuInfo> skuInfoMap = vo.getSkuInfos().stream().collect(Collectors.toMap(UpdateSkuOriginalInfoVo.SkuInfo::getGoodsItemId, Function.identity(), (v1, v2) -> v1));
        Date now = new Date();
        List<GoodsItem> updateGoodsItems = Lists.newArrayList();

        List<GoodsItem> goodsItems = goodsItemService.queryGoodsItemByGoodsId(vo.getGoodsId());
        goodsItems.forEach(goodsItem -> {
            UpdateSkuOriginalInfoVo.SkuInfo skuInfo = skuInfoMap.get(goodsItem.getId());
            if (skuInfo != null) {
                goodsItem.setOriginalProductId(skuInfo.getOriginalProductId());
                goodsItem.setOriginalSkuId(skuInfo.getOriginalSkuId());
                goodsItem.setUpdateTime(now);
                updateGoodsItems.add(goodsItem);
            }
        });

        if (CollectionUtils.isNotEmpty(updateGoodsItems)) {
            goodsItemService.updateBatchById(updateGoodsItems);

            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(vo.getGoodsId());
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness("更新商品sku供应商信息");
            goodsSyncModel.setSourceService("vp");
            mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.TEN_SEC);
        }
    }

    @Override
    public void refreshGoodsEditInfoAddPrice() {
        long id  = 0;
        List<GoodsEditInfo> goodsEditInfoList;
        do{
            log.info("refreshGoodsEditInfoAddPrice process id = {}", id);
            goodsEditInfoList = goodsEditInfoService.lambdaQuery()
                    .gt(GoodsEditInfo::getId, id)
                    .orderByDesc(GoodsEditInfo::getId)
                    .last("limit 500")
                    .list();
            if(CollectionUtils.isEmpty(goodsEditInfoList)){
                log.info("refreshGoodsEditInfoAddPrice end id = {}", id);
                break;
            }
            id = goodsEditInfoList.get(goodsEditInfoList.size() - 1).getId();
            updateAddPrice(goodsEditInfoList);
        }while (CollectionUtils.isNotEmpty(goodsEditInfoList));
    }

    @Override
    public void refreshGoodsCountry(RefreshGoodsCountryVO refreshGoodsCountryVO) {
        CheckUtils.notNull(refreshGoodsCountryVO,ProductResultCode.PARAMETER_ERROR);
        CheckUtils.notNull(refreshGoodsCountryVO.getRefreshGoodsCountryDTOS(),ProductResultCode.PARAMETER_ERROR);
        List<RefreshGoodsCountryDTO> refreshGoodsCountryDTOS = refreshGoodsCountryVO.getRefreshGoodsCountryDTOS();
        if (refreshGoodsCountryDTOS.isEmpty()){
            return;
        }
        List<List<RefreshGoodsCountryDTO>> partitionList = Lists.partition(refreshGoodsCountryDTOS, 1000);
        partitionList.forEach(partition ->{
            //更新商品可售国家
            List<Long> goodsIds = partition.stream().map(RefreshGoodsCountryDTO::getGoodsId).collect(Collectors.toList());
            List<Goods> goodsList = goodsService.queryGoodsByIds(goodsIds);
            if (CollectionUtils.isEmpty(goodsList)) {
                return;
            }
            Map<Long, Goods> idGoodsMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Function.identity(), (v1, v2) -> v1));
            Map<Long, String> oldGoodsCountryMap = goodsList.stream().collect(Collectors.toMap(Goods::getId, Goods::getCountry));
            // 单个去校验国家
            List<Goods> needUpdateGoods = new ArrayList<>();
            List<Goods> needSendTongDunGoods = new ArrayList<>();
            partition.forEach(refreshGoodsCountryDTO -> {
                Long goodsId = refreshGoodsCountryDTO.getGoodsId();
                try {
                    if (goodsId == null){
                        return;
                    }
                    Goods goods = idGoodsMap.get(goodsId);
                    if (goods == null){
                        return;
                    }
                    String oldCountry = goods.getCountry();
                    LogUtils.info(log, "updateCountry goodsId:{}", goodsId);
                    String newCountry = refreshGoodsCountryDTO.getCountry();
                    if(StringUtils.isBlank(newCountry) || newCountry.equalsIgnoreCase("SYSTEM")){
                        newCountry = GoodsTransferUtils.DEFAULT_SYSTEM_COUNTRY_LIST;
                    }
                    Long shopId = goods.getShopId();
                    Goods updateGoods = new Goods();
                    updateGoods.setId(goodsId);
                    // 获取有效的物流属性国家
                    GoodsEffectiveCountryDTO goodsEffectiveCountryDTO = new GoodsEffectiveCountryDTO();
                    goodsEffectiveCountryDTO.setGoodsId(goods.getId());
                    goodsEffectiveCountryDTO.setShopId(shopId);
                    goodsEffectiveCountryDTO.setLogisticsProperty(goods.getLogisticsProperty());
                    goodsEffectiveCountryDTO.setCountry(newCountry);
                    goodsEffectiveCountryDTO.setCategoryId(goods.getCategoryId());
                    String effectiveCountry = logisticsPropertyConfigCoreService.getGoodsEffectiveCountries(goodsEffectiveCountryDTO);
                    updateGoods.setCountry(effectiveCountry);
                    updateGoods.setUpdateTime(LocalDateTime.now());
                    needUpdateGoods.add(updateGoods);
                    //校验是否需要发送同盾
                    LogUtils.info(log, "updateCountry--> goodsId:{}, oldCountry:{}, newCountry:{}", goods.getId(),oldCountry,effectiveCountry);
                    if (logisticsPropertyConfigCoreService.checkIsSendTongDun(oldCountry,effectiveCountry,goods.getIsShow())){
                        needSendTongDunGoods.add(goods);
                    }
                }catch (Exception e){
                    LogUtils.info(log, "updateCountry error !! goodsId is : {}", goodsId);
                }
            });
            if(CollectionUtils.isEmpty(needUpdateGoods)){
                return;
            }
            Boolean bo = goodsCoreService.updateCountryBatchById(needUpdateGoods);
            if (bo) {
                log.info("开始刷新商品国家运费！！");
                needUpdateGoods.forEach(goods -> {
                    goodsCoreService.refreshGoodsFreight(goods.getId());
                });
                //修改完价格发送mq
                needUpdateGoods.forEach(good -> {
                    GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                    goodsSyncModel.setGoodsId(good.getId());
                    goodsSyncModel.setSyncTime(System.currentTimeMillis());
                    goodsSyncModel.setBusiness("更新商品可售国家");
                    goodsSyncModel.setSourceService("vp");
                    mqSender.sendDelay("SYNC_GOODS_TOPIC_BATCH", goodsSyncModel, MqDelayLevel.TEN_SEC);

                    GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                            .goodsId(good.getId())
                            .type(OperationLogTypeEnums.UPDATE_GOODS_COUNTRY)
                            .content(OperationLogTypeEnums.UPDATE_GOODS_COUNTRY.getDesc())
                            .oldData(oldGoodsCountryMap.get(good.getId()))
                            .newData(good.getCountry())
                            .status(1)
                            .user(getUserName());
                    log.info("发送操作事件 goodsId:{} type:{}", good.getId(), OperationLogTypeEnums.UPDATE_GOODS_COUNTRY.getDesc());
                    mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
                });
                // 美国站需要发送审核任务
                if (CollectionUtils.isNotEmpty(needSendTongDunGoods)){
                    tongDunGoodsImageCoreService.addTongDun(needSendTongDunGoods,1);
                }
            }
            log.info("存储数据库成功！");
        });
    }

    void updateAddPrice(List<GoodsEditInfo> goodsEditInfoList){
        List<Long> editIds = goodsEditInfoList.stream().map(GoodsEditInfo::getId).collect(Collectors.toList());
        Map<Long, String> editContentMap = goodsEditInfoDetailService.getByEditIds(editIds)
                .stream().collect(Collectors.toMap(GoodsEditInfoDetail::getEditId, GoodsEditInfoDetail::getContent, (v1, v2) -> v1));
        List<GoodsEditInfo> updateList = goodsEditInfoList.stream()
                .filter(info -> editContentMap.getOrDefault(info.getId(), "").contains("newPrice"))
                .map(info -> {
//                    String content = info.getContent();
                    String content = editContentMap.getOrDefault(info.getId(), "");
                    GoodsEditPriceDto goodsEditPriceDto = JSON.parseObject(content, GoodsEditPriceDto.class);
                    List<GoodsEditPriceDto.SkuChangeDto> skuChangeDtoList = goodsEditPriceDto.getSkuChangeDtoList();
                    if (CollectionUtils.isEmpty(skuChangeDtoList)) {
                        return info;
                    }
                    Set<Integer> changePrice = new HashSet<>();
                    for (GoodsEditPriceDto.SkuChangeDto changeDto : skuChangeDtoList) {
                        if (changeDto.getOldPrice().compareTo(changeDto.getNewPrice()) > 0) {
                            changePrice.add(0);
                        } else if(changeDto.getOldPrice().compareTo(changeDto.getNewPrice()) < 0){
                            changePrice.add(1);
                        }
                    }
                    if (changePrice.size() > 1) {
                        info.setAddPrice(2);
                    } else {
                        if (!changePrice.isEmpty()) {
                            info.setAddPrice(changePrice.contains(0) ? 0 : 1);
                        }
                    }
                    return info;
                })
                .filter(info -> Objects.nonNull(info.getAddPrice()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(updateList)){
            goodsEditInfoService.updateBatchById(updateList);
        }
    }

    @Override
    public void clearSkuStock(ClearSkuStockCondition condition) {
        LogUtils.info(log, "清零指定sku库存 condition:{}", condition);
        CheckUtils.check(Objects.isNull(condition.getGoodsId()) || Objects.isNull(condition.getSkuId()), ProductResultCode.PARAMETER_ERROR);

        Long goodsId = condition.getGoodsId();
        Goods goods = goodsService.getById(goodsId);
        CheckUtils.notNull(goods, ProductResultCode.GOODS_NOT_EXIST);

        GoodsItem goodsItem = goodsItemService.lambdaQuery()
                .eq(GoodsItem::getGoodsId, condition.getGoodsId())
                .eq(GoodsItem::getSkuId, condition.getSkuId())
                .one();
        CheckUtils.notNull(goodsItem, ProductResultCode.NOT_FIND_GOODS_ITEM_BY_SKU_ID);

        Long oldStock = goodsItem.getStock();
        goodsItem.setStock(0L);
        goodsItem.setUpdateTime(new Date());
        goodsItemService.updateById(goodsItem);


        GoodsEditPriceDto goodsEditPriceDto = new GoodsEditPriceDto();
        goodsEditPriceDto.setSkuChangeDtoList(Collections.singletonList(new GoodsEditPriceDto.SkuChangeDto(goodsItem.getId(), goodsItem.getOrginalPrice(), goodsItem.getOrginalPrice(), oldStock, 0L)));

        List<String> specialTagList = initSpecialTags(condition.getGoodsId());

        GoodsEditInfo editInfo = new GoodsEditInfo();
        editInfo.setGoodsId(goodsId);
        editInfo.setCategoryId(goods.getCategoryId());
        editInfo.setShopId(goods.getShopId());
        editInfo.setShopName(goods.getShopName());
        editInfo.setAuditUser("system");
        editInfo.setAuditTime(LocalDateTime.now());
        editInfo.setStatus(1);
        editInfo.setAddPrice(0);
        editInfo.setApplyUser(getUserName());
        editInfo.setApplyTime(LocalDateTime.now());
        editInfo.setApplyReason("商家取消订单并清零指定sku库存");
        editInfo.setIsDel(0);
        editInfo.setType(GoodsEditTypeEnums.STOCK.getCode());
        editInfo.setContent(JSON.toJSONString(goodsEditPriceDto));
        editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
        editInfo.setUpdateTime(LocalDateTime.now());
        goodsEditInfoService.save(editInfo);

        GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
        goodsEditInfoDetail.setEditId(editInfo.getId());
        goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
        goodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditPriceDto));
        goodsEditInfoDetailService.save(goodsEditInfoDetail);

        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(goodsId);
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("清零指定sku库存");
        goodsSyncModel.setSourceService("vp");
        mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.FIVE_SEC);

        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(goodsId)
                .type(OperationLogTypeEnums.CANCEL_ORDER_AND_CLEAR_SKU_STOCK)
                .oldData(oldStock.toString())
                .newData("0")
                .content(OperationLogTypeEnums.CANCEL_ORDER_AND_CLEAR_SKU_STOCK.getDesc())
                .status(1)
                .user(getUserName());
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
    }

    @Override
    public void batchUpdateName(InputStream inputStream) {
        LogUtils.info(log,"批量修改商品名称 start");
        List<BatchUpdateNameVo> voList = Lists.newArrayList();
        try {
            EasyExcel.read(inputStream, BatchUpdateNameVo.class, new AnalysisEventListener<BatchUpdateNameVo>() {
                @Override
                public void invoke(BatchUpdateNameVo data, AnalysisContext context) {
                    voList.add(data);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("批量修改商品名称 :{}", JSON.toJSONString(voList));
                }
            }).headRowNumber(1).sheet().doRead();
        } catch (Exception e) {
            log.error("批量修改商品名称 读取文件数据失败", e);
            CheckUtils.check(true, ProductResultCode.EXCEL_READ_ERROR);
        }

        List<Long> invalidNameGoodsIds = Lists.newArrayList();
        for (BatchUpdateNameVo vo : voList) {
            CheckUtils.check(vo.getGoodsId() == null, ProductResultCode.PARAMETER_GOODS_ID_NULL);
            CheckUtils.check(StringUtils.isBlank(vo.getName()), CustomResultCode.fill(ProductResultCode.PARAMETER_IS_NULL_EXT, "商品名称"));

            Set<String> sensitiveWords = sensitiveWordsCoreService.check(vo.getName());
            if (CollectionUtils.isNotEmpty(sensitiveWords)) {
                log.info("批量修改商品名称 存在敏感词 goodsId:{}, name:{}, 敏感词:{}", vo.getGoodsId(), vo.getName(), sensitiveWords);
                invalidNameGoodsIds.add(vo.getGoodsId());
            }
        }

        Map<Long, String> goodsNameMap = voList.stream().filter(vo -> !invalidNameGoodsIds.contains(vo.getGoodsId()))
                .collect(Collectors.toMap(BatchUpdateNameVo::getGoodsId, BatchUpdateNameVo::getName, (v1, v2) -> v1));

        List<Goods> goodsList = goodsService.queryGoodsByIds(goodsNameMap.keySet());
        if (CollectionUtils.isEmpty(goodsList)) {
            log.info("批量修改商品名称 待更新商品为空，return");
            return;
        }

        Map<Long, ImmutablePair<String, String>> changeMap = Maps.newHashMap();
        List<Goods> updateList = Lists.newArrayList();
        for (Goods goods : goodsList) {
            String newName = GoodsInfoUtils.formatGoodsName(goodsNameMap.get(goods.getId()));
            if (goods.getName().equals(newName)) {
                LogUtils.info(log, "批量修改商品名称 名称没变，跳过 id:{}, name:{}", goods.getId(), goods.getName());
                continue;
            }
            ImmutablePair<String, String> pair = new ImmutablePair<>(goods.getName(), newName);
            changeMap.put(goods.getId(), pair);

            goods.setName(newName);
            goods.setUpdateTime(LocalDateTime.now());
            updateList.add(goods);
            newRedisTemplate.opsForValue().increment("GOODS_NAME_UPDATE_COUNT:"+LocalDate.now());
        }

        if (CollectionUtils.isEmpty(updateList)) {
            log.info("批量修改商品名称 待更新商品为空，return2");
            return;
        }

        goodsService.updateBatchById(updateList);
        log.info("批量修改商品名称 update.DB.size:{}", updateList.size());

        for (Goods goods : updateList) {
            TermQueryBuilder termQueryBuilder = QueryBuilders.termQuery("id", goods.getId());
            Map<String, Object> map = new HashMap<>();
            map.put("name", goods.getName());
            map.put("syncTime", LocalDateTime.now());
            BulkByScrollResponse response = goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), termQueryBuilder, map);
            if (response == null) {
                GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
                goodsSyncModel.setGoodsId(goods.getId());
                goodsSyncModel.setSyncTime(System.currentTimeMillis());
                goodsSyncModel.setBusiness("批量修改商品名称");
                goodsSyncModel.setSourceService("vp");
                mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.FIVE_SEC);
            }
            log.info("批量修改商品名称 updateByQuery success,  goodsId:{}", goods.getId());
        }

        LogUtils.info(log, "批量修改商品名称 执行成功 chance:{}", changeMap);
    }

    /**
     * ERP刷新商品实拍图
     * @param refreshGoodsRealShotImgDTO
     * @return
     */
    @Override
    @Transactional
    public Boolean refreshGoodsRealShotImg(RefreshGoodsRealShotImgDTO refreshGoodsRealShotImgDTO) {
        CheckUtils.notNull(refreshGoodsRealShotImgDTO,ProductResultCode.PARAMETER_ERROR);
        CheckUtils.notNull(refreshGoodsRealShotImgDTO.getGoodsId(),ProductResultCode.PARAMETER_GOODS_ID_NULL);
        Long goodsId = refreshGoodsRealShotImgDTO.getGoodsId();
        List<String> realShotImgs = refreshGoodsRealShotImgDTO.getRealShotImgs();
        Goods goods = goodsService.queryGoodsById(goodsId);
        CheckUtils.notNull(goods,ProductResultCode.GOODS_NOT_EXIST);
        // 校验图片
        if (CollectionUtils.isNotEmpty(realShotImgs)){
            List<String> goodsRealShotImages = realShotImgs.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(s -> s.replace(" ", "").replace("\\n", ""))
                    .collect(Collectors.toList());
            goodsRealShotImages.forEach(s -> CheckUtils.check(!s.startsWith("http"), ProductResultCode.GOODS_IMAGE_FORMAT_ERROR));
            goodsRealShotImages.forEach(s -> CheckUtils.check(GoodsInfoUtils.verifyNotImage(s), ProductResultCode.GOODS_IMAGE_FORMAT_ERROR_HTML));
            realShotImgs = goodsRealShotImages;
        }
        //先删除商品相关的图片
        List<GoodsRealShotImg> oldGoodsRealShotImgList = goodsRealShotImgService.queryByGoodsId(goodsId);
        // 在重新添加图片
        Boolean save = goodsRealShotImgCoreService.addGoodsRealShotImg(goodsId, realShotImgs);
        CheckUtils.check(!save, ProductResultCode.ADD_REAL_SHOT_IMAGE_ERROR);
        // 记录修改日志
        ProductInfoInput productInfoInput = new ProductInfoInput();
        productInfoInput.setId(goodsId);
        productInfoInput.setShopId(goods.getShopId());
        productInfoInput.setShopName(goods.getShopName());
        productInfoInput.setCategoryId(goods.getCategoryId());
        log.info("ERP 实拍图 productInfoInput 入参:{}",JSON.toJSONString(productInfoInput));
        GoodsEditRealShotImgDTO goodsEditRealShotImgDTO = new GoodsEditRealShotImgDTO();
        goodsEditRealShotImgDTO.setOldRealShotImgs(oldGoodsRealShotImgList.stream().map(GoodsRealShotImg::getImgUrl).collect(Collectors.toList()));
        goodsEditRealShotImgDTO.setNewRealShotImgs(realShotImgs);
        log.info("ERP 实拍图 goodsEditRealShotImgDTO 入参:{}",JSON.toJSONString(goodsEditRealShotImgDTO));
        recordRealShotEditInfo(productInfoInput,goodsEditRealShotImgDTO);
        // 发送同盾
        tongDunGoodsImageCoreService.addTongDun(Lists.newArrayList(goods),1);
        return true;
    }

    @Override
    public Boolean updateFullGoods(ItemGoodsInfoInputDto dto) {
        log.info("【更新商品信息】dto:{}", JSONObject.toJSONString(dto));
        // 根据买手 & 商家 填充店铺信息
        fillItemGoodsInfoInputShopAndOperationLogType(dto);

        return updateFItemGoodsInfo(dto);
    }

    private Boolean updateFItemGoodsInfo(ItemGoodsInfoInputDto dto) {
        ItemGoodsInfoExtBo extBo = new ItemGoodsInfoExtBo();
        // 全部参数校验 & 填充extBo
        allParamsCheck(dto, extBo);

        // 更新商品信息
        Boolean success = updateGoodsInfoBiz.transactionUpdateProcessingGoodsInfo(dto, extBo);
        CheckUtils.check(!Boolean.TRUE.equals(success), ProductResultCode.UPDATE_ERROR);

        // 后置处理
        updateGoodsInfoBiz.postposition(dto, extBo);
        return Boolean.TRUE;
    }

    private void allParamsCheck(ItemGoodsInfoInputDto dto, ItemGoodsInfoExtBo extBo) {
        // 校验常规参数 & 图片信息
        AbsBaseGoodsInfo.checkParams(dto);
        CheckUtils.notNull(dto.getId(), ProductResultCode.PARAMETER_ID_ERROR);

        extBo.setTemplateGoods(AddGoodsInfoBiz.CHECK_SHOP_ID_LIST.contains(dto.getShopId()));

        // 校验新增属性信息 & 填充extBo.hasNewProperty
        updateGoodsInfoBiz.checkPropertyDetails(dto, extBo);

        // 获取类目信息
        Category category = categoryService.selectById(dto.getCategoryId());

        // 校验类目信息
        addGoodsInfoBiz.checkCategoryInfo(category);

        // 商户品牌校验
        addGoodsInfoBiz.checkMerchantBrand(dto, category);

        Goods goods = goodsService.queryGoodsById(dto.getId());

        CheckUtils.check(dto.getShopId() != null && !dto.getShopId().equals(goods.getShopId()), VoghionProductResultCode.GOODS_NOT_ALLOWED_OPERATER);
        dto.setIsShow(goods.getIsShow());

        // 非模版商品校验上架状态
        CheckUtils.check(!extBo.isTemplateGoods() && checkShowStatusForUpdate(Integer.parseInt(goods.getIsShow())), ProductResultCode.GOODS_SHOW_STATUS_CAN_NOT_UPDATE);

        // 敏感词校验
        updateGoodsInfoBiz.checkSensitiveWord(dto, category, goods);

        // 校验物流属性
        goodsLogisticsCoreService.checkLogisticProperty(dto.getCategoryId(), dto.getLogisticsProperty());

        // listing商品
        // 填充dto.userType & dto.isShow & extBo.listingId
        checkListingAndFill(dto, extBo);

        // 中文校验
        updateGoodsInfoBiz.checkItemGoodsContainChinese(dto);

        // 获取商家信息
        FaMerchantsApply faMerchantsApply = faMerchantsApplyService.selectById(goods.getShopId());

        // 店铺数据冗余 && 海外仓数据兼容
        GoodsInfoUtils.extractedShopInfo(dto, faMerchantsApply);

        // 运费校验
        updateGoodsInfoBiz.nationalFreight(dto, extBo, faMerchantsApply);

        // 非模版类型校验店铺主营类目
        updateGoodsInfoBiz.checkNonTemplateMainCategories(faMerchantsApply, dto, extBo);

        // 尺码表/图 校验  1尺码表尺码图二选一 2必须维护尺码表
        Integer limitType = categoryCoreService.checkSizeIsNeed(dto.getCategoryId());
        CheckUtils.check(!extBo.isTemplateGoods() && limitType == 1 && dto.getSizeChartTemplateId() == null && StringUtils.isBlank(dto.getSizeImage()), ProductResultCode.SIZE_IMAGE_ERROR);
        CheckUtils.check(!extBo.isTemplateGoods() && limitType == 2 && dto.getSizeChartTemplateId() == null, ProductResultCode.SIZE_CHART_TEMPLATE_MUST);

        // 获取所有父类目id 包含自身
        List<Long> pathCategoryIds = getCategoryIds(dto.getCategoryId(), category);

        // 尺码表校验
        checkSizeChartTemplate(dto.getSizeChartTemplateId(), dto.getShopId(), pathCategoryIds);

        // 校验商品维护规范
        goodsStandardCoreService.checkGoodsStandard(dto);

        // 检测sku限价信息(scm自营店铺不校验)
        updateGoodsInfoBiz.checkSkuLimitPrice(dto, faMerchantsApply);

        // 判断是否满足建议降价
        updateGoodsInfoBiz.fillUpdateReductionPrice(dto);

        extBo.setCategory(category);
        extBo.setFaMerchantsApply(faMerchantsApply);
        extBo.setPathCategoryIds(pathCategoryIds);

        // 虚拟折扣，都是算的，不从外界取
        extBo.setVirtualDiscount(newAddGoodsCoreService.getVirtualDiscount(pathCategoryIds, dto.getShopId()));
    }

    private void checkListingAndFill(ItemGoodsInfoInputDto dto, ItemGoodsInfoExtBo extBo) {
        fillListingUseType(dto, extBo);
        CheckUtils.check(dto.getUseType() == 2 && OperationLogTypeEnums.ERP_UPDATE_GOODS.getCode().equals(dto.getOperationLogType()), ProductResultCode.LISTING_GOODS_FORBID_UPDATE);
        if (dto.getUseType() == 2 && GoodsIsShowEnums.WAIT_AUDIT.getType().toString().equals(dto.getIsShow())) {
            // listing首次保存无需审批直接上架
            extBo.setListingFirstEdit(true);
            dto.setIsShow(GoodsIsShowEnums.SHELF.getType().toString());
        }

        CheckUtils.check(dto.getUseType() != 1 && StringUtils.isBlank(dto.getItemCode()), ProductResultCode.ITEM_CODE_ERROR);
    }

    @NotNull
    private static List<Long> getCategoryIds(Long categoryId, Category category) {
        List<Long> pathCategoryIds = Lists.newArrayList(categoryId);
        if (StringUtils.isNotBlank(category.getPids())) {
            pathCategoryIds.addAll(Arrays.stream(category.getPids().split(",")).map(Long::parseLong).collect(Collectors.toList()));
        }

        return pathCategoryIds;
    }

    private void fillItemGoodsInfoInputShopAndOperationLogType(ItemGoodsInfoInputDto dto) {
        dto.setOperationLogType(OperationLogTypeEnums.UPDATE_GOODS.getCode());
        if (dto.getShopId() != null && StringUtils.isNotBlank(dto.getBuyerClientinfo())) {
            log.info("【更新商品信息】买手操作 shopId:{} buyerClientinfo:{}", dto.getShopId(), dto.getBuyerClientinfo());
            return;
        }

        Long shopId = LoginUserMsg.getShopId();
        String shopName = LoginUserMsg.getShopName();
        if (shopId != null && shopId > 0) {
            dto.setShopId(shopId);
            dto.setStoreName(shopName);
        }

        log.info("【更新商品信息】店铺id:{}", shopId);
    }

}
