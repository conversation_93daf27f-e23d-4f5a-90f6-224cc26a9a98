package com.voghion.product.core.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.lang.exception.CustomException;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.colorlight.base.mybatis.utils.PageTransferUtils;
import com.colorlight.base.utils.CheckUtils;
import com.colorlight.base.utils.CollectionUtil;
import com.colorlight.base.utils.TransferUtils;
import com.google.common.collect.Lists;
import com.onlest.GoodsSyncModel;
import com.voghion.es.service.GoodsEsService;
import com.voghion.product.api.dto.CustomTagInfoDTO;
import com.voghion.product.api.dto.QueryGoodsVO;
import com.voghion.product.bigquery.service.DingDingCoreService;
import com.voghion.product.bigquery.service.impl.DingDingCoreServiceImpl;
import com.voghion.product.core.CustomTagCoreService;
import com.voghion.product.core.GoodsCoreService;
import com.voghion.product.enums.CustomizedGoodsSyncEnums;
import com.voghion.product.model.enums.GoodsTagResultCode;
import com.voghion.product.model.po.Goods;
import com.voghion.product.model.po.GoodsTagCustom;
import com.voghion.product.model.po.GoodsTagRel;
import com.voghion.product.model.vo.GoodsInfoVO;
import com.voghion.product.model.vo.GoodsRelCustomTagImportVO;
import com.voghion.product.model.vo.GoodsRelCustomTagOutput;
import com.voghion.product.model.vo.condition.CustomTagCondition;
import com.voghion.product.model.vo.condition.CustomTagRelCondition;
import com.voghion.product.model.vo.customTag.CustomTagRelGoodsExportVO;
import com.voghion.product.model.vo.customTag.CustomTagRelGoodsReqVO;
import com.voghion.product.model.vo.customTag.CustomTagReqVO;
import com.voghion.product.model.vo.customTag.CustomTagVO;
import com.voghion.product.mq.MqDelayLevel;
import com.voghion.product.mq.MqSender;
import com.voghion.product.mq.model.CustomizedGoodsSyncModel;
import com.voghion.product.service.GoodsService;
import com.voghion.product.service.GoodsTagCustomService;
import com.voghion.product.service.GoodsTagRelService;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.opensearch.action.bulk.BulkItemResponse;
import org.opensearch.action.bulk.BulkRequest;
import org.opensearch.action.bulk.BulkResponse;
import org.opensearch.action.update.UpdateRequest;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.common.xcontent.XContentType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * CustomTagCoreServiceImpl
 *
 * <AUTHOR>
 * @date 2023/12/25
 */
@Service
@Slf4j
public class CustomTagCoreServiceImpl extends AbstractCommonServiceImpl implements CustomTagCoreService {

    @Resource
    private GoodsTagCustomService goodsTagCustomService;

    @Resource
    private GoodsTagRelService goodsTagRelService;

    @Resource
    private GoodsCoreService goodsCoreService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private MqSender mqSender;

    @Resource
    private RedisApi redisApi;


    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;
    @Resource
    private GoodsEsService goodsEsService;
    @Resource
    private DingDingCoreService dingDingCoreService;

    @Override
    public void saveCustomTag(CustomTagReqVO customTagReqVO) {
        //校验自定义标签非空
        CheckUtils.notNull(customTagReqVO.getTagName(), GoodsTagResultCode.TAG_NAME_NULL_ERROR);
        String user = getUserName();
        LocalDateTime time = LocalDateTime.now();
        if (Objects.isNull(customTagReqVO.getId())) {
            //校验自定义标签唯一
            List<GoodsTagCustom> customTagList = goodsTagCustomService.lambdaQuery()
                    .eq(GoodsTagCustom::getTagName, customTagReqVO.getTagName())
                    .eq(customTagReqVO.getType() != null, GoodsTagCustom::getType, customTagReqVO.getType())
                    .in(customTagReqVO.getType() == null, GoodsTagCustom::getType, Lists.newArrayList(1, 3))
                    .list();
            if (CollectionUtils.isNotEmpty(customTagList)) {
                throw new CustomException(GoodsTagResultCode.TAG_NAME_REPEAT_ERROR);
            }
            //新增自定义标签
            GoodsTagCustom goodsTagCustom = new GoodsTagCustom();
            goodsTagCustom.setTagName(customTagReqVO.getTagName());
            goodsTagCustom.setRemark(customTagReqVO.getRemark());
            goodsTagCustom.setCreateUser(user);
            goodsTagCustom.setCreateTime(time);
            goodsTagCustom.setUpdateUser(user);
            goodsTagCustom.setUpdateTime(time);
            goodsTagCustom.setType(customTagReqVO.getType());
            if (customTagReqVO.getType() != null && customTagReqVO.getType() == 2) {
                goodsTagCustom.setType(customTagReqVO.getType());
                goodsTagCustom.setIsShow(customTagReqVO.getIsShow());
                goodsTagCustom.setTagText(customTagReqVO.getTagText());
                goodsTagCustom.setBackImg(customTagReqVO.getBackImg());
                goodsTagCustom.setSort(customTagReqVO.getSort());
                goodsTagCustom.setIsCombo(customTagReqVO.getIsCombo());
                goodsTagCustom.setSexLabel("");
                List<Integer> sexLabelList = customTagReqVO.getSexLabelList();
                if(CollectionUtils.isNotEmpty(sexLabelList)){
                    goodsTagCustom.setSexLabel(StringUtils.join(sexLabelList,","));
                }
            }
            log.info("saveCustomTag add:{}", JSON.toJSONString(goodsTagCustom));
            goodsTagCustomService.insert(goodsTagCustom);
        } else {
            //更新自定义标签
            GoodsTagCustom goodsTagCustom = goodsTagCustomService.selectById(customTagReqVO.getId());
            if (Objects.isNull(goodsTagCustom)) {
                throw new CustomException(GoodsTagResultCode.TAG_INFO_ABSENT);
            }
            if (!StringUtils.equals(goodsTagCustom.getTagName(), customTagReqVO.getTagName())) {
                //校验自定义标签唯一
                List<GoodsTagCustom> customTagList = goodsTagCustomService.lambdaQuery()
                        .eq(GoodsTagCustom::getTagName, customTagReqVO.getTagName())
                        .eq(customTagReqVO.getType() != null, GoodsTagCustom::getType, customTagReqVO.getType())
                        .in(customTagReqVO.getType() == null, GoodsTagCustom::getType, Lists.newArrayList(1, 3))
                        .list();
                if (CollectionUtils.isNotEmpty(customTagList)) {
                    throw new CustomException(GoodsTagResultCode.TAG_NAME_REPEAT_ERROR);
                }
            }
            goodsTagCustom.setTagName(customTagReqVO.getTagName());
            goodsTagCustom.setRemark(customTagReqVO.getRemark());
            goodsTagCustom.setType(customTagReqVO.getType());
            goodsTagCustom.setUpdateTime(time);
            goodsTagCustom.setUpdateUser(user);
            if (customTagReqVO.getType() != null && customTagReqVO.getType() == 2) {
                goodsTagCustom.setType(customTagReqVO.getType());
                goodsTagCustom.setIsShow(customTagReqVO.getIsShow());
                goodsTagCustom.setTagText(customTagReqVO.getTagText());
                goodsTagCustom.setBackImg(customTagReqVO.getBackImg());
                goodsTagCustom.setSort(customTagReqVO.getSort());
                goodsTagCustom.setIsCombo(customTagReqVO.getIsCombo());
                List<Integer> sexLabelList = customTagReqVO.getSexLabelList();
                goodsTagCustom.setSexLabel("");
                if(CollectionUtils.isNotEmpty(sexLabelList)){
                    goodsTagCustom.setSexLabel(StringUtils.join(sexLabelList,","));
                }
            }
            log.info("saveCustomTag update:{}", JSON.toJSONString(goodsTagCustom));
            goodsTagCustomService.updateById(goodsTagCustom);
        }
        refreshCache();
    }

    @Override
    public Boolean deleteCustomTag(Long tagId) {
        GoodsTagCustom goodsTagCustom = goodsTagCustomService.selectById(tagId);
        if (Objects.isNull(goodsTagCustom)) {
            throw new CustomException(GoodsTagResultCode.TAG_INFO_ABSENT);
        }
        List<GoodsTagRel> tagRelList = goodsTagRelService.lambdaQuery().eq(GoodsTagRel::getTagId, tagId).list();
        if(CollectionUtils.isNotEmpty(tagRelList)){
            throw new CustomException(GoodsTagResultCode.DELETE_REL_GOODS_TAG_EXCEPTION);
        }
        boolean success = goodsTagCustomService.removeById(goodsTagCustom);
        if (success) {
            refreshCache();
        }
        return success;
    }

    @Override
    public PageView<CustomTagVO> listCustomTags(CustomTagCondition customTagCondition) {
        List<Long> tagIds = null;
        if (StringUtils.isNotEmpty(customTagCondition.getTagIdListStr())) {
             tagIds = Arrays.stream(customTagCondition.getTagIdListStr().split("\n"))
                    .filter(StringUtils::isNotBlank)
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toList());
        }

        IPage<GoodsTagCustom> goodsTagPage = goodsTagCustomService.lambdaQuery()
                .like(StringUtils.isNotBlank(customTagCondition.getTagName()), GoodsTagCustom::getTagName, customTagCondition.getTagName())
                .like(StringUtils.isNotBlank(customTagCondition.getCreateUser()), GoodsTagCustom::getCreateUser, customTagCondition.getCreateUser())
                .like(StringUtils.isNotBlank(customTagCondition.getRemark()), GoodsTagCustom::getRemark, customTagCondition.getRemark())
                .like(StringUtils.isNotBlank(customTagCondition.getUpdateUser()), GoodsTagCustom::getUpdateUser, customTagCondition.getUpdateUser())
                .like(customTagCondition.getSex()!=null, GoodsTagCustom::getSexLabel, customTagCondition.getSex())
                .eq(customTagCondition.getType() != null, GoodsTagCustom::getType, customTagCondition.getType())
                .in(customTagCondition.getType() == null, GoodsTagCustom::getType, Lists.newArrayList(1, 3))
                .eq(customTagCondition.getIsShow() != null, GoodsTagCustom::getIsShow, customTagCondition.getIsShow())
                .eq(customTagCondition.getIsCombo() != null, GoodsTagCustom::getIsCombo, customTagCondition.getIsCombo())
                .in(CollectionUtils.isNotEmpty(tagIds), GoodsTagCustom::getId, tagIds)
                .and(StringUtils.isNotBlank(customTagCondition.getSearch()), wrapper -> {
                    if (StringUtils.isNotBlank(customTagCondition.getSearch())) {
                        wrapper.like(GoodsTagCustom::getTagName, customTagCondition.getSearch())
                                .or().like(GoodsTagCustom::getRemark, customTagCondition.getSearch())
                                .or().eq(GoodsTagCustom::getId, customTagCondition.getSearch());
                    }
                    return wrapper;
                })
                .gt(customTagCondition.getStartTime() != null, GoodsTagCustom::getUpdateTime, customTagCondition.getStartTime())
                .lt(customTagCondition.getEndTime() != null, GoodsTagCustom::getUpdateTime, customTagCondition.getEndTime())
                .orderByDesc(GoodsTagCustom::getUpdateTime)
                .page(new Page<>(customTagCondition.getPageNow(), customTagCondition.getPageSize()));

        PageView<GoodsTagCustom> goodsTagPageView = new PageView<>();
        PageTransferUtils.tranferIpageToPageView(goodsTagPage, goodsTagPageView);
        PageView<CustomTagVO> resultPage = new PageView<>();
        TransferUtils.transferBean(goodsTagPageView, resultPage);
        if (CollectionUtil.isNotEmpty(goodsTagPage.getRecords())) {
            List<CustomTagVO> resultList = new ArrayList<>();
            for (GoodsTagCustom item : goodsTagPage.getRecords()) {
                CustomTagVO customTagVO = new CustomTagVO();
                TransferUtils.transferBean(item, customTagVO);
                if(StringUtils.isNotBlank(item.getSexLabel())){
                    List<Integer> sexLabelList = Arrays.stream(item.getSexLabel().split(",")).map(Integer::valueOf).collect(Collectors.toList());
                    customTagVO.setSexLabelList(sexLabelList);
                }
                resultList.add(customTagVO);
            }
            resultPage.setRecords(resultList);
        }
        return resultPage;
    }

    @Override
    public PageView<GoodsInfoVO> getCustomTagRelGoods(CustomTagRelCondition customTagRelCondition) {
        GoodsTagCustom goodsTagCustom = goodsTagCustomService.selectById(customTagRelCondition.getTagId());
        if (Objects.isNull(goodsTagCustom)) {
            throw new CustomException(GoodsTagResultCode.TAG_INFO_ABSENT);
        }
        List<GoodsTagRel> tagRelList = goodsTagRelService.lambdaQuery().eq(GoodsTagRel::getTagId, customTagRelCondition.getTagId()).list();
        if (CollectionUtils.isEmpty(tagRelList)) {
            throw new CustomException(GoodsTagResultCode.TAG_REL_GOODS_NULL);
        }
        QueryGoodsVO queryGoodsVO = new QueryGoodsVO();
        queryGoodsVO.setPageNow((int) customTagRelCondition.getPageNow());
        queryGoodsVO.setPageSize((int) customTagRelCondition.getPageSize());
        queryGoodsVO.setCategoryId(customTagRelCondition.getCategoryId());
        queryGoodsVO.setIsShow(customTagRelCondition.getIsShow());
        queryGoodsVO.setGoodsIdListStr(customTagRelCondition.getGoodsIdListStr());
        queryGoodsVO.setShopName(customTagRelCondition.getShopName());
        queryGoodsVO.setName(customTagRelCondition.getName());
        queryGoodsVO.setPrincipal(customTagRelCondition.getPrincipal());
        queryGoodsVO.setTag(customTagRelCondition.getTagId().toString());
        queryGoodsVO.setIsDel(0);
        return goodsCoreService.goodsByPageListWithEs(queryGoodsVO);
    }

    @Override
    @Transactional
    public Result<Boolean> customTagRelGoods(CustomTagRelGoodsReqVO requestVO) {
        CheckUtils.check(Objects.isNull(requestVO.getId()), GoodsTagResultCode.ID_NULL_ERROR);
        CheckUtils.check(Objects.isNull(requestVO.getRelType()), GoodsTagResultCode.TAG_REL_TYPE_NULL);
        CheckUtils.check(CollectionUtils.isEmpty(requestVO.getGoodsIdList()), GoodsTagResultCode.TAG_REL_GOODS_ID_NULL);
        GoodsTagCustom goodsTagCustom = goodsTagCustomService.selectById(requestVO.getId());
        if (Objects.isNull(goodsTagCustom)) {
            return Result.fail(GoodsTagResultCode.TAG_INFO_ABSENT);
        }
        CheckUtils.check(requestVO == null || (requestVO.getRelType() != 0 && requestVO.getRelType() != 1), GoodsTagResultCode.IS_SHOW_ERROR);

        LocalDateTime time = LocalDateTime.now();
        if(Objects.nonNull(getShopId())){
            requestVO.setUpdateBy("SYSTEM");
        }
        String user = StringUtils.isNotBlank(requestVO.getUpdateBy()) ? requestVO.getUpdateBy() : getUserName();

        //查询需要绑定自定义标签的商品,并校验是否都合法存在
        List<Goods> goods = goodsService.queryGoodsByIds(requestVO.getGoodsIdList());
        if (CollectionUtils.isEmpty(goods)) {
            return Result.fail(GoodsTagResultCode.TAG_REL_GOODS_ID_EXCEPTION.getCode(), JSON.toJSONString(requestVO.getGoodsIdList()));
        }
        List<Long> goodsIdDbList = goods.stream().map(Goods::getId).collect(Collectors.toList());
        List<Long> reduceList = requestVO.getGoodsIdList().stream().filter(item -> !goodsIdDbList.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reduceList)) {
            return Result.fail(GoodsTagResultCode.TAG_REL_GOODS_ID_EXCEPTION.getCode(), JSON.toJSONString(reduceList));
        }

        List<GoodsTagRel> tagRelList = goodsTagRelService.lambdaQuery().eq(GoodsTagRel::getTagId, requestVO.getId())
                .in(GoodsTagRel::getGoodsId, requestVO.getGoodsIdList()).list();
        List<Long> bindList = tagRelList.stream().map(GoodsTagRel::getGoodsId).collect(Collectors.toList());

        if (requestVO.getRelType() == 1) { //绑定
            List<GoodsTagRel> insertList = new ArrayList<>();
            List<Long> distinctList = requestVO.getGoodsIdList().stream().distinct().collect(Collectors.toList());
            for (Long goodsId : distinctList) {
                if (bindList.contains(goodsId)) {
                    continue;
                }
                GoodsTagRel insertModel = new GoodsTagRel();
                insertModel.setGoodsId(goodsId);
                insertModel.setTagId(requestVO.getId());
                insertModel.setCreateTime(time);
                insertModel.setUpdateTime(time);
                insertList.add(insertModel);
            }
            if (CollectionUtils.isNotEmpty(insertList)) {
                goodsTagRelService.insertBatch(insertList);
                sendMq(requestVO.getGoodsIdList(), true, "同步自定义标签(绑定)");
            }
        } else { // 解绑
            List<Long> removeList = new ArrayList<>();
            for (Long goodsId : requestVO.getGoodsIdList()) {
                if (bindList.contains(goodsId)) {
                    removeList.add(goodsId);
                }
            }
            if (CollectionUtils.isNotEmpty(removeList)) {
                QueryWrapper<GoodsTagRel> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("goods_id", removeList);
                queryWrapper.eq("tag_id", requestVO.getId());
                goodsTagRelService.remove(queryWrapper);
                sendMq(removeList, true, "同步自定义标签(解绑)");

            }
        }
        goodsTagCustom.setUpdateUser(user);
        goodsTagCustom.setUpdateTime(time);
        goodsTagCustomService.updateById(goodsTagCustom);
        return Result.success(Boolean.TRUE);
    }

    @Override
    @Transactional
    public Result<Boolean> customTagRelGoodsBatch(CustomTagRelGoodsReqVO requestVO) {
        CheckUtils.isEmpty(requestVO.getTagIds(), GoodsTagResultCode.TAG_IDS_NULL_ERROR);
        CheckUtils.notNull(requestVO.getRelType(), GoodsTagResultCode.TAG_REL_TYPE_NULL);
        CheckUtils.check(requestVO.getRelType() != 0 && requestVO.getRelType() != 1, GoodsTagResultCode.IS_SHOW_ERROR);
        CheckUtils.isEmpty(requestVO.getGoodsIdList(), GoodsTagResultCode.TAG_REL_GOODS_ID_NULL);

        LocalDateTime time = LocalDateTime.now();
        String user = getUserName();

        List<Goods> goods = goodsService.queryGoodsByIds(requestVO.getGoodsIdList());
        if (CollectionUtils.isEmpty(goods)) {
            return Result.fail(GoodsTagResultCode.TAG_REL_GOODS_ID_EXCEPTION.getCode(), JSON.toJSONString(requestVO.getGoodsIdList()));
        }
        List<Long> goodsIdDbList = goods.stream().map(Goods::getId).collect(Collectors.toList());
        List<Long> invalidGoods = requestVO.getGoodsIdList().stream()
                .filter(goodsId -> !goodsIdDbList.contains(goodsId))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(invalidGoods)) {
            return Result.fail(GoodsTagResultCode.TAG_REL_GOODS_ID_EXCEPTION.getCode(), JSON.toJSONString(invalidGoods));
        }

        for (Long tagId : requestVO.getTagIds()) {
            GoodsTagCustom goodsTagCustom = goodsTagCustomService.selectById(tagId);
            if (Objects.isNull(goodsTagCustom)) {
                return Result.fail(GoodsTagResultCode.TAG_INFO_ABSENT);
            }

            List<GoodsTagRel> tagRelList = goodsTagRelService.lambdaQuery()
                    .eq(GoodsTagRel::getTagId, tagId)
                    .in(GoodsTagRel::getGoodsId, requestVO.getGoodsIdList())
                    .list();
            List<Long> bindList = tagRelList.stream()
                    .map(GoodsTagRel::getGoodsId)
                    .collect(Collectors.toList());

            if (requestVO.getRelType() == 1) {
                List<GoodsTagRel> insertList = new ArrayList<>();
                List<Long> distinctGoodsIds = requestVO.getGoodsIdList().stream().distinct().collect(Collectors.toList());
                for (Long goodsId : distinctGoodsIds) {
                    if (bindList.contains(goodsId)) {
                        continue;
                    }
                    GoodsTagRel insertModel = new GoodsTagRel();
                    insertModel.setGoodsId(goodsId);
                    insertModel.setTagId(tagId);
                    insertModel.setCreateTime(time);
                    insertModel.setUpdateTime(time);
                    insertList.add(insertModel);
                }
                if (CollectionUtils.isNotEmpty(insertList)) {
                    goodsTagRelService.insertBatch(insertList);
                    // 发送mq通知（绑定消息）
                    sendMq(requestVO.getGoodsIdList(), true, "根据标签id集合批量关联商品列表");
                }
            } else {
                List<Long> removeList = new ArrayList<>();
                for (Long goodsId : requestVO.getGoodsIdList()) {
                    if (bindList.contains(goodsId)) {
                        removeList.add(goodsId);
                    }
                }
                if (CollectionUtils.isNotEmpty(removeList)) {
                    QueryWrapper<GoodsTagRel> queryWrapper = new QueryWrapper<>();
                    queryWrapper.in("goods_id", removeList);
                    queryWrapper.eq("tag_id", tagId);
                    goodsTagRelService.remove(queryWrapper);
                    sendMq(removeList, true, "根据标签id集合批量解除商品列表");
                }
            }
            goodsTagCustom.setUpdateUser(user);
            goodsTagCustom.setUpdateTime(time);
            goodsTagCustomService.updateById(goodsTagCustom);
        }
        return Result.success(Boolean.TRUE);
    }

    @Override
    public void saveCombo(CustomTagReqVO customTagReqVO) {
        List<Long> ids = customTagReqVO.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            throw new CustomException(GoodsTagResultCode.COMBOS_IDS_NULL_ERROR);
        }
        Integer isCombo = Optional.ofNullable(customTagReqVO.getIsCombo()).orElse(0);
        Collection<GoodsTagCustom> goodsTagCustoms = goodsTagCustomService.selectByIds(ids);
        if (CollectionUtils.isEmpty(goodsTagCustoms)) {
            throw new CustomException(GoodsTagResultCode.TAG_INFO_ABSENT);
        }
        for (GoodsTagCustom goodsTagCustom : goodsTagCustoms) {
            goodsTagCustom.setIsCombo(isCombo);
            goodsTagCustom.setUpdateUser(getUserName());
            goodsTagCustom.setUpdateTime(LocalDateTime.now());
        }
        boolean success = goodsTagCustomService.updateBatchById(goodsTagCustoms);
        if (success) {
            refreshCache();
        }
    }

    @Override
    public void syncGoodsTagRel(List<Long> goodsIds) {
        List<GoodsTagRel> goodsTagRelList = goodsTagRelService.lambdaQuery()
                .in(GoodsTagRel::getGoodsId, goodsIds)
                .list();
        if (CollectionUtils.isEmpty(goodsTagRelList)) {
            return;
        }
        LocalDateTime time = LocalDateTime.now();
        String user = getUserName();
        List<Long> tagIds = goodsTagRelList.stream().map(GoodsTagRel::getTagId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tagIds)) {
            goodsTagCustomService.lambdaUpdate()
                    .set(GoodsTagCustom::getUpdateUser, user)
                    .set(GoodsTagCustom::getUpdateTime, time)
                    .in(GoodsTagCustom::getId, tagIds)
                    .update();
        }
        List<Long> ids = goodsTagRelList.stream().map(GoodsTagRel::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ids)) {
            goodsTagRelService.removeByIds(ids);
        }

        CustomizedGoodsSyncModel goodsSyncModel = new CustomizedGoodsSyncModel();
        goodsSyncModel.setGoodsIds(goodsIds);
        goodsSyncModel.setValues(Collections.singletonList(CustomizedGoodsSyncEnums.GOODS_EXT_CONFIG.getVal()));
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("删除商品时同步删除商品标签MQ");
        goodsSyncModel.setSourceService("vp");
        mqSender.send("CUSTOMIZED_SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel));
    }

    @Override
    public List<CustomTagRelGoodsExportVO> getCustomTagRelGoodsIdList(Long tagId) {
        CheckUtils.check(Objects.isNull(tagId), GoodsTagResultCode.ID_NULL_ERROR);
        GoodsTagCustom goodsTagCustom = goodsTagCustomService.selectById(tagId);
        if (Objects.isNull(goodsTagCustom)) {
            throw new CustomException(GoodsTagResultCode.TAG_INFO_ABSENT);
        }
        List<GoodsTagRel> tagRelList = goodsTagRelService.lambdaQuery().eq(GoodsTagRel::getTagId, tagId).list();
        if (CollectionUtils.isNotEmpty(tagRelList)) {
            return TransferUtils.transferList(tagRelList, CustomTagRelGoodsExportVO.class);
        }
        return new ArrayList<>();
    }

    @Override
    public List<CustomTagRelGoodsExportVO> getCustomTagRelGoodsIdList(CustomTagRelCondition customTagRelCondition) {
        List<CustomTagRelGoodsExportVO> resultList = new ArrayList<>();
        CheckUtils.check(Objects.isNull(customTagRelCondition.getTagId()), GoodsTagResultCode.ID_NULL_ERROR);
        GoodsTagCustom goodsTagCustom = goodsTagCustomService.selectById(customTagRelCondition.getTagId());
        if (Objects.isNull(goodsTagCustom)) {
            throw new CustomException(GoodsTagResultCode.TAG_INFO_ABSENT);
        }
        List<GoodsTagRel> tagRelList = goodsTagRelService.lambdaQuery().eq(GoodsTagRel::getTagId, customTagRelCondition.getTagId()).list();
        if (CollectionUtils.isNotEmpty(tagRelList)) {
            List<Long> relGoodsIdList = tagRelList.stream().map(GoodsTagRel::getGoodsId).collect(Collectors.toList());
            QueryGoodsVO queryGoodsVO = new QueryGoodsVO();
            queryGoodsVO.setPageNow(1);
            queryGoodsVO.setPageSize(500);
            queryGoodsVO.setCategoryId(customTagRelCondition.getCategoryId());
            queryGoodsVO.setIsShow(customTagRelCondition.getIsShow());
            queryGoodsVO.setGoodsIdListStr(customTagRelCondition.getGoodsIdListStr());
            queryGoodsVO.setShopName(customTagRelCondition.getShopName());
            queryGoodsVO.setName(customTagRelCondition.getName());
            queryGoodsVO.setIsDel(0);
            for (List<Long> goodsIdList : Lists.partition(relGoodsIdList, 500)) {
                queryGoodsVO.setGoodsIds(goodsIdList);
                PageView<GoodsInfoVO> goodsInfoVOPageView = goodsCoreService.goodsByPageListWithEs(queryGoodsVO);
                if (Objects.nonNull(goodsInfoVOPageView) && CollectionUtils.isNotEmpty(goodsInfoVOPageView.getRecords())) {
                    for (GoodsInfoVO goodsInfoVO : goodsInfoVOPageView.getRecords()) {
                        CustomTagRelGoodsExportVO customTagRelGoodsExportVO = new CustomTagRelGoodsExportVO();
                        customTagRelGoodsExportVO.setGoodsId(goodsInfoVO.getId());
                        resultList.add(customTagRelGoodsExportVO);
                    }
                }
            }
        }
        return resultList;
    }

    @Override
    @Transactional
    public GoodsRelCustomTagOutput batchImportGoodsRelCustomTag(Long tagId, List<GoodsRelCustomTagImportVO> list) {
        GoodsRelCustomTagOutput res = new GoodsRelCustomTagOutput();
        Map<Long, String> errorInfoMap = new HashMap<>();
        CheckUtils.check(Objects.isNull(tagId), GoodsTagResultCode.ID_NULL_ERROR);
        CheckUtils.check(CollectionUtils.isEmpty(list), GoodsTagResultCode.TAG_REL_GOODS_ID_NULL);
        GoodsTagCustom goodsTagCustom = goodsTagCustomService.selectById(tagId);
        CheckUtils.check(Objects.isNull(goodsTagCustom), GoodsTagResultCode.TAG_INFO_ABSENT);
        LocalDateTime time = LocalDateTime.now();
        String user = getUserName();

        List<Long> goodsIdList = list.stream().map(GoodsRelCustomTagImportVO::getGoodsId).distinct().collect(Collectors.toList());
        //查询需要绑定自定义标签的商品,并校验是否都合法存在
        List<Goods> goods = goodsService.queryGoodsByIds(goodsIdList);
        if (CollectionUtils.isEmpty(goods)) {
            for (Long goodsId : goodsIdList) {
                errorInfoMap.put(goodsId, "查询商品信息为空");
            }
            res.setErrorInfoMap(errorInfoMap);
            return res;
        }
        List<Long> goodsIdDbList = goods.stream().map(Goods::getId).collect(Collectors.toList());
        List<Long> reduceList = goodsIdList.stream().filter(item -> !goodsIdDbList.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reduceList)) {
            for (Long goodsId : reduceList) {
                errorInfoMap.put(goodsId, "查询商品信息为空");
            }
            res.setErrorInfoMap(errorInfoMap);
            return res;
        }

        List<GoodsTagRel> tagRelList = goodsTagRelService.lambdaQuery().eq(GoodsTagRel::getTagId, tagId)
                .in(GoodsTagRel::getGoodsId, goodsIdList).list();
        List<Long> bindList = tagRelList.stream().map(GoodsTagRel::getGoodsId).collect(Collectors.toList());

        List<GoodsTagRel> insertList = new ArrayList<>();
        for (Long goodsId : goodsIdList) {
            if (bindList.contains(goodsId)) {
                continue;
            }
            GoodsTagRel insertModel = new GoodsTagRel();
            insertModel.setGoodsId(goodsId);
            insertModel.setTagId(tagId);
            insertModel.setCreateTime(time);
            insertModel.setUpdateTime(time);
            insertList.add(insertModel);
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            goodsTagRelService.insertBatch(insertList);
            sendMq(goodsIdList, true, "批量导入商品关联标签");
        }
        goodsTagCustom.setUpdateUser(user);
        goodsTagCustom.setUpdateTime(time);
        goodsTagCustomService.updateById(goodsTagCustom);
        return res;
    }

    @Override
    public List<CustomTagVO> listAll(CustomTagCondition customTagCondition) {
        List<Integer> typeList = Lists.newArrayList(1, 3);
        if (customTagCondition != null && customTagCondition.getType() != null) {
            typeList = Lists.newArrayList(customTagCondition.getType());
        }
        List<CustomTagVO> customTagList = BeanCopyUtil.transformList(goodsTagCustomService.lambdaQuery()
                .in(GoodsTagCustom::getType, typeList)
                .eq(GoodsTagCustom::getIsShow, 1)
                .eq(customTagCondition != null && customTagCondition.getIsCombo() != null, GoodsTagCustom::getIsCombo, customTagCondition.getIsCombo())
                .list(), CustomTagVO.class);
        for (CustomTagVO goodsTagCustom : customTagList) {
            if(StringUtils.isNotBlank(goodsTagCustom.getSexLabel())){
                List<Integer> sexLabelList = Arrays.stream(goodsTagCustom.getSexLabel().split(",")).map(Integer::valueOf).collect(Collectors.toList());
                goodsTagCustom.setSexLabelList(sexLabelList);
            }
        }
        return customTagList;
    }

    @Override
    public Boolean refreshGoodsTagRelNum() {
        List<GoodsTagCustom> goodsTagCustoms = goodsTagCustomService.list();
        if(CollectionUtils.isEmpty(goodsTagCustoms)){
            return Boolean.TRUE;
        }
        Map<Long, Long> goodsRelNumMap = goodsTagRelService.countRelGoodsNum();
        List<GoodsTagCustom> updateList = new ArrayList<>();
        for(GoodsTagCustom tagCustom : goodsTagCustoms){
            if(Objects.isNull(goodsRelNumMap)){
                break;
            }
            Long relNum = goodsRelNumMap.getOrDefault(tagCustom.getId(), 0L);
            if(tagCustom.getRelGoodsNum() != relNum){
                tagCustom.setRelGoodsNum(relNum);
                updateList.add(tagCustom);
            }
        }
        if(CollectionUtils.isNotEmpty(updateList)){
            goodsTagCustomService.updateBatchById(updateList);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<CustomTagInfoDTO> listCustomTagInfo() {
        return BeanCopyUtil.transformList(goodsTagCustomService.lambdaQuery()
                .eq(GoodsTagCustom::getType, 2)
                .eq(GoodsTagCustom::getIsShow, 1)
                .list(), CustomTagInfoDTO.class);
    }

    @Override
    public Map<Long, List<CustomTagVO>> queryGoodsCustomTag(CustomTagCondition customTagCondition) {
        if (CollectionUtils.isEmpty(customTagCondition.getGoodsIds())) {
            return Collections.emptyMap();
        }
        List<GoodsTagCustom> goodsTagCustoms = goodsTagCustomService.lambdaQuery()
                .eq(GoodsTagCustom::getType, 2)
                .eq(GoodsTagCustom::getIsCombo, 1)
                .eq(GoodsTagCustom::getIsShow, 1)
                .list();

        if (CollectionUtils.isEmpty(goodsTagCustoms)) {
            return Collections.emptyMap();
        }

        Map<Long, GoodsTagCustom> tagMap = goodsTagCustoms.stream()
                .collect(Collectors.toMap(GoodsTagCustom::getId, Function.identity(), (k1, k2) -> k2));

        List<GoodsTagRel> tagRelList = goodsTagRelService.lambdaQuery()
                .in(GoodsTagRel::getGoodsId, customTagCondition.getGoodsIds())
                .in(GoodsTagRel::getTagId, tagMap.keySet())
                .list();

        if (CollectionUtils.isEmpty(tagRelList)) {
            return Collections.emptyMap();
        }

        Map<Long, List<CustomTagVO>> result = new HashMap<>();
        for (GoodsTagRel tagRel : tagRelList) {
            Long goodsId = tagRel.getGoodsId();
            Long tagId = tagRel.getTagId();

            GoodsTagCustom goodsTagCustom = tagMap.get(tagId);
            if (goodsTagCustom != null) {
                CustomTagVO customTagVO = new CustomTagVO();
                TransferUtils.transferBean(goodsTagCustom, customTagVO);
                if(StringUtils.isNotBlank(goodsTagCustom.getSexLabel())){
                    List<Integer> sexLabelList = Arrays.stream(goodsTagCustom.getSexLabel().split(",")).map(Integer::valueOf).collect(Collectors.toList());
                    customTagVO.setSexLabelList(sexLabelList);
                }
                result.computeIfAbsent(goodsId, k -> new ArrayList<>()).add(customTagVO);
            }
        }
        return result;
    }

    @Override
    public void noticeDingDing() {
        try {
            Map<Long, Long> goodsRelNumMap = goodsTagRelService.countRelGoodsNum();
            List<Long> idList = Arrays.asList(100108L, 100116L, 100114L, 100107L);

            // 部分自定义商品标签-自建站-商品数低于5k
            idList = idList.stream().filter(id -> goodsRelNumMap.getOrDefault(id, 0L) < 5000).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(idList)) {
                StringBuilder sb = new StringBuilder();
                sb.append("自定义商品标签-自建站-商品数低于5k").append("\n").append("\n");
                Collection<GoodsTagCustom> goodsTagCustoms = goodsTagCustomService.listByIds(idList);
                for (GoodsTagCustom goodsTagCustom : goodsTagCustoms) {
                    sb.append("标签ID ： ").append(goodsTagCustom.getId()).append("\n")
                            .append("标签名称 ： ").append(goodsTagCustom.getTagName()).append("\n")
                            .append("标签商品数 ： ").append(goodsRelNumMap.getOrDefault(goodsTagCustom.getId(), 0L)).append("\n")
                            .append("最近操作人 ： ").append(goodsTagCustom.getUpdateUser()).append("\n").append("\n");
                }
                dingDingCoreService.callDingDingSync(DingDingCoreServiceImpl.CUSTOM_LIST, sb.toString());
            }
        } catch (Exception ignored) {}
    }

    @Override
    public List<CustomTagInfoDTO> queryCustomTagByGoodsId(Long goodsId) {
        if (goodsId == null) {
            return Lists.newArrayList();
        }
        List<GoodsTagRel> tagRelList = goodsTagRelService.lambdaQuery()
                .eq(GoodsTagRel::getGoodsId, goodsId)
                .list();

        if (CollectionUtils.isEmpty(tagRelList)) {
            return Lists.newArrayList();
        }
        List<Long> tagIds = tagRelList.stream().map(GoodsTagRel::getTagId).distinct().collect(Collectors.toList());
        List<GoodsTagCustom> goodsTagCustoms = goodsTagCustomService.lambdaQuery()
                .eq(GoodsTagCustom::getType, 2)
                .eq(GoodsTagCustom::getIsShow, 1)
                .in(GoodsTagCustom::getId, tagIds)
                .list();

        if (CollectionUtils.isEmpty(goodsTagCustoms)) {
            return Lists.newArrayList();
        }

        return BeanCopyUtil.transformList(goodsTagCustoms, CustomTagInfoDTO.class);
    }

    public void batchInsertOrUpdateData(String index, List<Pair<String, String>> jsonList, String bizName) throws Exception {
        if (CollectionUtils.isEmpty(jsonList)) {
            log.info("batchInsertData jsonList is empty, index = {}", index);
            return;
        }
        BulkRequest request = new BulkRequest();
        jsonList.forEach(item -> request.add(new UpdateRequest(index, item.getLeft())
                .doc(item.getRight(), XContentType.JSON)
                .docAsUpsert(true))
        );
        List<String> failList = syncInsert(request, bizName);
        log.error("batchInsertOrUpdateData failList = {}", failList);
    }

    private List<String> syncInsert(BulkRequest request, String bizName) throws Exception {
        BulkResponse responses = restHighLevelClient.bulk(request, RequestOptions.DEFAULT);
        if (responses == null) {
            log.error("[EsBaseService#syncInsert], responses为空, bizName = {}", bizName);
            return Lists.newArrayList();
        }
        log.info("[EsBaseService#syncInsert] {} 批量同步数据插入耗时: {}", bizName, responses.getTook().getMillis());
        //如果同步插入到ES失败，则获取失败的数据
        if (responses.hasFailures()) {
            List<String> failLists = Arrays.stream(responses.getItems()).map(BulkItemResponse::getId).collect(Collectors.toList());
            return failLists;
        }
        return Lists.newArrayList();
    }

    private void sendMq(List<Long> goodsIds, boolean delay, String business) {
        goodsIds.stream().distinct().forEach(goodsId -> {
            GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
            goodsSyncModel.setGoodsId(goodsId);
            goodsSyncModel.setSyncTime(System.currentTimeMillis());
            goodsSyncModel.setBusiness(Optional.ofNullable(business).orElse("同步商品自定义标签"));
            goodsSyncModel.setSourceService("vp");
            if (delay) {
                mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE_CUSTOM_TAG", JSON.toJSONString(goodsSyncModel), MqDelayLevel.THIRTY_SEC);
            } else {
                mqSender.send("SYNC_GOODS_TOPIC_UPDATE_CUSTOM_TAG", JSON.toJSONString(goodsSyncModel));
            }
        });
    }

    private void refreshCache() {
        redisApi.del("DISTRIBUTE_GOODS_CUSTOM_TAG_INFO_V2");
    }
}
