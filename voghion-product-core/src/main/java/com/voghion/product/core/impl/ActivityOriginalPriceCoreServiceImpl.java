package com.voghion.product.core.impl;

import com.alibaba.fastjson.JSON;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.lang.exception.CustomException;
import com.google.common.collect.Lists;
import com.onlest.GoodsSyncModel;
import com.voghion.marketing.api.output.activity.ActivityDto;
import com.voghion.product.VoghionProductResultCode;
import com.voghion.product.api.dto.ActivityOriginalPriceDto;
import com.voghion.product.api.dto.ActivityTagGoodsDTO;
import com.voghion.product.api.dto.BindTagDTO;
import com.voghion.product.api.dto.GoodsOperationLogDto;
import com.voghion.product.api.enums.GoodsEditTypeEnums;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.client.ActivityRemoteClientFactory;
import com.voghion.product.core.*;
import com.voghion.product.model.dto.GoodsEditPriceDto;
import com.voghion.product.model.enums.GoodsLockLabelTypEnums;
import com.voghion.product.model.po.*;
import com.voghion.product.model.po.goods.GoodsSkuPo;
import com.voghion.product.model.vo.GoodsVirtualDiscountVO;
import com.voghion.product.model.vo.condition.UpdateLockLabelCondition;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.util.BeanCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品表原始价格 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Service
@Slf4j
public class ActivityOriginalPriceCoreServiceImpl implements ActivityOriginalPriceCoreService {


    @Resource
    private GoodsService goodsService;

    @Resource
    private GoodsItemService goodsItemService;

    @Resource
    private ActivityOriginalPriceService activityOriginalPriceService;

    @Resource
    private MqSender mqSender;

    @Resource
    private VirtualDiscountCoreService virtualDiscountCoreService;

    @Resource
    private ActivityRemoteClientFactory activityRemoteClientFactory;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private RedisApi redisApi;

    @Resource
    private GoodsLockCoreService goodsLockCoreService;

    @Resource
    private GoodsExtConfigService goodsExtConfigService;

    @Resource
    private GoodsTagCoreService goodsTagCoreService;

    @Resource
    private GoodsEditInfoService goodsEditInfoService;

    @Resource
    private GoodsEditInfoDetailService goodsEditInfoDetailService;

    @Resource
    private GoodsLockInfoService goodsLockInfoService;

    @Resource
    private ListingFollowGoodsService listingFollowGoodsService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private GoodsSkuService goodsSkuService;

    /**
     * hash初始化大小
     */
    private static final int DEFAULT_INITIAL_CAPACITY = 1 << 4;

    /**
     * 批量大小
     */
    private static final int DEFAULT_BATCH_SIZE = 1000;

    /**
     * 告警mq主题
     */
    public static final String WARING_ERROR_TOPIC = "waring_error_topic";
    /**
     * 改价topic
     */
    public static final String ACTIVITY_PRICE_BACK_UP = "activity_price_back_up";
    /**
     * 恢复价格topic
     */
    public static final String ACTIVITY_PRICE_RESTORE = "activity_price_restore";

    /**
     * redis
     */
    private static final String REDIS_PREFIX = "ACTIVITY_ORIGINAL_PRICE";


    @Override
    public void backupConsumer(ActivityTagGoodsDTO activityTagGoodsDTO) {
        Long goodsId = activityTagGoodsDTO.getGoodsId();
        Long activityId = activityTagGoodsDTO.getActivityId();
        String redisKey = REDIS_PREFIX + "_" + activityId + "_" + goodsId;
        if (redisApi.hasKey(redisKey)) {
            log.info("活动id:{},商品id:{} 已经消费过", activityId, goodsId);
            return;
        }
        log.info("活动id:{},商品id:{} 开始消费进行活动改价", activityId, goodsId);

        if (activityTagGoodsDTO.getActivityType() != null && activityTagGoodsDTO.getActivityType() == 3) {
            bindActivityTag(activityTagGoodsDTO);
            sendMq(goodsId);
            return;
        }
        //判断是否已经改过价
        List<ActivityOriginalPrice> activityOriginalPrices = activityOriginalPriceService.queryPriceByActivityIdAndGoodsId(activityId, goodsId);
        if (CollectionUtils.isNotEmpty(activityOriginalPrices)) {
            log.info("活动id:{} 活动商品id:{} 已改价", activityId, goodsId);
            return;
        }

        if (chekCurrentNormanActivity(goodsId, activityId)) return;

        Goods goods = goodsService.queryById(goodsId);
        if (goods == null || goods.getType() == 3) {
            if (goods != null) {
                bindActivityTag(activityTagGoodsDTO);
                setGoodsVirtualDiscount(activityTagGoodsDTO, goods);
                sendMq(goodsId);
            }
            log.warn("活动id:{} 活动商品id:{} 对应不到不到对应商品或是批发商品", activityId, goodsId);
            return;
        }

        List<GoodsItem> goodsItems = goodsItemService.queryGoodsItemByGoodsId(goodsId);
        if (CollectionUtils.isEmpty(goodsItems)) {
            log.warn("活动id:{} 活动商品id:{} 查不到商品明细", activityId, goodsId);
            return;
        }
        List<GoodsEditPriceDto.SkuChangeDto> skuChangeList = Lists.newArrayList();
        List<ActivityOriginalPrice> activityOriginalPriceList = new ArrayList<>();
        //以商品id为key [0]min_price [1]max_price
        Map<Long, BigDecimal[]> maximinPrice = new HashMap<>(DEFAULT_INITIAL_CAPACITY);
        Map<Long, BigDecimal[]> maximinGrouponPrice = new HashMap<>(DEFAULT_INITIAL_CAPACITY);
        Set<Integer> changePrice = new HashSet<>();
        List<GoodsItem> newGoodsItemList = goodsItems.stream().map(goodsItem -> {
            ActivityOriginalPrice activityOriginalPrice = new ActivityOriginalPrice();
            activityOriginalPrice.setGoodsId(goodsId);
            activityOriginalPrice.setGoodsItemId(goodsItem.getId());
            activityOriginalPrice.setActivityId(activityId);
            activityOriginalPrice.setCreateTime(LocalDateTime.now());

            BigDecimal oldPrice = goodsItem.getPrice();
            BigDecimal oldOriginalPrice = goodsItem.getOrginalPrice();

            BigDecimal oldGrouponPrice = goodsItem.getGrouponPrice() == null ? oldPrice : goodsItem.getGrouponPrice();
            BigDecimal oldOriginalGrouponPrice = goodsItem.getOriginalGrouponPrice() == null ? oldOriginalPrice : goodsItem.getOriginalGrouponPrice();

            BigDecimal oldDefaultDelivery = goodsItem.getDefaultDelivery();
            //备份原价
            activityOriginalPrice.setOldPrice(oldPrice);
            //备份原原始价格
            activityOriginalPrice.setOldOrginalPrice(oldOriginalPrice);

            //备份原拼团价
            activityOriginalPrice.setOldGrouponPrice(oldGrouponPrice);
            //备份原原始拼团价格
            activityOriginalPrice.setOldOrginalGrouponPrice(oldOriginalGrouponPrice);

            //备份原默认运费
            activityOriginalPrice.setOldDefaultDelivery(oldDefaultDelivery);
            BigDecimal discount = activityTagGoodsDTO.getDiscount();
            BigDecimal discountOriginalPrice = oldOriginalPrice.multiply(discount);
            BigDecimal newOriginalPrice = setScalePrice(discountOriginalPrice);
            //备份现原始价格
            activityOriginalPrice.setNewOrginalPrice(newOriginalPrice);

            BigDecimal discountOriginalGrouponPrice = oldOriginalGrouponPrice.multiply(discount);
            BigDecimal newOriginalGrouponPrice = setScalePrice(discountOriginalGrouponPrice);
            activityOriginalPrice.setNewOrginalGrouponPrice(newOriginalGrouponPrice);

            BigDecimal discountDefaultDelivery = oldDefaultDelivery.multiply(discount);
            BigDecimal newDefaultDelivery = setScalePrice(discountDefaultDelivery);
            //备份现默认运费
            activityOriginalPrice.setNewDefaultDelivery(newDefaultDelivery);

            BigDecimal newPrice = newOriginalPrice.add(newDefaultDelivery);
            activityOriginalPrice.setNewPrice(newPrice);

            BigDecimal newGrouponPrice = newOriginalGrouponPrice.add(newDefaultDelivery);
            activityOriginalPrice.setNewGrouponPrice(newGrouponPrice);

            if(activityOriginalPrice.getOldOrginalPrice().compareTo(activityOriginalPrice.getNewOrginalPrice()) > 0){
                changePrice.add(0);
            }else if(activityOriginalPrice.getOldOrginalPrice().compareTo(activityOriginalPrice.getNewOrginalPrice()) < 0){
                changePrice.add(1);
            }
            GoodsEditPriceDto.SkuChangeDto skuChangeDto = new GoodsEditPriceDto.SkuChangeDto(goodsItem.getId(), activityOriginalPrice.getOldOrginalPrice(), activityOriginalPrice.getNewOrginalPrice(), goodsItem.getStock(), goodsItem.getStock());
            skuChangeList.add(skuChangeDto);

            activityOriginalPriceList.add(activityOriginalPrice);
            //获取最大最小价格
            getMaxMinPrice(maximinPrice, goodsId, newPrice);
            //获取最大最小团购价格
            getMaxMinPrice(maximinGrouponPrice, goodsId, newGrouponPrice);
            return getGoodsItem(newOriginalPrice, newDefaultDelivery, newPrice, goodsItem.getId(), goodsItem.getSkuId(), newOriginalGrouponPrice, newGrouponPrice);
        }).collect(Collectors.toList());

        Map<Long, GoodsItem> goodsItemMap = newGoodsItemList.stream().collect(Collectors.toMap(GoodsItem::getSkuId, Function.identity()));
        List<GoodsSkuPo> goodsSkuPoList = goodsSkuService.findListByGoodsSkuIds(Lists.newArrayList(goodsItemMap.keySet()), goodsId);
        if (CollectionUtils.isNotEmpty(goodsSkuPoList)) {
            for (GoodsSkuPo skuPo : goodsSkuPoList) {
                GoodsItem goodsItem = goodsItemMap.get(skuPo.getId());
                skuPo.setPrice(goodsItem.getPrice());
                skuPo.setOriginalPrice(goodsItem.getOrginalPrice());
                skuPo.setDefaultDelivery(goodsItem.getDefaultDelivery());
            }
        }

        Goods newGoods = getGoods(maximinPrice, goodsId, maximinGrouponPrice);
        //改价完锁定商品
        newGoods.setIsLock(1);

        transactionTemplate.execute(transactionStatus -> {
            try {
                //修改商品价格
                goodsItemService.updateBatchById(newGoodsItemList, DEFAULT_BATCH_SIZE);
                if (CollectionUtils.isNotEmpty(goodsSkuPoList)) {
                    goodsSkuService.updateBatchByIdAndGoodsId(goodsSkuPoList);
                }

                //修改最大最小值
                goodsService.updateById(newGoods);
                //备份原始价格 活动id 商品明细表id 创建时间
                activityOriginalPriceService.insertBatch(activityOriginalPriceList);
                bindActivityTag(activityTagGoodsDTO);
            } catch (Exception e) {
                log.error("活动id:" + activityId + " 商品id:" + goodsId + " 改价失败", e);
                mqSender.send(WARING_ERROR_TOPIC, "活动id:" + activityId + " 商品id:" + goodsId + " 改价失败");
                throw new CustomException(VoghionProductResultCode.CHANGE_PRICE_ERROR);
            }
            return !redisApi.hasKey(redisKey);
        });

        UpdateLockLabelCondition condition = new UpdateLockLabelCondition(Collections.singletonList(goodsId),
                Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()), "自动加标签(flashDeal价格变更及备份)", OperationLogTypeEnums.FLASH_DEAL_BACKUP.getCode());
        goodsLockCoreService.addLockByApi(condition);

        GoodsEditPriceDto goodsEditPriceDto = new GoodsEditPriceDto();
        goodsEditPriceDto.setSkuChangeDtoList(skuChangeList);

        GoodsEditInfo editInfo = new GoodsEditInfo();
        editInfo.setStatus(0);
        editInfo.setGoodsId(goods.getId());
        editInfo.setCategoryId(goods.getCategoryId());
        editInfo.setShopId(goods.getShopId());
        editInfo.setShopName(goods.getShopName());

        editInfo.setAuditUser("flashDeal");
        editInfo.setAuditTime(LocalDateTime.now());
        editInfo.setStatus(1);

        editInfo.setApplyUser("flashDeal");
        editInfo.setApplyTime(LocalDateTime.now());
        editInfo.setApplyReason("flashDeal活动开始，自动改价");
        editInfo.setIsDel(0);
        editInfo.setType(GoodsEditTypeEnums.PRICE.getCode());
        editInfo.setContent(JSON.toJSONString(goodsEditPriceDto));
        editInfo.setSpecialTag(StringUtils.join(initSpecialTags(goodsId), ","));
        editInfo.setUpdateTime(LocalDateTime.now());

        if(changePrice.size() > 1){
            editInfo.setAddPrice(2);
        }else{
            if(!changePrice.isEmpty()){
                editInfo.setAddPrice(changePrice.contains(0) ? 0 : 1);
            }
        }
        goodsEditInfoService.save(editInfo);

        GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
        goodsEditInfoDetail.setEditId(editInfo.getId());
        goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
        goodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditPriceDto));
        goodsEditInfoDetailService.save(goodsEditInfoDetail);

        //备份完价格放redis 防止重复
        redisApi.set(redisKey, redisKey, 60L * 60);

        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(goodsId)
                .type(OperationLogTypeEnums.FLASH_DEAL_BACKUP)
                .content(OperationLogTypeEnums.FLASH_DEAL_BACKUP.getDesc())
                .status(1)
                .user("system");
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);

        setGoodsVirtualDiscount(activityTagGoodsDTO, goods);
        //修改完价格发送mq
        sendMq(goodsId);
    }

    private boolean chekCurrentNormanActivity(Long goodsId, Long activityId) {
        try {
            List<ActivityDto> activityDtos = activityRemoteClientFactory.queryCurrentNormalActivity();
            if (CollectionUtils.isNotEmpty(activityDtos)) {
                List<Long> actIds = activityDtos.stream().map(ActivityDto::getId).collect(Collectors.toList());
                log.info("正在进行中的活动id:{}", actIds);
                List<ActivityOriginalPrice> existActivityGoods = activityOriginalPriceService.queryPriceByActivityIdsAndGoodsId(actIds, goodsId);
                if (CollectionUtils.isNotEmpty(existActivityGoods)) {
                    ActivityOriginalPrice activityOriginalPrice = existActivityGoods.get(0);
                    mqSender.send(WARING_ERROR_TOPIC, "活动id:" + activityId + " 商品id:" + goodsId + "和另外一个正在生效的活动id:" + activityOriginalPrice.getActivityId() + " 冲突改价失败");
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("校验活动和正在生效中的活动失败:{}", e.getMessage(), e);
        }
        return false;
    }

    private void bindActivityTag(ActivityTagGoodsDTO activityTagGoodsDTO) {
        Long tagId = activityTagGoodsDTO.getTagId();
        if (tagId != null && tagId > 0) {
            //商品打标签
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setGoodsIds(Collections.singletonList(activityTagGoodsDTO.getGoodsId()));
            bindTagDTO.setTagId(tagId);
            goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);

            GoodsTag goodsTag = getGoodsTag(activityTagGoodsDTO);
            //回填标签插入冗余字段 开始结束时间和活动ID
            goodsTagCoreService.updateGoodsTagActivityInfo(goodsTag);
        }
    }

    private void setGoodsVirtualDiscount(ActivityTagGoodsDTO activityTagGoodsDTO, Goods goods) {
        Long goodsId = activityTagGoodsDTO.getGoodsId();
        if (activityTagGoodsDTO.getShowDiscountMin() == null
                || activityTagGoodsDTO.getShowDiscountMin().compareTo(BigDecimal.ZERO) < 1
                || activityTagGoodsDTO.getShowDiscountMin().compareTo(BigDecimal.ONE) > 0) {
            return;
        }
        try {
            GoodsVirtualDiscountVO goodsVirtualDiscountVO = new GoodsVirtualDiscountVO();
            // 获取最低和最高折扣的范围
            BigDecimal range = activityTagGoodsDTO.getShowDiscountMax().subtract(activityTagGoodsDTO.getShowDiscountMin());
            double randomValue = RandomUtils.nextDouble(0.0, 1.0);
            BigDecimal randomDiscount = BigDecimal.valueOf(randomValue).multiply(range).setScale(2, RoundingMode.HALF_UP);
            BigDecimal discount = activityTagGoodsDTO.getShowDiscountMin().add(randomDiscount);
            goodsVirtualDiscountVO.setDiscount(discount);
            if (goods.getMinMarketPrice() != null) {
                BigDecimal minPrice = goods.getMinPrice();
                BigDecimal minMarketPrice = goods.getMinMarketPrice();
                BigDecimal oldDiscount = minPrice.divide(minMarketPrice, 2, RoundingMode.HALF_UP);
                if (oldDiscount.compareTo(discount) < 0) {
                    return;
                }
            }
            if (activityTagGoodsDTO.getEffectStartTime() == null || activityTagGoodsDTO.getEffectStartTime().compareTo(LocalDateTime.now()) < 0) {
                activityTagGoodsDTO.setEffectStartTime(LocalDateTime.now());
            }
            goodsVirtualDiscountVO.setEffectStartTime(activityTagGoodsDTO.getEffectStartTime().toLocalDate());
            long millisecondsDifference = ChronoUnit.MILLIS.between(activityTagGoodsDTO.getEffectStartTime(), activityTagGoodsDTO.getEffectEndTime());
            long millisInDay = 24L * 60 * 60 * 1000;
            goodsVirtualDiscountVO.setDays((int) Math.round((double) millisecondsDifference / millisInDay));
            virtualDiscountCoreService.addVirtualDiscount(goodsVirtualDiscountVO, Lists.newArrayList(goodsId));
        } catch (Exception e) {
            log.error("活动id:" + activityTagGoodsDTO.getActivityId() + " 商品id:" + goodsId + " 修改商品划线价", e);
        }
    }

    private List<String> initSpecialTags(Long goodsId) {
        List<String> specialTagList = Lists.newArrayList();
        GoodsLockInfo goodsLockInfo = goodsLockInfoService.lambdaQuery()
                .eq(GoodsLockInfo::getGoodsId, goodsId)
                .eq(GoodsLockInfo::getIsDel, 0)
                .one();
        if (goodsLockInfo != null) {
            specialTagList = GoodsLockLabelTypEnums.listNamesByCodes(goodsLockInfo.getLabel());
        }

        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getGoodsId, goodsId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .one();
        if (listingFollowGoods != null) {
            specialTagList.add("listing");
        }
        Collections.sort(specialTagList);
        return specialTagList;
    }

    /**
     * 插入商品拓展配置表
     *
     * @param goodsId 商品表
     * @param tagId   标签表
     * @return
     */
    private static GoodsExtConfig getGoodsExtConfig(Long goodsId, Long tagId) {
        GoodsExtConfig goodsExtConfig = new GoodsExtConfig();
        goodsExtConfig.setGoodsId(goodsId);
        goodsExtConfig.setTagId(tagId);
        LocalDateTime now = LocalDateTime.now();
        goodsExtConfig.setCreateTime(now);
        goodsExtConfig.setUpdateTime(now);
        return goodsExtConfig;
    }

    /**
     * 回填标签信息
     *
     * @param activityTagGoodsDTO
     * @return
     */
    private static GoodsTag getGoodsTag(ActivityTagGoodsDTO activityTagGoodsDTO) {
        GoodsTag goodsTag = new GoodsTag();
        goodsTag.setId(activityTagGoodsDTO.getTagId());
        goodsTag.setActivityId(activityTagGoodsDTO.getActivityId());
        goodsTag.setEffectStartTime(activityTagGoodsDTO.getEffectStartTime());
        goodsTag.setEffectEndTime(activityTagGoodsDTO.getEffectEndTime());
        goodsTag.setUpdateTime(LocalDateTime.now());
        goodsTag.setUpdateUser("yangbing");
        return goodsTag;
    }

    /**
     * 设置商品明细信息
     *
     * @param originalPrice   原始价格
     * @param defaultDelivery 运费
     * @param price           价格
     * @param goodsItemId     商品明细id
     * @param skuId
     * @return goodsItem
     */
    private GoodsItem getGoodsItem(BigDecimal originalPrice, BigDecimal defaultDelivery, BigDecimal price, Long goodsItemId, Long skuId, BigDecimal originalGrouponPrice, BigDecimal grouponPrice) {
        GoodsItem goodsItem = new GoodsItem();
        goodsItem.setId(goodsItemId);
        goodsItem.setSkuId(skuId);
        goodsItem.setOrginalPrice(originalPrice);
        goodsItem.setDefaultDelivery(defaultDelivery);
        goodsItem.setPrice(price);
        goodsItem.setOriginalGrouponPrice(originalGrouponPrice);
        goodsItem.setGrouponPrice(grouponPrice);
        goodsItem.setUpdateTime(new Date());
        return goodsItem;
    }


    /**
     * 恢复价格
     *
     * @param activityTagGoodsDTO 活动商品
     */
    @Override
    public void restoreConsumer(ActivityTagGoodsDTO activityTagGoodsDTO) {
        Long activityId = activityTagGoodsDTO.getActivityId();
        Long goodsId = activityTagGoodsDTO.getGoodsId();

        if (activityTagGoodsDTO.getActivityType() != null && activityTagGoodsDTO.getActivityType() == 3) {
            removeActivityTag(activityTagGoodsDTO);
            sendMq(goodsId);
            return;
        }

        log.info("活动id:{},商品id:{} 开始消费进行价格恢复", activityId, goodsId);
        List<ActivityOriginalPrice> activityOriginalPrices = activityOriginalPriceService.queryPriceByActivityIdAndGoodsId(activityId, goodsId);
        if (CollectionUtils.isEmpty(activityOriginalPrices)) {
            log.warn("活动id:{} 活动商品id:{} 查无备份信息", activityId, goodsId);
            return;
        }
        List<GoodsEditPriceDto.SkuChangeDto> skuChangeList = Lists.newArrayList();
        //商品明细id和价格的映射
        Map<Long, ActivityOriginalPrice> goodsItemIdAndPriceMap = activityOriginalPrices.stream().collect(Collectors.toMap(ActivityOriginalPrice::getGoodsItemId, Function.identity(), (oldValue, newValue) -> oldValue));
        Set<Long> goodsItemIds = goodsItemIdAndPriceMap.keySet();
        Collection<GoodsItem> goodsItems = goodsItemService.selectByIds(new ArrayList<>(goodsItemIds));
        if (CollectionUtils.isEmpty(goodsItems)) {
            log.error("活动id:{} 根据商品明细id:{} 查无商品明细信息", activityId, JSON.toJSONString(goodsItemIds));
            mqSender.send(WARING_ERROR_TOPIC, "活动id：" + activityId + "价格恢复:根据商品明细id" + JSON.toJSONString(goodsItemIds) + "查无商品明细信息");
            goodsItems = Lists.newArrayList();
        }
        //以商品id为key [0]min_price [1]max_price
        Map<Long, BigDecimal[]> maximinPrice = new HashMap<>(DEFAULT_INITIAL_CAPACITY);
        Map<Long, BigDecimal[]> maximinGrouponPrice = new HashMap<>(DEFAULT_INITIAL_CAPACITY);
        Set<Integer> changePrice = new HashSet<>();
        List<GoodsItem> originGoodsItemsPrice = goodsItems.stream()
                .filter(goodsItem -> goodsItemIdAndPriceMap.get(goodsItem.getId()) != null)
                .map(goodsItem -> {
                    ActivityOriginalPrice activityOriginalPrice = goodsItemIdAndPriceMap.get(goodsItem.getId());

                    if(activityOriginalPrice.getOldOrginalPrice().compareTo(goodsItem.getOrginalPrice()) > 0){
                        changePrice.add(1);
                    }else if(activityOriginalPrice.getOldOrginalPrice().compareTo(goodsItem.getOrginalPrice()) < 0){
                        changePrice.add(0);
                    }

                    GoodsEditPriceDto.SkuChangeDto skuChangeDto = new GoodsEditPriceDto.SkuChangeDto(goodsItem.getId(), goodsItem.getOrginalPrice(), activityOriginalPrice.getOldOrginalPrice(), goodsItem.getStock(), goodsItem.getStock());
                    skuChangeList.add(skuChangeDto);
                    //活动结束设置原来价格
                    BigDecimal oldOriginalPrice = activityOriginalPrice.getOldOrginalPrice();
                    goodsItem.setOrginalPrice(oldOriginalPrice);

                    BigDecimal oldOriginalGrouponPrice = activityOriginalPrice.getOldOrginalGrouponPrice() == null ? oldOriginalPrice : activityOriginalPrice.getOldOrginalGrouponPrice();
                    goodsItem.setOriginalGrouponPrice(oldOriginalGrouponPrice);

                    BigDecimal oldDefaultDelivery = activityOriginalPrice.getOldDefaultDelivery();
                    goodsItem.setDefaultDelivery(oldDefaultDelivery);

                    BigDecimal oldPrice = oldOriginalPrice.add(oldDefaultDelivery);
                    goodsItem.setPrice(oldPrice);

                    BigDecimal oldGrouponPrice = oldOriginalGrouponPrice.add(oldDefaultDelivery);
                    goodsItem.setGrouponPrice(oldGrouponPrice);

                    goodsItem.setUpdateTime(new Date());
                    getMaxMinPrice(maximinPrice, goodsId, oldPrice);
                    getMaxMinPrice(maximinGrouponPrice, goodsId, oldGrouponPrice);

                    return getGoodsItem(oldOriginalPrice, oldDefaultDelivery, oldPrice, goodsItem.getId(), goodsItem.getSkuId(), oldOriginalGrouponPrice, oldGrouponPrice);
                }).collect(Collectors.toList());

        Goods goods = goodsService.selectById(goodsId);
        if (goods == null || goods.getType() == 3) {
            if (goods != null) {
                removeActivityTag(activityTagGoodsDTO);
                cancelGoodsVirtualDiscount(activityTagGoodsDTO);
                sendMq(goodsId);
            }
            log.warn("活动id:{} 根据商品id:{},查无商品信息或是批发商品", activityId, goodsId);
            return;
        }

        Map<Long, GoodsItem> goodsItemMap = originGoodsItemsPrice.stream().collect(Collectors.toMap(GoodsItem::getSkuId, Function.identity()));
        List<GoodsSkuPo> goodsSkuPoList = goodsSkuService.findListByGoodsSkuIds(Lists.newArrayList(goodsItemMap.keySet()), goodsId);
        if (CollectionUtils.isNotEmpty(goodsSkuPoList)) {
            for (GoodsSkuPo skuPo : goodsSkuPoList) {
                GoodsItem goodsItem = goodsItemMap.get(skuPo.getId());
                skuPo.setPrice(goodsItem.getPrice());
                skuPo.setOriginalPrice(goodsItem.getOrginalPrice());
                skuPo.setDefaultDelivery(goodsItem.getDefaultDelivery());
            }
        }

        Goods oldGoods = getGoods(maximinPrice, goodsId, maximinGrouponPrice);
        //恢复完解锁商品
        oldGoods.setIsLock(0);
        List<Long> activityOriginalGoodsIds = activityOriginalPrices.stream().map(ActivityOriginalPrice::getId).collect(Collectors.toList());
        transactionTemplate.execute(transactionStatus -> {
            try {
                if (CollectionUtils.isNotEmpty(originGoodsItemsPrice)) {
                    //修改商品价格
                    goodsItemService.updateBatchById(originGoodsItemsPrice, DEFAULT_BATCH_SIZE);
                }

                if (CollectionUtils.isNotEmpty(goodsSkuPoList)) {
                    goodsSkuService.updateBatchByIdAndGoodsId(goodsSkuPoList);
                }

                //修改最大最小值
                goodsService.updateById(oldGoods);
                //逻辑删除
                activityOriginalPriceService.deleteByIds(activityOriginalGoodsIds);
                removeActivityTag(activityTagGoodsDTO);

            } catch (Exception e) {
                log.error("活动id:" + activityId + " 商品id:" + goodsId + " 恢复价格失败", e);
                mqSender.send(WARING_ERROR_TOPIC, "活动id:" + activityId + " 商品id:" + goodsId + " 恢复价格失败");
                throw new CustomException(VoghionProductResultCode.RESTORE_PRICE_ERROR);
            }
            return Boolean.TRUE;
        });

        UpdateLockLabelCondition condition = new UpdateLockLabelCondition(Collections.singletonList(goodsId), Collections.singletonList(GoodsLockLabelTypEnums.FLASH_DEAL.getCode()),"自动解标签(flashDeal恢复价格)", OperationLogTypeEnums.UNLOCK_AUTO.getCode());
        goodsLockCoreService.removeLockByApi(condition);

        GoodsEditPriceDto goodsEditPriceDto = new GoodsEditPriceDto();
        goodsEditPriceDto.setSkuChangeDtoList(skuChangeList);

        GoodsEditInfo editInfo = new GoodsEditInfo();
        editInfo.setStatus(0);
        editInfo.setGoodsId(goods.getId());
        editInfo.setCategoryId(goods.getCategoryId());
        editInfo.setShopId(goods.getShopId());
        editInfo.setShopName(goods.getShopName());

        editInfo.setAuditUser("flashDeal");
        editInfo.setAuditTime(LocalDateTime.now());
        editInfo.setStatus(1);

        editInfo.setApplyUser("flashDeal");
        editInfo.setApplyTime(LocalDateTime.now());
        editInfo.setApplyReason("flashDeal活动结束，自动还原价格");
        editInfo.setIsDel(0);
        editInfo.setType(GoodsEditTypeEnums.PRICE.getCode());
        editInfo.setContent(JSON.toJSONString(goodsEditPriceDto));
        editInfo.setSpecialTag(StringUtils.join(initSpecialTags(goodsId), ","));
        editInfo.setUpdateTime(LocalDateTime.now());
        goodsEditInfoService.save(editInfo);

        GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
        goodsEditInfoDetail.setEditId(editInfo.getId());
        goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
        goodsEditInfoDetail.setContent(JSON.toJSONString(goodsEditPriceDto));
        goodsEditInfoDetailService.save(goodsEditInfoDetail);

        //恢复完价格删掉redisKey
        redisApi.del(REDIS_PREFIX + "_" + activityId + "_" + goodsId);

        cancelGoodsVirtualDiscount(activityTagGoodsDTO);

        //修改完价格发送mq
        sendMq(goodsId);

        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(goodsId)
                .type(OperationLogTypeEnums.FLASH_DEAL_RESTORE)
                .content(OperationLogTypeEnums.FLASH_DEAL_RESTORE.getDesc())
                .status(1)
                .user("system");
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
    }

    private void removeActivityTag(ActivityTagGoodsDTO activityTagGoodsDTO) {
        Long tagId = activityTagGoodsDTO.getTagId();
        if (tagId != null) {
            //删除标签
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setGoodsIds(Collections.singletonList(activityTagGoodsDTO.getGoodsId()));
            bindTagDTO.setTagId(tagId);
            goodsExtConfigCoreService.removeGoodsTag(bindTagDTO);
        }
    }

    private void cancelGoodsVirtualDiscount(ActivityTagGoodsDTO activityTagGoodsDTO) {
        Long goodsId = activityTagGoodsDTO.getGoodsId();
        try {
            GoodsVirtualDiscountVO goodsVirtualDiscountVO = new GoodsVirtualDiscountVO();
            goodsVirtualDiscountVO.setGoodsId("" + goodsId);
            virtualDiscountCoreService.cancelVirtualDiscount(goodsVirtualDiscountVO);
        } catch (Exception e) {
            log.error("活动id:" + activityTagGoodsDTO.getActivityId() + " 商品id:" + goodsId + " 取消商品划线价", e);
        }
    }

    /**
     * 要修改价格的商品
     *
     * @param maximinPrice 最大最小价格
     * @param goodsId      商品id
     * @return goods
     */
    private Goods getGoods(Map<Long, BigDecimal[]> maximinPrice, Long goodsId, Map<Long, BigDecimal[]> maximinGrouponPrice) {
        Goods goods = new Goods();
        if (!maximinPrice.isEmpty()) {
            goods.setMinPrice(maximinPrice.get(goodsId)[0]);
            goods.setMaxPrice(maximinPrice.get(goodsId)[1]);
        }
        if (!maximinGrouponPrice.isEmpty()) {
            goods.setMinGrouponPrice(maximinGrouponPrice.get(goodsId)[0]);
            goods.setMaxGrouponPrice(maximinGrouponPrice.get(goodsId)[1]);
        }
        goods.setUpdateTime(LocalDateTime.now());
        goods.setId(goodsId);
        return goods;
    }

    /**
     * 获取商品最大最小价格映射
     *
     * @param maximinPrice 商品最大最小价格映射  [0]min_price [1]max_price
     * @param goodsId      商品id
     * @param price        价格
     */
    private void getMaxMinPrice(Map<Long, BigDecimal[]> maximinPrice, Long goodsId, BigDecimal price) {
        BigDecimal[] bigDecimals;
        if (maximinPrice.containsKey(goodsId)) {
            bigDecimals = maximinPrice.get(goodsId);
            bigDecimals[0] = price.compareTo(bigDecimals[0]) < 0 ? price : bigDecimals[0];
            bigDecimals[1] = price.compareTo(bigDecimals[1]) > 0 ? price : bigDecimals[1];
        } else {
            bigDecimals = new BigDecimal[]{price, price};
        }
        maximinPrice.put(goodsId, bigDecimals);
    }

    /**
     * 保留两位小数 进1
     *
     * @param price 价格
     * @return
     */
    private BigDecimal setScalePrice(BigDecimal price) {
        return price.setScale(2, RoundingMode.UP);
    }

    /**
     * 修改完价格发送mq
     *
     * @param goodsId
     */
    private void sendMq(Long goodsId) {
        //修改完价格发送mq
        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(goodsId);
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("活动改价MQ");
        goodsSyncModel.setSourceService("vp");
        mqSender.send("SYNC_GOODS_TOPIC_UPDATE", goodsSyncModel);
        log.info("商品id{},发送mq同步es", goodsId);
    }


    @Override
    public List<ActivityOriginalPriceDto> queryStartingActivityGoodsInfo(Long activityId, List<Long> goodsIds) {
        List<ActivityOriginalPrice> list = activityOriginalPriceService.lambdaQuery()
                .eq(ActivityOriginalPrice::getActivityId, activityId)
                .in(ActivityOriginalPrice::getGoodsId, goodsIds)
                .eq(ActivityOriginalPrice::getIsDel, 0)
                .list();
        return BeanCopyUtil.transformList(list, ActivityOriginalPriceDto.class);
    }
}
