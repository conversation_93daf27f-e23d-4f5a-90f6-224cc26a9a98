package com.voghion.product.core.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.lang.exception.CustomException;
import com.colorlight.base.model.PageView;
import com.colorlight.base.utils.CheckUtils;
import com.colorlight.base.utils.MD5Support;
import com.colorlight.base.utils.TransferUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.voghion.es.service.GoodsEsService;
import com.voghion.product.VoghionProductResultCode;
import com.voghion.product.api.dto.*;
import com.voghion.product.api.enums.GoodsEditTypeEnums;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.api.service.GoodsEsRemoteService;
import com.voghion.product.core.CategoryTreeCoreService;
import com.voghion.product.core.GoodsFreightCoreService;
import com.voghion.product.core.GoodsPriceReductionCoreService;
import com.voghion.product.core.RecommendCoreService;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.enums.*;
import com.voghion.product.model.dto.GoodsDTO;
import com.voghion.product.model.dto.GoodsFreightDTO;
import com.voghion.product.model.dto.GoodsInfoDTO;
import com.voghion.product.model.enums.GoodsPriceReductionEnums;
import com.voghion.product.model.enums.ProductResultCode;
import com.voghion.product.model.po.*;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import com.voghion.product.switchcenter.MarketingSwitchCenter;
import com.voghion.product.util.LogUtils;
import com.voghion.product.utils.CommonConstants;
import com.voghion.product.utils.LocalDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeansException;
import org.opensearch.index.query.TermQueryBuilder;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.action.search.SearchScrollRequest;
import org.opensearch.action.search.SearchType;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.common.unit.TimeValue;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.index.query.TermsQueryBuilder;
import org.opensearch.search.Scroll;
import org.opensearch.search.SearchHit;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GoodsPriceReductionCoreServiceImpl extends AbstractCommonServiceImpl implements GoodsPriceReductionCoreService, ApplicationContextAware {
    @Autowired
    private GoodsPriceReductionService goodsPriceReductionService;

    @Autowired
    private SkuReductionPriceService skuReductionPriceService;

    @Resource
    private GoodsEsRemoteService goodsEsRemoteService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private GoodsItemService goodsItemService;

    @Resource
    private MqSender mqSender;

    @Resource
    private CategoryTreeCoreService categoryTreeCoreService;

    @Resource
    private GoodsFreightCoreService goodsFreightCoreService;

//    @Autowired
//    private GoodsReductionFreightService goodsReductionFreightService;

    @Resource
    private RedisApi redisApi;

    @Resource
    GoodsFreightService goodsFreightService;

    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private WarehouseStockBackupsService warehouseStockBackupsService;

    @Resource
    private GoodsEsService goodsEsService;

    @Resource
    private GoodsEditInfoService goodsEditInfoService;

    @Resource
    private GoodsEditInfoDetailService goodsEditInfoDetailService;

    @Resource
    private RecommendCoreService recommendCoreService;

    @Resource
    private GoodsLockInfoService goodsLockInfoService;

    @Resource
    private ListingFollowGoodsService listingFollowGoodsService;

    @Resource
    private MarketingSwitchCenter marketingSwitchCenter;

    private static String GOODS_PRICE_REDUCTION_KEY="GOODS_PRICE_REDUCTION_KEY_";

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Value("${reduction.job.interval:7}")
    private Integer reductionInterval;

    @Value("${reduction.job.discount:9}")
    private BigDecimal reductionDiscount;

    @Value("${stock.reduction.job.discount:7}")
    private BigDecimal stockUpReductionDiscount;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addGoodsPriceReduction(List<GoodsPriceReductionInputVO> list, String operator) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (list.size() > 500) {
            CheckUtils.notNull(null, ProductResultCode.PRICE_REDUCTION_GOODS_INVALID);
        }
        List<Long> goodsIdList = list.stream().map(GoodsPriceReductionInputVO::getGoodsId).filter(Objects::nonNull).distinct().sorted().collect(Collectors.toList());
        String reductionKey = GOODS_PRICE_REDUCTION_KEY + MD5Support.MD5(JSONObject.toJSONString(goodsIdList));
        if (redisApi.get(reductionKey) != null) {
            CheckUtils.notNull(null, ProductResultCode.PRICE_REDUCTION_GOODS_FREQUENCY);
        }
        //一分钟内不可重复操作同一批数据
        redisApi.set(reductionKey,"1",30);
        //查看生效中的商品id
        List<GoodsPriceReduction> goodsPriceReductions = goodsPriceReductionService.queryByGoodsIdList(goodsIdList, GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
        //生效中商品id
        List<Long> validGoodsIds = new ArrayList<>();
        Map<Long, Double> goodsDiscountMap = list.stream().collect(Collectors.toMap(GoodsPriceReductionInputVO::getGoodsId, GoodsPriceReductionInputVO::getDiscount, (v1, v2) -> v1));

        if (CollectionUtils.isNotEmpty(goodsPriceReductions)) {
            validGoodsIds.addAll(goodsPriceReductions.stream().map(GoodsPriceReduction::getGoodsId).collect(Collectors.toList()));
            refreshReduction(goodsPriceReductions,list,operator,goodsDiscountMap,validGoodsIds);
        }
        goodsIdList.removeAll(validGoodsIds);
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return;
        }
        GoodsDTO goodsDTO = new GoodsDTO();
        goodsDTO.setGoodsIds(goodsIdList);
        goodsDTO.setIsDel(0);
        goodsDTO.setIsShow("1");
        goodsDTO.setPageNow(1);
        goodsDTO.setPageSize(goodsIdList.size());
        PageView<GoodsInfoDTO> goodsIPage = goodsEsRemoteService.queryPageBackgroundByOption(goodsDTO);
        if (null == goodsIPage || CollectionUtils.isEmpty(goodsIPage.getRecords())) {
             CheckUtils.notNull(null,ProductResultCode.PRICE_REDUCTION_GOODS_INVALID);
        }
        List<GoodsInfoDTO> records = goodsIPage.getRecords();
        List<GoodsPriceReduction> insertList = initGoodsPriceReduction(records, operator, goodsDiscountMap);
        if (CollectionUtils.isNotEmpty(insertList)) {
            goodsPriceReductionService.insertBatch(insertList);
            List<GoodsItem> goodsItems = goodsItemService.queryGoodsIdsList(goodsIdList);
//            List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(goodsIdList);
            Map<Long, List<GoodsItem>> goodsItemMap = goodsItems.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));
//            Map<Long, List<GoodsFreight>> goodsFreghtMap = goodsFreights.stream().collect(Collectors.groupingBy(GoodsFreight::getGoodsId));
            List<SkuReductionPrice> skuReductionPrices = initSkuReductionPrice(insertList, goodsItemMap);
//            List<GoodsReductionFreight> goodsReductionFreights = initGoodsReductionFreight(insertList, goodsFreghtMap);
            if (CollectionUtils.isNotEmpty(skuReductionPrices)) {
                skuReductionPriceService.insertBatch(skuReductionPrices);
            }
//            if (CollectionUtils.isNotEmpty(goodsReductionFreights)) {
//                goodsReductionFreightService.insertBatch(goodsReductionFreights);
//            }
            updateGoodsInfo(goodsIdList, 1);
        }
    }

    @Override
    public PageView<GoodsPriceReductionVO> queryPage(GoodsPriceReductionQuery query) {
        if (query == null) {
            query = new GoodsPriceReductionQuery();
        }
        if (query.getPageNow() < 0) {
            query.setPageNow(1);
        }
        if (query.getPageSize() < 0) {
            query.setPageSize(10);
        }
        //如果有类目 查询所有的叶子类目
        if (null != query.getCategoryId() && query.getCategoryId() > 0) {
            List<Long> categoryIds = categoryTreeCoreService.getAllLeafCategoryByCategoryId(query.getCategoryId());
            if (CollectionUtils.isNotEmpty(categoryIds)) {
                query.setCategoryIds(categoryIds);
                query.setCategoryId(null);
            }
        }
        List<Long> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(query.getGoodsIdListStr())) {
            String[] s = query.getGoodsIdListStr().split("\n");
            for (String id : s) {
                if (StringUtils.isNotBlank(id)) {
                    list.add(Long.valueOf(id.replaceAll(" ", "")));
                }
            }
            query.setGoodsIds(list);
        }
        PageView<GoodsPriceReductionVO> pageView = new PageView<>(query.getPageSize(), query.getPageNow());
        Long shopId = getShopId();
        //商家端调用 只使用shopId、status查询条件
        if(Objects.nonNull(shopId)){
            //商家端加入开关是否展示 商品建议降价列表
            if(!marketingSwitchCenter.getMerchantReductionSwitch()){
                return pageView;
            }
            int pageNow = query.getPageNow();
            Integer pageSize = query.getPageSize();

            query = new GoodsPriceReductionQuery();
            query.setPageSize(pageSize);
            query.setPageNow(pageNow);
            query.setShopId(shopId);
            query.setStatus(GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
        }
        IPage<GoodsPriceReduction> iPage = goodsPriceReductionService.queryPage(query);
        if (null == iPage || CollectionUtils.isEmpty(iPage.getRecords())) {
            return pageView;
        }
        List<GoodsPriceReduction> records = iPage.getRecords();
        List<GoodsPriceReductionVO> res = TransferUtils.transferList(records, GoodsPriceReductionVO.class);
        List<Long> categoryIds = res.stream().map(GoodsPriceReductionVO::getCategoryId).collect(Collectors.toList());
        //查询完整类目路径
        Map<Long, String> categoryTreeMap = categoryTreeCoreService.getCategoryPathByIds(categoryIds);
        for (GoodsPriceReductionVO reductionVO : res) {
            reductionVO.setStatusName(GoodsPriceReductionEnums.getDescByCode(reductionVO.getStatus()));
            if (null != reductionVO.getCategoryId() && reductionVO.getCategoryId() > 0 && categoryTreeMap.containsKey(reductionVO.getCategoryId())) {
                reductionVO.setCategoryName(categoryTreeMap.get(reductionVO.getCategoryId()));
            }
        }
        List<Long> ids = res.stream().map(GoodsPriceReductionVO::getGoodsId).collect(Collectors.toList());
        List<GoodsFreight> goodsFreightsList = goodsFreightService.queryByGoodsIds(ids);
        HashMap<Long, List<GoodsFreightDTO>> goodsFreightMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(goodsFreightsList)) {
            for (GoodsFreight goodsFreight : goodsFreightsList) {
                Long goodsId = goodsFreight.getGoodsId();
                try {
                    GoodsFreightDTO newGoodsFreight = new GoodsFreightDTO();
                    newGoodsFreight.setCountry(goodsFreight.getCode());
                    newGoodsFreight.setCurrentFreight(goodsFreight.getCurrentFreight());
                    if (!goodsFreightMap.containsKey(goodsId)) {
                        List<GoodsFreightDTO> goodsFreightList = new ArrayList<>();
                        goodsFreightList.add(newGoodsFreight);
                        goodsFreightMap.put(goodsId, goodsFreightList);
                    } else {
                        List<GoodsFreightDTO> goodsFreightDTOS = goodsFreightMap.get(goodsId);
                        goodsFreightDTOS.add(newGoodsFreight);
                        goodsFreightMap.put(goodsId, goodsFreightDTOS);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        List<Goods> goodList = goodsService.queryGoodsByIds(ids);
        Map<Long, Goods> goodsMap = goodList.stream().collect(Collectors.toMap(Goods::getId,e->e));

        List<Long> reduction = res.stream().map(GoodsPriceReductionVO::getId).collect(Collectors.toList());
        List<SkuReductionPrice> skuReductionPrices = skuReductionPriceService.queryByReductionIds(reduction,0);
        Map<Long, List<SkuReductionPrice>> skuReductionMap = skuReductionPrices.stream().collect(Collectors.groupingBy(SkuReductionPrice::getGoodsId));

        for (GoodsPriceReductionVO goodsPriceReductionVO : res) {
            Long goodsId = goodsPriceReductionVO.getGoodsId();
            if (goodsFreightMap.containsKey(goodsId)) {
                goodsPriceReductionVO.setGoodsFreightList(goodsFreightMap.get(goodsId));
            }

            Goods goods = goodsMap.get(goodsId);
            if(Objects.nonNull(goods)){
                goodsPriceReductionVO.setMinPrice(goods.getMinPrice());
                goodsPriceReductionVO.setMaxPrice(goods.getMaxPrice());
            }

            List<SkuReductionPrice> skuReductionPriceList = skuReductionMap.get(goodsId);
            if(CollectionUtils.isNotEmpty(skuReductionPriceList)){

                Optional<BigDecimal> maxPrice = skuReductionPriceList.stream().map(x->Optional.ofNullable(x.getPrice()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(x.getFreight()).orElse(BigDecimal.ZERO))).max(BigDecimal::compareTo);
                Optional<BigDecimal> minPrice = skuReductionPriceList.stream().map(x->Optional.ofNullable(x.getPrice()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(x.getFreight()).orElse(BigDecimal.ZERO))).min(BigDecimal::compareTo);
                minPrice.ifPresent(goodsPriceReductionVO::setReductionMinPrice);
                maxPrice.ifPresent(goodsPriceReductionVO::setReductionMaxPrice);
            }
        }
        pageView.setRecords(res);
        pageView.setPageCount(iPage.getPages());
        pageView.setRowCount(iPage.getTotal());
        pageView.setPageNow((int) iPage.getCurrent());
        pageView.setPageSize((int) iPage.getSize());
        return pageView;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGoodsPriceReduction(GoodsPriceReductionUpdateVO updateVO) {
        List<GoodsPriceReduction> goodsPriceReductions = goodsPriceReductionService.queryByGoodsIdList(Collections.singletonList(updateVO.getGoodsId()), GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
        if (CollectionUtils.isEmpty(goodsPriceReductions)) {
            return;
        }
        List<Long> goodsIds = new ArrayList<>();
        List<GoodsPriceReduction> updateList = new ArrayList<>();
        List<Long> reductionIds = new ArrayList<>();
        for (GoodsPriceReduction goodsPriceReduction : goodsPriceReductions) {
            GoodsPriceReduction update = new GoodsPriceReduction();
            update.setId(goodsPriceReduction.getId());
            update.setUpdateUser(updateVO.getOperator());
            update.setStatus(updateVO.getStatus());
            update.setUpdateTime(LocalDateTime.now());
            reductionIds.add(goodsPriceReduction.getId());
            goodsIds.add(goodsPriceReduction.getGoodsId());
            updateList.add(update);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            goodsPriceReductionService.updateBatchById(updateList);
            skuReductionPriceService.updateByReductionIds(reductionIds, updateVO.getOperator(), 1);
//            goodsReductionFreightService.updateByReductionIds(reductionIds, 1);
            updateGoodsInfo(goodsIds, 0);
        }
    }

    @Override
    public List<SkuReductionPriceVO> querySkuReductionListByGoodsId(Long goodsId) {
        if (goodsId == null) {
            return new ArrayList<>();
        }
        List<GoodsItem> goodsItems = goodsItemService.queryGoodsIdsList(Collections.singletonList(goodsId));
        if (CollectionUtils.isEmpty(goodsItems)) {
            return new ArrayList<>();
        }
        List<GoodsPriceReduction> goodsPriceReductions = goodsPriceReductionService.queryByGoodsIdList(Collections.singletonList(goodsId), GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
        if (CollectionUtils.isEmpty(goodsPriceReductions)) {
            return new ArrayList<>();
        }
        List<Long> reductionIds = goodsPriceReductions.stream().map(GoodsPriceReduction::getId).collect(Collectors.toList());
        List<SkuReductionPrice> skuReductionPrices = skuReductionPriceService.queryByReductionIds(reductionIds, 0);
        if (CollectionUtils.isEmpty(skuReductionPrices)) {
            return new ArrayList<>();
        }
        Map<Long, GoodsItem> goodsItemMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getSkuId, Function.identity(), (v1, v2) -> v1));
        Map<Long, SkuReductionPrice> skuRecutionMap = skuReductionPrices.stream().collect(Collectors.toMap(SkuReductionPrice::getSkuId, Function.identity(), (v1, v2) -> v1));
        List<SkuReductionPriceVO> res = new ArrayList<>();
        for (Map.Entry<Long, SkuReductionPrice> entry : skuRecutionMap.entrySet()) {
            Long skuId = entry.getKey();
            SkuReductionPrice value = entry.getValue();
            if (goodsItemMap.containsKey(skuId)) {
                GoodsItem goodsItem = goodsItemMap.get(skuId);
                if (value.getPrice().compareTo(goodsItem.getOrginalPrice()) < 0 || value.getFreight().compareTo(goodsItem.getDefaultDelivery()) < 0) {
                    SkuReductionPriceVO vo = new SkuReductionPriceVO();
                    vo.setReductionId(value.getReductionId());
                    vo.setGoodsId(value.getGoodsId());
                    vo.setSkuId(skuId);
                    vo.setSkuName(goodsItem.getName());
                    BigDecimal price = value.getPrice().compareTo(goodsItem.getOrginalPrice()) > 0 ? goodsItem.getOrginalPrice() : value.getPrice();
                    vo.setPrice(price);
                    vo.setCurrentPrice(goodsItem.getOrginalPrice());
                    BigDecimal freight = value.getFreight().compareTo(goodsItem.getDefaultDelivery()) > 0 ? goodsItem.getDefaultDelivery() : value.getFreight();
                    vo.setFreight(freight);
                    vo.setCurrentFreight(goodsItem.getDefaultDelivery());
                    res.add(vo);
                }
            }
        }
        return res;
    }

    @Override
    public List<GoodsFreightReductionVO> queryGoodsReductionFreightList(Long goodsId) {
//        if (goodsId == null) {
            return new ArrayList<>();
//        }
//        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(Collections.singletonList(goodsId));
//        if (CollectionUtils.isEmpty(goodsFreights)) {
//            return new ArrayList<>();
//        }
//        List<GoodsPriceReduction> goodsPriceReductions = goodsPriceReductionService.queryByGoodsIdList(Collections.singletonList(goodsId), GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
//        if (CollectionUtils.isEmpty(goodsPriceReductions)) {
//            return new ArrayList<>();
//        }
//        List<Long> reductionIds = goodsPriceReductions.stream().map(GoodsPriceReduction::getId).collect(Collectors.toList());
//        List<GoodsReductionFreight> goodsReductionFreights = goodsReductionFreightService.queryByReductionIds(reductionIds, 0);
//        if (CollectionUtils.isEmpty(goodsReductionFreights)) {
//            return new ArrayList<>();
//        }
//        Map<String, GoodsFreight> goodsFreghtMap = goodsFreights.stream().collect(Collectors.toMap(GoodsFreight::getCode, Function.identity(), (v1, v2) -> v1));
//        Map<String, GoodsReductionFreight> reductionFreightMap = goodsReductionFreights.stream().collect(Collectors.toMap(GoodsReductionFreight::getCountry, Function.identity(), (v1, v2) -> v1));
//        List<GoodsFreightReductionVO> res = new ArrayList<>();
//        for (Map.Entry<String, GoodsReductionFreight> entry : reductionFreightMap.entrySet()) {
//            String country = entry.getKey();
//            GoodsReductionFreight value = entry.getValue();
//            if (goodsFreghtMap.containsKey(country)) {
//                GoodsFreight goodsFreight = goodsFreghtMap.get(country);
//                if (value.getFreight().compareTo(goodsFreight.getCurrentFreight()) < 0) {
//                    GoodsFreightReductionVO vo = new GoodsFreightReductionVO();
//                    vo.setReductionId(value.getReductionId());
//                    vo.setGoodsId(value.getGoodsId());
//                    vo.setCountryCode(country);
//                    try {
//                        vo.setCountryName(SaleableCountryEnum.valueOf(country).getNameEn());
//                    } catch (Exception e) {
//                        log.info("转换国家异常，异常code：{}", country);
//                    }
//                    vo.setCountryFreight(value.getFreight());
//                    vo.setCurrentCountryFreight(goodsFreight.getCurrentFreight());
//                    res.add(vo);
//                }
//            }
//        }
//        return res;
    }

    @Override
    public GoodsReductionVO queryGoodsReductionList(Long goodsId, Integer type) {
        GoodsReductionVO vo = new GoodsReductionVO();
        vo.setSkuReductionPriceVOList(this.querySkuReductionListByGoodsId(goodsId));
        vo.setGoodsId(goodsId);
//        if (type != null && type == 1) {
//            vo.setGoodsFreightVOList(this.queryGoodsReductionFreightList(goodsId));
//        }
        return vo;
    }

    @Override
    public void removeReductionRecord() {
        String date = LocalDateTimeUtils.formatToString(LocalDateTime.now().minusDays(30));
        Long maxId = 1L;
        List<GoodsPriceReduction> list = null;
        while (CollectionUtils.isNotEmpty(list = goodsPriceReductionService.queryByIdAndStatus(maxId, GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode(), date))) {
            log.info("批量停止30天未改价数据,起始id:{}", maxId);
            List<Long> idList = list.stream().map(GoodsPriceReduction::getId).collect(Collectors.toList());
            List<Long> goodsIds = list.stream().map(GoodsPriceReduction::getGoodsId).collect(Collectors.toList());
            goodsPriceReductionService.unpdateStatusById(idList, GoodsPriceReductionEnums.SHUT_DOWN.getCode(), "SYSTEM");
//            goodsReductionFreightService.updateByReductionIds(idList, 1);
            skuReductionPriceService.updateByReductionIds(idList, "SYSTEM", 1);
            updateGoodsInfo(goodsIds, 0);
            maxId = list.get(list.size() - 1).getId() + 1;
        }
    }

    @Override
    public List<GoodsPriceReductionOutput> queryExportRecords(GoodsPriceReductionQuery query) {
        if (query == null) {
            query = new GoodsPriceReductionQuery();
        }
        //如果有类目 查询所有的叶子类目
        if (null != query.getCategoryId() && query.getCategoryId() > 0) {
            List<Long> categoryIds = categoryTreeCoreService.getAllLeafCategoryByCategoryId(query.getCategoryId());
            if (CollectionUtils.isNotEmpty(categoryIds)) {
                query.setCategoryIds(categoryIds);
                query.setCategoryId(null);
            }
        }
        List<Long> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(query.getGoodsIdListStr())) {
            String[] s = query.getGoodsIdListStr().split("\n");
            for (String id : s) {
                if (StringUtils.isNotBlank(id)) {
                    list.add(Long.valueOf(id.replaceAll(" ", "")));
                }
            }
            query.setGoodsIds(list);
        }
        List<GoodsPriceReduction> goodsPriceReductions = goodsPriceReductionService.queryForExport(query);
        if (CollectionUtils.isEmpty(goodsPriceReductions)) {
            return new ArrayList<>();
        }
        List<Long> reductionIds = new ArrayList<>();
        List<Long> goodsIds = new ArrayList<>();
        Map<Long, Long> goodsShopMap = new HashMap<>();
        goodsPriceReductions.forEach(k -> {
            reductionIds.add(k.getId());
            goodsIds.add(k.getGoodsId());
            goodsShopMap.put(k.getGoodsId(), k.getShopId());
        });
        List<GoodsItem> goodsItems = goodsItemService.queryGoodsIdsList(goodsIds);
        if (CollectionUtils.isEmpty(goodsItems)) {
            return new ArrayList<>();
        }
        List<SkuReductionPrice> skuReductionPrices = skuReductionPriceService.queryByReductionIds(reductionIds, null);
        if (CollectionUtils.isEmpty(skuReductionPrices)) {
            return new ArrayList<>();
        }
        List<GoodsPriceReductionOutput> res = new ArrayList<>();
        Map<Long, GoodsItem> goodsItemMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getSkuId, Function.identity(), (v1, v2) -> v1));
        Map<String, SkuReductionPrice> skuRecutionMap = skuReductionPrices.stream().collect(Collectors.toMap(k -> String.valueOf(k.getSkuId()) + "-" + String.valueOf(k.getReductionId()), Function.identity(), (v1, v2) -> v1));
        for (Map.Entry<String, SkuReductionPrice> entry : skuRecutionMap.entrySet()) {
            GoodsPriceReductionOutput vo = new GoodsPriceReductionOutput();
            vo.setGoodsId(entry.getValue().getGoodsId());
            vo.setReductionId(entry.getValue().getReductionId());
            vo.setSkuId(entry.getValue().getSkuId());
            vo.setPrice(entry.getValue().getPrice());
            vo.setFreight(entry.getValue().getFreight());
            vo.setShopId(goodsShopMap.get(entry.getValue().getGoodsId()));
            String[] array = entry.getKey().split("-");
            Long skuId = Long.parseLong(array[0]);
            if (goodsItemMap.containsKey(skuId)) {
                vo.setCurrentPrice(goodsItemMap.get(skuId).getOrginalPrice());
                vo.setCurrentFreight(goodsItemMap.get(skuId).getDefaultDelivery());
            }
            res.add(vo);
        }
        res.sort(Comparator.comparing(GoodsPriceReductionOutput::getReductionId));

        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReductionPrice(GoodsReductionVO vo, String operator) {
        if (vo == null) {
            return;
        }
        Long goodsId = vo.getGoodsId();
        Long reductionId = null;
        List<GoodsPriceReduction> goodsPriceReductions = goodsPriceReductionService.queryByGoodsIdList(Collections.singletonList(goodsId), GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
        if (CollectionUtils.isNotEmpty(goodsPriceReductions)) {
            reductionId = goodsPriceReductions.get(0).getId();
        }
        List<GoodsEditPriceDto.SkuChangeDto> skuChangeDtoList = updateGoodsItemPrice(vo.getSkuReductionPriceVOList());
//        updateGoodsCountryFreight(vo.getGoodsFreightVOList());
        if (reductionId != null) {
            updateGoodsReduction(reductionId, operator);
        }

        //记录商品修改记录
        saveEditInfo(goodsId, skuChangeDtoList);

        updateGoodsInfo(Collections.singletonList(goodsId), 0);
    }

    private void saveEditInfo(Long goodsId, List<GoodsEditPriceDto.SkuChangeDto> skuChangeDtoList) {
        List<String> specialTagList = Lists.newArrayList();
        List<GoodsLockInfo> goodsLockInfoList = goodsLockInfoService.lambdaQuery()
                .eq(GoodsLockInfo::getGoodsId, goodsId)
                .eq(GoodsLockInfo::getIsDel, 0)
                .list();
        if (CollectionUtils.isNotEmpty(goodsLockInfoList)) {
            specialTagList = GoodsLockLabelTypEnums.listNamesByCodes(goodsLockInfoList.get(goodsLockInfoList.size() - 1).getLabel());
        }

        ListingFollowGoods listingFollowGoods = listingFollowGoodsService.lambdaQuery()
                .eq(ListingFollowGoods::getGoodsId, goodsId)
                .eq(ListingFollowGoods::getIsDel, 0)
                .eq(ListingFollowGoods::getStatus, 1)
                .one();
        if (listingFollowGoods != null) {
            specialTagList.add("listing");
        }

        Goods goods = goodsService.getById(goodsId);

        GoodsEditInfo editInfo = new GoodsEditInfo();
        editInfo.setStatus(0);
        editInfo.setGoodsId(goodsId);
        editInfo.setCategoryId(goods.getCategoryId());
        editInfo.setShopId(goods.getShopId());
        editInfo.setShopName(goods.getShopName());
        editInfo.setAuditUser("system");
        editInfo.setAuditTime(LocalDateTime.now());
        editInfo.setStatus(1);
        editInfo.setApplyUser("system");
        editInfo.setApplyTime(LocalDateTime.now());
        editInfo.setApplyReason("商家接受建议降价");
        editInfo.setIsDel(0);
        editInfo.setType(GoodsEditTypeEnums.PRICE.getCode());
        editInfo.setAddPrice(0);
        editInfo.setContent(JSON.toJSONString(new GoodsEditPriceDto(skuChangeDtoList)));
        editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
        editInfo.setUpdateTime(LocalDateTime.now());
        goodsEditInfoService.save(editInfo);

        GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
        goodsEditInfoDetail.setEditId(editInfo.getId());
        goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
        goodsEditInfoDetail.setContent(JSON.toJSONString(new GoodsEditPriceDto(skuChangeDtoList)));
        goodsEditInfoDetailService.save(goodsEditInfoDetail);

        // 写入推荐算法日志
        recommendCoreService.saveRecommendGoodsChangeLog(editInfo);

        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .goodsId(goodsId)
                .type(OperationLogTypeEnums.SHOP_ACCEPT_REDUCTION)
                .newData(JSON.toJSONString(skuChangeDtoList))
                .content(OperationLogTypeEnums.SHOP_ACCEPT_REDUCTION.getDesc())
                .status(1)
                .user(getUserName());
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
    }

    public void updatePriceAll() {
        String operator = getUserName();
        Long shopId = getShopId();
        if (Objects.isNull(shopId)) {
            throw new CustomException(VoghionProductResultCode.SHOP_ID_NULL_ERROR);
        }
        int count = CommonConstants.GOODS_COUNT/CommonConstants.PROCESS_REDUCTION_COUNT;
        int index = 0;
        while (true) {
            List<GoodsPriceReduction> list = goodsPriceReductionService.queryByShopId(shopId, GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode(), CommonConstants.PROCESS_REDUCTION_COUNT);
            if(list.isEmpty()){
                break;
            }
            processReduction(list,operator);
            index++;
            if(index > count){
                log.info("shopId: {} 接受所有降价 超过循环次数 跳出循环",shopId);
                break;
            }
        }
    }

    @Override
    public void processReduction(ProcessReductionVO vo) {
        List<Long> refuseGoodsIds = vo.getRefuseGoodsIds();
        String operator = getUserName();
        Long shopId = getShopId();
        if (Objects.isNull(shopId)) {
            throw new CustomException(VoghionProductResultCode.SHOP_ID_NULL_ERROR);
        }
        if(CollectionUtils.isNotEmpty(refuseGoodsIds)){
            goodsPriceReductionService.unpdateStatusByGoodsIds(shopId,refuseGoodsIds,GoodsPriceReductionEnums.PRICE_REFUSE.getCode(),operator);
            updateGoodsInfo(refuseGoodsIds, 0);
        }
        List<Long> acceptGoodsIds = vo.getAcceptGoodsIds();
        if(CollectionUtils.isNotEmpty(acceptGoodsIds)){
            List<GoodsPriceReduction> list = goodsPriceReductionService.queryByShopIdAndGoodsIdList(shopId,acceptGoodsIds,GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
            if(list.isEmpty()){
                return;
            }
            processReduction(list,operator);
        }
    }

    private void processReduction(List<GoodsPriceReduction> list, String operator){
        for (GoodsPriceReduction reduction : list) {
            Long goodsId = reduction.getGoodsId();
            try {
                GoodsReductionVO vo = new GoodsReductionVO();
                vo.setSkuReductionPriceVOList(this.querySkuReductionListByGoodsId(goodsId));
                vo.setGoodsId(goodsId);
                //获取当前类的代理对象，防止事务失效
                GoodsPriceReductionCoreService goodsPriceReductionCoreService = applicationContext.getBean(GoodsPriceReductionCoreService.class);
                goodsPriceReductionCoreService.updateReductionPrice(vo, operator);
            } catch (Exception e) {
                log.error(" goodsId: {} 更新金额发生异常 {}", goodsId, ExceptionUtils.getStackTrace(e));
            }
        }
    }

    @Override
    public void updateReductionRecord(Long goodsId, String operator) {
        List<GoodsPriceReduction> goodsPriceReductions = goodsPriceReductionService.queryByGoodsIdList(Collections.singletonList(goodsId), GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
        if (CollectionUtils.isEmpty(goodsPriceReductions)) {
            return;
        }
        Long id = goodsPriceReductions.get(0).getId();
        goodsPriceReductionService.unpdateStatusById(Collections.singletonList(id), GoodsPriceReductionEnums.PRICE_REDUCED.getCode(), operator);
//        goodsReductionFreightService.updateByReductionIds(Collections.singletonList(id), 1);
        skuReductionPriceService.updateByReductionIds(Collections.singletonList(id), operator, 1);
    }

    @Override
    public Pair<Map<Long, String>,Map<Long, String>> queryPriceReductionRange(List<Long> goodsIdList) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return null;
        }
        List<GoodsPriceReduction> goodsPriceReductions = goodsPriceReductionService.queryByGoodsIdList(goodsIdList, GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
        if (CollectionUtils.isEmpty(goodsPriceReductions)) {
            return null;
        }
        List<GoodsItem> goodsItems = goodsItemService.queryGoodsIdsList(goodsIdList);
        Map<Long, GoodsItem> goodsItemMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getSkuId, Function.identity(), (v1, v2) -> v1));
        List<Long> reductionIds = goodsPriceReductions.stream().map(GoodsPriceReduction::getId).distinct().collect(Collectors.toList());
        List<SkuReductionPrice> skuReductionPrices = skuReductionPriceService.queryByReductionIds(reductionIds, 0);
        if (CollectionUtils.isEmpty(skuReductionPrices)) {
            return null;
        }
        Map<Long, List<SkuReductionPrice>> skuReductionMap = skuReductionPrices.stream().collect(Collectors.groupingBy(SkuReductionPrice::getGoodsId));
        Map<Long, String> res = new HashMap<>();
        Map<Long, String> skuPercent = new HashMap<>();
        for (Map.Entry<Long, List<SkuReductionPrice>> entry : skuReductionMap.entrySet()) {
            Optional<BigDecimal> maxPrice = entry.getValue().stream().map(skuReductionPrice -> skuReductionPrice.getPrice().add(skuReductionPrice.getFreight())).filter(Objects::nonNull).max(BigDecimal::compareTo);
            Optional<BigDecimal> minPrice = entry.getValue().stream().map(skuReductionPrice -> skuReductionPrice.getPrice().add(skuReductionPrice.getFreight())).filter(Objects::nonNull).min(BigDecimal::compareTo);
            if (maxPrice.isPresent() && minPrice.isPresent()) {
                res.put(entry.getKey(), minPrice.get().toString() + "~" + maxPrice.get().toString());
            }
            int totalCount=entry.getValue().size();
            int num=0;
            //统计sku降价达到目标数量
            for (SkuReductionPrice skuReductionPrice : entry.getValue()) {
                if (goodsItemMap.containsKey(skuReductionPrice.getSkuId())) {
                    GoodsItem goodsItem = goodsItemMap.get(skuReductionPrice.getSkuId());
                    if (goodsItem.getOrginalPrice().compareTo(skuReductionPrice.getPrice()) <= 0
                            && goodsItem.getDefaultDelivery().compareTo(skuReductionPrice.getFreight()) <= 0) {
                        num++;
                    }
                }
            }
            String percent="0%";
            if (num > 0) {
                BigDecimal multiply = BigDecimal.valueOf(num).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                percent = multiply.toString() + "%";
            }
            skuPercent.put(entry.getKey(), percent);
        }
        return new ImmutablePair<>(res,skuPercent);
    }

    @Override
    public void removeByGoodsId(List<Long> goodsIds) {
        List<GoodsPriceReduction> list =  goodsPriceReductionService.queryAllByGoodsIdList(goodsIds);
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        List<Long> idList = list.stream().map(GoodsPriceReduction::getId).collect(Collectors.toList());
        log.info("移除建议降价主键:{}",JSONObject.toJSONString(idList));
        goodsPriceReductionService.unpdateStatusById(idList, GoodsPriceReductionEnums.SHUT_DOWN.getCode(), "SYSTEM");
//        goodsReductionFreightService.updateByReductionIds(idList, 1);
        skuReductionPriceService.updateByReductionIds(idList, "SYSTEM", 1);
        updateGoodsInfo(goodsIds, 0);

    }

    private List<GoodsEditPriceDto.SkuChangeDto> updateGoodsItemPrice(List<SkuReductionPriceVO> skuReductionPriceVOList) {
        if (CollectionUtils.isEmpty(skuReductionPriceVOList)) {
            return Collections.emptyList();
        }
        List<GoodsEditPriceDto.SkuChangeDto> skuChangeDtoList = Lists.newArrayList();
        Long goodsId = skuReductionPriceVOList.get(0).getGoodsId();
        List<GoodsItem> goodsItems = goodsItemService.queryGoodsItemByGoodsId(goodsId);
        List<GoodsItem> updateGoodsItemList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsItems)) {
            Map<Long, GoodsItem> skuMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getSkuId, Function.identity(), (v1, v2) -> v1));
            for (SkuReductionPriceVO skuReductionPriceVO : skuReductionPriceVOList) {
                if (skuReductionPriceVO.getPrice() == null || skuReductionPriceVO.getFreight() == null) {
                    continue;
                }
                if (skuMap.containsKey(skuReductionPriceVO.getSkuId())) {
                    GoodsItem originalGoodsItem = skuMap.get(skuReductionPriceVO.getSkuId());
                    GoodsItem item = new GoodsItem();
                    item.setId(originalGoodsItem.getId());
                    item.setUpdateTime(new Date());
                    item.setPrice(skuReductionPriceVO.getPrice().add(skuReductionPriceVO.getFreight()));
                    item.setOrginalPrice(skuReductionPriceVO.getPrice());
                    item.setDefaultDelivery(skuReductionPriceVO.getFreight());
                    updateGoodsItemList.add(item);

                    skuChangeDtoList.add(new GoodsEditPriceDto.SkuChangeDto(item.getId(), originalGoodsItem.getOrginalPrice(), skuReductionPriceVO.getPrice(), originalGoodsItem.getStock(), originalGoodsItem.getStock()));
                }
            }
        }
        if (CollectionUtils.isNotEmpty(updateGoodsItemList)) {
            boolean b = goodsItemService.updateBatchById(updateGoodsItemList);
            CheckUtils.check(!b, ProductResultCode.UPDATE_ERROR);
        }

        return skuChangeDtoList;
    }

//    private void updateGoodsCountryFreight(List<GoodsFreightReductionVO> goodsFreightVOList) {
//        if (CollectionUtils.isEmpty(goodsFreightVOList)) {
//            return;
//        }
//        Long goodsId = goodsFreightVOList.get(0).getGoodsId();
//        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(Collections.singletonList(goodsId));
//        if (CollectionUtils.isEmpty(goodsFreights)) {
//            return;
//        }
//        List<GoodsFreight> updateList = new ArrayList<>();
//        Map<String, GoodsFreight> goodsFreghtMap = goodsFreights.stream().collect(Collectors.toMap(GoodsFreight::getCode, Function.identity(), (v1, v2) -> v1));
//        for (GoodsFreightReductionVO goodsFreightVO : goodsFreightVOList) {
//            if (goodsFreightVO.getCountryFreight() == null || StringUtils.isBlank(goodsFreightVO.getCountryCode())) {
//                continue;
//            }
//            if (goodsFreghtMap.containsKey(goodsFreightVO.getCountryCode())) {
//                GoodsFreight freight = new GoodsFreight();
//                freight.setId(goodsFreghtMap.get(goodsFreightVO.getCountryCode()).getId());
//                freight.setCurrentFreight(goodsFreightVO.getCountryFreight());
//                freight.setUpdateTime(LocalDateTime.now());
//                updateList.add(freight);
//            }
//        }
//        if (CollectionUtils.isNotEmpty(updateList)) {
//            boolean b = goodsFreightService.updateBatchById(updateList);
//            CheckUtils.check(!b, ProductResultCode.UPDATE_ERROR);
//
//        }
//    }

    private void updateGoodsReduction(Long id, String operator) {
        goodsPriceReductionService.unpdateStatusById(Collections.singletonList(id), GoodsPriceReductionEnums.PRICE_REDUCED.getCode(), operator);
//        goodsReductionFreightService.updateByReductionIds(Collections.singletonList(id), 1);
        skuReductionPriceService.updateByReductionIds(Collections.singletonList(id), operator, 1);
    }

    private List<GoodsPriceReduction> initGoodsPriceReduction(List<GoodsInfoDTO> records, String operator, Map<Long, Double> goodsDiscountMap) {
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        List<GoodsPriceReduction> res = new ArrayList<>();
        for (GoodsInfoDTO dto : records) {
            Double discount = goodsDiscountMap.get(dto.getId());
            GoodsPriceReduction reduction = new GoodsPriceReduction();
            reduction.setGoodsId(dto.getId());
            reduction.setGoodsName(dto.getName());
            reduction.setShopName(dto.getShopName());
            reduction.setShopId(dto.getShopId());
            reduction.setCategoryId(dto.getCategoryId());
            reduction.setMainImage(dto.getMainImage());
            if (dto.getMinPrice().compareTo(dto.getMaxPrice()) == 0) {
                reduction.setPriceSnapshot(dto.getMinPrice().toString());
            } else {
                reduction.setPriceSnapshot(dto.getMinPrice().toString() + "~" + dto.getMaxPrice().toString());
            }
            reduction.setStatus(GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
            reduction.setReasonType("1");
            reduction.setCreateUser(operator);
            reduction.setUpdateUser(operator);
            reduction.setCreateTime(LocalDateTime.now());
            reduction.setUpdateTime(LocalDateTime.now());
            if (discount != null) {
                if (discount < 0 || discount > 10) {
                    reduction.setRecommendDiscount("10");
                } else {
                    reduction.setRecommendDiscount(String.valueOf(discount));
                }
            } else {
                reduction.setRecommendDiscount("10");
            }
            res.add(reduction);
        }
        return res;
    }

    private List<SkuReductionPrice> initSkuReductionPrice(List<GoodsPriceReduction> goodsPriceReductions, Map<Long, List<GoodsItem>> goodsItemMap) {
        if (goodsItemMap.isEmpty()) {
            return new ArrayList<>();
        }
        List<SkuReductionPrice> res = new ArrayList<>();
        for (GoodsPriceReduction goodsPriceReduction : goodsPriceReductions) {
            if (!goodsItemMap.containsKey(goodsPriceReduction.getGoodsId())) {
                continue;
            }
            BigDecimal discount = new BigDecimal(goodsPriceReduction.getRecommendDiscount());
            discount = discount.divide(BigDecimal.TEN, 2, BigDecimal.ROUND_HALF_UP);
            for (GoodsItem goodsItem : goodsItemMap.get(goodsPriceReduction.getGoodsId())) {
                if (goodsItem.getSkuId() == null) {
                    continue;
                }
                BigDecimal price = goodsItem.getOrginalPrice() == null ? BigDecimal.ZERO : goodsItem.getOrginalPrice();
                BigDecimal delivery = goodsItem.getDefaultDelivery() == null ? BigDecimal.ZERO : goodsItem.getDefaultDelivery();
                SkuReductionPrice skuReductionPrice = new SkuReductionPrice();
                skuReductionPrice.setGoodsId(goodsItem.getGoodsId());
                skuReductionPrice.setSkuId(goodsItem.getSkuId());
                skuReductionPrice.setIsDel(0);
                skuReductionPrice.setReductionId(goodsPriceReduction.getId());
                skuReductionPrice.setCreateTime(LocalDateTime.now());
                skuReductionPrice.setUpdateTime(LocalDateTime.now());
                skuReductionPrice.setCreateUser(goodsPriceReduction.getCreateUser());
                skuReductionPrice.setUpdateUser(goodsPriceReduction.getUpdateUser());
                skuReductionPrice.setPrice(price.multiply(discount).setScale(2, BigDecimal.ROUND_HALF_UP));
                skuReductionPrice.setFreight(delivery);
                res.add(skuReductionPrice);
            }
        }
        return res;
    }

//    private List<GoodsReductionFreight> initGoodsReductionFreight(List<GoodsPriceReduction> goodsPriceReductions, Map<Long, List<GoodsFreight>> goodsFreightMap) {
//        if (goodsFreightMap.isEmpty()) {
//            return new ArrayList<>();
//        }
//        List<GoodsReductionFreight> res = new ArrayList<>();
//        for (GoodsPriceReduction goodsPriceReduction : goodsPriceReductions) {
//            if (!goodsFreightMap.containsKey(goodsPriceReduction.getGoodsId())) {
//                continue;
//            }
//            BigDecimal discount = new BigDecimal(goodsPriceReduction.getRecommendDiscount());
//            discount = discount.divide(BigDecimal.TEN, 2, BigDecimal.ROUND_HALF_UP);
//            for (GoodsFreight goodsFreight : goodsFreightMap.get(goodsPriceReduction.getGoodsId())) {
//                GoodsReductionFreight freight = new GoodsReductionFreight();
//                TransferUtils.transferBean(goodsFreight, freight);
//                BigDecimal price = goodsFreight.getCurrentFreight() == null ? BigDecimal.ZERO : goodsFreight.getCurrentFreight();
//                freight.setFreight(price.multiply(discount).setScale(2, BigDecimal.ROUND_HALF_UP));
//                freight.setCreateTime(LocalDateTime.now());
//                freight.setUpdateTime(LocalDateTime.now());
//                freight.setReductionId(goodsPriceReduction.getId());
//                freight.setCountry(goodsFreight.getCode());
//                res.add(freight);
//            }
//        }
//        return res;
//    }

    private void updateGoodsInfo(List<Long> goodsIds, Integer isReduction) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return;
        }

        List<GoodsItem> goodsItems = goodsItemService.queryGoodsIdsList(goodsIds);
        Map<Long, List<GoodsItem>> goodsItemGroupMap = goodsItems.stream().collect(Collectors.groupingBy(GoodsItem::getGoodsId));

        List<Goods> goodsList = new ArrayList<>();
        for (Long goodsId : goodsIds) {
            Goods goods = new Goods();
            goods.setId(goodsId);
            goods.setUpdateTime(LocalDateTime.now());
            goods.setIsReduction(isReduction);

            List<GoodsItem> goodsItemList = goodsItemGroupMap.get(goodsId);
            if (CollectionUtils.isNotEmpty(goodsItemList)) {
                goodsItemList.stream().map(GoodsItem::getPrice).min(BigDecimal::compareTo).ifPresent(goods::setMinPrice);
                goodsItemList.stream().map(GoodsItem::getPrice).max(BigDecimal::compareTo).ifPresent(goods::setMaxPrice);
            }
            goodsList.add(goods);
        }
        goodsService.updateBatchById(goodsList);

        for (Goods goods : goodsList) {
            Map<String, Object> map = new HashMap<>();
            map.put("isReduction", isReduction);
            if (goods.getMinPrice() != null) {
                map.put("minPrice", goods.getMinPrice());
            }
            if (goods.getMaxPrice() != null) {
                map.put("maxPrice", goods.getMaxPrice());
            }
            TermQueryBuilder queryBuilder = QueryBuilders.termQuery("id", goods.getId());
//            TermsQueryBuilder queryBuilder = QueryBuilders.termsQuery("id", goodsIds);
            goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), queryBuilder, map);
        }
    }

    private void refreshReduction(List<GoodsPriceReduction> goodsPriceReductions,List<GoodsPriceReductionInputVO> list,
                                  String operator,Map<Long, Double> goodsDiscountMap,List<Long> goodsIdList){
        if(CollectionUtils.isEmpty(goodsPriceReductions) || CollectionUtils.isEmpty(list)){
            return;
        }
        List<GoodsItem> goodsItems = goodsItemService.queryGoodsIdsList(goodsIdList);
//        List<GoodsFreight> goodsFreights = goodsFreightCoreService.queryFreightByGoodsIds(goodsIdList);

        Map<Long, GoodsItem> goodsItemMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getSkuId,Function.identity(),(v1,v2)->v1));
//        Map<String, GoodsFreight> goodsFreghtMap = goodsFreights.stream().collect(Collectors.toMap(k->k.getGoodsId()+"-"+k.getCode(),Function.identity(),(v1,v2)->v1));
        List<GoodsPriceReduction> updateList=updateGoodsPriceReduction(goodsPriceReductions,operator,goodsDiscountMap);
        Map<Long, String> reductionDiscountMap = updateList.stream().collect(Collectors.toMap(GoodsPriceReduction::getId, GoodsPriceReduction::getRecommendDiscount));
        List<SkuReductionPrice> skuReductionPrices = updateSkuReductionPrice(reductionDiscountMap, goodsItemMap,operator);
//        List<GoodsReductionFreight> goodsReductionFreights = updateGoodsReductionFreight(reductionDiscountMap, goodsFreghtMap);
        if(CollectionUtils.isNotEmpty(updateList)){
            goodsPriceReductionService.updateBatchById(updateList);
        }
        if(CollectionUtils.isNotEmpty(skuReductionPrices)){
            skuReductionPriceService.updateBatchById(skuReductionPrices);
        }
//        if(CollectionUtils.isNotEmpty(goodsReductionFreights)){
//            goodsReductionFreightService.updateBatchById(goodsReductionFreights);
//        }

    }

    private List<GoodsPriceReduction> updateGoodsPriceReduction(List<GoodsPriceReduction> goodsPriceReductions, String operator, Map<Long, Double> goodsDiscountMap) {
        if (CollectionUtils.isEmpty(goodsPriceReductions)) {
            return new ArrayList<>();
        }
        List<GoodsPriceReduction> res = new ArrayList<>();
        for (GoodsPriceReduction goodsPriceReduction : goodsPriceReductions) {
            Double discount = goodsDiscountMap.get(goodsPriceReduction.getGoodsId());
            GoodsPriceReduction reduction = new GoodsPriceReduction();
            reduction.setId(goodsPriceReduction.getId());
            reduction.setUpdateUser(operator);
            reduction.setUpdateTime(LocalDateTime.now());
            if (discount != null) {
                if (discount < 0 || discount > 10) {
                    reduction.setRecommendDiscount("10");
                } else {
                    reduction.setRecommendDiscount(String.valueOf(discount));
                }
            } else {
                reduction.setRecommendDiscount("10");
            }
            res.add(reduction);
        }
        return res;
    }

    private List<SkuReductionPrice> updateSkuReductionPrice(Map<Long,String> reductionMap, Map<Long, GoodsItem> goodsItemMap,String operator) {
        if (goodsItemMap.isEmpty()) {
            return new ArrayList<>();
        }
        List<SkuReductionPrice> res = new ArrayList<>();
        List<Long> reductionIds = new ArrayList<>(reductionMap.keySet());
        List<SkuReductionPrice> skuReductionPrices = skuReductionPriceService.queryByReductionIds(reductionIds, 0);
        for (SkuReductionPrice skuReductionPrice : skuReductionPrices) {
            if (!goodsItemMap.containsKey(skuReductionPrice.getSkuId())) {
                continue;
            }
            BigDecimal discount = new BigDecimal(reductionMap.get(skuReductionPrice.getReductionId()));
            discount = discount.divide(BigDecimal.TEN, 2, BigDecimal.ROUND_HALF_UP);
            GoodsItem goodsItem = goodsItemMap.get(skuReductionPrice.getSkuId());
            BigDecimal price = goodsItem.getOrginalPrice() == null ? BigDecimal.ZERO : goodsItem.getOrginalPrice();
            BigDecimal delivery = goodsItem.getDefaultDelivery() == null ? BigDecimal.ZERO : goodsItem.getDefaultDelivery();
            SkuReductionPrice update = new SkuReductionPrice();
            update.setId(skuReductionPrice.getId());
            update.setUpdateTime(LocalDateTime.now());
            update.setUpdateUser(operator);
            update.setPrice(price.multiply(discount).setScale(2, BigDecimal.ROUND_HALF_UP));
            update.setFreight(delivery);
            res.add(update);
        }
        return res;
    }

//    private List<GoodsReductionFreight> updateGoodsReductionFreight(Map<Long,String> reductionMap, Map<String, GoodsFreight> goodsFreightMap) {
//        if (goodsFreightMap.isEmpty()) {
//            return new ArrayList<>();
//        }
//        List<GoodsReductionFreight> res = new ArrayList<>();
//        List<Long> reductionIds = new ArrayList<>(reductionMap.keySet());
//        List<GoodsReductionFreight> goodsReductionFreights = goodsReductionFreightService.queryByReductionIds(reductionIds, 0);
//        for (GoodsReductionFreight goodsReductionFreight : goodsReductionFreights) {
//            String key=goodsReductionFreight.getGoodsId()+"-"+goodsReductionFreight.getCountry();
//            if (!goodsFreightMap.containsKey(key)) {
//                continue;
//            }
//            BigDecimal discount = new BigDecimal(reductionMap.get(goodsReductionFreight.getReductionId()));
//            discount = discount.divide(BigDecimal.TEN, 2, BigDecimal.ROUND_HALF_UP);
//            GoodsFreight goodsFreight=goodsFreightMap.get(key);
//            GoodsReductionFreight freight = new GoodsReductionFreight();
//            BigDecimal price = goodsFreight.getCurrentFreight() == null ? BigDecimal.ZERO : goodsFreight.getCurrentFreight();
//            freight.setFreight(price.multiply(discount).setScale(2, BigDecimal.ROUND_HALF_UP));
//            freight.setUpdateTime(LocalDateTime.now());
//            freight.setId(goodsReductionFreight.getId());
//            res.add(freight);
//        }
//        return res;
//    }


    @Override
    public void checkGoodsForReductionJob() {
        LogUtils.info(log,"checkGoodsForReductionJob job start...");
        LogUtils.info(log, "checkGoodsForReductionJob reductionDiscount:{}, reductionDiscount:{}", reductionInterval, reductionDiscount);
        //1、最近7天累计点击uv＞1 且加购或立即购买uv=0的在架未删除商品
        //2、最近7天累计加购或立即购买uv大于1且成交uv=0的在架末删除商品
        //3、备货入仓的商品在库天数＞7天且成交uv=0
        //4、备货入仓的商品库存周转天数＞30天
        //1、2、3、4条件关系为或
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("runDays", 7))
                .must(QueryBuilders.rangeQuery("createDay").from(LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))));

        BoolQueryBuilder conditionQueryBuilder = QueryBuilders.boolQuery();

        BoolQueryBuilder condition1 = QueryBuilders.boolQuery()
                .must(QueryBuilders.rangeQuery("clickUv").gt(1))
                .must(QueryBuilders.termQuery("addUv", 0));
        conditionQueryBuilder.should(condition1);

        BoolQueryBuilder condition2 = QueryBuilders.boolQuery()
                .must(QueryBuilders.rangeQuery("addUv").gt(1))
                .must(QueryBuilders.termQuery("dealUv", 0));
        conditionQueryBuilder.should(condition2);

        List<Long> stockOver7daysGoodsIds = warehouseStockBackupsService.lambdaQuery()
                .eq(WarehouseStockBackups::getCreateDay, LocalDate.now())
                .gt(WarehouseStockBackups::getStockAge, 7)
                .select(WarehouseStockBackups::getGoodsId)
                .list()
                .stream().map(WarehouseStockBackups::getGoodsId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(stockOver7daysGoodsIds)) {
            BoolQueryBuilder condition3 = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("goodsId", stockOver7daysGoodsIds))
                    .must(QueryBuilders.termQuery("dealUv", 0));
            conditionQueryBuilder.should(condition3);
        }
        boolQueryBuilder.must(conditionQueryBuilder);


        Set<Long> lowConversionGoodsIds = Sets.newHashSet();
        String goodsEveryDayIndex = queryCurrentIndex(EsEnums.GOODS_EVERY_DAY_ENUMS.getIndex());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .fetchSource("goodsId", null)
                .size(1000)
                .sort("goodsId", SortOrder.ASC);
        log.info("checkGoodsForReductionJob sourceBuilder:{}", sourceBuilder);

        SearchRequest searchRequest = new SearchRequest(goodsEveryDayIndex)
                .scroll(new Scroll(TimeValue.timeValueMinutes(1)))
                .searchType(SearchType.DEFAULT)
                .source(sourceBuilder);
        SearchResponse searchGoodsIdResponse = null;
        try {
            searchGoodsIdResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("checkGoodsForReductionJob scroll 111 异常", e);
        }
        if (searchGoodsIdResponse == null) {
            return;
        }

        SearchHit[] hits = searchGoodsIdResponse.getHits().getHits();
        String scrollId = searchGoodsIdResponse.getScrollId();
        int batch = 1;

        while (ArrayUtils.isNotEmpty(hits)) {
            for (SearchHit hit : hits) {
                Object goodsId = hit.getSourceAsMap().get("goodsId");
                if (goodsId != null) {
                    lowConversionGoodsIds.add(Long.parseLong(goodsId.toString()));
                }
            }
            int finalBatch = batch;
            log.info("checkGoodsForReductionJob batch:{}, lowConversionGoodsIds.size:{}", finalBatch, lowConversionGoodsIds.size());



            SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId).scroll(TimeValue.timeValueMinutes(1));
            try {
                searchGoodsIdResponse = restHighLevelClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
            } catch (IOException e) {
                log.error("checkGoodsForReductionJob scroll 222 异常", e);
            }
            if (searchGoodsIdResponse == null) {
                break;
            }
            scrollId = searchGoodsIdResponse.getScrollId();
            hits = searchGoodsIdResponse.getHits().getHits();
            batch++;
        }


        LogUtils.info(log, "checkGoodsForReductionJob es查询完毕 lowConversionGoodsIds.size:{}", lowConversionGoodsIds.size());


        List<Long> stockOver30daysGoodsIds = warehouseStockBackupsService.lambdaQuery()
                .eq(WarehouseStockBackups::getCreateDay, LocalDate.now())
                .gt(WarehouseStockBackups::getStockAge, 30)
                .select(WarehouseStockBackups::getGoodsId)
                .list()
                .stream().map(WarehouseStockBackups::getGoodsId).distinct().collect(Collectors.toList());
        LogUtils.info(log, "checkGoodsForReductionJob 库存周转天数＞30天 stockOver30daysGoodsIds.size:{}", stockOver30daysGoodsIds.size());

        Set<Long> goodsIds = Sets.newHashSet();
        goodsIds.addAll(lowConversionGoodsIds);
        goodsIds.addAll(stockOver30daysGoodsIds);
        LogUtils.info(log, "checkGoodsForReductionJob 总共goodsIds.size:{}", goodsIds.size());

        //一星期内接受过降价
        List<Long> acceptedGoodsIds = goodsPriceReductionService.lambdaQuery()
                .in(GoodsPriceReduction::getGoodsId, goodsIds)
                .ge(GoodsPriceReduction::getCreateTime, LocalDateTime.now().minusDays(reductionInterval).minusHours(1))
                .eq(GoodsPriceReduction::getStatus, 2)
                .select(GoodsPriceReduction::getGoodsId)
                .list()
                .stream().map(GoodsPriceReduction::getGoodsId).collect(Collectors.toList());
        LogUtils.info(log, "checkGoodsForReductionJob 过去{}天，接受过降价的商品 acceptedGoodsIds.size:{}", reductionInterval, acceptedGoodsIds.size());

        //超过一星期未处理
        Map<Long, GoodsPriceReduction> existReductionGoodsMap = goodsPriceReductionService.lambdaQuery()
                .in(GoodsPriceReduction::getGoodsId, goodsIds)
                .le(GoodsPriceReduction::getCreateTime, LocalDateTime.now().minusDays(reductionInterval))
                .eq(GoodsPriceReduction::getStatus, 1)
                .list()
                .stream().collect(Collectors.toMap(GoodsPriceReduction::getGoodsId, Function.identity(), (v1, v2) -> v1));
        LogUtils.info(log, "checkGoodsForReductionJob 过去{}天，已存在降价信息商品 existReductionGoodsIds.size:{}", reductionInterval, existReductionGoodsMap.values().size());

//        goodsIds.removeAll(existReductionGoodsIds);
        goodsIds.removeAll(acceptedGoodsIds);
        if (CollectionUtils.isEmpty(goodsIds)) {
            LogUtils.info(log, "checkGoodsForReductionJob isEmpty 111");
            return;
        }

        List<Goods> todoGoodsList = goodsService.lambdaQuery()
                .in(Goods::getId, goodsIds)
                .eq(Goods::getIsDel, 0)
                .eq(Goods::getIsShow, GoodsIsShowEnums.SHELF.getType())
                .list();
        LogUtils.info(log, "checkGoodsForReductionJob 在架未删除size:{}", todoGoodsList.size());
        if (CollectionUtils.isEmpty(todoGoodsList)) {
            LogUtils.info(log, "checkGoodsForReductionJob isEmpty 222");
            return;
        }

        LogUtils.info(log, "checkGoodsForReductionJob update -> all batch:{}", todoGoodsList.size() / 100);
        int batch2 = 1;
        for (List<Goods> goodsList : Lists.partition(todoGoodsList, 100)) {
            LogUtils.info(log, "checkGoodsForReductionJob update batch:{}", batch2);
            List<GoodsPriceReduction> goodsPriceReductionSaveList = Lists.newArrayList();
            List<Long> batchGoodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());
            Map<Long, List<GoodsItem>> goodsItemGroupMap = goodsItemService.queryGoodsIdsList(batchGoodsIds)
                    .stream()
                    .collect(Collectors.groupingBy(GoodsItem::getGoodsId));

            for (Goods goods : goodsList) {
                List<GoodsItem> goodsItemList = goodsItemGroupMap.get(goods.getId());
                if (CollectionUtils.isEmpty(goodsItemList)) {
                    continue;
                }
                GoodsPriceReduction reduction = existReductionGoodsMap.get(goods.getId());
                if (reduction == null) {
                    reduction = new GoodsPriceReduction();
                    reduction.setGoodsId(goods.getId());
                    reduction.setGoodsName(goods.getName());
                    reduction.setShopName(goods.getShopName());
                    reduction.setShopId(goods.getShopId());
                    reduction.setCategoryId(goods.getCategoryId());
                    reduction.setMainImage(goods.getMainImage());
                    reduction.setStatus(GoodsPriceReductionEnums.TO_BE_ADJUSTED.getCode());
                    reduction.setCreateUser("system job");
                    reduction.setCreateTime(LocalDateTime.now());
                    reduction.setRecommendDiscount(stockOver30daysGoodsIds.contains(goods.getId()) ? stockUpReductionDiscount.toString() : reductionDiscount.toString());
                }

                List<Integer> reasonTypes = Lists.newArrayList();
                if (lowConversionGoodsIds.contains(goods.getId())) {
                    reasonTypes.add(1);
                }
                if (stockOver30daysGoodsIds.contains(goods.getId())) {
                    reasonTypes.add(2);
                }
                reduction.setReasonType(StringUtils.join(reasonTypes,","));

                reduction.setPriceSnapshot(goods.getMinPrice().compareTo(goods.getMaxPrice()) == 0 ? goods.getMinPrice().toString() : goods.getMinPrice().toString() + "~" + goods.getMaxPrice().toString());
                reduction.setUpdateUser("system job");
                reduction.setUpdateTime(LocalDateTime.now());
                goodsPriceReductionSaveList.add(reduction);
            }
            goodsPriceReductionService.saveBatch(goodsPriceReductionSaveList);

            List<SkuReductionPrice> allSkuReductionSaveList = Lists.newArrayList();
            for (GoodsPriceReduction goodsPriceReduction : goodsPriceReductionSaveList) {
                List<GoodsItem> goodsItemList = goodsItemGroupMap.get(goodsPriceReduction.getGoodsId());
                List<SkuReductionPrice> skuReductionSaveList = goodsItemList.stream().map(goodsItem -> {
                    SkuReductionPrice skuReductionPrice = new SkuReductionPrice();
                    skuReductionPrice.setGoodsId(goodsItem.getGoodsId());
                    skuReductionPrice.setSkuId(goodsItem.getSkuId());
                    skuReductionPrice.setIsDel(0);
                    skuReductionPrice.setReductionId(goodsPriceReduction.getId());
                    skuReductionPrice.setCreateTime(LocalDateTime.now());
                    skuReductionPrice.setUpdateTime(LocalDateTime.now());
                    skuReductionPrice.setCreateUser("system job");
                    skuReductionPrice.setUpdateUser("system job");
                    skuReductionPrice.setPrice(goodsItem.getOrginalPrice().multiply(stockOver30daysGoodsIds.contains(goodsPriceReduction.getGoodsId()) ? stockUpReductionDiscount : reductionDiscount).divide(BigDecimal.TEN, 2, BigDecimal.ROUND_HALF_UP));
                    skuReductionPrice.setFreight(goodsItem.getDefaultDelivery());
                    return skuReductionPrice;
                }).collect(Collectors.toList());
                allSkuReductionSaveList.addAll(skuReductionSaveList);
            }
            skuReductionPriceService.saveBatch(allSkuReductionSaveList);

            boolean success = goodsService.lambdaUpdate()
                    .in(Goods::getId, batchGoodsIds)
                    .set(Goods::getIsReduction, 1)
                    .update();
            if (success) {
                Map<String, Object> map = new HashMap<>();
                map.put("isReduction", 1);
                TermsQueryBuilder queryBuilder = QueryBuilders.termsQuery("id", batchGoodsIds);
                goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), queryBuilder, map);
            }
            LogUtils.info(log, "checkGoodsForReductionJob update batch:{} end", batch2);
            batch2++;
        }

        LogUtils.info(log, "checkGoodsForReductionJob end >>>");

        //PUSH降价30%，如果商家同意降价，降价成功的商品自动锁定且7日内不再提醒
        //7日后如果再次命中规则，继续推送降价提醒。
    }

    private String queryCurrentIndex(String index) {
        SearchRequest searchRequest = new SearchRequest(index);
        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            if (searchResponse == null || searchResponse.getHits() == null || searchResponse.getHits().getTotalHits().value == 0) {
                return null;
            }
            for (SearchHit hit : searchResponse.getHits().getHits()) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                if (MapUtils.isEmpty(sourceAsMap)) {
                    continue;
                }
                Object indexName = sourceAsMap.get("indexName");
                if (Objects.isNull(indexName)) {
                    continue;
                }
                return indexName.toString();
            }
        } catch (IOException e) {
            log.error("queryGcrCurrentIndex error", e);
        }

        return null;
    }

    @Override
    public void checkUnReductionGoodsJob() {
        LogUtils.info(log,"checkUnReductionGoodsJob job start...");
        //PUSH降价30%，如果商家同意降价，降价成功的商品自动锁定且7日内不再提醒
        //7日后如果再次命中规则，继续推送降价提醒。
        //2024.3.1 只自动拒绝转化率低的
        List<GoodsPriceReduction> timeOutReductionList = goodsPriceReductionService.lambdaQuery()
//                .le(GoodsPriceReduction::getUpdateTime, LocalDateTime.now().minusDays(reductionInterval))
                .eq(GoodsPriceReduction::getStatus, 1)
                .eq(GoodsPriceReduction::getReasonType, "1")
                .select(GoodsPriceReduction::getId, GoodsPriceReduction::getGoodsId)
                .list();
        LogUtils.info(log, "checkUnReductionGoodsJob 自动拒绝数量:{}", timeOutReductionList.size());
        if (CollectionUtils.isEmpty(timeOutReductionList)) {
            return;
        }

        for (GoodsPriceReduction goodsPriceReduction : timeOutReductionList) {
            goodsPriceReduction.setStatus(-1);
            goodsPriceReduction.setUpdateUser("定时job检测超时，自动拒绝");
            goodsPriceReduction.setUpdateTime(LocalDateTime.now());
        }
        goodsPriceReductionService.updateBatchById(timeOutReductionList);

        List<Long> timeOutGoodsIds = timeOutReductionList.stream().map(GoodsPriceReduction::getGoodsId).distinct().collect(Collectors.toList());
        for (List<Long> goodsIds : Lists.partition(timeOutGoodsIds, 100)) {
            boolean success = goodsService.lambdaUpdate()
                    .in(Goods::getId, goodsIds)
                    .set(Goods::getIsReduction, 0)
                    .update();
            if (success) {
                Map<String, Object> map = new HashMap<>();
                map.put("isReduction", 0);
                TermsQueryBuilder queryBuilder = QueryBuilders.termsQuery("id", goodsIds);
                goodsEsService.updateByQuery(EsEnums.GOODS_ES.getIndex(), queryBuilder, map);
            }
        }
    }
}
