package com.voghion.product.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.colorlight.base.common.redis.RedisApi;
import com.colorlight.base.model.enums.CountryEnums;
import com.colorlight.base.utils.CheckUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.onlest.GoodsSyncModel;
import com.voghion.product.api.dto.BindTagDTO;
import com.voghion.product.api.dto.GoodsOperationLogDto;
import com.voghion.product.api.enums.GoodsEditTypeEnums;
import com.voghion.product.api.enums.OperationLogTypeEnums;
import com.voghion.product.core.*;
import com.voghion.product.model.bo.CommonListingInfoBo;
import com.voghion.product.model.bo.GoodsItemPropertyValueBo;
import com.voghion.product.model.bo.ListingTemplateGoodsBo;
import com.voghion.product.model.bo.SyncListingFollowGoodsBo;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.enums.CustomResultCode;
import com.voghion.product.model.enums.DeliveryTypeEnum;
import com.voghion.product.model.enums.GoodsLockLabelTypEnums;
import com.voghion.product.model.enums.ProductResultCode;
import com.voghion.product.model.po.*;
import com.voghion.product.model.po.goods.*;
import com.voghion.product.model.vo.PropertyGoodsInfoVO;
import com.voghion.product.mq.MqDelayLevel;
import com.voghion.product.mq.MqSender;
import com.voghion.product.service.*;
import com.voghion.product.util.BeanCopyUtil;
import com.voghion.product.util.SnowflakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/6/24 17:43
 */
@Slf4j
@Component
public class ListingGoodsBiz {

    @Resource
    private GoodsService goodsService;

    @Resource
    private GoodsDetailService goodsDetailService;
    @Resource
    private GoodsExtDetailService goodsExtDetailService;

    @Resource
    private GoodsExtDetailImgService goodsExtDetailImgService;

    @Resource
    private GoodsImageService goodsImageService;

    @Resource
    private ProductSkuService productSkuService;

    @Resource
    private GoodsItemService goodsItemService;

    @Resource
    private ProductService productService;

    @Resource
    private PropertyInfoService propertyInfoService;

    @Resource
    private PropertyImgDetailService propertyImgDetailService;

    @Resource
    private MqSender mqSender;

    @Resource
    public RedisApi redisApi;
    @Resource
    private GoodsFreightService goodsFreightService;

    @Autowired
    private PropertyGoodsInfoCoreService propertyGoodsInfoCoreService;

    @Resource
    private ListingInfoService listingInfoService;

    @Resource
    private ListingFollowGoodsService listingFollowGoodsService;

    @Resource
    private GoodsExtConfigService goodsExtConfigService;

    @Resource
    private GoodsEditInfoService goodsEditInfoService;

    @Resource
    private GoodsEditInfoDetailService goodsEditInfoDetailService;

    @Resource
    private GoodsLockInfoService goodsLockInfoService;

    @Resource
    private GoodsExtConfigCoreService goodsExtConfigCoreService;

    @Resource
    private GoodsSkuService goodsSkuService;

    @Resource
    private GoodsPropertyRelevantService goodsPropertyRelevantService;

    @Resource
    private GoodsPropertyImgRelevantService goodsPropertyImgRelevantService;

    @Resource
    private GoodsPropertySkuRelevantService goodsPropertySkuRelevantService;

    @Resource
    private PropertyAssociationService propertyAssociationService;

    @Resource(name = "syncListingFollowGoodsPool")
    private Executor syncListingFollowGoodsPool;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private FaMerchantsApplyCoreService faMerchantsApplyCoreService;

    @Resource
    private GoodsFreightCoreService goodsFreightCoreService;

    public void syncListingFollowGoods(Long listingId) {
        log.info("【listing模版更新&同步更新跟卖商品】listingId:{}", listingId);
        if (listingId == null) {
            return;
        }

        List<Long> followGoodsIds = listingFollowGoodsService.getListingFollowGoods(listingId).stream().map(ListingFollowGoods::getGoodsId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(followGoodsIds)) {
            log.warn("【listing模版更新&同步更新跟卖商品】No merchandise for sale, listingId:{}", listingId);
            return;
        }

        log.info("【listing模版更新&同步更新跟卖商品】followGoodsIds:{}", JSON.toJSONString(followGoodsIds));
        SyncListingFollowGoodsBo syncListingFollowGoodsBo = buildSyncListingFollowGoods(listingId, followGoodsIds);
        transactionTemplate.execute(action -> {
            try {
                syncListingFollowGoods(syncListingFollowGoodsBo);
            } catch (Exception e) {
                log.error("【listing模版更新&同步更新跟卖商品】syncListingFollowGoods error", e);
                action.setRollbackOnly();
            }

            return true;
        });
    }

    private void syncListingFollowGoods(SyncListingFollowGoodsBo syncListingFollowGoodsBo) {
        ListingTemplateGoodsBo listingTemplateGoodsBo = syncListingFollowGoodsBo.getListingTemplateGoodsBo();
        List<GoodsExtConfig> goodsExtConfigList = listingTemplateGoodsBo.getGoodsExtConfigList();
        List<Long> followGoodsIds = syncListingFollowGoodsBo.getFollowGoodsIds();

        // 绑定商品标签信息
        if (CollectionUtils.isNotEmpty(goodsExtConfigList)) {
            List<Long> tagIdList = goodsExtConfigList.stream().map(GoodsExtConfig::getTagId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            BindTagDTO bindTagDTO = new BindTagDTO();
            bindTagDTO.setTagIdList(tagIdList);
            bindTagDTO.setGoodsIds(followGoodsIds);
            goodsExtConfigCoreService.bindGoodsTag(bindTagDTO);
        }

        Map<Long, String> goodsLockLabelMap = goodsLockInfoService.lambdaQuery().in(GoodsLockInfo::getGoodsId, followGoodsIds).eq(GoodsLockInfo::getIsDel, 0).list()
                .stream().collect(Collectors.toMap(GoodsLockInfo::getGoodsId, GoodsLockInfo::getLabel, (v1, v2) -> v1));

        GoodsEditDetailDto goodsEditDetailDto;
        GoodsEditPropertyDto goodsEditPropertyDto;

        Goods listingGoods = listingTemplateGoodsBo.getGoods();
        for (Map.Entry<Long, ListingTemplateGoodsBo> entry : syncListingFollowGoodsBo.getSellAlongGoodsMap().entrySet()) {
            Long goodsId = entry.getKey();
            log.info("【listing模版更新&同步更新跟卖商品】goodsId:{}", goodsId);

            goodsEditDetailDto = new GoodsEditDetailDto();
            goodsEditPropertyDto = new GoodsEditPropertyDto();

            // 跟卖商品
            ListingTemplateGoodsBo sellTemplateGoodsBo = entry.getValue();
            Goods sellGoods = sellTemplateGoodsBo.getGoods();

            // 填充商品修改详情信息
            boolean differentProductNames = !Objects.equals(sellGoods.getName(), listingGoods.getName());
            fillEditDetailInfo(goodsEditDetailDto::setOldName, sellGoods::getName, differentProductNames);
            fillEditDetailInfo(goodsEditDetailDto::setNewName, listingGoods::getName, differentProductNames);

            Product product = sellTemplateGoodsBo.getProduct();
            product.setCategoryId(sellGoods.getCategoryId());
            boolean productSaveSuccess = productService.updateById(product);
            log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} productSaveSuccess:{}", goodsId, productSaveSuccess);

            // 修改produtSku & 删除goodsItem
            modifyProductSkuInfo(sellTemplateGoodsBo, listingTemplateGoodsBo, product, goodsId);

            //goodsItem
            List<GoodsItem> originalGoodsItemList = sellTemplateGoodsBo.getGoodsItems();
            Map<String, Long> originalSkuNameIdMap = originalGoodsItemList.stream().collect(Collectors.toMap(GoodsItem::getName, GoodsItem::getSkuId, (v1, v2) -> v1));
            log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} originalGoodsItemList:{}", goodsId, JSONObject.toJSONString(originalGoodsItemList));

            // 同步新规格和新sku
            Map<String, Long> skuNameAndIdMap = syncNewPropertyAndSku(goodsId, originalSkuNameIdMap,
                    sellTemplateGoodsBo.getGoodsSkuPoList(), listingTemplateGoodsBo.getGoodsSkuPoList(),
                    sellTemplateGoodsBo.getGoodsPropertyRelevantList(), listingTemplateGoodsBo.getGoodsPropertyRelevantList(),
                    sellTemplateGoodsBo.getGoodsPropertyImgRelevantList(), listingTemplateGoodsBo.getGoodsPropertyImgRelevantList(),
                    sellTemplateGoodsBo.getGoodsPropertySkuRelevantList(), listingTemplateGoodsBo.getGoodsPropertySkuRelevantList(),
                    sellTemplateGoodsBo.getGoodsPropertyAssociationList(), listingTemplateGoodsBo.getGoodsPropertyAssociationList());
            log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} skuNameAndIdMap:{}", goodsId, JSONObject.toJSONString(skuNameAndIdMap));

            Integer deliveryType = Optional.ofNullable(sellTemplateGoodsBo.getFaMerchantsApply()).map(FaMerchantsApply::getDeliveryType).orElse(null);
            boolean isWarehouseOversea = Objects.equals(DeliveryTypeEnum.DIRECT.getCode(), deliveryType);
            log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} isWarehouseOversea:{}", goodsId, isWarehouseOversea);

            // 修改商品运费&获取默认运费
            BigDecimal defaultDelivery = modifyGoodsFreightAndObtainDefaultDelivery(isWarehouseOversea, sellGoods, listingTemplateGoodsBo, sellTemplateGoodsBo, goodsId);

            List<GoodsItem> updateGoodsItemList = getUpdateSellGoodsItemList(isWarehouseOversea, listingTemplateGoodsBo, sellTemplateGoodsBo, goodsId, skuNameAndIdMap, defaultDelivery, listingGoods);

            // 是否修改了规格信息
            boolean changeProperty = updateGoodsItemList.stream().anyMatch(goodsItem -> Objects.isNull(goodsItem.getId()));
            log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} changeProperty:{}", goodsId, changeProperty);

            boolean goodsItemSaveSuccess = goodsItemService.saveOrUpdateBatch(updateGoodsItemList);
            log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} goodsItemSaveSuccess:{}", goodsId, goodsItemSaveSuccess);

            // 更新跟卖商品信息
            updateSellGoodsInfo(updateGoodsItemList, sellGoods, listingGoods);

            // 填充商品修改规格信息
            fillEditPropertyInfo(goodsEditPropertyDto, originalGoodsItemList, updateGoodsItemList, goodsId);

            // 填充商品详情描述 & 修改商品详情信息
            fillAndModifySellGoodsDetail(listingTemplateGoodsBo, sellTemplateGoodsBo, goodsEditDetailDto);

            // 填充商品扩展信息 & 修改商品扩展信息
            fillAndModifySellGoodsExtDetail(listingTemplateGoodsBo, sellTemplateGoodsBo, goodsEditDetailDto);

            // 填充商品扩展图片信息 & 修改商品扩展图片信息
            fillAndModifySellGoodsExtDetailImgList(sellTemplateGoodsBo, listingTemplateGoodsBo, goodsEditDetailDto, goodsId);

            // 填充商品主图信息 & 修改商品主图信息
            fillAndModifySellGoodsImgList(sellTemplateGoodsBo, listingTemplateGoodsBo, goodsEditDetailDto, goodsId);

            // 修改商品属性信息
            modifyPropertyGoods(listingTemplateGoodsBo, goodsId);

            // 修改商品属性图片信息
            modifyPropertyImgDetail(sellTemplateGoodsBo, listingTemplateGoodsBo, product);

            // 新增商品修改信息
            saveGoodsEditInfo(goodsLockLabelMap, goodsId, sellGoods, changeProperty, goodsEditPropertyDto, goodsEditDetailDto);

            // 发送商品修改MQ
            sendSyncGoodsTopicUpdate(goodsId);

            // 发送商品修改日志MQ
            sendSyncGoodsOperationLog(goodsId);
        }
    }

    private void sendSyncGoodsOperationLog(Long goodsId) {
        GoodsOperationLogDto goodsOperationLogDto = new GoodsOperationLogDto()
                .traceId(MDC.get("traceId"))
                .goodsId(goodsId)
                .type(OperationLogTypeEnums.UPDATE_GOODS_FROM_SYNC_LISTING)
                .content(OperationLogTypeEnums.UPDATE_GOODS_FROM_SYNC_LISTING.getDesc())
                .status(1)
                .user("system");
        mqSender.send("SYNC_GOODS_OPERATION_LOG", goodsOperationLogDto);
    }

    private void sendSyncGoodsTopicUpdate(Long goodsId) {
        GoodsSyncModel goodsSyncModel = new GoodsSyncModel();
        goodsSyncModel.setGoodsId(goodsId);
        goodsSyncModel.setSyncTime(System.currentTimeMillis());
        goodsSyncModel.setBusiness("listing模板更新同步跟卖商品");
        goodsSyncModel.setSourceService("vp");
        mqSender.sendDelay("SYNC_GOODS_TOPIC_UPDATE", JSON.toJSONString(goodsSyncModel), MqDelayLevel.ONE_MIN);
    }

    private void saveGoodsEditInfo(Map<Long, String> goodsLockLabelMap, Long goodsId, Goods sellGoods, boolean changeProperty, GoodsEditPropertyDto goodsEditPropertyDto, GoodsEditDetailDto goodsEditDetailDto) {
        List<String> specialTagList = getSpecialTagList(goodsLockLabelMap, goodsId);
        GoodsEditInfo editInfo = new GoodsEditInfo();
        editInfo.setGoodsId(goodsId);
        editInfo.setStatus(0);
        editInfo.setCategoryId(sellGoods.getCategoryId());
        editInfo.setShopId(sellGoods.getShopId());
        editInfo.setShopName(sellGoods.getShopName());
        editInfo.setAuditUser("system");
        editInfo.setAuditTime(LocalDateTime.now());
        editInfo.setStatus(1);
        editInfo.setApplyReason("所属listing模板修改,同步商品信息");
        editInfo.setApplyUser("system");
        editInfo.setApplyTime(LocalDateTime.now());
        editInfo.setIsDel(0);
        editInfo.setType(changeProperty ? GoodsEditTypeEnums.PROPERTY.getCode() : GoodsEditTypeEnums.DETAIL.getCode());
        editInfo.setContent(changeProperty ? JSON.toJSONString(goodsEditPropertyDto) : JSON.toJSONString(goodsEditDetailDto));
        editInfo.setSpecialTag(specialTagList.stream().sorted().collect(Collectors.joining(",")));
        editInfo.setUpdateTime(LocalDateTime.now());
        boolean goodsEditInfoSaveSuccess = goodsEditInfoService.save(editInfo);
        log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} goodsEditInfoSaveSuccess:{}", goodsId, goodsEditInfoSaveSuccess);

        GoodsEditInfoDetail goodsEditInfoDetail = new GoodsEditInfoDetail();
        goodsEditInfoDetail.setEditId(editInfo.getId());
        goodsEditInfoDetail.setGoodsId(editInfo.getGoodsId());
        goodsEditInfoDetail.setContent(changeProperty ? JSON.toJSONString(goodsEditPropertyDto) : JSON.toJSONString(goodsEditDetailDto));
        boolean goodsEditInfoDetailSaveSuccess = goodsEditInfoDetailService.save(goodsEditInfoDetail);
        log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} goodsEditInfoDetailSaveSuccess:{}", goodsId, goodsEditInfoDetailSaveSuccess);
    }

    private static List<String> getSpecialTagList(Map<Long, String> goodsLockLabelMap, Long goodsId) {
        List<String> specialTagList = Lists.newArrayList();
        String lockLabel = goodsLockLabelMap.get(goodsId);
        if (lockLabel != null) {
            specialTagList = GoodsLockLabelTypEnums.listNamesByCodes(lockLabel);
        } else {
            specialTagList.add("listing");
        }
        return specialTagList;
    }

    private void modifyPropertyImgDetail(ListingTemplateGoodsBo sellTemplateGoodsBo, ListingTemplateGoodsBo listingTemplateGoodsBo, Product product) {
        List<PropertyImgDetail> propertyImgDetails = sellTemplateGoodsBo.getPropertyImgDetails();
        if (CollectionUtils.isNotEmpty(propertyImgDetails)) {
            propertyImgDetailService.deleteByIds(propertyImgDetails.stream().map(PropertyImgDetail::getId).collect(Collectors.toList()));
        }

        List<PropertyImgDetail> listingPropertyImgDetails = listingTemplateGoodsBo.getPropertyImgDetails();
        if (CollectionUtils.isNotEmpty(listingPropertyImgDetails)) {
            List<PropertyImgDetail> propertyImgDetailList = BeanCopyUtil.transformList(listingPropertyImgDetails, PropertyImgDetail.class);
            for (PropertyImgDetail propertyImgDetail : propertyImgDetailList) {
                propertyImgDetail.setId(null);
                propertyImgDetail.setSpuId(product.getId());
                propertyImgDetail.setCreateTime(new Date());
            }

            boolean propertyImgDetailSaveSuccess = propertyImgDetailService.saveBatch(propertyImgDetailList);
            log.info("【listing模版更新&同步更新跟卖商品】productId:{} propertyImgDetailSaveSuccess:{}", product.getId(), propertyImgDetailSaveSuccess);
        }
    }

    private void modifyPropertyGoods(ListingTemplateGoodsBo listingTemplateGoodsBo, Long goodsId) {
        List<PropertyGoodsInfoVO> listingPropertyGoodsInfoVOS = listingTemplateGoodsBo.getPropertyGoodsInfoList();
        if (CollectionUtils.isNotEmpty(listingPropertyGoodsInfoVOS)) {
            listingPropertyGoodsInfoVOS.forEach(propertyGoodsInfoVO -> propertyGoodsInfoVO.setGoodsId(goodsId));
            propertyGoodsInfoCoreService.saveOrUpdatePropertyGoods(listingPropertyGoodsInfoVOS);
            log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} propertyGoodsInfoVOS:{}", goodsId, JSONObject.toJSONString(listingPropertyGoodsInfoVOS));
        }
    }

    private void fillAndModifySellGoodsImgList(ListingTemplateGoodsBo sellTemplateGoodsBo, ListingTemplateGoodsBo listingTemplateGoodsBo, GoodsEditDetailDto goodsEditDetailDto, Long goodsId) {
        List<GoodsImage> sellGoodsImageList = sellTemplateGoodsBo.getGoodsImages();
        List<GoodsImage> listingGoodsImageList = listingTemplateGoodsBo.getGoodsImages();

        List<String> oldGoodsImages = sellGoodsImageList.stream().filter(goodsImage -> goodsImage.getImageType() == 1).map(GoodsImage::getUrl).collect(Collectors.toList());
        List<String> newGoodsImages = listingGoodsImageList.stream().filter(goodsImage -> goodsImage.getImageType() == 1).map(GoodsImage::getUrl).collect(Collectors.toList());
        boolean differentGoodsImages = CollectionUtils.isNotEmpty(CollectionUtils.disjunction(oldGoodsImages, newGoodsImages));
        fillEditDetailInfo(goodsEditDetailDto::setOldGoodsImages, () -> oldGoodsImages, differentGoodsImages);
        fillEditDetailInfo(goodsEditDetailDto::setNewGoodsImages, () -> newGoodsImages, differentGoodsImages);

        List<String> oldGoodsVideos = sellGoodsImageList.stream().filter(goodsImage -> goodsImage.getImageType() == 2).map(GoodsImage::getUrl).collect(Collectors.toList());
        List<String> newGoodsVideos = listingGoodsImageList.stream().filter(goodsImage -> goodsImage.getImageType() == 2).map(GoodsImage::getUrl).collect(Collectors.toList());
        boolean differentGoodsVideos = CollectionUtils.isNotEmpty(CollectionUtils.disjunction(oldGoodsVideos, newGoodsVideos));
        fillEditDetailInfo(goodsEditDetailDto::setOldGoodsImages, () -> oldGoodsVideos, differentGoodsVideos);
        fillEditDetailInfo(goodsEditDetailDto::setNewGoodsImages, () -> newGoodsVideos, differentGoodsVideos);

        modifySellGoodsImgList(goodsId, listingGoodsImageList);
    }

    private void modifySellGoodsImgList(Long goodsId, List<GoodsImage> listingGoodsImageList) {
        goodsImageService.deleteByGoodsId(goodsId);
        List<GoodsImage> goodsImageList = BeanCopyUtil.transformList(listingGoodsImageList, GoodsImage.class);
        goodsImageList.forEach(goodsImage -> {
            goodsImage.setId(null);
            goodsImage.setGoodsId(goodsId);
            goodsImage.setCreateTime(LocalDateTime.now());
        });

        boolean saveGoodsImageSuccess = goodsImageService.saveBatch(goodsImageList);
        log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} saveGoodsImageSuccess:{}", goodsId, saveGoodsImageSuccess);
    }

    private void fillAndModifySellGoodsExtDetailImgList(ListingTemplateGoodsBo sellTemplateGoodsBo, ListingTemplateGoodsBo listingTemplateGoodsBo, GoodsEditDetailDto goodsEditDetailDto, Long goodsId) {
        // 填充商品修改详情图片信息
        List<String> sellGoodsExtDetailImgList = sellTemplateGoodsBo.getGoodsExtDetailImgList().stream().map(GoodsExtDetailImg::getImgUrl).collect(Collectors.toList());
        List<String> listingGoodsExtDetailImgList = listingTemplateGoodsBo.getGoodsExtDetailImgList().stream().map(GoodsExtDetailImg::getImgUrl).collect(Collectors.toList());
        boolean differentExtDetailImg = CollectionUtils.isNotEmpty(CollectionUtils.disjunction(sellGoodsExtDetailImgList, listingGoodsExtDetailImgList));
        fillEditDetailInfo(goodsEditDetailDto::setOldDetailsImgs, () -> sellGoodsExtDetailImgList, differentExtDetailImg);
        fillEditDetailInfo(goodsEditDetailDto::setNewDetailsImgs, () -> listingGoodsExtDetailImgList, differentExtDetailImg);

        // 填充商品修改详情图片信息
        modifySellGoodsExtDetailImgList(goodsId, listingGoodsExtDetailImgList);
    }

    private void modifySellGoodsExtDetailImgList(Long goodsId, List<String> listingGoodsExtDetailImgList) {
        goodsExtDetailImgService.deleteByGoodsId(goodsId);
        List<GoodsExtDetailImg> goodsExtDetailImgs = BeanCopyUtil.transformList(listingGoodsExtDetailImgList, GoodsExtDetailImg.class);
        goodsExtDetailImgs.forEach(goodsExtDetailImg -> {
            goodsExtDetailImg.setId(null);
            goodsExtDetailImg.setGoodsId(goodsId);
            goodsExtDetailImg.setCreateTime(LocalDate.now());
        });

        boolean saveGoodsExtDetailImgSuccess = goodsExtDetailImgService.saveBatch(goodsExtDetailImgs);
        log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} saveGoodsExtDetailImgSuccess:{}", goodsId, saveGoodsExtDetailImgSuccess);
    }

    private void fillAndModifySellGoodsExtDetail(ListingTemplateGoodsBo listingTemplateGoodsBo, ListingTemplateGoodsBo sellTemplateGoodsBo, GoodsEditDetailDto goodsEditDetailDto) {
        GoodsExtDetail listingGoodsExtDetail = listingTemplateGoodsBo.getGoodsExtDetail();
        // 填充商品扩展商品编码信息
        GoodsExtDetail sellGoodsExtDetail = sellTemplateGoodsBo.getGoodsExtDetail();
        boolean differentItemCode = !StringUtils.equals(sellGoodsExtDetail.getItemCode(), listingGoodsExtDetail.getItemCode());
        fillEditDetailInfo(goodsEditDetailDto::setOldItemCode, sellGoodsExtDetail::getItemCode, differentItemCode);
        fillEditDetailInfo(goodsEditDetailDto::setOldItemCode, listingGoodsExtDetail::getItemCode, differentItemCode);

        // 修改商品扩展详情
        modifySellGoodsExtDetail(sellGoodsExtDetail, listingGoodsExtDetail);
    }

    private void modifySellGoodsExtDetail(GoodsExtDetail sellGoodsExtDetail, GoodsExtDetail listingGoodsExtDetail) {
        sellGoodsExtDetail.setWeight(listingGoodsExtDetail.getWeight());
        sellGoodsExtDetail.setPackageSize(listingGoodsExtDetail.getPackageSize());
        sellGoodsExtDetail.setItemCode(listingGoodsExtDetail.getItemCode());
        boolean updateGoodsExtDetailSuccess = goodsExtDetailService.updateById(sellGoodsExtDetail);
        log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} updateGoodsExtDetailSuccess:{}", sellGoodsExtDetail.getGoodsId(), updateGoodsExtDetailSuccess);
    }

    private void fillAndModifySellGoodsDetail(ListingTemplateGoodsBo listingTemplateGoodsBo, ListingTemplateGoodsBo sellTemplateGoodsBo, GoodsEditDetailDto goodsEditDetailDto) {
        GoodsDetail listingGoodsDetail = listingTemplateGoodsBo.getGoodsDetail();
        // 填充商品修改详情描述信息
        GoodsDetail sellGoodsDetail = sellTemplateGoodsBo.getGoodsDetail();
        boolean differentDescription = !sellGoodsDetail.getDescription().equals(listingGoodsDetail.getDescription());
        fillEditDetailInfo(goodsEditDetailDto::setOldDescription, sellGoodsDetail::getDescription, differentDescription);
        fillEditDetailInfo(goodsEditDetailDto::setNewDescription, listingGoodsDetail::getDescription, differentDescription);

        // 修改详情信息
        modifySellGoodsDetail(sellGoodsDetail, listingGoodsDetail);
    }

    private void modifySellGoodsDetail(GoodsDetail sellGoodsDetail, GoodsDetail listingGoodsDetail) {
        sellGoodsDetail.setTitle(listingGoodsDetail.getTitle());
        sellGoodsDetail.setDescription(listingGoodsDetail.getDescription());
        boolean updateGoodsDetailSuccess = goodsDetailService.updateById(sellGoodsDetail);
        log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} updateGoodsDetailSuccess:{}", sellGoodsDetail.getGoodsId(), updateGoodsDetailSuccess);
    }

    @NotNull
    private BigDecimal modifyGoodsFreightAndObtainDefaultDelivery(boolean isWarehouseOversea, Goods sellGoods, ListingTemplateGoodsBo listingTemplateGoodsBo, ListingTemplateGoodsBo sellTemplateGoodsBo, Long goodsId) {

        if (isWarehouseOversea) {
            return BigDecimal.ZERO;
        }

        // 获取跟卖商品运费信息
        List<GoodsFreight> goodsFreightList = getGoodsFreights(sellGoods, listingTemplateGoodsBo, sellTemplateGoodsBo, goodsId);

        BigDecimal defaultDelivery = BigDecimal.ZERO;
        // 保存跟卖运费信息 & 获取默认运费信息
        if (CollectionUtils.isNotEmpty(goodsFreightList)) {
            boolean goodsFreightSaveSuccess = goodsFreightService.saveOrUpdateBatch(goodsFreightList);
            log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} goodsFreightSaveSuccess:{}", goodsId, goodsFreightSaveSuccess);
            defaultDelivery = getDefaultDelivery(goodsFreightList);
        }

        log.info("【listing模版更新&同步更新跟卖商品】goodsId:{} defaultDelivery:{}", goodsId, defaultDelivery);
        return defaultDelivery;
    }

    private static BigDecimal getDefaultDelivery(List<GoodsFreight> goodsFreightList) {
        GoodsFreight deFreight = goodsFreightList.stream()
                .filter(goodsFreight1 -> goodsFreight1.getIsDel() == 0 && goodsFreight1.getCode().equals(CountryEnums.DE.getCode()))
                .findAny()
                .orElse(null);
        if (deFreight != null) {
            return deFreight.getCurrentFreight();
        }

        return goodsFreightList.stream()
                .filter(ex -> ex.getIsDel() == 0)
                .map(GoodsFreight::getCurrentFreight)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }

    private void fillEditPropertyInfo(GoodsEditPropertyDto goodsEditPropertyDto, List<GoodsItem> originalGoodsItemList, List<GoodsItem> updateGoodsItemList, Long goodsId) {
        try {
            goodsEditPropertyDto.setOldPropertyList(getPropertyDTOS(originalGoodsItemList));
            goodsEditPropertyDto.setNewPropertyList(getPropertyDTOS(updateGoodsItemList));
            goodsEditPropertyDto.setOldSkuList(BeanCopyUtil.transformList(originalGoodsItemList, GoodsItemDTO.class));
            goodsEditPropertyDto.setNewSkuList(BeanCopyUtil.transformList(updateGoodsItemList, GoodsItemDTO.class));
        } catch (Exception e) {
            log.error("【listing模版更新&同步更新跟卖商品】填充修改规格信息，goodsId:{} error:{}", goodsId, e);
        }
    }

    @NotNull
    private List<PropertyDTO> getPropertyDTOS(List<GoodsItem> goodsItems) {
        if (CollectionUtils.isEmpty(goodsItems)) {
            return Collections.emptyList();
        }

        Map<Long, List<Long>> propertyValueMap = getPropertyValueMap(goodsItems);
        List<Long> propertyIds = Lists.newArrayList();
        List<Long> propertyValueIds = Lists.newArrayList();

        propertyValueMap.forEach((propertyId, propertyValueIdList) -> {
            propertyIds.add(propertyId);
            propertyValueIds.addAll(propertyValueIdList);
        });

        List<Property> propertyList = propertyInfoService.queryPropertyByIds(propertyIds);
        if (CollectionUtils.isEmpty(propertyList)) {
            return Collections.emptyList();
        }

        Map<Long, List<PropertyValue>> propertyValueGroupMap = propertyInfoService.queryPropertyValueByIds(propertyValueIds).stream().collect(Collectors.groupingBy(PropertyValue::getPropertyId));
        return propertyList.stream()
                .map(property -> {
                            PropertyDTO propertyDTO = BeanCopyUtil.transform(property, PropertyDTO.class);
                            List<PropertyValue> propertyValues = propertyValueGroupMap.get(property.getId());
                            if (CollectionUtils.isEmpty(propertyValues)) {
                                return propertyDTO;
                            }

                            List<PropertyValueDTO> propertyValueDTOList = propertyValues.stream().map(propertyValue -> BeanCopyUtil.transform(propertyValue, PropertyValueDTO.class)).collect(Collectors.toList());
                            propertyDTO.setValues(propertyValueDTOList);
                            return propertyDTO;
                        }
                ).collect(Collectors.toList());
    }

    @NotNull
    private static Map<Long, List<Long>> getPropertyValueMap(List<GoodsItem> updateGoodsItemList) {
        Map<Long, List<Long>> newPropertyValueMap = Maps.newHashMap();
        for (GoodsItem goodsItem : updateGoodsItemList) {
            if (StringUtils.isBlank(goodsItem.getPvalueStr())) {
                continue;
            }

            for (String s : goodsItem.getPvalueStr().split(";")) {
                if (StringUtils.isBlank(s)) {
                    continue;
                }

                String[] pvalueArr = s.split(":");
                if (pvalueArr.length != 2) {
                    continue;
                }

                Long propertyId = Long.parseLong(pvalueArr[0]);
                Long propertyValueId = Long.parseLong(pvalueArr[1]);
                newPropertyValueMap.computeIfAbsent(propertyId, k -> Lists.newArrayList()).add(propertyValueId);
            }
        }

        return newPropertyValueMap;
    }

    private void updateSellGoodsInfo(List<GoodsItem> updateGoodsItemList, Goods sellGoods, Goods listingGoods) {
        updateGoodsItemList.stream().map(GoodsItem::getPrice).min(BigDecimal::compareTo).ifPresent(sellGoods::setMinPrice);
        updateGoodsItemList.stream().map(GoodsItem::getPrice).max(BigDecimal::compareTo).ifPresent(sellGoods::setMaxPrice);
        sellGoods.setName(listingGoods.getName());
        sellGoods.setMainImage(listingGoods.getMainImage());
        sellGoods.setCategoryId(listingGoods.getCategoryId());
        sellGoods.setLogisticsProperty(listingGoods.getLogisticsProperty());
        sellGoods.setUpdateTime(LocalDateTime.now());
        boolean goodsSaveSuccess = goodsService.updateById(sellGoods);
        log.info("【listing模版更新&同步更新跟卖商品】goodsSaveSuccess:{} goodsId:{}", goodsSaveSuccess, sellGoods.getId());
    }

    @NotNull
    private static List<GoodsItem> getUpdateSellGoodsItemList(boolean isWarehouseOversea, ListingTemplateGoodsBo listingTemplateGoodsBo, ListingTemplateGoodsBo sellTemplateGoodsBo, Long goodsId, Map<String, Long> skuNameAndIdMap, BigDecimal defaultDelivery, Goods listingGoods) {
        List<GoodsItem> updateGoodsItemList = Lists.newArrayList();

        Map<String, GoodsItem> reorderingGoodsItemMap = getReorderingSkuMap(listingTemplateGoodsBo.getGoodsItems(), GoodsItem::getPvalueDesc);
        Map<String, GoodsItem> reorderingSellGoodsItemMap = getReorderingSkuMap(sellTemplateGoodsBo.getGoodsItems(), GoodsItem::getPvalueDesc);
        for (Map.Entry<String, GoodsItem> itemEntry : reorderingGoodsItemMap.entrySet()) {
            String listingPvalueDesc = itemEntry.getKey();
            GoodsItem listingGoodsItem = itemEntry.getValue();
            GoodsItem goodsItem = reorderingSellGoodsItemMap.get(listingPvalueDesc);
            if (goodsItem == null) {
                goodsItem = new GoodsItem();
                BeanCopyUtil.copyProperties(listingGoodsItem, goodsItem);
                goodsItem.setId(null);
                goodsItem.setGoodsId(goodsId);
                goodsItem.setSkuId(skuNameAndIdMap.get(goodsItem.getName()));
                goodsItem.setCreateTime(LocalDateTime.now());
                goodsItem.setStock(0L);
            }

            goodsItem.setWeight(listingGoodsItem.getWeight());
            goodsItem.setPvalueStr(listingGoodsItem.getPvalueStr());
            goodsItem.setPvalueDesc(listingGoodsItem.getPvalueDesc());
            goodsItem.setPropertyValueRecordSnap(listingGoodsItem.getPropertyValueRecordSnap().replace(String.valueOf(listingGoods.getId()), String.valueOf(goodsId)));
            goodsItem.setName(listingGoodsItem.getName());
            goodsItem.setSkuImage(listingGoodsItem.getSkuImage());
            goodsItem.setUpdateTime(new Date());
            if (!isWarehouseOversea) {
                goodsItem.setDefaultDelivery(defaultDelivery);
                goodsItem.setPrice(goodsItem.getOrginalPrice().add(defaultDelivery));
            }

            updateGoodsItemList.add(goodsItem);
        }
        return updateGoodsItemList;
    }

    private void modifyProductSkuInfo(ListingTemplateGoodsBo sellTemplateGoodsBo, ListingTemplateGoodsBo listingTemplateGoodsBo, Product product, Long goodsId) {
        List<ProductSku> sellProductSkuList = sellTemplateGoodsBo.getProductSkuList();
        // key为pvalueDesc
        Map<String, ProductSku> pvalueDescProductSkuMap = sellProductSkuList.stream().collect(Collectors.toMap(ProductSku::getPvalueDesc, Function.identity(), (v1, v2) -> v1));
        // key为pvalueDesc重排序后组装
        Map<String, ProductSku> reorderingProductSkuMap = getReorderingSkuMap(sellProductSkuList, ProductSku::getPvalueDesc);

        // 老结构 产品sku更新和删除
        List<ProductSku> updateProductSkuList = getUpdateProductSkuList(listingTemplateGoodsBo, pvalueDescProductSkuMap, reorderingProductSkuMap, product);

        List<GoodsItem> sellGoodsItems = sellTemplateGoodsBo.getGoodsItems();
        Map<String, GoodsItem> originalGoodsItemPvalueDescMap = sellGoodsItems.stream().filter(goodsItemDTO -> StringUtils.isNotBlank(goodsItemDTO.getPvalueStr()))
                .collect(Collectors.toMap(GoodsItem::getPvalueDesc, Function.identity(), (v1, v2) -> v1));

        // 保留的产品skuId列表
        List<Long> reservedSkuIds = updateProductSkuList.stream().map(ProductSku::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        List<Long> delProductSkuIdList = new ArrayList<>();
        List<Long> delGoodsSkuIdList = new ArrayList<>();
        for (ProductSku oldSku : sellProductSkuList) {
            if (reservedSkuIds.contains(oldSku.getId())) {
                continue;
            }

            String pvalueDesc = oldSku.getPvalueDesc();
            delProductSkuIdList.add(oldSku.getId());
            GoodsItem goodsItem = originalGoodsItemPvalueDescMap.get(pvalueDesc);
            if (goodsItem != null) {
                delGoodsSkuIdList.add(goodsItem.getSkuId());
            }
        }

        boolean productSkuSaveSuccess = productSkuService.saveOrUpdateBatch(updateProductSkuList);
        log.info("【listing模版更新&同步更新产品sku】productSkuSaveSuccess:{} goodsId:{} productId:{}", productSkuSaveSuccess, goodsId, product.getId());

        if (CollectionUtils.isNotEmpty(delProductSkuIdList)) {
            ProductSkuDTO skuBO = new ProductSkuDTO();
            skuBO.setIds(delProductSkuIdList);
            boolean delProductSkuSuccess = productSkuService.deleteProductSkuByOption(skuBO);
            log.info("【listing模版更新&同步删除产品sku】delProductSkuSuccess:{} goodsId:{} delProductSkuIdList:{}", delProductSkuSuccess, goodsId, JSON.toJSONString(delProductSkuIdList));
        }

        if (CollectionUtils.isNotEmpty(delGoodsSkuIdList)) {
            GoodsItemDTO goodsItemInput = new GoodsItemDTO();
            goodsItemInput.setSkuIds(delGoodsSkuIdList);
            goodsItemInput.setGoodsId(goodsId);
            boolean delGoodsSkuSuccess = goodsItemService.deleteGoodsItemByOption(goodsItemInput);
            log.info("【listing模版更新&同步删除商品sku】delGoodsSkuSuccess:{} goodsId:{} delGoodsSkuIdList:{}", delGoodsSkuSuccess, goodsId, JSON.toJSONString(delGoodsSkuIdList));
        }
    }

    @NotNull
    private static List<ProductSku> getUpdateProductSkuList(ListingTemplateGoodsBo listingTemplateGoodsBo, Map<String, ProductSku> pvalueDescProductSkuMap, Map<String, ProductSku> reorderingProductSkuMap, Product product) {
        List<ProductSku> updateProductSkuList = Lists.newArrayList();
        for (ProductSku listingProductSku : listingTemplateGoodsBo.getProductSkuList()) {
            ProductSku productSku = getProductSku(listingProductSku, pvalueDescProductSkuMap, reorderingProductSkuMap);
            if (productSku == null) {
                productSku = new ProductSku();
                BeanCopyUtil.copyProperties(listingProductSku, productSku);
                productSku.setId(null);
                productSku.setProductId(product.getId());
                productSku.setCreateTime(LocalDateTime.now());
            }

            productSku.setIsDel(0);
            productSku.setName(listingProductSku.getName());
            productSku.setPvalueStr(listingProductSku.getPvalueStr());
            productSku.setPvalueDesc(listingProductSku.getPvalueDesc());
            updateProductSkuList.add(productSku);
        }

        return updateProductSkuList;
    }

    @NotNull
    private static <T> Map<String, T> getReorderingSkuMap(List<T> sellProductSkuList, Function<T, String> func) {
        return sellProductSkuList.stream().collect(Collectors.toMap((sku) -> {
            List<String> pvalueList = Lists.newArrayList(func.apply(sku).split("&gt;"));
            Collections.sort(pvalueList);
            return String.join("&gt;", pvalueList);
        }, Function.identity(), (v1, v2) -> v1));
    }

    @Nullable
    private static ProductSku getProductSku(ProductSku listingProductSku, Map<String, ProductSku> pvalueDescProductSkuMap, Map<String, ProductSku> reorderingProductSkuMap) {
        ProductSku productSku = pvalueDescProductSkuMap.get(listingProductSku.getPvalueDesc());
        if (productSku == null) {
            // 如果直接匹配失败，则尝试将字符串按'&gt;'分割后排序再比较
            String[] listingParts = listingProductSku.getPvalueDesc().split("&gt;");
            List<String> listingPartList = Arrays.asList(listingParts);
            Collections.sort(listingPartList);

            productSku = reorderingProductSkuMap.get(String.join("&gt;", listingPartList));
        }

        return productSku;
    }

    @NotNull
    private List<GoodsFreight> getGoodsFreights(Goods sellGoods, ListingTemplateGoodsBo listingTemplateGoodsBo, ListingTemplateGoodsBo sellTemplateGoodsBo, Long goodsId) {
        List<String> countryList = Lists.newArrayList(sellGoods.getCountry().split(","));
        List<GoodsFreight> goodsFreightList = Optional.ofNullable(sellTemplateGoodsBo.getGoodsFreightList()).orElse(Lists.newArrayList());
        Map<String, GoodsFreight> goodsFreightMap = goodsFreightList.stream().collect(Collectors.toMap(GoodsFreight::getCode, Function.identity(), (v1, v2) -> v1));

        List<Long> tagIdList = Optional.ofNullable(sellTemplateGoodsBo.getGoodsExtConfigList()).map(list -> list.stream().map(GoodsExtConfig::getTagId).distinct().collect(Collectors.toList())).orElse(Collections.emptyList());
        String weight = listingTemplateGoodsBo.getGoodsExtDetail().getWeight();
        List<GoodsFreight> newFreightList = goodsFreightCoreService.countAndInitFreight(sellGoods, weight, tagIdList);

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(newFreightList)) {
            for (GoodsFreight newFreight : newFreightList) {
                GoodsFreight goodsFreight = goodsFreightMap.get(newFreight.getCode());
                if (goodsFreight == null) {
                    goodsFreight = BeanCopyUtil.transform(newFreight, GoodsFreight.class);
                    goodsFreightList.add(goodsFreight);
                }
                goodsFreight.setGoodsId(goodsId);
                goodsFreight.setCurrentFreight(newFreight.getCurrentFreight());
                goodsFreight.setUpdateTime(LocalDateTime.now());
            }

            goodsFreightList.forEach(goodsFreight -> {
                if (!countryList.contains(goodsFreight.getCode())) {
                    goodsFreight.setIsDel(1);
                    goodsFreight.setUpdateTime(LocalDateTime.now());
                }
            });
        } else {
            goodsFreightList.forEach(goodsFreight -> {
                goodsFreight.setIsDel(1);
                goodsFreight.setUpdateTime(LocalDateTime.now());
            });
        }

        return goodsFreightList;
    }

    private <T> void fillEditDetailInfo(Consumer<T> consumer, Supplier<T> supplier, boolean condition) {
        if (!condition) {
            return;
        }

        consumer.accept(supplier.get());
    }

    private SyncListingFollowGoodsBo buildSyncListingFollowGoods(Long listingId, List<Long> followGoodsIds) {
        SyncListingFollowGoodsBo followGoodsBo = new SyncListingFollowGoodsBo();
        // 填充listing模版商品信息 & 基础信息
        fillListingTemplateInfo(listingId, followGoodsBo);

        // 填充跟卖商品信息
        fillSellGoodsInfo(followGoodsIds, followGoodsBo);
        return followGoodsBo;
    }

    private void fillSellGoodsInfo(List<Long> followGoodsIds, SyncListingFollowGoodsBo followGoodsBo) {
        List<Goods> goodsList = goodsService.lambdaQuery().in(Goods::getId, followGoodsIds).eq(Goods::getIsDel, 0).list();
        List<Long> finalFollowGoodsIds = goodsList.stream().map(Goods::getId).collect(Collectors.toList());
        List<Long> productIds = goodsList.stream().map(Goods::getProductId).collect(Collectors.toList());
        List<Long> shopIds = goodsList.stream().map(Goods::getShopId).distinct().collect(Collectors.toList());

        CompletableFuture<Map<Long, List<GoodsItem>>> goodsItemMapFuture = CompletableFuture.supplyAsync(() -> getListMap(goodsItemService::queryGoodsIdsList, finalFollowGoodsIds, GoodsItem::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<GoodsSkuPo>>> goodsSkuMapFuture = CompletableFuture.supplyAsync(() -> getListMap(goodsSkuService::findListByGoodsIds, finalFollowGoodsIds, GoodsSkuPo::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<GoodsPropertyRelevantPo>>> goodsPropertyRelevantMapFuture = CompletableFuture.supplyAsync(() -> getListMap(goodsPropertyRelevantService::findListByGoodsIds, finalFollowGoodsIds, GoodsPropertyRelevantPo::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<GoodsPropertyImgRelevantPo>>> goodsPropertyImgRelevantMapFuture = CompletableFuture.supplyAsync(() -> getListMap(goodsPropertyImgRelevantService::findListByGoodsIds, finalFollowGoodsIds, GoodsPropertyImgRelevantPo::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<GoodsPropertySkuRelevantPo>>> goodsPropertySkuRelevantMapFuture = CompletableFuture.supplyAsync(() -> getListMap(goodsPropertySkuRelevantService::findListByGoodsIds, finalFollowGoodsIds, GoodsPropertySkuRelevantPo::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<PropertyAssociationPo>>> propertyAssociationMapFuture = CompletableFuture.supplyAsync(() -> getListMap(propertyAssociationService::findListByGoodsIds, finalFollowGoodsIds, PropertyAssociationPo::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, Product>> productMapFuture = CompletableFuture.supplyAsync(() -> getMap(productService::findListByProductIds, productIds, Product::getId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<ProductSku>>> productSkuMapFuture = CompletableFuture.supplyAsync(() -> getListMap(productSkuService::findListByProductIds, productIds, ProductSku::getProductId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, GoodsDetail>> goodsDetailMapFuture = CompletableFuture.supplyAsync(() -> getMap(goodsDetailService::queryGoodsDetailByGoodsIds, followGoodsIds, GoodsDetail::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, GoodsExtDetail>> goodsExtDetailMapFuture = CompletableFuture.supplyAsync(() -> getMap(goodsExtDetailService::queryGoodsExtDetailByGoodsIds, followGoodsIds, GoodsExtDetail::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<PropertyImgDetail>>> propertyImgDetailMapFuture = CompletableFuture.supplyAsync(() -> getListMap(propertyImgDetailService::findListByProductIds, productIds, PropertyImgDetail::getSpuId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<GoodsFreight>>> goodsFreightMapFuture = CompletableFuture.supplyAsync(() -> getListMap(goodsFreightService::queryByGoodsIds, followGoodsIds, GoodsFreight::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<GoodsExtDetailImg>>> goodsExtDetailImgMapFuture = CompletableFuture.supplyAsync(() -> getListMap(goodsExtDetailImgService::queryListByGoodsIds, followGoodsIds, GoodsExtDetailImg::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<GoodsImage>>> goodsImgMapFuture = CompletableFuture.supplyAsync(() -> getListMap(goodsImageService::queryListByGoodsIds, finalFollowGoodsIds, GoodsImage::getGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, FaMerchantsApply>> faMerchantsApplyMapFuture = CompletableFuture.supplyAsync(() -> getMap(faMerchantsApplyCoreService::queryByShopIds, shopIds, FaMerchantsApply::getId), syncListingFollowGoodsPool);
        CompletableFuture<Map<Long, List<GoodsExtConfig>>> goodsExtConfigMapFuture = CompletableFuture.supplyAsync(() -> getListMap(goodsExtConfigService::selectByGoodsIds, finalFollowGoodsIds, GoodsExtConfig::getGoodsId), syncListingFollowGoodsPool);

        CompletableFuture.allOf(goodsItemMapFuture, goodsSkuMapFuture, goodsPropertyRelevantMapFuture, goodsPropertyImgRelevantMapFuture, goodsPropertySkuRelevantMapFuture, propertyAssociationMapFuture, productMapFuture, productSkuMapFuture, goodsDetailMapFuture, goodsExtDetailMapFuture, propertyImgDetailMapFuture, goodsFreightMapFuture, goodsExtDetailImgMapFuture, goodsExtConfigMapFuture, goodsImgMapFuture, faMerchantsApplyMapFuture).join();

        Map<Long, ListingTemplateGoodsBo> sellAlongGoodsMap = Maps.newHashMap();
        for (Goods goods : goodsList) {
            ListingTemplateGoodsBo templateGoodsBo = new ListingTemplateGoodsBo();
            templateGoodsBo.setGoods(goods);
            templateGoodsBo.setProduct(productMapFuture.join().getOrDefault(goods.getProductId(), null));
            templateGoodsBo.setGoodsDetail(goodsDetailMapFuture.join().getOrDefault(goods.getId(), null));
            templateGoodsBo.setGoodsExtDetail(goodsExtDetailMapFuture.join().getOrDefault(goods.getId(), null));
            templateGoodsBo.setGoodsItems(goodsItemMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            templateGoodsBo.setGoodsSkuPoList(goodsSkuMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            templateGoodsBo.setGoodsPropertyRelevantList(goodsPropertyRelevantMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            templateGoodsBo.setGoodsPropertyImgRelevantList(goodsPropertyImgRelevantMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            templateGoodsBo.setGoodsPropertySkuRelevantList(goodsPropertySkuRelevantMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            templateGoodsBo.setGoodsPropertyAssociationList(propertyAssociationMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            templateGoodsBo.setProductSkuList(productSkuMapFuture.join().getOrDefault(goods.getProductId(), Collections.emptyList()));
            templateGoodsBo.setPropertyImgDetails(propertyImgDetailMapFuture.join().getOrDefault(goods.getProductId(), Collections.emptyList()));
            templateGoodsBo.setGoodsFreightList(goodsFreightMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            templateGoodsBo.setGoodsExtDetailImgList(goodsExtDetailImgMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            templateGoodsBo.setGoodsImages(goodsImgMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            templateGoodsBo.setFaMerchantsApply(faMerchantsApplyMapFuture.join().getOrDefault(goods.getShopId(), null));
            templateGoodsBo.setGoodsExtConfigList(goodsExtConfigMapFuture.join().getOrDefault(goods.getId(), Collections.emptyList()));
            sellAlongGoodsMap.put(goods.getId(), templateGoodsBo);
        }

        followGoodsBo.setSellAlongGoodsMap(sellAlongGoodsMap);
        followGoodsBo.setFollowGoodsIds(finalFollowGoodsIds);
    }

    @NotNull
    private <R> Map<Long, List<R>> getListMap(Function<List<Long>, List<R>> func, List<Long> ids, Function<R, Long> groupFunction) {
        List<R> apply = func.apply(ids);
        if (CollectionUtils.isEmpty(apply)) {
            return Maps.newHashMap();
        }

        return apply.stream().collect(Collectors.groupingBy(groupFunction));
    }

    @NotNull
    private <R> Map<Long, R> getMap(Function<List<Long>, List<R>> func, List<Long> ids, Function<R, Long> groupFunction) {
        List<R> apply = func.apply(ids);
        if (CollectionUtils.isEmpty(apply)) {
            return Maps.newHashMap();
        }

        return apply.stream().collect(Collectors.toMap(groupFunction, Function.identity(), (v1, v2) -> v1));
    }

    private void fillListingTemplateInfo(Long listingId, SyncListingFollowGoodsBo followGoodsBo) {
        ListingInfo listingInfo = listingInfoService.getById(listingId);
        Long listingGoodsId = listingInfo.getGoodsId();

        Goods listingGoods = goodsService.getById(listingGoodsId);
        CheckUtils.notNull(listingGoods, CustomResultCode.fill(ProductResultCode.GOODS_NOT_EXIST_EXT, listingGoodsId.toString()));

        Long productId = listingGoods.getProductId();
        // 老结构
        CompletableFuture<Product> productFuture = CompletableFuture.supplyAsync(() -> productService.getById(productId), syncListingFollowGoodsPool);
        CompletableFuture<GoodsDetail> goodsDetailFuture = CompletableFuture.supplyAsync(() -> goodsDetailService.queryGoodsDetailByGoodsId(productId), syncListingFollowGoodsPool);
        CompletableFuture<GoodsExtDetail> goodsExtDetailFuture = CompletableFuture.supplyAsync(() -> goodsExtDetailService.findOneByGoodsId(productId), syncListingFollowGoodsPool);
        CompletableFuture<List<GoodsExtDetailImg>> goodsExtDetailImgFuture = CompletableFuture.supplyAsync(() -> goodsExtDetailImgService.queryByGoodsId(listingGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<List<GoodsImage>> goodsImgFuture = CompletableFuture.supplyAsync(() -> goodsImageService.queryListByGoodsId(listingGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<List<ProductSku>> productSkuFuture = CompletableFuture.supplyAsync(() -> productSkuService.queryProductSkuByProductId(productId), syncListingFollowGoodsPool);
        CompletableFuture<List<GoodsItem>> goodsItemsFuture = CompletableFuture.supplyAsync(() -> goodsItemService.queryGoodsItemByGoodsId(listingGoodsId), syncListingFollowGoodsPool);

        // 新结构
        CompletableFuture<List<GoodsSkuPo>> goodsSkuFuture = CompletableFuture.supplyAsync(() -> goodsSkuService.findListByGoodsId(listingGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<List<GoodsPropertyRelevantPo>> propertyRelevantFuture = CompletableFuture.supplyAsync(() -> goodsPropertyRelevantService.findListByGoodsId(listingGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<List<GoodsPropertyImgRelevantPo>> imgRelevantFuture = CompletableFuture.supplyAsync(() -> goodsPropertyImgRelevantService.findListByGoodsId(listingGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<List<GoodsPropertySkuRelevantPo>> skuRelevantFuture = CompletableFuture.supplyAsync(() -> goodsPropertySkuRelevantService.findListByGoodsId(listingGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<List<PropertyAssociationPo>> propertyAssociationFuture = CompletableFuture.supplyAsync(() -> propertyAssociationService.findListByGoodsId(listingGoodsId), syncListingFollowGoodsPool);

        CompletableFuture.allOf(productFuture, goodsDetailFuture, goodsExtDetailFuture, goodsExtDetailImgFuture, goodsImgFuture, productSkuFuture, goodsItemsFuture, goodsSkuFuture,
                propertyRelevantFuture, imgRelevantFuture, skuRelevantFuture, propertyAssociationFuture).join();

        Product listingProduct = productFuture.join();
        CheckUtils.notNull(listingProduct, CustomResultCode.fill(ProductResultCode.PRODUCT_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsItem> listingGoodsItems = goodsItemsFuture.join();
        CheckUtils.isEmpty(listingGoodsItems, CustomResultCode.fill(ProductResultCode.GOODS_ITEM_NOT_EXIST, listingGoodsId.toString()));

        List<ProductSku> listingProductSkuList = productSkuFuture.join();
        CheckUtils.isEmpty(listingProductSkuList, CustomResultCode.fill(ProductResultCode.PRODUCT_SKU_NOT_EXIST, listingGoodsId.toString()));

        GoodsDetail listingGoodsDetail = goodsDetailFuture.join();
        CheckUtils.notNull(listingGoodsDetail, CustomResultCode.fill(ProductResultCode.GOODS_DETAIL_NOT_EXIST_EXT, listingGoodsId.toString()));

        GoodsExtDetail listingGoodsExtDetail = goodsExtDetailFuture.join();
        CheckUtils.notNull(listingGoodsExtDetail, CustomResultCode.fill(ProductResultCode.GOODS_EXT_DETAIL_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsExtDetailImg> listingGoodsExtDetailImgList = goodsExtDetailImgFuture.join();
        CheckUtils.isEmpty(listingGoodsExtDetailImgList, CustomResultCode.fill(ProductResultCode.GOODS_EXT_DETAIL_IMAGE_NOT_EXIST, listingGoodsId.toString()));

        List<GoodsImage> listingGoodsImages = goodsImgFuture.join();
        CheckUtils.isEmpty(listingGoodsImages, CustomResultCode.fill(ProductResultCode.GOODS_IMAGE_NOT_EXIST, listingGoodsId.toString()));

        CommonListingInfoBo commonListingInfoBo = buildCommonListingInfo(listingGoodsItems);

        CompletableFuture<List<PropertyImgDetail>> propertyImgDetailFuture = CompletableFuture.supplyAsync(() -> propertyInfoService.queryByImgList(listingProduct.getId(), commonListingInfoBo.getListingPropertyValueIds()), syncListingFollowGoodsPool);
        CompletableFuture<List<PropertyGoodsInfoVO>> propertyGoodsInfoFuture = CompletableFuture.supplyAsync(() -> propertyGoodsInfoCoreService.queryByGoodsId(listingGoodsId), syncListingFollowGoodsPool);
        CompletableFuture<List<GoodsExtConfig>> goodsExtConfigFuture = CompletableFuture.supplyAsync(() -> goodsExtConfigService.selectGoodsTagConfig(Collections.singletonList(listingGoodsId), Lists.newArrayList(40L, 45L, 50L)), syncListingFollowGoodsPool);
        CompletableFuture.allOf(propertyImgDetailFuture, propertyGoodsInfoFuture, goodsExtConfigFuture).join();

        List<PropertyImgDetail> listingPropertyImgDetails = propertyImgDetailFuture.join();
        List<PropertyGoodsInfoVO> listingPropertyGoodsInfoVOS = propertyGoodsInfoFuture.join();

        ListingTemplateGoodsBo templateBo = new ListingTemplateGoodsBo();
        templateBo.setProduct(listingProduct);
        templateBo.setProductSkuList(listingProductSkuList);
        templateBo.setPropertyImgDetails(listingPropertyImgDetails);
        templateBo.setPropertyGoodsInfoList(listingPropertyGoodsInfoVOS);
        templateBo.setGoods(listingGoods);
        templateBo.setGoodsDetail(listingGoodsDetail);
        templateBo.setGoodsExtConfigList(goodsExtConfigFuture.join());
        templateBo.setGoodsItems(listingGoodsItems);
        templateBo.setGoodsExtDetail(listingGoodsExtDetail);
        templateBo.setGoodsImages(listingGoodsImages);
        templateBo.setGoodsSkuPoList(goodsSkuFuture.join());
        templateBo.setGoodsPropertyRelevantList(propertyRelevantFuture.join());
        templateBo.setGoodsPropertyAssociationList(propertyAssociationFuture.join());
        templateBo.setGoodsPropertyImgRelevantList(imgRelevantFuture.join());
        templateBo.setGoodsPropertySkuRelevantList(skuRelevantFuture.join());
        templateBo.setGoodsExtDetailImgList(listingGoodsExtDetailImgList);

        // 填充
        followGoodsBo.setListingTemplateGoodsBo(templateBo);
        followGoodsBo.setCommonListingInfoBo(commonListingInfoBo);
    }

    @NotNull
    private static CommonListingInfoBo buildCommonListingInfo(List<GoodsItem> listingGoodsItems) {
        CommonListingInfoBo commonListingInfoBo = new CommonListingInfoBo();
        final List<Long> listingPropertyValueIds = Lists.newArrayList();
        final List<String> listingPvalueDescList = Lists.newArrayList();
        final List<GoodsItemPropertyValueBo> goodsItemPropertyValueList = Lists.newArrayList();
        for (GoodsItem goodsItem : listingGoodsItems) {
            Stream.of(goodsItem.getPvalueStr().split(";"))
                    .filter(StringUtils::isNotBlank)
                    .map(s -> Long.parseLong(s.split(":")[1]))
                    .forEach(listingPropertyValueIds::add);

            listingPvalueDescList.add(goodsItem.getPvalueDesc());

            List<String> pvalueList = Lists.newArrayList(goodsItem.getPvalueDesc().split("&gt;"));
            Collections.sort(pvalueList);
            goodsItemPropertyValueList.add(new GoodsItemPropertyValueBo(goodsItem.getId(), goodsItem.getSkuId(), String.join("&gt;", pvalueList)));
        }

        commonListingInfoBo.setListingPvalueDescList(listingPvalueDescList);
        commonListingInfoBo.setListingPropertyValueIds(listingPropertyValueIds);
        commonListingInfoBo.setGoodsItemPropertyValueList(goodsItemPropertyValueList);
        return commonListingInfoBo;
    }

    @NotNull
    private Map<String, Long> syncNewPropertyAndSku(Long goodsId, Map<String, Long> originalSkuNameIdMap,
                                                    List<GoodsSkuPo> originalSkuPoList, List<GoodsSkuPo> listingGoodsSkuPoList,
                                                    List<GoodsPropertyRelevantPo> originalPropertyRelevantPoList, List<GoodsPropertyRelevantPo> listingGoodsPropertyRelevantPoList,
                                                    List<GoodsPropertyImgRelevantPo> originalImgRelevantPoList, List<GoodsPropertyImgRelevantPo> listingGoodsPropertyImgRelevantPoList,
                                                    List<GoodsPropertySkuRelevantPo> originalPropertySkuRelevantPoList, List<GoodsPropertySkuRelevantPo> listingGoodsPropertySkuRelevantPoList,
                                                    List<PropertyAssociationPo> originalPropertyAssociationList, List<PropertyAssociationPo> propertyAssociationList) {
        if (CollectionUtils.isEmpty(listingGoodsSkuPoList) || CollectionUtils.isEmpty(listingGoodsPropertyRelevantPoList) || CollectionUtils.isEmpty(listingGoodsPropertySkuRelevantPoList) || CollectionUtils.isEmpty(propertyAssociationList)) {
            return Maps.newHashMap();
        }

        Map<Long, GoodsPropertyRelevantPo> goodsPropertyRelevantMap = originalPropertyRelevantPoList.stream().collect(Collectors.toMap(GoodsPropertyRelevantPo::getGlobalPropertyValueId, Function.identity(), (v1, v2) -> v1));

        List<GoodsPropertyRelevantPo> updatePropertyRelevantList = getGoodsPropertyRelevantPos(goodsId, originalPropertyRelevantPoList, listingGoodsPropertyRelevantPoList, goodsPropertyRelevantMap);
        if (CollectionUtils.isNotEmpty(updatePropertyRelevantList)) {
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} updatePropertyRelevantList:{}", goodsId, JSON.toJSONString(updatePropertyRelevantList));
            boolean goodsPropertyRelevantUpdateFlag = goodsPropertyRelevantService.saveOrUpdateBatch(updatePropertyRelevantList);
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} goodsPropertyRelevantUpdateFlag:{}", goodsId, goodsPropertyRelevantUpdateFlag);
        }

        Map<Long, Long> goodsPropertyRelevantIdMappingMap = getGoodsPropertyRelevantIdMappingMap(listingGoodsPropertyRelevantPoList, updatePropertyRelevantList);
        log.info("【listing模版更新&同步更新产品sku】goodsId:{} goodsPropertyRelevantIdMappingMap:{}", goodsId, JSON.toJSONString(goodsPropertyRelevantIdMappingMap));

        List<GoodsPropertyImgRelevantPo> newImgPoList = getGoodsPropertyImgRelevantPos(goodsId, originalImgRelevantPoList, listingGoodsPropertyImgRelevantPoList, goodsPropertyRelevantIdMappingMap);
        if (CollectionUtils.isNotEmpty(newImgPoList)) {
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} newImgPoList:{}", goodsId, JSON.toJSONString(newImgPoList));
            boolean imgRelevantUpdateFlag = goodsPropertyImgRelevantService.saveOrUpdateBatch(newImgPoList);
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} imgRelevantUpdateFlag:{}", goodsId, imgRelevantUpdateFlag);
        }

        List<PropertyAssociationPo> newPropertyAssociationList = getPropertyAssociationPos(goodsId, originalPropertyAssociationList, propertyAssociationList, goodsPropertyRelevantIdMappingMap);
        if (CollectionUtils.isNotEmpty(newPropertyAssociationList)) {
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} newPropertyAssociationList:{}", goodsId, JSON.toJSONString(newPropertyAssociationList));
            boolean propertyAssociationUpdateFlag = propertyAssociationService.saveOrUpdateBatch(newPropertyAssociationList);
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} propertyAssociationUpdateFlag:{}", goodsId, propertyAssociationUpdateFlag);
        }

        List<GoodsSkuPo> updateSkuList = getGoodsSkuPos(goodsId, originalSkuNameIdMap, originalSkuPoList, listingGoodsSkuPoList, originalPropertyRelevantPoList, listingGoodsPropertyRelevantPoList, listingGoodsPropertySkuRelevantPoList, originalPropertySkuRelevantPoList);
        if (CollectionUtils.isNotEmpty(updateSkuList)) {
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} updateSkuList:{}", goodsId, JSON.toJSONString(updateSkuList));
            boolean goodsSkuUpdateFlag = goodsSkuService.saveOrUpdateBatch(updateSkuList);
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} goodsSkuUpdateFlag:{}", goodsId, goodsSkuUpdateFlag);
        }

        Map<Long, Long> goodsSkuIdMappingMap = Maps.newHashMap();
        for (GoodsSkuPo listingSku : listingGoodsSkuPoList) {
            for (GoodsSkuPo mySku : updateSkuList) {
                if (mySku.getDeleted() == 1) {
                    continue;
                }
                if (listingSku.getName().equals(mySku.getName())) {
                    goodsSkuIdMappingMap.put(listingSku.getId(), mySku.getId());
                }
            }
        }

        List<GoodsPropertySkuRelevantPo> updatePropertySkuRelevantList = getGoodsPropertySkuRelevantPos(goodsId, originalPropertySkuRelevantPoList, listingGoodsPropertySkuRelevantPoList, updateSkuList, goodsSkuIdMappingMap, goodsPropertyRelevantIdMappingMap);
        if (CollectionUtils.isNotEmpty(updatePropertySkuRelevantList)) {
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} updatePropertySkuRelevantList:{}", goodsId, JSON.toJSONString(updatePropertySkuRelevantList));
            boolean goodsPropertySkuRelevantUpdateFlag = goodsPropertySkuRelevantService.saveOrUpdateBatch(updatePropertySkuRelevantList);
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} goodsPropertySkuRelevantUpdateFlag:{}", goodsId, goodsPropertySkuRelevantUpdateFlag);
        }

        modifyGoodsSkuList(goodsId, updatePropertySkuRelevantList, updateSkuList);
        return updateSkuList.stream().filter(po -> po.getDeleted() == 0).collect(Collectors.toMap(GoodsSkuPo::getName, GoodsSkuPo::getId, (v1, v2) -> v1));
    }

    private void modifyGoodsSkuList(Long goodsId, List<GoodsPropertySkuRelevantPo> updatePropertySkuRelevantList, List<GoodsSkuPo> updateSkuList) {
        Map<Long, List<GoodsPropertySkuRelevantPo>> skuPropertySkuRelevantGroupMap = updatePropertySkuRelevantList.stream()
                .filter(po -> !po.getDeleted())
                .collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuId));
        for (GoodsSkuPo goodsSkuPo : updateSkuList) {
            if (goodsSkuPo.getDeleted() == 1) {
                continue;
            }
            List<GoodsPropertySkuRelevantPo> goodsPropertySkuRelevantPos = skuPropertySkuRelevantGroupMap.get(goodsSkuPo.getId());
            String skuPropertyRelevantStr = goodsId + "_" +
                    goodsPropertySkuRelevantPos.stream()
                            .sorted((o1, o2) -> o1.getSort() - o2.getSort())
                            .map(po -> po.getId().toString())
                            .collect(Collectors.joining("_"));
            goodsSkuPo.setSkuPropertyRelevantStr(skuPropertyRelevantStr);
        }
        if (CollectionUtils.isNotEmpty(updateSkuList)) {
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} last updateSkuList:{}", goodsId, JSON.toJSONString(updateSkuList));
            boolean goodsSkuUpdateFlag = goodsSkuService.updateBatchByIdAndGoodsId(updateSkuList);
            log.info("【listing模版更新&同步更新产品sku】goodsId:{} last goodsSkuUpdateFlag:{}", goodsId, goodsSkuUpdateFlag);
        }
    }

    @NotNull
    private static List<GoodsPropertySkuRelevantPo> getGoodsPropertySkuRelevantPos(Long goodsId, List<GoodsPropertySkuRelevantPo> originalPropertySkuRelevantPoList, List<GoodsPropertySkuRelevantPo> listingGoodsPropertySkuRelevantPoList, List<GoodsSkuPo> updateSkuList, Map<Long, Long> goodsSkuIdMappingMap, Map<Long, Long> goodsPropertyRelevantIdMappingMap) {
        Map<Long, String> skuIdAndCodeMap = updateSkuList.stream().filter(sku -> sku.getDeleted() == 0).collect(Collectors.toMap(GoodsSkuPo::getId, GoodsSkuPo::getSkuCode, (v1, v2) -> v2));
        List<GoodsPropertySkuRelevantPo> updatePropertySkuRelevantList = Lists.newArrayList();
        Map<Long, List<GoodsPropertySkuRelevantPo>> originalSkuPropertyRelevantGroupMap = originalPropertySkuRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuId));
        for (Map.Entry<Long, List<GoodsPropertySkuRelevantPo>> entry : listingGoodsPropertySkuRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuId)).entrySet()) {
            Long listingSkuId = entry.getKey();
            List<GoodsPropertySkuRelevantPo> listingPropertySkuRelevantList = entry.getValue();

            Long mySkuId = goodsSkuIdMappingMap.get(listingSkuId);
            if (mySkuId == null) {
                continue;
            }

            String mySkuCode = skuIdAndCodeMap.get(mySkuId);

            List<GoodsPropertySkuRelevantPo> myGoodsPropertySkuRelevantPos = originalSkuPropertyRelevantGroupMap.get(mySkuId);
            Map<Long, GoodsPropertySkuRelevantPo> propertySkuRelevantMap = CollectionUtils.isEmpty(myGoodsPropertySkuRelevantPos)
                    ? Maps.newHashMap() : myGoodsPropertySkuRelevantPos.stream().collect(Collectors.toMap(GoodsPropertySkuRelevantPo::getGoodsPropertyRelevantId, Function.identity(), (v1, v2) -> v1));

            for (GoodsPropertySkuRelevantPo listingSkuRelevantPo : listingPropertySkuRelevantList) {
                Long myPropertyRelevantId = goodsPropertyRelevantIdMappingMap.get(listingSkuRelevantPo.getGoodsPropertyRelevantId());
                GoodsPropertySkuRelevantPo myPropertySkuRelevantPo = propertySkuRelevantMap.get(myPropertyRelevantId);
                if (myPropertySkuRelevantPo == null) {
                    myPropertySkuRelevantPo = BeanCopyUtil.transform(listingSkuRelevantPo, GoodsPropertySkuRelevantPo.class);
                    myPropertySkuRelevantPo.setId(null);
                    myPropertySkuRelevantPo.setGoodsId(goodsId);
                    myPropertySkuRelevantPo.setGoodsSkuId(mySkuId);
                    myPropertySkuRelevantPo.setGoodsSkuCode(mySkuCode);
                    myPropertySkuRelevantPo.setGoodsPropertyRelevantId(myPropertyRelevantId);
                    myPropertySkuRelevantPo.setCreateTime(LocalDateTime.now());
                }
                myPropertySkuRelevantPo.setSort(listingSkuRelevantPo.getSort());
                myPropertySkuRelevantPo.setUpdateTime(LocalDateTime.now());
                updatePropertySkuRelevantList.add(myPropertySkuRelevantPo);
            }
        }
        List<Long> existPropertySkuRelevantIds = updatePropertySkuRelevantList.stream().map(GoodsPropertySkuRelevantPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        originalPropertySkuRelevantPoList.stream()
                .filter(po -> !existPropertySkuRelevantIds.contains(po.getId()))
                .peek(po -> {
                    po.setDeleted(true);
                    po.setUpdateTime(LocalDateTime.now());
                }).forEach(updatePropertySkuRelevantList::add);
        return updatePropertySkuRelevantList;
    }

    @NotNull
    private List<GoodsSkuPo> getGoodsSkuPos(Long goodsId, Map<String, Long> originalSkuNameIdMap, List<GoodsSkuPo> originalSkuPoList, List<GoodsSkuPo> listingGoodsSkuPoList,
                                            List<GoodsPropertyRelevantPo> originalPropertyRelevantPoList, List<GoodsPropertyRelevantPo> listingGoodsPropertyRelevantPoList,
                                            List<GoodsPropertySkuRelevantPo> listingGoodsPropertySkuRelevantPoList, List<GoodsPropertySkuRelevantPo> originalPropertySkuRelevantPoList) {
        List<GoodsSkuPo> updateSkuList = Lists.newArrayList();

        Map<Long, GoodsSkuPo> originalSkuMap = originalSkuPoList.stream().collect(Collectors.toMap(GoodsSkuPo::getId, Function.identity()));
        for (GoodsSkuPo listingGoodsSkuPo : listingGoodsSkuPoList) {
            Long goodsSkuId = getGoodsSkuId(listingGoodsSkuPo, listingGoodsPropertyRelevantPoList, listingGoodsPropertySkuRelevantPoList, originalPropertyRelevantPoList, originalPropertySkuRelevantPoList);
            GoodsSkuPo goodsSkuPo = originalSkuMap.get(goodsSkuId);

            if (goodsSkuPo == null) {
                goodsSkuPo = BeanCopyUtil.transform(listingGoodsSkuPo, GoodsSkuPo.class);
                goodsSkuPo.setId(originalSkuNameIdMap.get(listingGoodsSkuPo.getName()));
                goodsSkuPo.setGoodsId(goodsId);

                String skuCode = String.format("%d-%d", goodsId, SnowflakeUtil.generateId());
                goodsSkuPo.setSkuCode(skuCode);
                goodsSkuPo.setCreateTime(LocalDateTime.now());
                goodsSkuPo.setUpdateTime(LocalDateTime.now());
            }

            // 有可能顺序变了，所以名字变一下
            goodsSkuPo.setName(listingGoodsSkuPo.getName());
            goodsSkuPo.setSkuImage(listingGoodsSkuPo.getSkuImage());

            updateSkuList.add(goodsSkuPo);
        }

        List<Long> existSkuIds = updateSkuList.stream().map(GoodsSkuPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        originalSkuPoList.stream()
                .filter(skuPo -> !existSkuIds.contains(skuPo.getId()))
                .peek(skuPo -> {
                    skuPo.setDeleted(1);
                    skuPo.setUpdateTime(LocalDateTime.now());
                })
                .forEach(updateSkuList::add);
        return updateSkuList;
    }

    @NotNull
    private static List<PropertyAssociationPo> getPropertyAssociationPos(Long goodsId, List<PropertyAssociationPo> originalPropertyAssociationList, List<PropertyAssociationPo> propertyAssociationList, Map<Long, Long> goodsPropertyRelevantIdMappingMap) {
        List<PropertyAssociationPo> newPropertyAssociationList = Lists.newArrayList();
        Map<Long, List<PropertyAssociationPo>> originalPropertyAssociationGroupMap = originalPropertyAssociationList.stream().collect(Collectors.groupingBy(PropertyAssociationPo::getPropertyRelevantId));
        Map<Long, List<PropertyAssociationPo>> listingPropertyAssociationGroupMap = propertyAssociationList.stream().collect(Collectors.groupingBy(PropertyAssociationPo::getPropertyRelevantId));
        for (Map.Entry<Long, List<PropertyAssociationPo>> listingEntry : listingPropertyAssociationGroupMap.entrySet()) {
            Long listingPropertyId = listingEntry.getKey();
            List<PropertyAssociationPo> listingPropertyAssociationList = listingEntry.getValue();
            Long myPropertyId = goodsPropertyRelevantIdMappingMap.get(listingPropertyId);
            if (myPropertyId == null) {
                continue;
            }

            List<PropertyAssociationPo> originalPAList = originalPropertyAssociationGroupMap.get(myPropertyId);
            Map<Long, Map<Long, PropertyAssociationPo>> paMap = CollectionUtils.isEmpty(originalPAList) ? Maps.newHashMap() : originalPAList.stream().collect(Collectors.groupingBy(PropertyAssociationPo::getPropertyId, Collectors.mapping(Function.identity(), Collectors.toMap(PropertyAssociationPo::getPropertyValueId, Function.identity()))));

            listingPropertyAssociationList.stream().map(listingPaPo -> {
                PropertyAssociationPo myPaPo = null;
                Map<Long, PropertyAssociationPo> associationPoMap = paMap.get(listingPaPo.getPropertyId());
                if (MapUtils.isNotEmpty(associationPoMap)) {
                    myPaPo = associationPoMap.get(listingPaPo.getPropertyValueId());
                }

                if (myPaPo == null) {
                    myPaPo = BeanCopyUtil.transform(listingPaPo, PropertyAssociationPo.class);
                    myPaPo.setId(null);
                    myPaPo.setGoodsId(goodsId);
                    myPaPo.setCreateTime(LocalDateTime.now());
                    myPaPo.setPropertyRelevantId(myPropertyId);
                }

                myPaPo.setUpdateTime(LocalDateTime.now());
                return myPaPo;
            }).forEach(newPropertyAssociationList::add);
        }

        List<Long> existPropertyAssociationIds = newPropertyAssociationList.stream().map(PropertyAssociationPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        for (PropertyAssociationPo propertyAssociationPo : originalPropertyAssociationList) {
            if (!existPropertyAssociationIds.contains(propertyAssociationPo.getId())) {
                propertyAssociationPo.setDeleted(true);
                propertyAssociationPo.setUpdateTime(LocalDateTime.now());
                newPropertyAssociationList.add(propertyAssociationPo);
            }
        }

        return newPropertyAssociationList;
    }

    @NotNull
    private static List<GoodsPropertyImgRelevantPo> getGoodsPropertyImgRelevantPos(Long goodsId, List<GoodsPropertyImgRelevantPo> originalImgRelevantPoList, List<GoodsPropertyImgRelevantPo> listingGoodsPropertyImgRelevantPoList, Map<Long, Long> goodsPropertyRelevantIdMappingMap) {
        List<GoodsPropertyImgRelevantPo> newImgPoList = Lists.newArrayList();
        Map<Long, List<GoodsPropertyImgRelevantPo>> originalPropertyRelevantImgGroupMap = originalImgRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertyImgRelevantPo::getGoodsPropertyRelevantId));
        Map<Long, List<GoodsPropertyImgRelevantPo>> listingPropertyRelevantImgGroupMap = listingGoodsPropertyImgRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertyImgRelevantPo::getGoodsPropertyRelevantId));
        for (Map.Entry<Long, List<GoodsPropertyImgRelevantPo>> listingEntry : listingPropertyRelevantImgGroupMap.entrySet()) {
            Long listingPropertyId = listingEntry.getKey();
            List<GoodsPropertyImgRelevantPo> listingImgList = listingEntry.getValue();
            Long myPropertyId = goodsPropertyRelevantIdMappingMap.get(listingPropertyId);
            if (myPropertyId == null) {
                continue;
            }
            List<GoodsPropertyImgRelevantPo> originalImgList = originalPropertyRelevantImgGroupMap.get(myPropertyId);
            Map<String, GoodsPropertyImgRelevantPo> imgUrlPoMap = CollectionUtils.isEmpty(originalImgList) ? Maps.newHashMap() : originalImgList.stream().collect(Collectors.toMap(GoodsPropertyImgRelevantPo::getImgUrl, Function.identity(), (v1, v2) -> v1));

            listingImgList.stream().map(listingImgPo -> {
                GoodsPropertyImgRelevantPo myImgPo = imgUrlPoMap.get(listingImgPo.getImgUrl());
                if (myImgPo == null) {
                    myImgPo = BeanCopyUtil.transform(listingImgPo, GoodsPropertyImgRelevantPo.class);
                    myImgPo.setId(null);
                    myImgPo.setGoodsId(goodsId);
                    myImgPo.setCreateTime(LocalDateTime.now());
                    myImgPo.setGoodsPropertyRelevantId(myPropertyId);
                }
                myImgPo.setSort(listingImgPo.getSort());
                myImgPo.setUpdateTime(LocalDateTime.now());
                return myImgPo;
            }).forEach(newImgPoList::add);
        }

        List<Long> existImgIds = newImgPoList.stream().map(GoodsPropertyImgRelevantPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        for (GoodsPropertyImgRelevantPo imgRelevantPo : originalImgRelevantPoList) {
            if (!existImgIds.contains(imgRelevantPo.getId())) {
                imgRelevantPo.setDeleted(true);
                imgRelevantPo.setUpdateTime(LocalDateTime.now());
                newImgPoList.add(imgRelevantPo);
            }
        }

        return newImgPoList;
    }

    @NotNull
    private static Map<Long, Long> getGoodsPropertyRelevantIdMappingMap(List<GoodsPropertyRelevantPo> listingGoodsPropertyRelevantPoList, List<GoodsPropertyRelevantPo> updatePropertyRelevantList) {
        Map<Long, Long> goodsPropertyRelevantIdMappingMap = Maps.newHashMap();
        for (GoodsPropertyRelevantPo myProperty : updatePropertyRelevantList) {
            if (myProperty.getDeleted()) {
                continue;
            }
            for (GoodsPropertyRelevantPo listingProperty : listingGoodsPropertyRelevantPoList) {
                if (listingProperty.getGlobalPropertyValueId().equals(myProperty.getGlobalPropertyValueId())) {
                    goodsPropertyRelevantIdMappingMap.put(listingProperty.getId(), myProperty.getId());
                }
            }
        }
        return goodsPropertyRelevantIdMappingMap;
    }

    @NotNull
    private static List<GoodsPropertyRelevantPo> getGoodsPropertyRelevantPos(Long goodsId, List<GoodsPropertyRelevantPo> originalPropertyRelevantPoList, List<GoodsPropertyRelevantPo> listingGoodsPropertyRelevantPoList, Map<Long, GoodsPropertyRelevantPo> goodsPropertyRelevantMap) {
        List<GoodsPropertyRelevantPo> updatePropertyRelevantList = Lists.newArrayList();
        for (GoodsPropertyRelevantPo listingGoodsPropertyRelevantPo : listingGoodsPropertyRelevantPoList) {
            GoodsPropertyRelevantPo goodsPropertyRelevantPo = goodsPropertyRelevantMap.get(listingGoodsPropertyRelevantPo.getGlobalPropertyValueId());
            if (goodsPropertyRelevantPo == null) {
                goodsPropertyRelevantPo = BeanCopyUtil.transform(listingGoodsPropertyRelevantPo, GoodsPropertyRelevantPo.class);
                goodsPropertyRelevantPo.setId(null);
                goodsPropertyRelevantPo.setGoodsId(goodsId);
                goodsPropertyRelevantPo.setCreateTime(LocalDateTime.now());
            }

            goodsPropertyRelevantPo.setMainProperty(listingGoodsPropertyRelevantPo.getMainProperty());
            goodsPropertyRelevantPo.setNameSort(listingGoodsPropertyRelevantPo.getNameSort());
            goodsPropertyRelevantPo.setValueSort(listingGoodsPropertyRelevantPo.getValueSort());
            goodsPropertyRelevantPo.setPropertyAliasName(listingGoodsPropertyRelevantPo.getPropertyAliasName());
            goodsPropertyRelevantPo.setPropertyValueAliasName(listingGoodsPropertyRelevantPo.getPropertyValueAliasName());
            goodsPropertyRelevantPo.setUpdateTime(LocalDateTime.now());
            updatePropertyRelevantList.add(goodsPropertyRelevantPo);
        }

        List<Long> existIds = updatePropertyRelevantList.stream().map(GoodsPropertyRelevantPo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        for (GoodsPropertyRelevantPo relevantPo : originalPropertyRelevantPoList) {
            if (!existIds.contains(relevantPo.getId())) {
                relevantPo.setDeleted(true);
                relevantPo.setUpdateTime(LocalDateTime.now());
                updatePropertyRelevantList.add(relevantPo);
            }
        }

        return updatePropertyRelevantList;
    }

    /**
     * 寻找原来的skuId（规格相同）
     */
    private Long getGoodsSkuId(GoodsSkuPo listingGoodsSkuPo, List<GoodsPropertyRelevantPo> listingGoodsPropertyRelevantPoList, List<GoodsPropertySkuRelevantPo> listingGoodsPropertySkuRelevantPoList,
                               List<GoodsPropertyRelevantPo> originalPropertyRelevantPoList, List<GoodsPropertySkuRelevantPo> originalPropertySkuRelevantPoList) {
        // 当前listing sku的规格id
        List<Long> goodsPropertyRelevantIds = listingGoodsPropertySkuRelevantPoList.stream().filter(po -> Objects.equals(listingGoodsSkuPo.getId(), po.getGoodsSkuId())).map(GoodsPropertySkuRelevantPo::getGoodsPropertyRelevantId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsPropertyRelevantIds)) {
            return null;
        }

        // 当前规格实体
        List<GoodsPropertyRelevantPo> goodsPropertyRelevantPos = listingGoodsPropertyRelevantPoList.stream().filter(po -> goodsPropertyRelevantIds.contains(po.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsPropertyRelevantPos)) {
            return null;
        }

        // 获取跟卖商品原规格Map<规格名id, Map<规格值id, 原规格id>>
        Map<Long, Map<Long, Long>> globalPropertyGroupMap = originalPropertyRelevantPoList.stream().filter(po -> Boolean.FALSE.equals(po.getDeleted())).collect(Collectors.groupingBy(GoodsPropertyRelevantPo::getGlobalPropertyId, Collectors.mapping(Function.identity(), Collectors.toMap(GoodsPropertyRelevantPo::getGlobalPropertyValueId, GoodsPropertyRelevantPo::getId))));
        // 获取跟卖原商品与listing商品规格一致的规格id
        Set<Long> originalPropertyRelevantIdList = goodsPropertyRelevantPos.stream().map(po -> {
            Map<Long, Long> map = globalPropertyGroupMap.get(po.getGlobalPropertyId());
            if (MapUtils.isEmpty(map)) {
                return -1L;
            }

            return Optional.ofNullable(map.get(po.getGlobalPropertyValueId())).orElse(-1L);
        }).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(originalPropertyRelevantIdList)) {
            return null;
        }


        // 查看与老商品相符合的skuId
        Map<Long, Set<Long>> propertySkuRelevantMap = originalPropertySkuRelevantPoList.stream().collect(Collectors.groupingBy(GoodsPropertySkuRelevantPo::getGoodsSkuId, Collectors.mapping(GoodsPropertySkuRelevantPo::getGoodsPropertyRelevantId, Collectors.toSet())));
        for (Map.Entry<Long, Set<Long>> entry : propertySkuRelevantMap.entrySet()) {
            if (entry.getValue().equals(originalPropertyRelevantIdList)) {
                return entry.getKey();
            }
        }

        return null;
    }

}
