package com.voghion.product.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.voghion.product.core.ChanceGoodsCoreService;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

public class ChanceGoodsBatchSaveListener extends AnalysisEventListener<BatchSaveChanceGoodsVo> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ChanceGoodsBatchSaveListener.class);
    /**
     * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 3000;
    List<BatchSaveChanceGoodsVo> list = new ArrayList<>();

    @Resource
    private ChanceGoodsCoreService chanceGoodsCoreService;

    public ChanceGoodsBatchSaveListener(ChanceGoodsCoreService service) {
        this.chanceGoodsCoreService = service;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @SneakyThrows
    @Override
    public void invoke(BatchSaveChanceGoodsVo data, AnalysisContext context) {
        LOGGER.info("解析到一条数据:{}", JSON.toJSONString(data));
        list.add(data);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (list.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            list.clear();
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @SneakyThrows
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        LOGGER.info("所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    @SneakyThrows
    private void saveData() {
        LOGGER.info("{}条数据，开始存储数据库！", list.size());
        chanceGoodsCoreService.batchSaveTemplate(list);
        LOGGER.info("存储数据库成功！");
    }
}
