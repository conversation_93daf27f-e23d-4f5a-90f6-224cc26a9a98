package com.voghion.product.api.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.input.GoodsRecommendVO;
import com.voghion.product.api.output.CustomListCategoryDTO;
import com.voghion.product.api.output.GoodsOnlyDTO;
import com.voghion.product.model.po.CustomListItems;
import com.voghion.product.model.vo.CustomImportVO;
import com.voghion.product.model.vo.CustomListGoodsVO;

import java.util.List;

public interface CustomListCategoryRemoteService {


    /**
     * 根据虚拟列表Id获取对应配置的类目
     * @param customId
     * @return
     */
    Result<List<CustomListCategoryDTO>> queryCategoryByCustom(Long customId);

    Result<PageView<GoodsOnlyDTO>> queryGoodsListByCategory(GoodsRecommendVO goodsRecommendVO);

    Boolean eliminateJob();

    void updateCustomListItem(CustomListGoodsVO customListGoodsVO);

    void deleteCustomListItem(CustomListGoodsVO customListGoodsVO);

    void deletePaidGoodsCustomListItem();

    Boolean customListItemAutoUpdateJob(CustomListGoodsVO customListGoodsVO);

    CustomListItems pageCustomListItems(Page<CustomListItems> page, Long customId);

    Result<Boolean> coverCustomGoods(CustomListGoodsVO customListGoodsVO);
    List<CustomImportVO> pageCustomListItems(Long customId);

    void customListSync();

    void syncCustomTopList();
}
