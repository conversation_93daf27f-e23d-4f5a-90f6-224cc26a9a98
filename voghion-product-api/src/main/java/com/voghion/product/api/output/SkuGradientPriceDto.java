package com.voghion.product.api.output;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SkuGradientPriceDto implements Serializable {
    private static final long serialVersionUID = -873143393711959990L;

    private Long goodsId;

    private Long skuId;

    private List<GradientPriceDetail> priceMap;

    public SkuGradientPriceDto(Long goodsId, Long skuId) {
        this.goodsId = goodsId;
        this.skuId = skuId;
    }

    @Data
    @ApiModel
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GradientPriceDetail implements Serializable{
        private static final long serialVersionUID = 2119035583445286679L;

        @ApiModelProperty("当前梯度最小数量")
        private Integer minLimit;

        @ApiModelProperty("当前梯度最大数量")
        private Integer maxLimit;

        @ApiModelProperty("当前梯度sku单价")
        private BigDecimal price;
    }
}
