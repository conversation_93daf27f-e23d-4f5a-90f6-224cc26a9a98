package com.voghion.product.api.service;

import com.colorlight.base.model.Result;

public interface GoodsMonitoringRemoteService {

    Result<Boolean> productMonitor();

    Result<Boolean> putProductMonitor();

    Result<Boolean> updateGoodsMonitoringStatus();

    //同步bq的热销数据
    void syncHotGoodsMonitorFromBigQuery();

    //清理失效热销商品数据
    void cleanHotGoodsMonitor(Integer daysInterval);

    //刷新热销商品部分数据
    void refreshHotGoodsMonitor();

}
