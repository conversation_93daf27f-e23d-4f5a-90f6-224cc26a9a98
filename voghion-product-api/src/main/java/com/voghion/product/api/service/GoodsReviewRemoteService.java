package com.voghion.product.api.service;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.GoodsReviewDTO;
import com.voghion.product.api.output.GoodsDataOutput;
import com.voghion.product.model.po.GoodsReview;

import java.util.List;

public interface GoodsReviewRemoteService {


    /**
     * 分页查询商品集合
     * @return
     */

    Result<PageView<GoodsDataOutput>> queryReviewsByPage(GoodsReviewDTO dto);


    Result<List<Long>> queryReviewsGoodsIds(GoodsReviewDTO dto);

    Result<Boolean> deleteGoodsReview(GoodsReviewDTO dto, List<Long> goodsIds);

    Result<Boolean> deleteGoodsReviewNew(GoodsReviewDTO dto);

    Result<List<GoodsReview>> getRUGNew(GoodsReviewDTO dto);

}
