package com.voghion.product.api.service;

import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.GoodsTagDTO;
import com.voghion.product.api.dto.ShopTagDTO;
import com.voghion.product.api.input.ShopTagInput;

import java.util.List;

/**
 * GoodsTagRemoteService
 *
 * <AUTHOR>
 * @date 2022/12/13
 */
public interface GoodsTagRemoteService {
    Result<GoodsTagDTO> queryGoodsTagByTagId(Long tagId);

    Result<List<GoodsTagDTO>> queryGoodsTagByTagIds(List<Long> tagIds);

    Result<List<GoodsTagDTO>> queryGoodsTags();

    Result<List<ShopTagDTO>> queryShopTagsInput(ShopTagInput shopTagInput);
}
