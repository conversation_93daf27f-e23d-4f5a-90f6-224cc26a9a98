package com.voghion.product.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/17 15:06
 */
@Data
public class ItemGoodsSkuInfoRemoteDto implements Serializable {

    private Long skuId;

    private String skuPropertyRelevantStr;

    private String name;

    private String imageUrl;

    private String length;

    private String width;

    private String height;

    private String weight;

    private Long weightNumber;

    private String packageSize;

    private Long stock;

    private BigDecimal price;

    private BigDecimal orginalPrice;

    private BigDecimal orginalMarketPrice;

    private BigDecimal costPrice;

    private String originalSkuId;

    private BigDecimal defaultDelivery;

    private Integer minPurchaseQuantity;

    private Map<String, String> skuProperty;

}
