package com.voghion.product.api.service;

import com.voghion.product.api.input.GoodsTestQueryInput;
import com.voghion.product.model.vo.GoodsTestSetV2Input;

import java.util.List;

public interface GoodsTestSetCoreRemoteService {

    public List<Long> queryTestGoodsIds(GoodsTestQueryInput input);

    void terminateTheTestGoods(Integer type);

    void updateTestGoods();

    void terminateTheTestGoodsV2();

    void updateGoodsState();

    void addTestGoods(List<GoodsTestSetV2Input> inputs, String operator);
}
