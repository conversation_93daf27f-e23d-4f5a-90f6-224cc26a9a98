package com.voghion.product.api.service;

import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.FaMerchantsApplyDTO;

import java.util.List;

/**
 * <p>
 * 店铺
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface FaMerchantsApplyRemoteService {

    Result<List<Long>> getTop500ManufacturerShopIds();

    Result<List<FaMerchantsApplyDTO>> queryByShopIds(List<Long> shopIds);

    Result<List<Long>> getAllNegativeShopIds();

}
