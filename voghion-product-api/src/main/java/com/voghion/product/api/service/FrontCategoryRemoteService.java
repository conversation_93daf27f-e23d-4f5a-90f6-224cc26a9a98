package com.voghion.product.api.service;

import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.FrontCategoryListDTO;
import com.voghion.product.api.input.FrontCategoryInputVO;
import com.voghion.product.api.output.*;
import com.voghion.product.model.po.FrontCategory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2021/6/3 16:12
 * @describe
 */
public interface FrontCategoryRemoteService {

    /**
     * 查询首页类目列表
     * @param input
     * @return
     */
    Result<List<FrontCategoryVO>> findFrontCategory(FrontCategoryInputVO input);

    /**
     * 根据前台类型查询后台类目
     * @param frontCategoryInfoVO
     * @return
     */
    Result<FrontCategoryInfoVO> findCategoryIdsByFrontCategoryids(FrontCategoryInfoVO frontCategoryInfoVO);

    /**
     * 根据性别权重进行排序返回首页类目信息
     * @param input
     * @return
     */
    Result<List<FrontCategoryVO>> findIndexFrontCategory(FrontCategoryInputVO input);

    Result<List<FrontCategoryVO>> queryFrontCategorysByIds(List<Long> frontCategoryIds);

    Result<List<FrontCategoryExtVO>> queryCategoryIdByFrontCategoryId(Long frontCategoryId);

    Result<List<FrontCategoryDTO>> queryFrontCategoriesByFrontIds(List<Long> frontCategoryIds);

    /**
     * 根据前台类型查询后台类目
     * @param frontCategoryInfoVO
     * @return
     */
    Result<List<FrontCategoryDTO>> findFrontCategoryByCategoryIds(FrontCategoryInfoVO frontCategoryInfoVO);

    Result<Map<Long, List<FrontCategoryDTO>>> findFrontCategoryMapByCategoryIds(FrontCategoryInfoVO frontCategoryInfoVO);

    Result<Map<Long, List<FrontCategoryDTO>>> findFrontCategoryMapByCategoryId(List<Long> categoryIds);

    /**
     * 根据前台类目查询对应后台类目id集合
     * @param categoryIds
     * @return
     */
    Result<Map<String, List<String>>> findCategoryIdsByFrontCategoryIds(List<Long> categoryIds);

    Result<List<FrontCategoryListDTO>> queryNewFrontCatList();


    Result<List<Long>> findCurrentWithAllChildFrontCategoryIds(Long currentId,Integer isDel,Integer status);
}
