package com.voghion.product.api.service;

import com.voghion.product.model.dto.GoodsFreightDTO;
import com.voghion.product.model.dto.QueryFreight;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface GoodsFreightRemoteService {

    GoodsFreightDTO queryByGoodsId(Long goodsId, String countryName);

    List<GoodsFreightDTO> queryFreightByGoodsIdsAndCountry(List<Long> goodsIds, String country);

    Map<String, BigDecimal> countFreight(QueryFreight queryFreight);

}
