package com.voghion.product.api.service;


import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.SizeChartTemplateDTO;
import com.voghion.product.model.vo.CategoryStandardVO;
import com.voghion.product.model.vo.SizeChartTemplateVo;
import com.voghion.product.model.vo.condition.SizeChartTemplateQueryCondition;

import java.util.List;

/**
 * 尺码表
 *
 * <AUTHOR>
 * @date 2022/9/21
 */
public interface SizeTemplateRemoteService {

    /**
     * 根据模板id获取信息
     *
     * @param templateId 模板id
     * @return SizeChartTemplateVo
     */
    Result<SizeChartTemplateDTO> getSizeTemplateByTemplateId(Long templateId);

    /**
     * 根据id查询模板
     */
    Result<List<SizeChartTemplateVo>> querySizeTemplateByIds(List<Long> sizeChartTemplateIds);

    /**
     * 新增商家模板
     *
     * @param sizeChartTemplateVo
     * @return
     */
    Result<Boolean> addShopTemplate(SizeChartTemplateVo sizeChartTemplateVo);

    /**
     * 修改商家模板
     *
     * @param sizeChartTemplateVo
     * @return
     */
    Result<Boolean> updateShopTemplate(SizeChartTemplateVo sizeChartTemplateVo);

    /**
     * 根据模板id查询商家模板
     *
     * @param templateId
     * @return
     */
    Result<SizeChartTemplateVo> getShopTemplate(Long templateId, Long shopId);

    /**
     * 根据类目id查询系统模板
     *
     * @param id
     * @return
     */
    Result<SizeChartTemplateVo> getSystemTemplateByCategory(Long id);

    /**
     * 商家模板列表
     *
     * @param condition
     * @return
     */
    Result<PageView<SizeChartTemplateVo>> listShopTemplate(SizeChartTemplateQueryCondition condition);

    /**
     * 根据类目id查询尺码属性
     *
     * @param categoryId
     * @param shopId
     * @return
     */
    Result<CategoryStandardVO> checkCategory(Long categoryId, Long shopId);
}
