package com.voghion.product.api.service;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.*;
import com.voghion.product.api.input.*;
import com.voghion.product.api.output.*;
import com.voghion.product.api.output.GoodsFreightVO;
import com.voghion.product.model.dto.*;
import com.voghion.product.model.po.FaMerchantsApply;
import com.voghion.product.model.vo.*;
import com.voghion.product.model.vo.GoodsInfoVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2021/5/17 15:19
 * @describe
 */
public interface GoodsRemoteService {

    /**
     * 根就商品ids查询所有商品
     *
     * @param ids
     * @return
     */
    Result<List<GoodsOutput>> queryGoodsByIds(List<Long> ids);

    /**
     * 根就商品ids查询所有商品(后台)
     *
     * @param ids
     * @return
     */
    Result<List<GoodsOutput>> queryBackendGoodsByIds(List<Long> ids);

    /**
     * 根就商品ids查询所有商品
     *
     * @param ids
     * @param isValid true 有效的  过滤删除 有效状态  false 不过滤删除  有些状态  null 查询所有
     * @return
     */
    Result<List<GoodsOutput>> queryGoodsByIds(List<Long> ids, Boolean isValid);

    Result<Boolean> addGoodsSales(Map<Long, Integer> goodsSales);

    Result<Long> addGoods(ProductInfoInput productInfoInput);

    Result<Long> threePartiesAddGoodsInfo(ItemGoodsInfoRemoteInputDto dto);


    Result<Long> queryGoodsCount(Long shopId);

    Result<Boolean> updateGoods(ProductInfoInput infoInput);

    Result<Boolean> updateBkGoodsStatus(Long goodsId);

    Result<PageView<GoodsListVO>> queryList(QueryGoodsDTO queryGoodsDTO);

    /**
     * 通过后台类目id获取新上商品id
     *
     * @param queryGoodsDTO 条件
     * @return 商品id集合
     */
    Result<List<Long>> queryGoodsIdsByCategoryIdsPage(QueryGoodsDTO queryGoodsDTO);

    Result<GoodsDetailInfoVO> queryDetail(QueryGoodsDTO queryGoodsDTO);

    List<GoodsOutput> queryGoodsSevenByIds(List<Long> ids);

    /* Boolean queryIsSevenDayGood(Long goodsId);*/
    List<Long> queryGoodsIsSevenDay(List<Long> goodsIds);

    Result<Boolean> updateLockBatchById(List<GoodsOutput> needLockList);

    Result<Boolean> updateTag(List<GoodsTagInfoDTO> goodsTagInfoDTOList);

    Result<Boolean> deleteTag(List<GoodsTagInfoDTO> goodsTagInfoDTOList);


    Result<Boolean> addOrUpdateTagVO(GoodsTagUpdateInput goodsTagUpdateInput);

    Result<Boolean> deleteTagVO(GoodsTagUpdateInput goodsTagUpdateInput);


    Result<List<SpecialActGoodsDataDTO>> fillData(List<SpecialActGoodsDataDTO> datas);

    /**
     * admin
     */
    Result<List<SpecialActGoodsDataDTO>> fillDataBack(List<SpecialActGoodsDataDTO> datas);

    Result<List<GoodsFieldVo>> queryGoodsFieldByIds(List<Long> goodsIds);

    Result<List<GoodsOutput>> queryAllActivityGoodsInfoByGoodsId(List<Long> goodsIds);

    Result<Map<String, List<GoodsOutput>>> queryActivityGoodsInfoByGoodsId(StartingActivityGoodsQueryDto queryDto);

//    Result<GoodsItemDTO> queryItemPriceBySkuIdAndCountry(Long skuId, String country);

//    Result<Map<Long, GoodsItemDTO>> queryItemPriceBySkuIdsAndCountry(List<Long> skuIds, String country);

    /**
     * 根据shopId 上下架商品
     *
     * @param ids
     * @param shopId
     * @return
     */
    Result<Boolean> showGoods(List<Long> ids, Long shopId, String isShow);

    List<GoodsFreightVO> queryGoodsFreightByGoodsIds(List<Long> goodsIds);

    PageView<GoodsOutput> queryGoodsByParam(GoodsQueryCondition goodsEsQueryVo);

    /**
     * 查询黑名单商品
     *
     * @param goodsIds
     * @return
     */
    List<Long> blacklistedGoods(List<Long> goodsIds);

    /**
     * 查询所有敏感词
     * @return
     */
    List<String> listAllSensitiveWords();

    /**
     * 敏感词校验
     *
     * @param text 待校验名词
     * @return 校验名称是否合法
     */
    Result<Boolean> checkSensitiveWords(String text);

    List<String> checkSensitiveWords2(String text);

    /**
     * 根据条件查询商品分页信息(es)
     *
     * @param queryGoodsVO
     * @return
     */
    Result<PageView<GoodsInfoVO>> goodsByPageListWithEs(QueryGoodsVO queryGoodsVO);

    void calculateGoodsSort();

    /**
     * 新增商品操作记录
     *
     * @param goodsOperationLogDto
     */
    void addGoodsOperationLog(GoodsOperationLogDto goodsOperationLogDto);

    Result<List<SystemBidGoodsImportVo>> queryGoods(List<Long> goodsIds);

    Result<List<FaMerchantsApply>> queryShopName(List<Long> shopIds);

    Result<BottomGoodsVO> queryByGoodsId(Long goodsId);

    Result<BottomGoodsVO> queryValidGoods(Long goodsId);

    boolean syncPassGoods(List<Long> goodsIds);

    boolean syncAdjustWithPriceGoods(List<Long> goodsIds);

    boolean syncAdjustNotPriceGoods(List<Long> goodsIds);

    boolean syncShopDisableGoods(List<Long> goodsIds);

    Result<PickedShopGoodsDTO> queryPickedShopGoodsInfo(PickedShopGoodsVO vo);

    List<String> batchImportGoods(Long fileSyncRecordId);

    List<BrandVO> queryShopBrandList(Long shopId, Long categoryId);

    void deleteGoodsDraft();

    Map<Long,Integer> quereyShopDeliveryTypeMap(List<Long> shopIds);

    List<Long> hitAdvertGoodsByKeyword(String keyword);


    void createNewGoodsCustom();

    /**
     * 查询小二名称负责的商家
     * @param getPrincipal
     * @return
     */
    Result<List<FaMerchantsApplyDTO>> listFaMerchantsApplyListByPrinciple(String getPrincipal);

    Result<Boolean> systemGoodsIsShow(GoodsIsShowInput goodsIsShowInput);

    Result<Boolean> insertNotice();

    Result<Boolean> pushOrderInfo();

    Result<Boolean> backupRelatedHotGoods();

    Result<Map<String, BigDecimal>> calculateGoodsPrice(GoodsCalulatePriceInput input);

    Result<Map<String, BigDecimal>> calculateGoodsPriceV2(GoodsCalulatePriceInput input);

    Result<Map<Long, BigDecimal>> calculateGoodsPriceV3(GoodsCalulatePriceInput input);

    Map<Long, BigDecimal> rmb2Eub(GoodsCalulatePriceInput input);

    Map<Long, BigDecimal> eub2Rmb(GoodsCalulatePriceInput input);

    //更新商品库存或价格
    Result<Boolean> updateGoodsPriceOrStock(GoodsItemUpdateInput input);

    Result<List<Long>> queryListingGoodsById(Long goodsId);

    Result<Boolean> updatePriceAndStock(UpdatePriceAndStockVo vo);

    Result<Boolean> updateProperty(UpdatePropertyVo vo);

    Result<Boolean> updateDetail(UpdateDetailVo vo);

    Result<Boolean> scanProhibitionGoods(Integer type);

    Result<Boolean> phpGoodsIsShow(GoodsIsShowInput goodsIsShowInput);

    Result<Boolean> updateGoodsShopInfo(UpdateShopInfoVo input);

    Result<Boolean> batchUpdateFreight(List<BatchUpdateGoodsFreightVo> list);


    List<GoodsBriefInfoDTO> queryGoodsBriefInfo(QueryGoodsVO queryGoodsVO);

    /**
     * 查询负业商品名单
     * 查询 40、45、46、50 标签
     * @param goodsIds
     * @return
     */
    List<Long> selectNegativeGoodsIds(List<Long> goodsIds);

    /**
     * 根据商品id获取所有sku所有档位的价格(不含vat)
     */
    Result<List<SkuGradientPriceDto>> queryGoodsPriceByGradient(Long goodsId, String country);

    /**
     * 根据sku数量查询sku单价
     * @return key:skuId ; value:sku默认单价和当前数量的梯度单价
     */
    Result<Map<Long, SkuGradientPriceListDto>> querySkuPriceByGradient(QueryAllSkuPriceByGradientCondition condition);

    /**
     * 计算批发商品总运费
     */
    Result<BigDecimal> calculateWholesaleFreightAmount(List<CalculateWholesaleFreightDto> dtoList);

    /**
     * 计算批发商品运费信息
     */
    Result<CalculateWholesaleFreightResultDto> calculateWholesaleFreightInfo(List<CalculateWholesaleFreightSkuDto> dtoList);

    /**
     * 更新商品采购价
     */
    Result<Boolean> batchUpdateCostPrice(List<UpdateCostPriceDto> dtoList);

    Result<Boolean> refreshGoodsCountry(RefreshGoodsCountryVO refreshGoodsCountryVO);

    Result<Boolean> syncPassGoodsNew(List<Long> goodsIds);

    /**
     * 下架零库存sku商品：目前只对批发商品做校验
     */
    void   takeOffZeroStockSkuGoods();

    /**
     * 根据商品id集合查询对应的标签
     */
    Result<Map<Long, List<Long>>> queryTagByGoodsIds(List<Long> goodsIds);

    /**
     * 自营店铺商品变更店铺
     * @param input
     * @return
     */
    Result<Boolean> selfSupportGoodsChangeShop(ChangeShopInfoVo input);

    Result<Boolean> refreshGoodsRealShotImg(RefreshGoodsRealShotImgDTO refreshGoodsRealShotImgDTO);

    Result<Boolean> conversionGoodsIsShow(ConversionGoodsIsShowInput conversionGoodsIsShowInput);

    Result<Boolean> productSaleExportTask();

    /**
     * 根据goodsId和类型查询国家商品说明书详情
     *
     * @return
     */
    Result<GoodsManualVO> queryGoodsManualByGoodsId(Long goodsId, String countryKey);

    /**
     * 负向批发商品 退出批发((tagId=151 or type=3) && tagId=[40,45,50])
     */
    void negativeGoodsExitWholesale();

    Result<Map<Long, Integer>> queryShopRealGoodsCountByShopIds(List<Long> shopIds);

    Result<List<ShopInfoDto>> queryShopInfoByShopIds(List<Long> shopIds);
}
