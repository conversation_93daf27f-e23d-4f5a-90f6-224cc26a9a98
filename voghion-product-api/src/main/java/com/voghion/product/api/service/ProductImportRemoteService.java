package com.voghion.product.api.service;

import com.colorlight.base.model.PageView;
import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.GoodsInfoInputVO;
import com.voghion.product.api.dto.GoodsVO;
import com.voghion.product.api.dto.PluginQueryGoodsVO;

/**
 * 商品导入服务
 * archer
 */
public interface ProductImportRemoteService {
    


    /**
     * 通过插件商品导入
     * @param goodsAdminInput
     * @return
     */
    public Result<Boolean> importProduct(GoodsInfoInputVO goodsAdminInput);


    /**
     * 通过插件更新商品信息
     * @param goodsAdminInput
     * @return
     */
    public Result<Boolean> updateProduct(GoodsInfoInputVO goodsAdminInput);


    /**
     * 通过插件查询更新商品
     * @param queryGoodsVO
     * @return
     */
    Result<PageView<GoodsVO>> queryProduct(PluginQueryGoodsVO queryGoodsVO);
}
