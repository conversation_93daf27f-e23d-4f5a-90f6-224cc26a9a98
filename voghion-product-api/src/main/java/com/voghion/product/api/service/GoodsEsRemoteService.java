package com.voghion.product.api.service;

import com.colorlight.base.model.PageView;
import com.voghion.product.api.dto.GoodsPriceDTO;
import com.voghion.product.api.input.*;
import com.voghion.product.api.output.*;
import com.voghion.product.model.dto.GoodsDTO;
import com.voghion.product.model.dto.GoodsInfoDTO;
import com.voghion.product.model.dto.GoodsInfoItemDTO;
import com.voghion.product.model.po.CategoryWord;
import com.voghion.product.model.po.ForBidWord;
import com.voghion.product.model.po.LimitWord;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public interface GoodsEsRemoteService {

    Map<String, BigDecimal> queryPriceByGoodsIdAndCountry(List<String> goodIds, String country);

    List<GoodsPriceDTO> queryAllPriceByGoodsIdAndCountry(List<String> goodIds, String country);

    List<GoodsESModelVo> queryGoodsByGoodsIdAndCountry(Map<Long, List<Long>> goodsIds, String country);

    List<GoodsESModelVo> queryGoodsByGoodsId(Map<Long, List<Long>> listMap);

    List<RecommentGoodsListESModelVo> queryRecommendGoodsByContition(RecommentGoodsListESModelDTO recommentGoodsListESModelDTO);


    List<GoodsESModelVo> queryGoodsByGoodsIdAndCountry(RecommentGoodsListESModelDTO recommentGoodsListESModelDTO);


    List<GoodsESModelVo> queryGoodsInfo(GoodsInput goodsInput);


    List<GoodsESModelVo> queryGoodsByCategoryIdAndCountry(RecommentGoodsListESModelDTO recommentGoodsListESModelDTO);


    List<Long> queryGoodsIdByGoodsIdAndCountry(RecommentGoodsListESModelDTO recommentGoodsListESModelDTO);

    List<GoodsESModelVo> queryGoodsByCondition(GoodsConditionInput goodsConditionInput);

    List<GoodsFreightVO> queryGoodsFreightFree(List<GoodsFreightDTO> goodsFreightDTOS);

    PageView<GoodsESModel> queryGoods(GoodsEsQueryInput input);

    PageView<GoodsOperateLogVo> queryGoodsItemOperationLog(GoodsInput input);

    List<GoodsOperateFreightLogVo> queryGoodsFreightOperationLog(GoodsInput input);

    Boolean deleteGoodsInES(List<Long> goodsIds);

    PageView<GoodsInfoDTO> queryPageBackgroundByOption(GoodsDTO goodsDTO);

    List<GoodsESModelVo> queryGoodsByIdsAndInvalidCategoryIds(List<Long> goodsIds, List<Long> invalidCategoryIds);

    List<GoodsESModelVo> queryGoodsByIdsAndInvalidCategoryIds2(List<Long> goodsIds, List<Long> invalidCategoryIds);

    GoodsEsDetailVo queryGoodsDetail(Long goodsId);

    /**
     * 查询商品查询结果带sku信息
     *
     * @param goodsDTO
     * @return
     */
    List<GoodsInfoItemDTO> queryGoodsBySkuItem(GoodsDTO goodsDTO);

    /**
     * 统计店铺下的可售商品数量
     *
     * @param shopId
     * @return
     */
    Long countGoodsByShopId(Long shopId);

    void addKeyWord(String index, String type, List<CategoryWord> categoryWords);

    void addKeyWord2(String index, String type, List<ForBidWord> forBidWordList);

    void addKeyWord3(String index, String type, List<LimitWord> forBidWordList);

    void dealGoods(List<String> goods) throws IOException;

    /**
     * 敏感词过滤
     */
    List<GoodsFilterVONew> filterGoods(Long shopId, List<Long> goodIds);

    List<GoodsFilterVONew> shopIdFilterGoods(Long shopIds);

    Map<Long, String> queryGoodsShowStatus(List<Long> goodsIds);

    List<GoodsESModelSimpleVo> querySimpleGoodsByGoodsIdAndCountry(RecommentGoodsQueryDTO recommentGoodsQueryDTO);
}
