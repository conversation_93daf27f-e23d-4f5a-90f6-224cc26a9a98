package com.voghion.product.api.service;


import com.colorlight.base.model.Result;
import com.voghion.product.api.dto.GoodsRankingConfigDTO;
import com.voghion.product.api.dto.GoodsRankingListDTO;
import com.voghion.product.api.input.GoodsRankingInput;

import java.util.List;

/**
 * GoodsRankingRemoteService
 *
 * <AUTHOR>
 * @date 2024/10/08
 */
public interface GoodsRankingRemoteService {

    Result<Boolean> syncSaleList();

    Result<Boolean> syncCommentList();

    Result<Boolean> syncGoodsRankingNum();

    Result<List<GoodsRankingConfigDTO>> getRankingLists();

    Result<List<GoodsRankingConfigDTO>> getSaleLists(GoodsRankingInput goodsRankingInput);

    Result<List<GoodsRankingConfigDTO>> getCommentLists(GoodsRankingInput goodsRankingInput);

    Result<GoodsRankingConfigDTO> getRankingConfig(GoodsRankingInput goodsRankingInput);


    Result<List<GoodsRankingListDTO>> getRankingListGoods(GoodsRankingInput goodsRankingInput);

    Result<List<GoodsRankingListDTO>> getBestAcrossSite(GoodsRankingInput goodsRankingInput);

}
