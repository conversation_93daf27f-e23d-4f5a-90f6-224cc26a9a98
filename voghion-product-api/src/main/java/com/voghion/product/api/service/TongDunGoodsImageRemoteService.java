package com.voghion.product.api.service;

import com.colorlight.base.model.Result;
import com.voghion.product.api.input.QueryTokenDTO;
import com.voghion.product.api.input.TongDunGoodsImagesVo;
import com.voghion.product.api.output.MerchantsTokenOutput;
import com.voghion.product.model.dto.ToAuditDto;
import com.voghion.product.model.po.TongDunGoodsImages;
import com.voghion.product.model.vo.TongDunGoodsImagesVO;
import com.voghion.product.model.vo.TongDunVO;

import java.util.List;

public interface TongDunGoodsImageRemoteService {

    Result<Boolean> tongDunGoodsImageTask();

    Result<Boolean> selfGoodsImageSendTask();

    Result<Boolean> simulateTongDunCheckFail();

    Result<Boolean> simulateTongDunCheckSuccess();

    Result<Boolean> simulateTongDunCheckPickedShopSuccess();

    Result<Boolean> addTongDun(TongDunGoodsImagesVo tongDunGoodsImagesVo);


    /**
     * 查询已经初始化但12h没发到同盾的告警
     * */
    Result<List<TongDunGoodsImages>> queryInitGoodsException();

    /**
     * 查询已经发到同盾但18h没有回调的告警
     * */
    Result<List<TongDunGoodsImages>> querySlowTongDun();

    Result<List<TongDunGoodsImagesVO>> queryByCondition(TongDunGoodsImagesVO vo);

    /**
     * 商品去审核（加入同盾）
     */
    Result<Boolean> addTongDun(ToAuditDto vo);
}
