package com.voghion.product.api.service;

import com.voghion.product.model.vo.condition.GoodsAppealTaskSaveVo;
import com.voghion.product.model.vo.GoodsAuditTaskUpdateLevelVo;

public interface GoodsAuditTaskRemoteService {
    void distributeTask();

    void autoRuleAudit(Boolean handleAll);

    Boolean saveAppealTask(GoodsAppealTaskSaveVo goodsAppealTaskSaveVo);

    Boolean updateAuditTaskLevel(GoodsAuditTaskUpdateLevelVo auditTaskUpdateLevelVo);

    void clearDeleteGoodsTask();
}
