package com.voghion.product.api.service;


import com.voghion.product.api.dto.ActivityOriginalPriceDto;

import java.util.List;

/**
 * 备份恢复价格
 *
 * <AUTHOR>
 * @date 2022/3/9
 */
public interface ActivityOriginalPriceRemoteService {
    /**
     * 查询进行中flashDeal活动的商品原价格
     * @param activityId
     * @param goodsIds
     * @return
     */
    List<ActivityOriginalPriceDto> queryStartingActivityGoodsInfo(Long activityId, List<Long> goodsIds);
}
